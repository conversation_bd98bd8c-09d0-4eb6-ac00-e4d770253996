export default defineNuxtPlugin(() => {
    const directusUrl = 'http://localhost:8055';
  
    return {
      provide: {
        fetchJson: async (endpoint: string) => {
          const res = await fetch(`${directusUrl}/items/${endpoint}`);
          return res.json();
        },
        savePage: async (data: any) => {
          return fetch(`${directusUrl}/items/wpage`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
          });
        },
        saveBlock: async (data: any) => {
          return fetch(`${directusUrl}/items/wblock`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
          });
        },
        updatePage: async (id: number, data: any) => {
          return fetch(`${directusUrl}/items/wpage/${id}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
          });
        },
        updateBlock: async (id: number, data: any) => {
          return fetch(`${directusUrl}/items/wblock/${id}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
          });
        }
      }
    };
  });
  