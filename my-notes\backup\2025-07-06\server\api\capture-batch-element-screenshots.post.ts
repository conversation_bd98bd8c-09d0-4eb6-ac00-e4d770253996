import { defineEvent<PERSON><PERSON><PERSON>, readBody, createError } from 'h3'
import puppeteer from 'puppeteer'
import { load } from 'cheerio'

// Функция нормализации HTML для точного сопоставления
function normalizeHtml(html: string): string {
  return html
    .replace(/\s+/g, ' ')     // Множественные пробелы в один
    .replace(/>\s+</g, '><')  // Пробелы между тегами
    .trim()                   // Убираем пробелы в начале и конце
}

interface ElementScreenshotItem {
  html: string
  selector: string // Оставляем для fallback
  elementTitle: string
  elementNumber: string
  elementHtml: string // Точный HTML код элемента для сопоставления
}

interface BatchElementScreenshotRequest {
  elements: ElementScreenshotItem[]
  width?: number
  height?: number
}

interface ElementScreenshotResult {
  fileId: string | null
  filename: string
  success: boolean
  error?: string
  elementTitle: string
  elementNumber: string
  dimensions?: { width: number; height: number }
}

interface BatchElementScreenshotResponse {
  results: ElementScreenshotResult[]
  totalTime: number
  successCount: number
  errorCount: number
  skippedCount: number
}

export default defineEventHandler(async (event) => {
  const startTime = Date.now()
  const { elements, width = 1400, height = 800 } = await readBody<BatchElementScreenshotRequest>(event)

  if (!elements || !Array.isArray(elements) || elements.length === 0) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Elements array is required and must not be empty'
    })
  }

  console.log(`🔄 Создание ${elements.length} скриншотов элементов в batch режиме...`)

  // Запускаем браузер ОДИН РАЗ для всех элементов
  const browser = await puppeteer.launch({
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-default-apps',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI',
      '--disable-extensions',
      '--disable-plugins'
    ]
  })

  const results: ElementScreenshotResult[] = []
  let skippedCount = 0

  try {
    // Группируем элементы по HTML документу для оптимизации
    const htmlGroups = new Map<string, ElementScreenshotItem[]>()

    elements.forEach(element => {
      const htmlKey = element.html
      if (!htmlGroups.has(htmlKey)) {
        htmlGroups.set(htmlKey, [])
      }
      htmlGroups.get(htmlKey)!.push(element)
    })

    console.log(`📊 Сгруппировано в ${htmlGroups.size} уникальных HTML документов`)

    // Обрабатываем каждую группу HTML
    for (const [htmlContent, groupElements] of htmlGroups) {
      console.log(`📄 Обработка HTML документа с ${groupElements.length} элементами...`)

      // ПРОСТОЕ РЕШЕНИЕ: ИСПОЛЬЗУЕМ СЕЛЕКТОРЫ С ПОРЯДКОВЫМИ НОМЕРАМИ
      const elementMap = new Map<string, ElementScreenshotItem>()

      groupElements.forEach((element, index) => {
        const uniqueId = `screenshot-element-${index}-${Date.now()}`
        elementMap.set(uniqueId, element)
        console.log(`🏷️ Подготовлен элемент ${element.elementTitle} с селектором ${element.selector}`)
      })

      const modifiedHtml = htmlContent // Используем оригинальный HTML без изменений

      const page = await browser.newPage()

      try {
        // Устанавливаем viewport ОДИН РАЗ для группы
        await page.setViewport({ width, height })
        console.log(`📐 Установлен viewport: ${width}x${height}`)

        // Загружаем МОДИФИЦИРОВАННЫЙ HTML контент с data-атрибутами
        try {
          await page.setContent(modifiedHtml, {
            waitUntil: 'domcontentloaded',
            timeout: 60000
          })
          console.log('📄 HTML контент с data-атрибутами загружен')

          // Оптимизированная базовая задержка для полной загрузки контента
          console.log('⏱️ Базовая задержка 3000ms для полной загрузки контента...')
          await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))

          // Ждем загрузки всех изображений с увеличенным timeout
          console.log('🖼️ Ожидание загрузки всех изображений с timeout 10000ms...')
          await page.evaluate(() => {
            return Promise.all(
              Array.from(document.images)
                .filter(img => !img.complete)
                .map(img => new Promise(resolve => {
                  img.onload = img.onerror = resolve
                  // Увеличенный timeout для изображений
                  setTimeout(resolve, 10000)
                }))
            )
          })

          // УМНАЯ прокрутка для активации scroll-анимаций (имитация ручного процесса)
          console.log('📜 УМНАЯ прокрутка для активации scroll-анимаций...')
          await page.evaluate(() => {
            return new Promise((resolve) => {
              // Имитируем ручной процесс: медленная прокрутка вниз, затем быстро вверх
              const scrollToBottom = () => {
                // Медленная прокрутка вниз по частям для активации всех scroll-триггеров
                let currentScroll = 0
                const maxScroll = document.body.scrollHeight
                const scrollStep = Math.max(200, maxScroll / 10) // Прокручиваем по частям

                const scrollInterval = setInterval(() => {
                  currentScroll += scrollStep
                  window.scrollTo(0, Math.min(currentScroll, maxScroll))

                  if (currentScroll >= maxScroll) {
                    clearInterval(scrollInterval)
                    // Пауза внизу для активации анимаций
                    setTimeout(scrollToTop, 2000)
                  }
                }, 300) // Медленная прокрутка для активации всех триггеров
              }

              // Быстрая прокрутка вверх
              const scrollToTop = () => {
                window.scrollTo(0, 0)
                setTimeout(resolve, 1500) // Пауза вверху для стабилизации
              }

              // Начинаем процесс
              setTimeout(scrollToBottom, 1000)
            })
          })

          // Анализ анимаций на странице
          console.log('🎭 Анализ анимаций на странице...')
          const animationInfo = await page.evaluate(() => {
            const info = {
              hasAnimations: false,
              hasJSAnimations: false,
              hasLibs: false,
              animationCount: 0,
              maxDuration: 0
            }

            // Проверяем CSS анимации
            const allElements = Array.from(document.querySelectorAll('*'))
            allElements.forEach(el => {
              const style = window.getComputedStyle(el)
              if (style.animationDuration !== '0s' || style.transitionDuration !== '0s') {
                info.hasAnimations = true
                info.animationCount++
                const animDuration = parseFloat(style.animationDuration) * 1000
                const transDuration = parseFloat(style.transitionDuration) * 1000
                info.maxDuration = Math.max(info.maxDuration, animDuration, transDuration)
              }
            })

            // Проверяем библиотеки анимаций
            info.hasLibs = !!(window.AOS || window.gsap || window.anime || window.ScrollMagic)

            // Проверяем JS анимации
            info.hasJSAnimations = !!(window.jQuery && window.jQuery.fn.animate) ||
              !!(window.requestAnimationFrame) ||
              document.querySelectorAll('[data-aos], [data-animate], .animate__animated').length > 0

            return info
          })

          // Определяем дополнительное время ожидания на основе анализа
          let additionalWaitTime = 0
          if (animationInfo.hasAnimations || animationInfo.hasJSAnimations) {
            if (animationInfo.maxDuration > 2000) {
              // Для длинных анимаций ждем дольше
              additionalWaitTime = Math.min(animationInfo.maxDuration + 1000, 5000)
            } else if (animationInfo.hasJSAnimations) {
              additionalWaitTime = 1500
            } else if (animationInfo.hasLibs) {
              // Для библиотек анимаций используем стандартную задержку
              additionalWaitTime = 1200
            } else {
              additionalWaitTime = 800
            }

            // Дополнительное время для множественных анимаций
            if (animationInfo.animationCount > 5) {
              additionalWaitTime += 300
            }
          }

          if (additionalWaitTime > 0) {
            console.log(`⏱️ Дополнительное ожидание ${additionalWaitTime}ms для анимаций...`)
            await page.evaluate((delay: number) => new Promise(resolve => setTimeout(resolve, delay)), additionalWaitTime)
          }

          // Оптимизированная финальная стабилизация после всех операций
          console.log('⏱️ Финальная стабилизация 2000ms...')
          await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 2000)))

          console.log(`⏱️ Анимации - ${animationInfo.hasAnimations}, JS анимации - ${animationInfo.hasJSAnimations}, библиотеки - ${animationInfo.hasLibs}, количество - ${animationInfo.animationCount}, макс. длительность - ${animationInfo.maxDuration}ms`)
          console.log('📄 Дополнительное время для ресурсов завершено')
        } catch (loadError) {
          console.warn('⚠️ Ошибка загрузки HTML, пробуем упрощенную загрузку:', loadError.message)

          await page.setContent(modifiedHtml, {
            waitUntil: 'domcontentloaded',
            timeout: 30000
          })
          console.log('📄 HTML контент загружен (упрощенный режим)')
          await new Promise(resolve => setTimeout(resolve, 1000))
        }

        // Обрабатываем элементы группами по 20 для стабильности
        const ELEMENT_BATCH_SIZE = 20
        const processedCount = 0

        // Создаем счетчики для каждого типа селектора
        const selectorCounts = new Map<string, number>()

        // Обрабатываем элементы по порядку
        for (const [uniqueId, element] of elementMap) {
          try {
            console.log(`🔍 Поиск элемента: ${element.elementTitle}`)
            console.log(`🔧 Базовый селектор: ${element.selector}`)

            // Получаем или инициализируем счетчик для этого селектора
            const currentCount = selectorCounts.get(element.selector) || 0
            selectorCounts.set(element.selector, currentCount + 1)

            let targetElement = null

            // Сначала проверяем, сколько элементов с таким селектором есть на странице
            const elementsCount = await page.$$eval(element.selector, elements => elements.length)
            console.log(`📊 Найдено ${elementsCount} элементов с селектором: ${element.selector}`)

            if (elementsCount === 0) {
              console.error(`❌ Элементы с селектором не найдены: ${element.selector}`)
            } else if (elementsCount === 1) {
              // Если элемент один - используем базовый селектор для всех
              console.log(`🎯 Один элемент - используем базовый селектор: ${element.selector}`)
              targetElement = await page.$(element.selector)
              if (targetElement) {
                console.log(`✅ Найден единственный элемент`)
              }
            } else {
              // Если элементов несколько - получаем все элементы массивом
              console.log(`🎯 Несколько элементов (${elementsCount}) - получаем по индексу ${currentCount}`)

              try {
                // Получаем все элементы с таким селектором
                const allElements = await page.$$(element.selector)
                console.log(`📋 Получено ${allElements.length} элементов массивом`)

                // Берем элемент по индексу (currentCount начинается с 0)
                if (currentCount < allElements.length) {
                  targetElement = allElements[currentCount]
                  console.log(`✅ Найден элемент по индексу ${currentCount} из ${allElements.length}`)
                } else {
                  console.warn(`⚠️ Индекс ${currentCount} больше количества элементов ${allElements.length}`)
                  // Берем последний доступный элемент
                  targetElement = allElements[allElements.length - 1]
                  console.log(`🔄 Используем последний элемент (индекс ${allElements.length - 1})`)
                }
              } catch (arrayError) {
                console.error(`❌ Ошибка получения массива элементов:`, arrayError)
                // Fallback на базовый селектор
                console.log(`🔄 Fallback на базовый селектор: ${element.selector}`)
                targetElement = await page.$(element.selector)
                if (targetElement) {
                  console.log(`⚠️ Найден элемент по базовому селектору (возможен дубликат)`)
                }
              }
            }

            if (!targetElement) {
              console.error(`❌ Элемент НЕ НАЙДЕН ни по одному селектору: ${element.elementTitle}`)
              results.push({
                fileId: null,
                filename: '',
                success: false,
                error: `Element not found by any selector`,
                elementTitle: element.elementTitle,
                elementNumber: element.elementNumber
              })
              continue
            }

            // Получаем размеры элемента
            const boundingBox = await targetElement.boundingBox()

            if (!boundingBox) {
              console.warn(`⚠️ Не удалось получить размеры элемента: ${element.selector}`)
              results.push({
                fileId: null,
                filename: '',
                success: false,
                error: `Could not get element dimensions`,
                elementTitle: element.elementTitle,
                elementNumber: element.elementNumber
              })
              continue
            }

            // ФИЛЬТРАЦИЯ: Пропускаем элементы с нулевыми размерами
            if (boundingBox.width <= 0 || boundingBox.height <= 0) {
              console.log(`⏭️ Пропускаем элемент ${element.elementTitle}: размеры ${boundingBox.width}x${boundingBox.height}px`)
              skippedCount++
              results.push({
                fileId: null,
                filename: '',
                success: false,
                error: `Element has zero dimensions: ${boundingBox.width}x${boundingBox.height}px`,
                elementTitle: element.elementTitle,
                elementNumber: element.elementNumber,
                dimensions: { width: boundingBox.width, height: boundingBox.height }
              })
              continue
            }

            // ФИЛЬТРАЦИЯ: Пропускаем слишком маленькие элементы (меньше 10x10px)
            if (boundingBox.width < 10 || boundingBox.height < 10) {
              console.log(`⏭️ Пропускаем слишком маленький элемент ${element.elementTitle}: ${boundingBox.width}x${boundingBox.height}px`)
              skippedCount++
              results.push({
                fileId: null,
                filename: '',
                success: false,
                error: `Element too small: ${boundingBox.width}x${boundingBox.height}px`,
                elementTitle: element.elementTitle,
                elementNumber: element.elementNumber,
                dimensions: { width: boundingBox.width, height: boundingBox.height }
              })
              continue
            }

            console.log(`📏 Размеры элемента: ${boundingBox.width}x${boundingBox.height}px (x:${boundingBox.x}, y:${boundingBox.y})`)

            // Создаем скриншот элемента
            const screenshotBuffer = await targetElement.screenshot({
              type: 'png',
              omitBackground: false
            })

            console.log(`📸 Скриншот элемента создан (${screenshotBuffer.length} bytes)`)

            // Загружаем скриншот в Directus
            const sanitizedTitle = element.elementTitle.replace(/[^a-zA-Z0-9\-_]/g, '_')
            const filename = `element_${element.elementNumber}_${sanitizedTitle}.png`

            const fileId = await uploadToDirectus(screenshotBuffer, filename)

            results.push({
              fileId,
              filename,
              success: true,
              elementTitle: element.elementTitle,
              elementNumber: element.elementNumber,
              dimensions: { width: boundingBox.width, height: boundingBox.height }
            })

            console.log(`✅ Скриншот элемента ${element.elementTitle} создан и загружен: ${fileId}`)

          } catch (elementError) {
            console.error(`❌ Ошибка обработки элемента ${element.elementTitle}:`, elementError)
            results.push({
              fileId: null,
              filename: '',
              success: false,
              error: elementError.message,
              elementTitle: element.elementTitle,
              elementNumber: element.elementNumber
            })
          }
        }

        console.log(`✅ Обработано ${elementMap.size} элементов`)

      } finally {
        // Закрываем страницу после обработки всех элементов группы
        await page.close()
      }
    }

  } catch (error) {
    console.error('❌ Критическая ошибка batch обработки элементов:', error)
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to process batch element screenshots'
    })
  } finally {
    // Закрываем браузер
    await browser.close()
    console.log('🔒 Браузер закрыт')
  }

  const totalTime = Date.now() - startTime
  const successCount = results.filter(r => r.success).length
  const errorCount = results.length - successCount

  console.log(`✅ Batch обработка элементов завершена: ${successCount}/${results.length} успешно, ${skippedCount} пропущено за ${totalTime}ms`)

  return {
    results,
    totalTime,
    successCount,
    errorCount,
    skippedCount
  } as BatchElementScreenshotResponse
})

// Функция загрузки в Directus
async function uploadToDirectus(screenshot: Buffer, filename: string): Promise<string> {
  const formData = new FormData()
  formData.append('title', filename)

  const blob = new Blob([screenshot], { type: 'image/png' })
  formData.append('file', blob, filename)

  const response = await fetch('http://localhost:8055/files', {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error(`Failed to upload to Directus: ${response.statusText}`)
  }

  const data = await response.json()
  return data.data.id
}
