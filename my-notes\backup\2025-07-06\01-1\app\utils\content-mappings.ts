// Маппинги для интеллектуального внедрения контента

export interface ContentMapping {
  type: string
  variant: string
  data: Record<string, any>
}

// Библиотека контента
export const contentLibrary = {
  headings: {
    short: [
      'Новости', 'О нас', 'Услуги', 'Контакты', 'Портфолио', 'Команда',
      'Цены', 'Отзывы', 'Блог', 'FAQ', 'Карьера', 'Партнеры'
    ],
    medium: [
      'Последние новости', 'О нашей компании', 'Наши услуги', 'Свяжитесь с нами',
      'Наше портфолио', 'Наша команда', 'Тарифы и цены', 'Отзывы клиентов',
      'Корпоративный блог', 'Часто задаваемые вопросы', 'Вакансии', 'Наши партнеры'
    ],
    long: [
      'Актуальные новости и события компании',
      'Подробная информация о нашей компании и истории',
      'Полный спектр предоставляемых услуг и решений',
      'Как связаться с нами и получить персональную консультацию',
      'Примеры наших успешных проектов и достижений',
      'Познакомьтесь с нашей профессиональной командой экспертов',
      'Прозрачные тарифы и гибкие условия сотрудничества',
      'Честные отзывы наших довольных клиентов',
      'Экспертные статьи и полезные материалы от наших специалистов',
      'Ответы на самые популярные вопросы наших клиентов',
      'Присоединяйтесь к нашей команде профессионалов',
      'Стратегические партнеры и надежные поставщики'
    ]
  },
  
  paragraphs: {
    short: [
      'Краткое описание наших возможностей.',
      'Основная информация о продуктах.',
      'Важные детали для клиентов.',
      'Преимущества нашего подхода.',
      'Качественные решения для бизнеса.'
    ],
    medium: [
      'Мы предлагаем инновационные решения, которые помогают нашим клиентам достигать поставленных целей и развивать свой бизнес.',
      'Наша команда профессионалов обладает многолетним опытом работы в различных отраслях и готова предложить оптимальные решения.',
      'Мы используем современные технологии и проверенные методики для обеспечения высокого качества наших услуг.',
      'Индивидуальный подход к каждому клиенту позволяет нам создавать персонализированные решения под конкретные задачи.',
      'Наша миссия - помочь вашему бизнесу расти и процветать в условиях современного рынка.'
    ],
    long: [
      'Наша компания специализируется на предоставлении комплексных решений для бизнеса любого масштаба. Мы понимаем, что каждый клиент уникален, поэтому разрабатываем индивидуальные стратегии, учитывающие специфику отрасли, размер компании и поставленные цели. Наш опыт работы с ведущими компаниями различных сфер деятельности позволяет нам предлагать проверенные и эффективные решения.',
      'За годы работы мы накопили обширную экспертизу в области современных технологий и бизнес-процессов. Наша команда состоит из высококвалифицированных специалистов, которые постоянно совершенствуют свои навыки и следят за последними тенденциями рынка. Мы гордимся тем, что можем предложить нашим клиентам самые передовые решения, которые помогают им оставаться конкурентоспособными.',
      'Качество наших услуг подтверждается многочисленными положительными отзывами клиентов и успешно реализованными проектами. Мы строим долгосрочные партнерские отношения, основанные на доверии, прозрачности и взаимном уважении. Наша цель - не просто выполнить поставленную задачу, а стать надежным партнером в развитии вашего бизнеса.'
    ]
  },
  
  buttons: {
    short: [
      'Далее', 'Купить', 'Узнать', 'Заказать', 'Связаться', 'Читать',
      'Скачать', 'Регистрация', 'Войти', 'Отправить', 'Выбрать', 'Начать'
    ],
    medium: [
      'Подробнее', 'Заказать сейчас', 'Узнать больше', 'Связаться с нами',
      'Читать далее', 'Скачать каталог', 'Зарегистрироваться', 'Войти в систему',
      'Отправить заявку', 'Выбрать тариф', 'Начать работу', 'Получить консультацию'
    ],
    long: [
      'Подробная информация', 'Заказать персональную консультацию', 'Узнать все подробности',
      'Связаться с нашими специалистами', 'Читать полную статью', 'Скачать подробный каталог',
      'Зарегистрироваться бесплатно', 'Войти в личный кабинет', 'Отправить подробную заявку',
      'Выбрать подходящий тариф', 'Начать бесплатный период', 'Получить бесплатную консультацию'
    ]
  },
  
  images: {
    short: [
      'https://via.placeholder.com/150x100/007bff/ffffff?text=Изображение',
      'https://via.placeholder.com/200x150/28a745/ffffff?text=Фото',
      'https://via.placeholder.com/250x200/dc3545/ffffff?text=Картинка'
    ],
    medium: [
      'https://via.placeholder.com/300x200/007bff/ffffff?text=Наша+Команда',
      'https://via.placeholder.com/400x300/28a745/ffffff?text=Наши+Услуги',
      'https://via.placeholder.com/350x250/dc3545/ffffff?text=Портфолио'
    ],
    long: [
      'https://via.placeholder.com/600x400/007bff/ffffff?text=Профессиональная+Команда',
      'https://via.placeholder.com/800x500/28a745/ffffff?text=Качественные+Услуги',
      'https://via.placeholder.com/700x450/dc3545/ffffff?text=Успешные+Проекты'
    ]
  },
  
  lists: {
    short: [
      ['Пункт 1', 'Пункт 2', 'Пункт 3'],
      ['Услуга А', 'Услуга Б', 'Услуга В'],
      ['Шаг 1', 'Шаг 2', 'Шаг 3'],
      ['Опция 1', 'Опция 2', 'Опция 3']
    ],
    medium: [
      ['Консультация специалистов', 'Разработка решения', 'Внедрение и поддержка'],
      ['Анализ потребностей', 'Создание стратегии', 'Реализация проекта'],
      ['Первичная встреча', 'Техническое задание', 'Выполнение работ'],
      ['Базовый пакет', 'Расширенный пакет', 'Премиум пакет']
    ],
    long: [
      [
        'Бесплатная консультация и анализ текущей ситуации',
        'Разработка индивидуального решения под ваши задачи',
        'Поэтапное внедрение с контролем качества',
        'Обучение персонала и техническая поддержка'
      ],
      [
        'Глубокий анализ бизнес-процессов и потребностей компании',
        'Создание комплексной стратегии развития и оптимизации',
        'Профессиональная реализация проекта с соблюдением сроков',
        'Постпроектное сопровождение и долгосрочная поддержка'
      ],
      [
        'Детальное изучение специфики вашего бизнеса и отрасли',
        'Формирование технического задания с учетом всех требований',
        'Качественное выполнение работ с промежуточным контролем',
        'Тестирование, оптимизация и передача готового решения'
      ]
    ]
  }
}

// Универсальные переменные для HBS шаблонов
export const universalVariables = {
  // Заголовки
  title: 'Заголовок',
  heading: 'Основной заголовок',
  subtitle: 'Подзаголовок',
  
  // Текст
  text: 'Основной текст',
  description: 'Описание',
  content: 'Содержимое',
  
  // Кнопки
  button: 'Кнопка',
  buttonText: 'Текст кнопки',
  link: 'Ссылка',
  
  // Изображения
  image: 'https://via.placeholder.com/300x200',
  imageUrl: 'https://via.placeholder.com/300x200',
  imageAlt: 'Описание изображения',
  
  // Списки
  list: ['Пункт 1', 'Пункт 2', 'Пункт 3'],
  items: ['Элемент 1', 'Элемент 2', 'Элемент 3'],
  
  // Контактная информация
  phone: '+7 (999) 123-45-67',
  email: '<EMAIL>',
  address: 'г. Москва, ул. Примерная, д. 123',
  
  // Социальные сети
  facebook: '#',
  twitter: '#',
  instagram: '#',
  linkedin: '#'
}

// Функция генерации контента по типу и варианту
export function generateContentData(contentType: string, variant: string = 'medium'): Record<string, any> {
  const content = contentLibrary[contentType]?.[variant] || []
  const randomIndex = Math.floor(Math.random() * content.length)
  
  // Базовые данные
  const baseData = { ...universalVariables }
  
  // Специфичные данные по типу контента
  switch (contentType) {
    case 'headings':
      baseData.title = content[randomIndex] || 'Заголовок'
      baseData.heading = content[randomIndex] || 'Заголовок'
      baseData.subtitle = contentLibrary.paragraphs[variant]?.[0] || 'Подзаголовок'
      break
      
    case 'paragraphs':
      baseData.text = content[randomIndex] || 'Текст'
      baseData.description = content[randomIndex] || 'Описание'
      baseData.content = content[randomIndex] || 'Содержимое'
      break
      
    case 'buttons':
      baseData.button = content[randomIndex] || 'Кнопка'
      baseData.buttonText = content[randomIndex] || 'Кнопка'
      baseData.link = '#'
      break
      
    case 'images':
      baseData.image = content[randomIndex] || universalVariables.image
      baseData.imageUrl = content[randomIndex] || universalVariables.imageUrl
      baseData.imageAlt = 'Изображение'
      break
      
    case 'lists':
      baseData.list = content[randomIndex] || universalVariables.list
      baseData.items = content[randomIndex] || universalVariables.items
      break
  }
  
  return baseData
}

// Функция получения всех типов контента
export function getContentTypes(): string[] {
  return Object.keys(contentLibrary)
}

// Функция получения вариантов для типа контента
export function getContentVariants(): string[] {
  return ['short', 'medium', 'long']
}

// Функция получения превью контента
export function getContentPreview(contentType: string, variant: string = 'medium', count: number = 3): any[] {
  const content = contentLibrary[contentType]?.[variant] || []
  
  return content.slice(0, count).map((item, index) => ({
    id: index + 1,
    title: `${contentType} ${index + 1}`,
    preview: Array.isArray(item) ? item.join(', ') : item.toString().substring(0, 50) + '...'
  }))
}
