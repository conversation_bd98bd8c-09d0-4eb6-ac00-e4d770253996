<template>
  <div class="filter-panel p-2 border rounded-md bg-gray-50 dark:bg-gray-800">
    <div class="flex justify-between items-center mb-2">
      <h3 class="text-sm font-medium">Фильтры</h3>
      <div class="flex items-center gap-2">
        <span class="text-xs">Режим:</span>
        <SelectButton
          v-model="filterMode"
          :options="filterModeOptions"
          option-label="label"
          option-value="value"
          class="text-xs"
          @change="$emit('filter-mode-change', filterMode)"
        />
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
      <!-- Фильтр по типам блоков -->
      <div class="field">
        <label class="block text-xs mb-1">Типы блоков</label>
        <MultiSelect
          v-model="selectedBlockTypes"
          :options="blockTypeOptions"
          placeholder="Выберите типы блоков"
          display="chip"
          class="w-full text-xs"
          panel-class="text-xs"
          :pt="{
            item: { class: 'text-xs' },
            header: { class: 'text-xs' },
          }"
          @change="applyFilters"
        />
      </div>
      
      <!-- Фильтр по макетам -->
      <div class="field">
        <label class="block text-xs mb-1">Макеты</label>
        <MultiSelect
          v-model="selectedLayouts"
          :options="layoutOptions"
          placeholder="Выберите макеты"
          display="chip"
          class="w-full text-xs"
          panel-class="text-xs"
          :pt="{
            item: { class: 'text-xs' },
            header: { class: 'text-xs' },
          }"
          @change="applyFilters"
        />
      </div>
      
      <!-- Фильтр по элементам -->
      <div class="field">
        <label class="block text-xs mb-1">Элементы</label>
        <MultiSelect
          v-model="selectedElements"
          :options="elementOptions"
          placeholder="Выберите элементы"
          display="chip"
          class="w-full text-xs"
          panel-class="text-xs"
          :pt="{
            item: { class: 'text-xs' },
            header: { class: 'text-xs' },
          }"
          @change="applyFilters"
        />
      </div>
      
      <!-- Фильтр по коллекциям -->
      <div class="field">
        <label class="block text-xs mb-1">Коллекции</label>
        <MultiSelect
          v-model="selectedCollections"
          :options="collectionOptions"
          placeholder="Выберите коллекции"
          display="chip"
          class="w-full text-xs"
          panel-class="text-xs"
          :pt="{
            item: { class: 'text-xs' },
            header: { class: 'text-xs' },
          }"
          @change="applyFilters"
        />
      </div>
      
      <!-- Фильтр по концепциям -->
      <div class="field">
        <label class="block text-xs mb-1">Концепции</label>
        <MultiSelect
          v-model="selectedConcepts"
          :options="conceptOptions"
          placeholder="Выберите концепции"
          display="chip"
          class="w-full text-xs"
          panel-class="text-xs"
          :pt="{
            item: { class: 'text-xs' },
            header: { class: 'text-xs' },
          }"
          @change="applyFilters"
        />
      </div>
      
      <!-- Фильтр по стилям -->
      <div class="field">
        <label class="block text-xs mb-1">Стили</label>
        <MultiSelect
          v-model="selectedStyles"
          :options="styleOptions"
          placeholder="Выберите стили"
          display="chip"
          class="w-full text-xs"
          panel-class="text-xs"
          :pt="{
            item: { class: 'text-xs' },
            header: { class: 'text-xs' },
          }"
          @change="applyFilters"
        />
      </div>
      
      <!-- Фильтр по особенностям -->
      <div class="field">
        <label class="block text-xs mb-1">Особенности</label>
        <MultiSelect
          v-model="selectedFeatures"
          :options="featuresOptions"
          placeholder="Выберите особенности"
          display="chip"
          class="w-full text-xs"
          panel-class="text-xs"
          :pt="{
            item: { class: 'text-xs' },
            header: { class: 'text-xs' },
          }"
          @change="applyFilters"
        />
      </div>
    </div>
    
    <div class="flex justify-end mt-3">
      <Button
        label="Сбросить фильтры"
        icon="pi pi-filter-slash"
        class="p-button-sm p-button-outlined"
        @click="resetFilters"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps, watch } from 'vue'
import Button from 'primevue/button'
import MultiSelect from 'primevue/multiselect'
import SelectButton from 'primevue/selectbutton'

const props = defineProps({
  blockTypeOptions: {
    type: Array,
    default: () => []
  },
  layoutOptions: {
    type: Array,
    default: () => []
  },
  elementOptions: {
    type: Array,
    default: () => []
  },
  collectionOptions: {
    type: Array,
    default: () => []
  },
  conceptOptions: {
    type: Array,
    default: () => []
  },
  styleOptions: {
    type: Array,
    default: () => []
  },
  featuresOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'update:filters',
  'filter-mode-change'
])

// Состояние фильтров
const selectedBlockTypes = ref([])
const selectedLayouts = ref([])
const selectedElements = ref([])
const selectedCollections = ref([])
const selectedConcepts = ref([])
const selectedStyles = ref([])
const selectedFeatures = ref([])

// Режим фильтрации (OR/AND)
const filterMode = ref('or')
const filterModeOptions = [
  { label: 'ИЛИ', value: 'or' },
  { label: 'И', value: 'and' }
]

// Применение фильтров
const applyFilters = () => {
  emit('update:filters', {
    blockTypes: selectedBlockTypes.value,
    layouts: selectedLayouts.value,
    elements: selectedElements.value,
    collections: selectedCollections.value,
    concepts: selectedConcepts.value,
    styles: selectedStyles.value,
    features: selectedFeatures.value,
    mode: filterMode.value
  })
}

// Сброс всех фильтров
const resetFilters = () => {
  selectedBlockTypes.value = []
  selectedLayouts.value = []
  selectedElements.value = []
  selectedCollections.value = []
  selectedConcepts.value = []
  selectedStyles.value = []
  selectedFeatures.value = []
  applyFilters()
}

// Отслеживание изменений режима фильтрации
watch(filterMode, () => {
  applyFilters()
})
</script>

<style scoped>
.filter-panel {
  max-width: 100%;
}
</style>