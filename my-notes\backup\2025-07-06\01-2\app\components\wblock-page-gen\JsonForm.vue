<template>
  <div class="h-full bg-white p-1 overflow-auto">
    <form class="space-y-1" @submit.prevent="saveJson">
      <!-- Основные поля -->
      <div class="space-y-1">
        <div class="field mb-0" style="margin-bottom: 0">
          <InputText
            id="art"
            v-model="formData.art"
            required
            class="w-full text-xs [&>input]:text-xs"
            placeholder="Артикул JSON*"
            style="padding: 4px; font-size: 8px"
          />
        </div>

        <div class="field mb-0" style="margin-bottom: 0">
          <InputText
            id="title"
            v-model="formData.title"
            required
            class="w-full text-xs [&>input]:text-xs"
            placeholder="Название*"
            style="padding: 2px; font-size: 10px"
          />
        </div>

        <div class="field mb-0" style="margin-bottom: 0">
          <Textarea
            id="description"
            v-model="formData.description"
            rows="2"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Описание"
            style="padding: 2px; font-size: 9px"
          />
        </div>

        <div class="field mb-0" style="margin-top: 0">
          <MultiSelect
            v-model="formData.tags"
            :options="tagOptions"
            display="chip"
            class="text-xs w-full p-0"
            placeholder="Выберите теги"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-1">
          
          <PrismEditor
            v-model="formData.json"
            class="my-editor h-[300px] overflow-auto text-xs"
            :highlight="highlightJson"
            placeholder="Введите JSON данные"
            line-numbers
          />
        </div>
      </div>
    </form>

    <!-- Кнопки управления -->
    <div class="flex justify-end gap-2 mt-4 pt-4 border-t border-surface-200 dark:border-surface-700">
      <Button
        label="Отмена"
        text
        class="p-button-sm"
        @click="$emit('cancel')"
      />
      <Button label="Сохранить" class="p-button-sm" @click="saveJson" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { PrismEditor } from 'vue-prism-editor'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import MultiSelect from 'primevue/multiselect'
import 'vue-prism-editor/dist/prismeditor.min.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import { useDirectusItems } from '#imports'

const props = defineProps({
  json: {
    type: Object,
    default: () => ({
      art: '',
      title: '',
      description: '',
      tags: [],
      json: ''
    }),
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['save', 'cancel'])

// Опции для тегов из Directus
const tagOptions = ref<string[]>([])

// Загрузка тегов из коллекции wjson
const loadTagOptions = async () => {
  try {
    const { getItems } = useDirectusItems()
    const items = await getItems({
      collection: 'wjson',
      params: {
        limit: -1,
        fields: ['tags'],
      },
    })

    if (Array.isArray(items)) {
      const tags = new Set()
      items.forEach((item) => {
        if (item.tags && Array.isArray(item.tags)) {
          item.tags.forEach((tag) => tags.add(tag))
        }
      })
      tagOptions.value = Array.from(tags)
    }
  } catch (error) {
    console.error('Error loading tag options:', error)
  }
}

// Данные формы
const formData = ref({
  ...props.json,
})

// Syntax highlighting function
const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

// Save function
const saveJson = () => {
  emit('save', { ...formData.value })
}

// Watch for prop changes
watch(() => props.json, (newJson) => {
  if (newJson && Object.keys(newJson).length > 0) {
    formData.value = { ...newJson }
  }
}, { immediate: true, deep: true })

onMounted(async () => {
  await loadTagOptions()
  if (props.json && Object.keys(props.json).length > 0) {
    formData.value = { ...props.json }
  }
})
</script>

<style scoped>
.my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 8px;
    line-height: 1.2;
    padding: 0px;
  }

.my-editor .prism-editor__textarea:focus {
  outline: none;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: var(--text-color);
}
</style>
