import { defineEventHand<PERSON>, readBody, createError } from 'h3';

// Экспортируем функцию для возможности тестирования
export function parseTemplate(template) {
  try {
    const lines = template.split('\n');
    const jsonOutput = {};
    const processedLines = [];
    let currentEachBlock = null;
    let eachBlockCount = 0;
    let inComponent = false;
    let currentComponent = [];
    let variableCounters = {};

    // Функция для подсчета отступов
    const getIndentLevel = (line) => {
      const match = line.match(/^\s*/);
      return match ? match[0].length : 0;
    };

    // Функция для обработки строки шаблона
    const processLine = (line) => {
      // Очень точное регулярное выражение для background-image
      const bgRegex = /style\s*=\s*['"].*?background-image\s*:\s*url\s*\(\s*['"]?(.*?)['"]?\s*\)\s*;?['"]?/i;
      const match = line.match(bgRegex);
      
      if (match) {
        const url = match[1].replace(/[\\'"]/g, '');
        
        // В каждом блоке обрабатываем background-image по-разному
        if (currentEachBlock && (line.includes('.swiper-slide') || line.includes('.position-absolute'))) {
          // Для элементов внутри итераций - добавляем в items
          return line.replace(bgRegex, `style=\`background-image: url(\${${currentEachBlock}[i].imageBackground})\``);
        } else {
          // Для корневых элементов
          return line; 
        }
      }
      return line;
    };

    // Функция для извлечения атрибутов
    const extractAttributes = (line) => {
      const attrs = {};
      
      const attrMatch = line.match(/\(([^)]+)\)/);
      if (attrMatch) {
        const attrStr = attrMatch[1];
        let inQuotes = false;
        let inBraces = 0;
        let currentPair = '';
        const pairs = [];
        
        for (let i = 0; i < attrStr.length; i++) {
          const char = attrStr[i];
          
          if ((char === '"' || char === "'") && (i === 0 || attrStr[i-1] !== '\\')) {
            inQuotes = !inQuotes;
            currentPair += char;
          } else if (char === '{') {
            inBraces++;
            currentPair += char;
          } else if (char === '}') {
            inBraces--;
            currentPair += char;
          } else if (char === ',' && !inQuotes && inBraces === 0) {
            pairs.push(currentPair.trim());
            currentPair = '';
          } else {
            currentPair += char;
          }
        }
        
        if (currentPair.trim()) {
          pairs.push(currentPair.trim());
        }
        
        pairs.forEach(pair => {
          const eqIndex = pair.indexOf('=');
          if (eqIndex > 0) {
            const key = pair.substring(0, eqIndex).trim();
            let value = pair.substring(eqIndex + 1).trim();
            
            if ((value.startsWith('"') && value.endsWith('"')) || 
                (value.startsWith("'") && value.endsWith("'"))) {
              value = value.substring(1, value.length - 1);
            }
            
            attrs[key] = value;
          }
        });
      }

      const cleanLine = line.replace(/\([^)]*\)/, '');
      const classMatch = cleanLine.match(/\.([\w-]+)/g);
      if (classMatch) {
        const classes = classMatch.map(c => c.substring(1)).filter(cls => {
          return !/\.(html|php|aspx?|jsp|com|ru)$/i.test(cls) && 
                 !/^(www|http|https)/i.test(cls);
        });
        
        if (classes.length > 0) {
          attrs.class = classes.join(' ');
        }
      }
      
      return attrs;
    };

    // Функция для извлечения текста
    const extractText = (line) => {
      const trimmedLine = line.trim();
    
      // Обработка строк с явным текстом через '|'
      if (trimmedLine.startsWith('|')) {
        return trimmedLine.substring(1).trim();
      }
    
      // Регулярное выражение для извлечения текста после тега, классов, ID и атрибутов
      const regex = /^(\w+)?(\.[\w-]+)*(\#[\w-]+)?(\([^)]*\))?\s*(.*)$/;
      const match = trimmedLine.match(regex);
    
      if (match) {
        const text = match[5].trim(); // Текст находится в последней группе
        // Возвращаем текст только если он не пустой и не является частью атрибутов
        if (text && !text.startsWith('(') && !text.endsWith(')') && !text.match(/^[\.#]/)) {
          return text;
        }
      }
    
      return ''; // Нет текста
    };

    // Функция для форматирования атрибутов
    const formatAttributes = (attrs, varPrefix = '', blockName = null) => {
      if (!attrs || Object.keys(attrs).length === 0) return '';
      
      const parts = [];
      const attrsList = [];
      
      for (const [key, value] of Object.entries(attrs)) {
        if (key === 'class') continue;
        
        if (key === 'style' && value.includes('background-image')) {
          // Получаем URL из background-image
          const bgMatch = value.match(/url\(['"]?([^'"]+)['"]?\)/);
          if (bgMatch) {
            const url = bgMatch[1].replace(/['"]/g, '');
            
            if (blockName) {
              // Для итерируемых элементов
              attrsList.push(`style=\`background-image: url(\${${varPrefix}imageBackground})\``);
            } else {
              // Для корневых элементов
              jsonOutput.imageBackground = url;
              attrsList.push(`style=\`background-image: url(\${imageBackground})\``);
            }
          }
        } else if (key.startsWith('data-')) {
          attrsList.push(`${key}='${value}'`);
        } else if (key === 'href' || key === 'src') {
          const varName = key === 'href' ? 'url' : 'image';
          const fullVarName = blockName ? `${varPrefix}${varName}` : varName;
          const counterKey = blockName ? `${blockName}-${varName}` : varName;
          variableCounters[counterKey] = (variableCounters[counterKey] || 0) + 1;
          const varCount = variableCounters[counterKey];
          const numberedVarName = varCount > 1 ? `${fullVarName}${varCount}` : fullVarName;
      
          if (!blockName) { 
            jsonOutput[numberedVarName] = value; 
          }
          attrsList.push(`${key}=${numberedVarName}`);
        } else {
          // Используем общий счетчик для каждого типа переменной, 
          // но с учетом наличия блока
          // const counterKey = blockName ? `${blockName}-${key}` : key;
          // variableCounters[counterKey] = (variableCounters[counterKey] || 0) + 1;
          // const varCount = variableCounters[counterKey];
          // const numberedVarName = varCount > 1 ? `${key}${varCount}` : key;
      
          // if (!blockName) {
          //   jsonOutput[numberedVarName] = value; 
          // }
          // attrsList.push(`${key}=${numberedVarName}`);
          attrsList.push(`${key}='${value}'`);
        }
      }
      
      if (attrsList.length > 0) {
        parts.push(`(${attrsList.join(', ')})`);
      }
      
      return parts.join('');
    };

    // Функция для обработки компонента
    const processComponent = (lines) => {
      const component = {};
      const counters = {};
      let originalTexts = [];
      
      lines.forEach(line => {
        // Добавляем обработку элементов с точкой
        if (line.trim().startsWith('.') && !line.trim().startsWith('//')) {
          const text = extractText(line);
          if (text) component.text = text;
        }
        
        const trimmedLine = line.trim();
        if (!trimmedLine || trimmedLine.startsWith('//')) return;
        
        // Проверяем наличие background-image в строке
        const bgRegex = /style\s*=\s*['"].*?background-image\s*:\s*url\s*\(\s*['"]?(.*?)['"]?\s*\)\s*;?['"]?/i;
        const bgMatch = line.match(bgRegex);
        if (bgMatch) {
          const url = bgMatch[1].replace(/[\\'"]/g, '');
          component.imageBackground = url;
        }
        
        const tagMatch = trimmedLine.match(/^(\w+)/);
        if (!tagMatch) {
          // Проверяем, является ли строка текстовой строкой без тега
          if (trimmedLine.startsWith('|')) {
            const pureText = trimmedLine.substring(1).trim();
            if (pureText) {
              originalTexts.push(pureText);
            }
          }
          return;
        }
        
        const tag = tagMatch[1];
        const attrs = extractAttributes(trimmedLine);
        const text = extractText(trimmedLine);
        
        if (tag.startsWith('h') && tag.length <= 3 && tag[1] >= '1' && tag[1] <= '6') { // h1-h6
          if (text) {
            if (!counters.title) {
              component.title = text;
              counters.title = 1;
            } else {
              counters.title++;
              component[`title${counters.title}`] = text;
            }
          }
        } else if (tag === 'a') { 
          if (attrs.href) component.url = attrs.href;
          if (text) {
            if (!counters.linkText) {
              component.linkText = text;
              counters.linkText = 1;
            } else {
              counters.linkText++;
              component[`linkText${counters.linkText}`] = text;
            }
          }
        } else if (tag === 'span') { 
          if (text) {
            if (!counters.excerpt) {
              component.excerpt = text;
              counters.excerpt = 1;
            } else {
              counters.excerpt++;
              component[`excerpt${counters.excerpt}`] = text;
            }
          }
        } else if (tag === 'p') { 
          if (text) {
            originalTexts.push(text);
          }
        } else if (tag === 'li') {
          if (text) {
            originalTexts.push(text);
          }
        } else if (tag === 'i' && attrs.class) {
          if (!counters.icon) {
            component.icon = attrs.class;
            counters.icon = 1;
          } else {
            counters.icon++;
            component[`icon${counters.icon}`] = attrs.class;
          }
        } else if (tag === 'img' && attrs.src) {
          if (!counters.image) {
            component.image = attrs.src;
            counters.image = 1;
          } else {
            counters.image++;
            component[`image${counters.image}`] = attrs.src;
          }
        } else if (text) { 
          originalTexts.push(text);
        }
      });

      // Добавляем сохраненные тексты по порядку
      if (originalTexts.length > 0) {
        component.text = originalTexts[0];
        for (let i = 1; i < originalTexts.length; i++) {
          component[`text${i+1}`] = originalTexts[i];
        }
      }
      
      return component;
    };

    // Функция для обработки each блока
    const processEachBlock = (blockLines, blockName) => {
      const components = [];
      let currentComponent = [];
      let inNestedComponent = false;
      
      blockLines.forEach(line => {
        const trimmedLine = line.trim();
        
        if (trimmedLine === '//s') {
          inNestedComponent = true;
          currentComponent = [];
        } else if (trimmedLine === '//e') {
          if (inNestedComponent && currentComponent.length > 0) {
            components.push([...currentComponent]);
          }
          inNestedComponent = false;
          currentComponent = [];
        } else if (inNestedComponent) {
          currentComponent.push(line);
        }
      });

      // Обрабатываем компоненты и добавляем их в JSON
      if (!jsonOutput[blockName]) {
        jsonOutput[blockName] = [];
      }
      
      components.forEach(comp => {
        const processed = processComponent(comp);
        let urlCount = 0;
        let attrs = {};
        let backgroundImageCount = 0;
        
        // Проверяем наличие background-image в компоненте
        comp.forEach(line => {
          const bgRegex = /style\s*=\s*['"].*?background-image\s*:\s*url\s*\(\s*['"]?(.*?)['"]?\s*\)\s*;?['"]?/i;
          const bgMatch = line.match(bgRegex);
          if (bgMatch) {
            backgroundImageCount++;
            const url = bgMatch[1].replace(/[\\'"]/g, '');
            // Генерируем имя переменной с учетом индекса компонента
            const imageBackgroundVar = backgroundImageCount > 1 ? `imageBackground${backgroundImageCount}` : 'imageBackground';
            processed[imageBackgroundVar] = url;
          }
          attrs = extractAttributes(line);
          if (attrs.href) {
            urlCount++; // Увеличиваем счетчик url
            const urlKey = urlCount > 1 ? `url${urlCount}` : 'url'; // Формируем ключ
            processed[urlKey] = attrs.href; // Используем новый ключ
          } 
        });
        
        // Проверяем что объект не пустой перед добавлением
        if (Object.keys(processed).length > 0) {
          jsonOutput[blockName].push(processed);
        }
      });

      // Возвращаем только первый компонент для шаблона
      if (components.length > 0) {
        return components[0].map(line => {
          return formatComponentLine(line, `${blockName}[i].`, blockName);
        }).filter(Boolean);
      }

      return [];
    };

    // Функция для форматирования строки компонента
    const formatComponentLine = (line, varPrefix = '', blockName = null) => {
      const indent = getIndentLevel(line);
      const content = line.trim();
    
      // Обработка для background-image в строке
      const bgRegex = /style\s*=\s*['"].*?background-image\s*:\s*url\s*\(\s*['"]?(.*?)['"]?\s*\)\s*;?['"]?/i;
      const bgMatch = content.match(bgRegex);
      if (bgMatch) {
        const url = bgMatch[1].replace(/[\\'"]/g, '');
        if (blockName) {
          variableCounters[blockName] = variableCounters[blockName] || {};
          const counterKey = `${blockName}-imageBackground`;
          variableCounters[counterKey] = (variableCounters[counterKey] || 0) + 1;
          const imageBackgroundCount = variableCounters[counterKey];
          const imageBackgroundVar = imageBackgroundCount > 1 ? `imageBackground${imageBackgroundCount}` : 'imageBackground';
          return `${' '.repeat(indent)}${content.replace(bgRegex, `style=\`background-image: url(\${${varPrefix}${imageBackgroundVar}})\``)}`;
        } else {
          const counterKey = 'imageBackground';
          variableCounters[counterKey] = (variableCounters[counterKey] || 0) + 1;
          const imageBackgroundCount = variableCounters[counterKey];
          const imageBackgroundVar = imageBackgroundCount > 1 ? `imageBackground${imageBackgroundCount}` : 'imageBackground';
          jsonOutput[imageBackgroundVar] = url;
          return `${' '.repeat(indent)}${content.replace(bgRegex, `style=\`background-image: url($\{${imageBackgroundVar}})\``)}`;
        }
      }
    
      // Обработка строк с '.' или тегами
      if (content.startsWith('.') || content.match(/^\w+/)) {
        const attrs = extractAttributes(content);
        const text = extractText(content);
    
        let newLine = '';
        let tag = '';
        if (content.startsWith('.') && !content.startsWith('//')) {
          newLine = `${' '.repeat(indent)}.${attrs.class?.replace(/ /g, '.') || ''}`;
        } else {
          const tagMatch = content.match(/^(\w+)/);
          if (!tagMatch) return line;
          tag = tagMatch[1];
          newLine = `${' '.repeat(indent)}${tag}`;
          if (attrs.class && tag !== 'i') { // Исключаем <i> из добавления классов здесь
            newLine += `.${attrs.class.split(' ').join('.')}`;
          }
        }
    
        // Проверяем, есть ли атрибуты, требующие подстановки
        const hasSubstitutableAttrs = attrs.href || attrs.src || (tag === 'i' && attrs.class);
    
        // Если нет текста и нет атрибутов для подстановки, возвращаем строку без изменений
        if (!text && !hasSubstitutableAttrs) {
          return line;
        }
    
        // Обработка атрибутов
        if (tag === 'i' && attrs.class) {
          if (!blockName) {
            if (!variableCounters.icon) {
              variableCounters.icon = 1;
              jsonOutput.icon = attrs.class;
              newLine = `${' '.repeat(indent)}i(class=icon)`;
            } else {
              variableCounters.icon++;
              jsonOutput[`icon${variableCounters.icon}`] = attrs.class;
              newLine = `${' '.repeat(indent)}i(class=icon${variableCounters.icon})`;
            }
          } else {
            const counterKey = `${blockName}-icon`;
            variableCounters[counterKey] = (variableCounters[counterKey] || 0) + 1;
            const iconCount = variableCounters[counterKey];
            const iconVar = iconCount > 1 ? `icon${iconCount}` : 'icon';
            newLine = `${' '.repeat(indent)}i(class=${varPrefix}${iconVar})`;
          }
        } else {
          const formattedAttrs = formatAttributes(attrs, varPrefix, blockName);
          if (formattedAttrs) newLine += formattedAttrs;
        }
    
        // Обработка текста
        if (text) {
          let varName;
          if (tag === 'a' || content.match(/^a/)) {
            varName = 'linkText';
          } else if (tag.match(/^h[1-6]/) || content.match(/^h[1-6]/)) {
            varName = 'title';
          } else if (tag === 'span' || content.match(/^span/)) {
            varName = 'excerpt';
          } else if (tag === 'p' || content.match(/^p/)) {
            varName = 'text';
          } else {
            varName = 'text';
          }
    
          const fullVarName = blockName ? `${varPrefix}${varName}` : varName;
          const counterKey = blockName ? `${blockName}-${varName}` : varName;
          variableCounters[counterKey] = (variableCounters[counterKey] || 0) + 1;
          const varCount = variableCounters[counterKey];
          const numberedVarName = varCount > 1 ? `${varName}${varCount}` : varName;
    
          if (blockName) {
            jsonOutput[blockName] = jsonOutput[blockName] || [];
            const lastIndex = jsonOutput[blockName].length - 1;
            if (lastIndex >= 0) {
              jsonOutput[blockName][lastIndex][numberedVarName] = text;
            }
            newLine += ` #{${varPrefix}${numberedVarName}}`;
          } else {
            jsonOutput[numberedVarName] = text;
            newLine += ` #{${numberedVarName}}`;
          }
        }
    
        return newLine;
      }
    
      // Обработка комментариев
      if (content.startsWith('//')) return line;
    
      // Обработка текстовых строк с '|'
      if (content.startsWith('|')) {
        const text = extractText(line);
        if (text) {
          let varName = 'text';
          if (blockName) {
            const counterKey = `${blockName}-text`;
            variableCounters[counterKey] = (variableCounters[counterKey] || 0) + 1;
            const varCount = variableCounters[counterKey];
            varName = varCount > 1 ? `${varName}${varCount}` : varName;
            jsonOutput[blockName] = jsonOutput[blockName] || {};
            jsonOutput[blockName][varName] = text;
            return `${' '.repeat(indent)}| #{${varPrefix}${varName}}`;
          } else {
            variableCounters.text = (variableCounters.text || 0) + 1;
            varName = variableCounters.text > 1 ? `${varName}${variableCounters.text}` : varName;
            jsonOutput[varName] = text;
            return `${' '.repeat(indent)}| #{${varName}}`;
          }
        }
        return line;
      }
    
      return line; // Возвращаем неизменённую строку для остальных случаев
    };

    // Основной цикл обработки шаблона
    let i = 0;
    while (i < lines.length) {
      const line = lines[i];
      // Сначала обрабатываем строки с background-image
      const processedLine = processLine(line);
      const trimmedLine = processedLine.trim();
      
      if (!trimmedLine) {
        processedLines.push(processedLine);
        i++;
        continue;
      }
      
      if (trimmedLine.startsWith('each')) {
        const blockName = eachBlockCount === 0 ? 'items' : `items${eachBlockCount + 1}`;
        currentEachBlock = blockName;
        const indent = getIndentLevel(processedLine);
        
        // Собираем все строки блока
        const blockLines = [];
        i++;
        while (i < lines.length && (getIndentLevel(lines[i]) > indent || !lines[i].trim())) {
          blockLines.push(lines[i]);
          i++;
        }
        
        // Добавляем строку each
        processedLines.push(`${' '.repeat(indent)}each i in ${blockName}`);
        
        // Обрабатываем блок
        const processedBlock = processEachBlock(blockLines, blockName);
        processedLines.push(...processedBlock);
        
        eachBlockCount++;
        continue;
      }
      
      if (trimmedLine === '//s') {
        inComponent = true;
        currentComponent = [];
      } else if (trimmedLine === '//e') {
        if (inComponent && currentComponent.length > 0) {
          const component = processComponent(currentComponent);
          
          if (currentEachBlock) {
            if (!jsonOutput[currentEachBlock]) {
              jsonOutput[currentEachBlock] = [];
            }
            jsonOutput[currentEachBlock].push(component);
          }
          
          const processed = formatComponentLine(currentComponent[0], currentEachBlock ? `${currentEachBlock}[i].` : '', currentEachBlock);
          if (processed) {
            processedLines.push(processed);
          }
        }
        inComponent = false;
        currentComponent = [];
      } else if (inComponent) {
        currentComponent.push(processedLine);
      } else {
        const processed = formatComponentLine(processedLine);
        if (processed) {
          processedLines.push(processed);
        }
      }
      
      i++;
    }

    return {
      pug: processedLines.join('\n'),
      json: jsonOutput
    };
  } catch (error) {
    console.error('Ошибка при обработке шаблона:', error);
    throw error;
  }
}

// Обработчик API-запроса 
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    
    if (!body || !body.template) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Отсутствует шаблон в запросе'
      });
    }
    
    return parseTemplate(body.template);
  } catch (error) {
    console.error('Ошибка при обработке запроса:', error);
    throw error;
  }
});