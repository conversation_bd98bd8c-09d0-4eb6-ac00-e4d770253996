// Предустановленные шаблоны Bootstrap сеток для конструктора комбинаций

export interface GridTemplate {
  id: string
  name: string
  description: string
  category: string
  preview: string
  structure: {
    container: string
    customClasses: string
    rows: Array<{
      id: string
      container: string
      customClasses: string
      columns: Array<{
        id: string
        width: string
        customClasses: string
        elements: number[]
      }>
    }>
  }
  tags: string[]
  created: string
  updated: string
}

// Предустановленные шаблоны
export const defaultGridTemplates: GridTemplate[] = [
  {
    id: 'single-column',
    name: 'Одна колонка',
    description: 'Простая структура с одной колонкой на всю ширину',
    category: 'basic',
    preview: '|████████████|',
    structure: {
      container: 'container-fluid',
      customClasses: '',
      rows: [
        {
          id: 'row-1',
          container: 'row',
          customClasses: '',
          columns: [
            {
              id: 'col-1',
              width: 'col-md-12',
              customClasses: '',
              elements: []
            }
          ]
        }
      ]
    },
    tags: ['простой', 'базовый', 'одна колонка'],
    created: '2024-01-01',
    updated: '2024-01-01'
  },
  {
    id: 'two-columns-equal',
    name: 'Две равные колонки',
    description: 'Две колонки одинаковой ширины',
    category: 'basic',
    preview: '|██████|██████|',
    structure: {
      container: 'container-fluid',
      customClasses: '',
      rows: [
        {
          id: 'row-1',
          container: 'row',
          customClasses: '',
          columns: [
            {
              id: 'col-1',
              width: 'col-md-6',
              customClasses: '',
              elements: []
            },
            {
              id: 'col-2',
              width: 'col-md-6',
              customClasses: '',
              elements: []
            }
          ]
        }
      ]
    },
    tags: ['две колонки', 'равные', '50/50'],
    created: '2024-01-01',
    updated: '2024-01-01'
  },
  {
    id: 'three-columns-equal',
    name: 'Три равные колонки',
    description: 'Три колонки одинаковой ширины',
    category: 'basic',
    preview: '|████|████|████|',
    structure: {
      container: 'container-fluid',
      customClasses: '',
      rows: [
        {
          id: 'row-1',
          container: 'row',
          customClasses: '',
          columns: [
            {
              id: 'col-1',
              width: 'col-md-4',
              customClasses: '',
              elements: []
            },
            {
              id: 'col-2',
              width: 'col-md-4',
              customClasses: '',
              elements: []
            },
            {
              id: 'col-3',
              width: 'col-md-4',
              customClasses: '',
              elements: []
            }
          ]
        }
      ]
    },
    tags: ['три колонки', 'равные', '33/33/33'],
    created: '2024-01-01',
    updated: '2024-01-01'
  },
  {
    id: 'sidebar-content',
    name: 'Сайдбар + Контент',
    description: 'Узкий сайдбар слева и широкий контент справа',
    category: 'layout',
    preview: '|███|█████████|',
    structure: {
      container: 'container-fluid',
      customClasses: '',
      rows: [
        {
          id: 'row-1',
          container: 'row',
          customClasses: '',
          columns: [
            {
              id: 'col-1',
              width: 'col-md-3',
              customClasses: '',
              elements: []
            },
            {
              id: 'col-2',
              width: 'col-md-9',
              customClasses: '',
              elements: []
            }
          ]
        }
      ]
    },
    tags: ['сайдбар', 'контент', 'асимметричный'],
    created: '2024-01-01',
    updated: '2024-01-01'
  },
  {
    id: 'hero-features',
    name: 'Hero + 3 Features',
    description: 'Большой блок сверху и три равные колонки снизу',
    category: 'landing',
    preview: '|████████████|\n|████|████|████|',
    structure: {
      container: 'container-fluid',
      customClasses: '',
      rows: [
        {
          id: 'row-1',
          container: 'row',
          customClasses: 'mb-4',
          columns: [
            {
              id: 'col-1',
              width: 'col-md-12',
              customClasses: '',
              elements: []
            }
          ]
        },
        {
          id: 'row-2',
          container: 'row',
          customClasses: '',
          columns: [
            {
              id: 'col-2',
              width: 'col-md-4',
              customClasses: '',
              elements: []
            },
            {
              id: 'col-3',
              width: 'col-md-4',
              customClasses: '',
              elements: []
            },
            {
              id: 'col-4',
              width: 'col-md-4',
              customClasses: '',
              elements: []
            }
          ]
        }
      ]
    },
    tags: ['hero', 'features', 'лендинг', 'многорядный'],
    created: '2024-01-01',
    updated: '2024-01-01'
  },
  {
    id: 'complex-layout',
    name: 'Сложная компоновка',
    description: 'Комплексная структура с разными размерами колонок',
    category: 'advanced',
    preview: '|████████████|\n|███|██|███████|\n|██████|██████|',
    structure: {
      container: 'container-fluid',
      customClasses: '',
      rows: [
        {
          id: 'row-1',
          container: 'row',
          customClasses: 'mb-3',
          columns: [
            {
              id: 'col-1',
              width: 'col-md-12',
              customClasses: '',
              elements: []
            }
          ]
        },
        {
          id: 'row-2',
          container: 'row',
          customClasses: 'mb-3',
          columns: [
            {
              id: 'col-2',
              width: 'col-md-3',
              customClasses: '',
              elements: []
            },
            {
              id: 'col-3',
              width: 'col-md-2',
              customClasses: '',
              elements: []
            },
            {
              id: 'col-4',
              width: 'col-md-7',
              customClasses: '',
              elements: []
            }
          ]
        },
        {
          id: 'row-3',
          container: 'row',
          customClasses: '',
          columns: [
            {
              id: 'col-5',
              width: 'col-md-6',
              customClasses: '',
              elements: []
            },
            {
              id: 'col-6',
              width: 'col-md-6',
              customClasses: '',
              elements: []
            }
          ]
        }
      ]
    },
    tags: ['сложный', 'многорядный', 'асимметричный', 'продвинутый'],
    created: '2024-01-01',
    updated: '2024-01-01'
  }
]

// Функции для работы с шаблонами
export function getTemplateById(id: string): GridTemplate | undefined {
  return defaultGridTemplates.find(template => template.id === id)
}

export function getTemplatesByCategory(category: string): GridTemplate[] {
  return defaultGridTemplates.filter(template => template.category === category)
}

export function searchTemplates(query: string): GridTemplate[] {
  const lowerQuery = query.toLowerCase()
  return defaultGridTemplates.filter(template =>
    template.name.toLowerCase().includes(lowerQuery) ||
    template.description.toLowerCase().includes(lowerQuery) ||
    template.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
  )
}

// Сохранение и загрузка пользовательских шаблонов
export function saveCustomTemplate(template: GridTemplate): void {
  if (typeof window !== 'undefined') {
    const customTemplates = loadCustomTemplates()
    const existingIndex = customTemplates.findIndex(t => t.id === template.id)

    if (existingIndex >= 0) {
      customTemplates[existingIndex] = { ...template, updated: new Date().toISOString() }
    } else {
      customTemplates.push({ ...template, created: new Date().toISOString(), updated: new Date().toISOString() })
    }

    localStorage.setItem('customGridTemplates', JSON.stringify(customTemplates))
  }
}

export function loadCustomTemplates(): GridTemplate[] {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('customGridTemplates')
    return stored ? JSON.parse(stored) : []
  }
  return []
}

export function getAllTemplates(): GridTemplate[] {
  const custom = loadCustomTemplates()
  return [...defaultGridTemplates, ...custom]
}

export function deleteCustomTemplate(id: string): void {
  if (typeof window !== 'undefined') {
    const customTemplates = loadCustomTemplates()
    const filtered = customTemplates.filter(t => t.id !== id)
    localStorage.setItem('customGridTemplates', JSON.stringify(filtered))
  }
}

// Генерация уникального ID для новых шаблонов
export function generateTemplateId(): string {
  return 'template-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11)
}
