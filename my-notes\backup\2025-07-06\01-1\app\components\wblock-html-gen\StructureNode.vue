<template>
  <div
    class="tree-node"
    draggable="true"
    @dragstart="onDragStart($event, node)"
    @dragover.prevent="onDragOver($event, node)"
    @drop="onDrop($event, node)"
    @click.stop="onNodeSelect(node)"
  >
    <div class="node-content" :class="{ selected: selectedKey === node.key }">
      <span :class="`pi ${getIcon(node)}`" />
      <span>{{ node.label }}</span>
    </div>
    <div v-if="node.children" class="children">
      <StructureNode
        v-for="child in node.children"
        :key="child.key"
        :node="child"
        :parent="node"
        :selected-key="selectedKey"
        @node-select="onNodeSelect"
        @update-structure="updateStructure"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, defineEmits } from 'vue'

  const props = defineProps({
    node: {
      type: Object,
      required: true,
    },
    parent: {
      type: Object,
      default: null,
    },
    selectedKey: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits(['node-select', 'update-structure'])

  const onDragStart = (event, node) => {
    event.dataTransfer?.setData('text/plain', node.key)
    emit('node-select', node)
  }

  const onDragOver = (event) => {
    event.preventDefault()
  }

  const onDrop = (event, targetNode) => {
    event.preventDefault()
    const draggedKey = event.dataTransfer?.getData('text/plain')
    if (draggedKey) {
      emit('update-structure', { draggedKey, targetKey: targetNode.key })
    }
  }

  const onNodeSelect = (node) => {
    emit('node-select', node)
  }

  const getIcon = (node) => {
    const icons = {
      div: 'pi pi-box',
      span: 'pi pi-tag',
      p: 'pi pi-paragraph',
      a: 'pi pi-link',
      img: 'pi pi-image',
      ul: 'pi pi-list',
      ol: 'pi pi-list',
      li: 'pi pi-circle-fill',
      h1: 'pi pi-heading',
      h2: 'pi pi-heading',
      h3: 'pi pi-heading',
      form: 'pi pi-table',
      input: 'pi pi-pencil',
      button: 'pi pi-stop-circle',
    }
    return icons[node.tag] || 'pi pi-code'
  }

  const updateStructure = () => {
    emit('update-structure')
  }
</script>

<style scoped>
  .tree-node {
    margin: 0.25rem 0;
    padding: 0.25rem;
    border-radius: 0.25rem;
    cursor: pointer;
  }

  .node-content {
    padding: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .node-content:hover {
    background-color: #f0f0f0;
  }

  .node-content.selected {
    background-color: #e0e0e0;
    font-weight: bold;
  }

  .children {
    margin-left: 1rem;
    border-left: 1px dashed #ccc;
    padding-left: 0.5rem;
  }
</style>
