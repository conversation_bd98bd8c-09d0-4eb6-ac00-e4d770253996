<template>
  <div class="flex flex-col">
    <!-- Основная панель элементов -->
    <div class="bg-surface-50 dark:bg-surface-800 flex flex-col h-full p-0">
      <div class="structure-toolbar toolbar-container p-1">
        <div class="flex justify-between items-center gap-1">
          <div class="flex items-center gap-1">
            <!-- Поиск с выпадающим окном -->
            <div class="relative">
              <Button
                icon="pi pi-search"
                class="p-button-sm p-button-text"
                data-search-button
                @click="toggleSearch"
              />
              <div v-if="showSearch" class="search-dropdown">
                <InputText
                  v-model="searchQuery"
                  placeholder="Поиск..."
                  class="w-full text-xs"
                  style="font-size: 12px"
                />
              </div>
            </div>
            <Button
              v-tooltip.bottom="'Обновить список элементов'"
              icon="pi pi-refresh"
              class="p-button-sm p-button-text"
              @click="loadElements"
            />

            <!-- Фильтры с выпадающим MultiSelect -->
            <div class="relative">
              <Button
                icon="pi pi-filter"
                class="p-button-sm p-button-text"
                data-filters-button
                @click="toggleFilters"
              />
              <div v-if="showFilters" class="filters-dropdown">
                <MultiSelect
                  v-model="selectedTypes"
                  :options="uniqueTypes"
                  filter
                  placeholder="Типы элементов"
                  class="w-48 text-xs"
                  display="chip"
                  panel-class="text-xs"
                  :pt="{
                    item: { class: 'text-xs' },
                    header: { class: 'text-xs' },
                  }"
                />
              </div>
            </div>
          </div>

          <!-- Правая группа - действия -->
          <div class="flex items-center gap-1">
            <Button
              v-tooltip="'Добавить'"
              icon="pi pi-plus"
              class="p-button-sm p-button-text"
              @click="openEditForm(null)"
            />
            <Button
              v-tooltip="'Редактировать'"
              icon="pi pi-pencil"
              class="p-button-sm p-button-text"
              :disabled="!selectedElement"
              @click="openEditForm(selectedElement)"
            />
            <Button
              v-tooltip="'Дублировать'"
              icon="pi pi-copy"
              class="p-button-sm p-button-text"
              :disabled="!selectedElement"
              @click="duplicateElement"
            />
            <Button
              v-tooltip="'Удалить'"
              icon="pi pi-trash"
              class="p-button-sm p-button-text p-button-danger"
              :disabled="!selectedElement"
              @click="deleteElement"
            />
          </div>
        </div>
      </div>

      <!-- DataTable -->
      <DataTable
        v-model:selection="selectedElement"
        
        :value="filteredElements"
        scrollable
        scroll-height="800px"
        :virtual-scroller-options="{ itemSize: 44 }"
        size="small"
        sort-field="number"
        :sort-order="1"
        striped-rows
        data-key="id"
        selection-mode="single"
        class="flex-1 text-xxs"
        
        style="--highlight-bg: var(--primary-50); padding: 1px; font-size: 11px"
        @row-click="onRowClick"
        
      >
        <template #empty>
          <div class="text-center p-4 text-sm">Элементы не найдены</div>
        </template>
        <template #loading>
          <div class="text-center p-4">
            <ProgressSpinner
              style="width: 50px; height: 50px"
              stroke-width="3"
            />
          </div>
        </template>
        <Column
          field="number"
          header="№"
          sortable
          style="font-size: 9px; padding: 1px"
        />
        <Column field="title" header="Название" sortable style="padding: 1px" />
        <!-- <Column field="elem_type" header="Тип">
          <template #body="{ data }">
            <div class="flex flex-wrap gap-1">
              <Tag
                v-for="type in data.elem_type"
                :key="type"
                :value="type"
                size="small"
                style="font-size: 10px"
              />
            </div>
          </template>
        </Column> -->
        <Column
          field="sketch"
          header="Эскиз"
          :sortable="true"
          style="padding: 1px"
        >
          <template #body="{ data }">
            <Image
              v-if="data.sketch"
              :src="`http://localhost:8055/assets/${data.sketch}`"
              alt="Эскиз"
              width="50"
              preview
            />
            <span v-else />
          </template>
        </Column>
        <Column header="Действия" style="padding: 1px">
          <template #body="{ data }">
            <div class="flex gap-1">
              <Button
                icon="pi pi-plus"
                style="padding: 1px"
                text
                severity="secondary"
                class="p-button p-component p-button-icon-only p-button-secondary p-button-text p-button-sm"
                tooltip="Добавить на холст"
                @click="addToCanvas(data)"
              />
              <!-- <Button
                icon="pi pi-pencil"
                style="padding: 1px"
                text
                severity="info"
                class="p-button p-component p-button-icon-only p-button-info p-button-text p-button-sm"
                tooltip="Редактировать"
                @click="openEditForm(data)"
              /> -->
            </div>
          </template>
        </Column>

        <template #groupheader="slotProps">
          {{ slotProps.data.elem_type[0] }}
        </template>
      </DataTable>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue'
  import { useDirectusItems } from '#imports'
  import { useToast } from 'primevue/usetoast'
  import Chips from 'primevue/chips'
  import MultiSelect from 'primevue/multiselect'
  import 'primevue/chips/style'
  import 'primevue/multiselect/style'
  import { PrismEditor } from 'vue-prism-editor'
  import 'vue-prism-editor/dist/prismeditor.min.css'
  import Prism from 'prismjs'
  import 'prismjs/components/prism-clike'
  import 'prismjs/components/prism-markup'
  import 'prismjs/components/prism-css'
  import 'prismjs/components/prism-javascript'
  import 'prismjs/themes/prism-tomorrow.css'

  interface Element {
    id: string
    number: string
    title: string
    description: string
    elem_type: string[]
    html: string
    css?: string
    js?: string
  }

  const elements = ref<Element[]>([])
  const loading = ref(true)
  const selectedElement = ref<Element | null>(null)
  const showDialog = ref(false)
  const editingElement = ref<Element | null>(null)
  const searchQuery = ref('')
  const selectedTypes = ref<string[]>([])
  const expandedRowGroups = ref<any[]>([])

  const showSearch = ref(false)
  const showFilters = ref(false)

  const toggleSearch = () => {
    console.log('ElementsPanel: toggleSearch clicked, current showSearch:', showSearch.value)
    showSearch.value = !showSearch.value
    if (showSearch.value) showFilters.value = false
    console.log('ElementsPanel: showSearch after toggle:', showSearch.value)
  }

  const toggleFilters = () => {
    console.log('ElementsPanel: toggleFilters clicked, current showFilters:', showFilters.value)
    showFilters.value = !showFilters.value
    if (showFilters.value) showSearch.value = false
    console.log('ElementsPanel: showFilters after toggle:', showFilters.value)
  }

  // Закрытие выпадающих элементов при клике вне области
  const handleClickOutside = (event) => {
    const searchDropdown = event.target.closest('.search-dropdown')
    const filtersDropdown = event.target.closest('.filters-dropdown')
    const searchButton = event.target.closest('[data-search-button]')
    const filtersButton = event.target.closest('[data-filters-button]')

    if (!searchDropdown && !searchButton) {
      showSearch.value = false
    }
    if (!filtersDropdown && !filtersButton) {
      showFilters.value = false
    }
  }

  const formData = ref<Partial<Element>>({
    number: '',
    title: '',
    description: '',
    elem_type: [],
    html: '',
    css: '',
    js: '',
  })

  const { getItems, createItems, updateItem, deleteItems } = useDirectusItems()
  const toast = useToast()

  // Типы элементов из базы данных
  const availableTypes = ref<string[]>([])

  // Загрузка типов элементов из базы данных
  const loadElementTypes = async () => {
    try {
      // Загружаем все элементы из коллекции welem_proto
      const elementsResponse = await getItems({
        collection: 'welem_proto',
        params: {
          fields: ['elem_type'],
          limit: -1,
        },
      })

      // Извлекаем уникальные типы элементов
      const types = new Set<string>()
      const elementsData = Array.isArray(elementsResponse)
        ? elementsResponse
        : elementsResponse?.data || []

      elementsData.forEach((element) => {
        if (element.elem_type && Array.isArray(element.elem_type)) {
          element.elem_type.forEach((type) => types.add(type))
        }
      })

      availableTypes.value = Array.from(types)
      console.log('Загружены типы элементов:', availableTypes.value)
    } catch (error) {
      console.error('Ошибка при получении типов элементов:', error)
      availableTypes.value = []
    }
  }

  // Получаем уникальные типы элементов
  const uniqueTypes = computed(() => {
    return availableTypes.value
  })

  // Фильтрация элементов
  const filteredElements = computed(() => {
    return elements.value.filter((element) => {
      const matchesSearch = searchQuery.value
        ? element.title
            .toLowerCase()
            .includes(searchQuery.value.toLowerCase()) ||
          element.description
            ?.toLowerCase()
            .includes(searchQuery.value.toLowerCase())
        : true

      const matchesTypes = selectedTypes.value.length
        ? element.elem_type &&
          selectedTypes.value.every((type) => element.elem_type.includes(type))
        : true

      return matchesSearch && matchesTypes
    })
  })

  const onRowClick = (event: { data: Element }) => {
    selectedElement.value = event.data
  }

  const emit = defineEmits([
    'update:elements',
    'open-edit-form',
    'edit-element',
    'update:canvas-width',
    'element-added',
  ])

  const addToCanvas = (element: Element) => {
    const canvasElement = {
      id: element.id, // Добавляем ID элемента
      name: element.title,
      type: 'html',
      icon: 'pi pi-code',
      template: element.html,
      elem_id: element.id, // Дублируем ID для совместимости
      css: element.css, // Добавляем CSS для интеллектуального обновления
      js: element.js, // Добавляем JS для интеллектуального обновления
    }
    // emit('update:elements', [canvasElement])
    // Также отправляем событие выбора элемента для автоматического добавления в MultiSelect
    emit('element-added', {
      element: canvasElement, // данные для превью
      source: element,
    })
  }

  const startDrag = (event: MouseEvent, element: Element) => {
    const canvasElement = {
      name: element.title,
      type: 'html',
      icon: 'pi pi-code',
      template: element.html,
      css: element.css, // Добавляем CSS для интеллектуального обновления
      js: element.js, // Добавляем JS для интеллектуального обновления
    }
    event.dataTransfer?.setData('element', JSON.stringify(canvasElement))
  }

  const loadElements = async () => {
    try {
      loading.value = true
      const response = await getItems({
        collection: 'welem_proto',
        params: {
          fields: ['*'],
          sort: ['number'],
          limit: -1, // Загружаем все элементы без ограничений
        },
      })

      if (Array.isArray(response)) {
        elements.value = response
      } else if (response && response.data) {
        elements.value = response.data
      }

      // Toast уведомление об успешном обновлении
      toast.add({
        severity: 'success',
        summary: 'Обновлено',
        detail: `Загружено ${elements.value.length} элементов`,
        life: 2000,
      })
    } catch (error) {
      console.error('Ошибка при получении данных:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось обновить список элементов',
        life: 3000,
      })
    } finally {
      loading.value = false
    }
  }

  const openEditForm = (element: Element | null) => {
    editingElement.value = element
    if (element) {
      formData.value = { ...element }
    } else {
      formData.value = {
        number: '',
        title: '',
        description: '',
        elem_type: [],
        html: '',
      }
    }
    // Отправляем событие для открытия сайдбара и обновления ширины канваса
    emit('open-edit-form', formData.value, availableTypes.value)
    emit('update:canvas-width', null, 'left')
  }

  const saveElement = async () => {
    try {
      loading.value = true

      // Убедимся, что все поля правильно заполнены
      const itemToSave = {
        ...formData.value,
        // Убедимся, что elem_type всегда массив
        elem_type: Array.isArray(formData.value.elem_type)
          ? formData.value.elem_type
          : [],
        // Убедимся, что html и description сохраняются
        html: formData.value.html || '',
        description: formData.value.description || '',
      }

      if (editingElement.value && editingElement.value.id) {
        // Обновляем существующий элемент
        await updateItem({
          collection: 'welem_proto',
          id: editingElement.value.id,
          item: itemToSave,
        })
      } else {
        // Создаем новый элемент
        await createItems({
          collection: 'welem_proto',
          items: [itemToSave],
        })
      }

      // Перезагружаем список элементов
      await loadElements()

      // Отправляем событие редактирования элемента для обновления UI
      emit('edit-element', itemToSave)
    } catch (error) {
      console.error('Ошибка при сохранении элемента:', error)
    } finally {
      loading.value = false
    }
  }

  // In ElementsPanel component:
  const handleElementSelect = (element) => {
    console.log('Selected element in panel:', element) // This should log the full element object

    // Make sure the id is present in the emitted event
    this.$emit('select-element', element.id)
    // OR
    this.$emit('update:elements', [element])
  }

  const deleteElement = async () => {
    if (!selectedElement.value?.id) return

    try {
      await deleteItems({
        collection: 'welem_proto',
        items: [selectedElement.value.id],
      })
      await loadElements()
      selectedElement.value = null
    } catch (error) {
      console.error('Ошибка при удалении элемента:', error)
    }
  }

  const duplicateElement = async () => {
    if (!selectedElement.value) return

    try {
      const { id, ...elementData } = selectedElement.value
      elementData.title = `${elementData.title}_copy`
      await createItems({
        collection: 'welem_proto',
        items: [elementData],
      })
      await loadElements()
    } catch (error) {
      console.error('Ошибка при дублировании элемента:', error)
    }
  }

  // Функция для подсветки кода в PrismEditor
  const highlightCode = (code, language, languageName) => {
    return Prism.highlight(code, language, languageName)
  }

  // Функция для определения поля группировки
  const getGroupField = (item) => {
    return item.elem_type && item.elem_type.length > 0 ? item.elem_type[0] : ''
  }

  // Обработчики событий для групп
  const onRowGroupExpand = (event) => {
    console.log('Группа раскрыта:', event.data)
  }

  const onRowGroupCollapse = (event) => {
    console.log('Группа свёрнута:', event.data)
  }

  onMounted(async () => {
    await loadElements()
    await loadElementTypes()
    // Раскрываем все группы по умолчанию
    // Получаем уникальные значения первого элемента массива elem_type для группировки
    const uniqueFirstTypes = new Set()
    elements.value.forEach((element) => {
      if (element.elem_type && element.elem_type.length > 0) {
        uniqueFirstTypes.add(element.elem_type[0])
      }
    })
    expandedRowGroups.value = Array.from(uniqueFirstTypes)
    console.log('Группы для раскрытия:', expandedRowGroups.value)

    // Добавляем обработчик для закрытия выпадающих элементов
    document.addEventListener('click', handleClickOutside)
  })

  // Убираем обработчик при размонтировании
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })

  // Экспортируем методы для использования в родительском компоненте
  defineExpose({ loadElements })
</script>

<style>
  .p-datatable-row-group-header > td {
    padding: 2px !important;
  }
  tr.p-datatable-row-group-header {
    background-color: #f3f3f3 !important;
  }
  button.p-datatable-row-toggle-button {
    height: 18px !important;
    top: 5px !important;
  }
  .structure-toolbar {
    border-bottom: 1px solid #e5e7eb;
    position: relative;
    overflow: visible;
  }

  /* Toolbar container styles */
  .toolbar-container {
    overflow: visible !important;
    position: relative;
  }

  .toolbar-container .flex {
    flex-wrap: nowrap !important;
    overflow: visible !important;
  }

  .toolbar-container .flex > * {
    flex-shrink: 0;
    min-width: 0;
  }

  .search-dropdown,
  .filters-dropdown {
    position: absolute !important;
    left: 0;
    top: calc(100% + 4px);
    padding: 12px;
    background: white !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25) !important;
    border-radius: 6px;
    min-width: 280px;
    max-width: 400px;
    z-index: 9999 !important;
    border: 2px solid #007bff !important;
    display: block !important;
    visibility: visible !important;
  }

  /* Предотвращаем горизонтальную прокрутку toolbar */
  .structure-toolbar .flex {
    flex-wrap: nowrap;
    overflow: hidden;
  }

  /* Ограничиваем ширину выпадающих элементов */
  .search-dropdown {
    right: auto;
    left: 0;
  }

  .filters-dropdown {
    right: auto;
    left: 0;
  }

  /* Адаптивность для узких панелей */
  @media (max-width: 400px) {
    .search-dropdown,
    .filters-dropdown {
      left: -50px;
      min-width: 180px;
    }
  }
</style>
