import { defineEventHandler, readBody } from 'h3'
import { analyzeHtml } from '../utils/htmlAnalyzer'

export default defineEventHandler(async (event) => {
  const { html } = await readBody(event)
  const result = analyzeHtml(html)
  
  return {
    layout: Array.from(result.layout),
    elements: Array.from(result.elements),
    graphics: Array.from(result.graphics),
    features: Array.from(result.features),
    treeStructure: result.treeStructure
  }
})
