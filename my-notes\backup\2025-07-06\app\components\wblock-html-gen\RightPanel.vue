<template>
  <div class="flex flex-col h-full w-full">
    <!-- Список использованных элементов (40% высоты) -->
    <div class="h-[40%] overflow-hidden border-b">
      <StructurePanel
        :html="htmlContent"
        class="w-full h-full"
        @update:html="$emit('update:html', $event)"
        @node-select="$emit('node-select', $event)"
      />
    </div>

    <!-- Список блоков (60% высоты) -->
    <div
      class="bg-surface-50 dark:bg-surface-800 flex flex-col h-[60%] overflow-hidden"
    >
      <div class="structure-toolbar toolbar-container p-1">
        <div class="flex justify-between items-center gap-1">
          <div class="flex items-center gap-1">
            <!-- Поиск с выпадающим окном -->
            <div class="relative">
              <Button
                icon="pi pi-search"
                class="p-button-sm p-button-text"
                data-search-button
                @click="toggleSearch"
              />
              <div v-if="showSearch" class="search-dropdown">
                <InputText
                  v-model="searchQuery"
                  placeholder="Поиск..."
                  class="w-full text-xs"
                  style="font-size: 12px"
                />
              </div>
            </div>
            <Button
              v-tooltip.bottom="'Обновить список блоков'"
              icon="pi pi-refresh"
              class="p-button-sm p-button-text"
              @click="loadBlocks"
            />

            <!-- Фильтры с выпадающим MultiSelect -->
            <div class="relative">
              <Button
                icon="pi pi-filter"
                class="p-button-sm p-button-text"
                data-filters-button
                @click="toggleFilters"
              />
              <div v-if="showFilters" class="filters-dropdown">
                <MultiSelect
                  v-model="selectedTypes"
                  :options="uniqueTypes"
                  filter
                  placeholder="Типы блоков"
                  class="w-48 text-xs"
                  display="chip"
                  panel-class="text-xs"
                  :pt="{
                    item: { class: 'text-xs' },
                    header: { class: 'text-xs' },
                  }"
                />
              </div>
            </div>
          </div>

          <!-- Правая группа - действия -->
          <div class="flex items-center gap-1">
            <Button
              v-tooltip="'Добавить'"
              icon="pi pi-plus"
              class="p-button-sm p-button-text"
              @click="openBlockForm(null)"
            />
            <Button
              v-tooltip="'Редактировать'"
              icon="pi pi-pencil"
              class="p-button-sm p-button-text"
              :disabled="!selectedBlock"
              @click="openBlockForm(selectedBlock)"
            />
            <Button
              v-tooltip="'Дублировать'"
              icon="pi pi-copy"
              class="p-button-sm p-button-text"
              :disabled="!selectedBlock"
              @click="duplicateBlock"
            />
            <Button
              v-tooltip="'Удалить'"
              icon="pi pi-trash"
              class="p-button-sm p-button-text p-button-danger"
              :disabled="!selectedBlock"
              @click="deleteBlock(selectedBlock)"
            />
          </div>
        </div>
      </div>

      <DataTable
        v-model:selection="selectedBlock"

        :value="filteredBlocks"
        scrollable
        scroll-height="500px"
        :virtual-scroller-options="{ itemSize: 44 }"
        size="small"
        sort-field="number"
        :sort-order="1"
        striped-rows
        data-key="id"
        selection-mode="single"
        class="flex-1 text-xxs"

        style="--highlight-bg: var(--primary-50); padding: 1px; font-size: 11px"
        @row-select="onBlockSelect"

      >
        <Column
          field="number"
          header="№"
          sortable
          style="font-size: 9px; padding: 1px"
          width="60"
        />
        <Column field="title" header="Название" sortable style="padding: 1px" width="130"/>
        <!-- <Column field="block_type" header="Тип">
          <template #body="{ data }">
            <div class="flex flex-wrap gap-1">
              <Tag
                v-for="type in data.block_type"
                :key="type"
                :value="type"
                size="small"
                class="text-xs [&>span]:text-xs"
              />
            </div>
          </template>
        </Column> -->
        <Column
          field="sketch"
          header="Эскиз"
          :sortable="true"
          style="padding: 1px"
          width="80"
        >
          <template #body="{ data }">
            <Image
              v-if="data.sketch"
              :src="`http://localhost:8055/assets/${data.sketch}`"
              alt="Эскиз"
              width="60"
              class="my"
              preview
            />
            <span v-else />
          </template>
        </Column>
        <Column header="+" style="padding: 1px" width="40">
          <template #body="{ data }">
            <div class="flex gap-1">
              <Button
                icon="pi pi-plus"
                style="padding: 1px"
                text
                severity="secondary"
                class="p-button p-component p-button-icon-only p-button-secondary p-button-text p-button-sm"
                tooltip="Добавить на холст"
                @click="addBlockToCanvas(data)"
              />
            </div>
          </template>
        </Column>
        <template #groupheader="slotProps">
          {{ slotProps.data.block_type[0] }}
        </template>
        <template #empty>
          <div class="text-center p-4 text-sm">Блоки не найдены</div>
        </template>
      </DataTable>
    </div>

    <div>
      <ul />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
  import { useDirectusItems } from '#imports'
  import { useToast } from 'primevue/usetoast'
  import StructurePanel from './StructurePanel.vue'
  import Button from 'primevue/button'
  import InputText from 'primevue/inputtext'
  import MultiSelect from 'primevue/multiselect'
  import 'primevue/multiselect/style'
  import Tag from 'primevue/tag'
  import Toast from 'primevue/toast'

  // Состояние таблицы блоков
  const searchQuery = ref('')
  const selectedBlock = ref(null)
  const selectedTypes = ref([])
  const expandedRowGroups = ref<any[]>([])
  const showSearch = ref(false)
  const showFilters = ref(false)

  const blocks = ref([])

  const toggleSearch = () => {
    console.log('RightPanel: toggleSearch clicked, current showSearch:', showSearch.value)
    showSearch.value = !showSearch.value
    if (showSearch.value) showFilters.value = false
    console.log('RightPanel: showSearch after toggle:', showSearch.value)
  }

  const toggleFilters = () => {
    console.log('RightPanel: toggleFilters clicked, current showFilters:', showFilters.value)
    showFilters.value = !showFilters.value
    if (showFilters.value) showSearch.value = false
    console.log('RightPanel: showFilters after toggle:', showFilters.value)
  }

  // Закрытие выпадающих элементов при клике вне области
  const handleClickOutside = (event) => {
    const searchDropdown = event.target.closest('.search-dropdown')
    const filtersDropdown = event.target.closest('.filters-dropdown')
    const searchButton = event.target.closest('[data-search-button]')
    const filtersButton = event.target.closest('[data-filters-button]')

    if (!searchDropdown && !searchButton) {
      showSearch.value = false
    }
    if (!filtersDropdown && !filtersButton) {
      showFilters.value = false
    }
  }

  // Это определение uniqueTypes удаляем, так как оно дублируется ниже
  // и использует availableTypes из loadElementTypes

  // Загрузка блоков
  async function loadBlocks() {
    try {
      blocks.value = await getItems({
        collection: 'wblock_proto',
        params: {
          sort: ['number'],
          limit: -1,
        },
      })
      console.log('Блоки загружены:', blocks.value.length)

      // Toast уведомление об успешном обновлении
      toast.add({
        severity: 'success',
        summary: 'Обновлено',
        detail: `Загружено ${blocks.value.length} блоков`,
        life: 2000,
      })
    } catch (error) {
      console.error('Error loading blocks:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось обновить список блоков',
        life: 3000,
      })
    }
  }

  // Типы элементов из базы данных
  const availableTypes = ref<string[]>([])

  // Загрузка типов элементов из базы данных
  const loadElementTypes = async () => {
    try {
      // Загружаем все элементы из коллекции wblock_proto
      const elementsResponse = await getItems({
        collection: 'wblock_proto',
        params: {
          fields: ['block_type'],
          limit: -1,
        },
      })

      // Извлекаем уникальные типы элементов
      const types = new Set<string>()
      const elementsData = Array.isArray(elementsResponse)
        ? elementsResponse
        : elementsResponse?.data || []

      elementsData.forEach((block) => {
        if (block.block_type && Array.isArray(block.block_type)) {
          block.block_type.forEach((type) => types.add(type))
        }
      })

      availableTypes.value = Array.from(types)
      console.log('Загружены типы элементов:', availableTypes.value)
    } catch (error) {
      console.error('Ошибка при получении типов элементов:', error)
      availableTypes.value = []
    }
  }

  // Получаем уникальные типы блоков
  const uniqueTypes = computed(() => {
    return availableTypes.value
  })

  // Фильтрация блоков
  const filteredBlocks = computed(() => {
    return blocks.value.filter((block) => {
      const matchesSearch = searchQuery.value
        ? block.number
            ?.toString()
            .toLowerCase()
            .includes(searchQuery.value.toLowerCase()) ||
          block.title
            ?.toLowerCase()
            .includes(searchQuery.value.toLowerCase()) ||
          block.description
            ?.toLowerCase()
            .includes(searchQuery.value.toLowerCase())
        : true

      const matchesTypes = selectedTypes.value.length
        ? selectedTypes.value.every((type) => block.block_type?.includes(type))
        : true

      return matchesSearch && matchesTypes
    })
  })

  // Обработчики событий блоков
  function openBlockForm(block) {
    if (block) {
      emit('edit-block', block) // Используем edit-block для редактирования
    } else {
      emit('open-block-form') // Для создания нового
    }
  }

  // Обработчик события сохранения блока
  function onBlockSave(savedBlock) {
    // Обновляем список блоков после сохранения
    loadBlocks()
    // Если это был существующий блок, обновляем выделение
    if (savedBlock.id === selectedBlock.value?.id) {
      selectedBlock.value = savedBlock
    }

    // Показываем уведомление об успешном сохранении
    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Список блоков обновлен',
      life: 3000,
    })
  }

  const { getItems, createItems, deleteItems, updateItem } = useDirectusItems()
  const toast = useToast()

  const duplicateBlock = async () => {
    if (!selectedBlock.value) return

    try {
      const { id, ...blockData } = selectedBlock.value
      blockData.title = `${blockData.title}_copy`
      await createItems({
        collection: 'wblock_proto',
        items: [blockData],
      })
      await loadBlocks()
    } catch (error) {
      console.error('Ошибка при дублировании блока:', error)
    }
  }

  async function deleteBlock(block) {
    if (!block?.id) return

    try {
      await deleteItems({
        collection: 'wblock_proto',
        items: [block.id],
      })
      await loadBlocks()
      selectedBlock.value = null
    } catch (error) {
      console.error('Ошибка при удалении блока:', error)
    }
  }

  function addBlockToCanvas(block) {
    // Создаем элемент для холста с HTML-шаблоном
    const canvasElement = {
      name: block.title,
      type: 'html',
      icon: 'pi pi-code',
      template: block.html || '',
    }

    // Добавляем элемент на холст
    emit('update:elements', [...props.elements, canvasElement])

    // Добавляем CSS из блока в поле customCss основной страницы
    if (block.css) {
      emit('update:css', block.css)
    }

    // Добавляем JavaScript из блока в поле customJs основной страницы
    if (block.js) {
      emit('update:js', block.js)
    }

    console.log(`✅ Добавлен блок ${block.title} с HTML${block.css ? ', CSS' : ''}${block.js ? ', JS' : ''}`)
  }

  function onBlockSelect(event) {
    emit('block-select', event.data)
  }

  function editBlock(block) {
    emit('edit-block', { ...block })
  }

  const emit = defineEmits([
    'open-block-form',
    'edit-block',
    'delete-block',
    'block-select',
    'add-block-to-canvas',
    'update:elements',
    'update:css',
    'update:js',
    'block-save',
    'update:html',   // Добавляем недостающее событие для обновления HTML
    'node-select',   // Добавляем недостающее событие для выбора узла
  ])

  // Экспортируем метод loadBlocks для вызова из родительского компонента
  defineExpose({ loadBlocks })

  const props = defineProps({
    elements: {
      type: Array,
      required: true,
      default: () => [],
    },
    htmlContent: {
      type: String,
      required: true,
    },
  })

  // Функция для добавления элемента в welem_proto
  const addElementToWelemProto = (element) => {
    // Логика для добавления элемента в welem_proto
    emit('update:elements', element)
  }

  // Функция для определения поля группировки
  const getGroupField = (item) => {
    return item.block_type && item.block_type.length > 0
      ? item.block_type[0]
      : ''
  }

  // Обработчики событий для групп
  const onRowGroupExpand = (event) => {
    console.log('Группа раскрыта:', event.data)
  }

  const onRowGroupCollapse = (event) => {
    console.log('Группа свёрнута:', event.data)
  }

  // Загрузка данных при монтировании
  onMounted(async () => {
    await loadBlocks()
    await loadElementTypes()
    const uniqueFirstTypes = new Set()
    blocks.value.forEach((block) => {
      if (block.block_type && block.block_type.length > 0) {
        uniqueFirstTypes.add(block.block_type[0])
      }
    })
    expandedRowGroups.value = Array.from(uniqueFirstTypes)
    console.log('Группы для раскрытия:', expandedRowGroups.value)

    // Добавляем обработчик для закрытия выпадающих элементов
    document.addEventListener('click', handleClickOutside)
  })

  // Убираем обработчик при размонтировании
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })

  // Реактивное обновление при изменении данных
  watch([searchQuery, selectedTypes], () => {
    loadBlocks()
  })
</script>

<style>
  .group {
    position: relative;
    transition: all 0.2s;
    border: 2px solid transparent;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
  }

  .group:hover {
    border-color: var(--primary-200);
  }

  /* Toolbar styles */
  .structure-toolbar {
    border-bottom: 1px solid #e5e7eb;
    position: relative;
    overflow: visible !important;
  }

  /* Toolbar container styles */
  .toolbar-container {
    overflow: visible !important;
    position: relative;
  }

  .toolbar-container .flex {
    flex-wrap: nowrap !important;
    overflow: visible !important;
  }

  .toolbar-container .flex > * {
    flex-shrink: 0;
    min-width: 0;
  }

  /* Dropdown styles for toolbar */
  .search-dropdown,
  .filters-dropdown {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    z-index: 9999 !important;
    background: white !important;
    border: 2px solid #007bff !important;
    border-radius: 6px;
    padding: 12px;
    min-width: 280px;
    max-width: 400px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25) !important;
    display: block !important;
    visibility: visible !important;
  }

  .search-dropdown .p-inputtext,
  .filters-dropdown .p-multiselect {
    min-height: 36px;
  }

  .search-dropdown input,
  .filters-dropdown input {
    font-size: 14px !important;
  }

  /* Ensure relative positioning for dropdown containers */
  .relative {
    position: relative !important;
    overflow: visible !important;
  }

  .p-datatable-row-group-header > td {
    padding: 2px !important;
  }

  tr.p-datatable-row-group-header {
    background-color: #f3f3f3 !important;
  }

  button.p-datatable-row-toggle-button {
    height: 18px !important;
    top: 5px !important;
  }
  .structure-toolbar {
    @apply border-b border-gray-200 dark:border-gray-700;
    position: relative;
    overflow: visible;
  }

  .search-dropdown,
  .filters-dropdown {
    @apply absolute left-0 mt-1 p-2 bg-white dark:bg-gray-800 shadow-lg rounded-md;
    min-width: 200px;
    max-width: 250px;
    z-index: 1000;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  /* Предотвращаем горизонтальную прокрутку toolbar */
  .structure-toolbar .flex {
    flex-wrap: nowrap;
    overflow: hidden;
  }

  /* Ограничиваем ширину выпадающих элементов */
  .search-dropdown {
    right: auto;
    left: 0;
  }

  .filters-dropdown {
    right: auto;
    left: 0;
  }

  /* Адаптивность для узких панелей */
  @media (max-width: 400px) {
    .search-dropdown,
    .filters-dropdown {
      left: -50px;
      min-width: 180px;
    }
  }
  .my {
    max-height: 80px;
  }
  .my img {
    object-fit: contain;
  }
</style>
