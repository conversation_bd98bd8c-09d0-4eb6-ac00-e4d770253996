export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { hbs } = body

    if (!hbs) {
      throw createError({
        statusCode: 400,
        statusMessage: 'HBS template is required'
      })
    }

    // Извлекаем все переменные из HBS шаблона
    const variableRegex = /\{\{\s*([^}]+)\s*\}\}/g
    const variables = new Set<string>()
    let match

    while ((match = variableRegex.exec(hbs)) !== null) {
      const variable = match[1].trim()
      // Исключаем хелперы Handlebars
      if (!variable.startsWith('#') && !variable.startsWith('/') && !variable.includes(' ')) {
        variables.add(variable)
      }
    }

    // Анализируем типы переменных на основе их имен
    const variableAnalysis = Array.from(variables).map(variable => {
      let type = 'text'
      let expectedLength = 'medium'

      // Определяем тип по имени переменной
      if (variable.includes('image') || variable.includes('Image')) {
        type = 'image'
        expectedLength = 'url'
      } else if (variable.includes('url') || variable.includes('Url') || variable.includes('href')) {
        type = 'url'
        expectedLength = 'url'
      } else if (variable.includes('title') || variable.includes('Title')) {
        type = 'text'
        expectedLength = 'short'
      } else if (variable.includes('text') || variable.includes('Text') || variable.includes('description')) {
        type = 'text'
        expectedLength = 'long'
      } else if (variable.includes('excerpt') || variable.includes('Excerpt')) {
        type = 'text'
        expectedLength = 'medium'
      } else if (variable.includes('linkText') || variable.includes('LinkText')) {
        type = 'text'
        expectedLength = 'short'
      } else if (variable.includes('icon') || variable.includes('Icon')) {
        type = 'text'
        expectedLength = 'short'
      }

      return {
        name: variable,
        type,
        expectedLength,
        required: true // Все переменные в HBS считаем обязательными
      }
    })

    return {
      success: true,
      variables: Array.from(variables),
      analysis: variableAnalysis,
      count: variables.size
    }

  } catch (error: any) {
    console.error('Ошибка анализа HBS переменных:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Internal server error'
    })
  }
})
