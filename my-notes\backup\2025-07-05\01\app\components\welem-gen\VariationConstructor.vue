<template>
  <div class="variation-constructor">
    <!-- Компактный заголовок -->
    <div class="header-section p-2 bg-surface-0 dark:bg-surface-900 border rounded mb-2">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <h4 class="text-sm font-semibold">Конструктор вариаций</h4>
          <ToggleButton
            v-model="isEditMode"
            on-label="Редактирование"
            off-label="Выбор"
            class="text-xs"
          />
        </div>
        <div class="flex items-center gap-1">
          <Button
            icon="pi pi-plus"
            class="text-xs p-button-outlined p-1"
            @click="addVariationGroup"
            v-tooltip="'Добавить группу'"
          />
          <Button
            icon="pi pi-save"
            class="text-xs p-button-success p-1"
            @click="saveVariations"
            v-tooltip="'Сохранить'"
          />
        </div>
      </div>
    </div>

    <!-- Режим выбора (компактный) -->
    <div v-if="!isEditMode" class="selection-mode">
      <div class="grid grid-cols-2 lg:grid-cols-3 gap-2">
        <div
          v-for="group in variationGroups"
          :key="group.id"
          class="group-card border rounded p-2 bg-white cursor-pointer transition-all"
          :class="{ 'ring-2 ring-blue-500': selectedGroups.includes(group.id) }"
          @click="toggleGroupSelection(group.id)"
        >
          <div class="flex items-center gap-2 mb-1">
            <i :class="group.icon" :style="{ color: group.color }" class="text-sm"></i>
            <span class="text-sm font-medium">{{ group.name }}</span>
            <Checkbox
              :model-value="selectedGroups.includes(group.id)"
              binary
              class="ml-auto"
              @click.stop
            />
          </div>
          <p class="text-xs text-gray-500 mb-2">{{ group.description }}</p>
          <div class="flex flex-wrap gap-1">
            <Badge
              v-for="rule in group.rules.slice(0, 3)"
              :key="rule.id"
              :value="rule.name"
              class="text-xs"
              severity="secondary"
            />
            <Badge
              v-if="group.rules.length > 3"
              :value="`+${group.rules.length - 3}`"
              class="text-xs"
              severity="info"
            />
          </div>
        </div>
      </div>

      <!-- Кнопка применения -->
      <div class="mt-3 text-center">
        <Button
          label="Применить выбранные вариации"
          icon="pi pi-check"
          class="text-sm"
          :disabled="selectedGroups.length === 0"
          @click="applySelectedVariations"
        />
      </div>
    </div>

    <!-- Режим редактирования (подробный) - одна группа в строку -->
    <div v-else class="edit-mode">
      <div class="space-y-4">
        <div
          v-for="(group, groupIndex) in variationGroups"
          :key="group.id"
          class="variation-group border rounded p-3 bg-white w-full"
        >
        <!-- Заголовок группы -->
        <div class="group-header flex items-center justify-between mb-3">
          <div class="flex items-center gap-3">
            <div class="flex items-center gap-2">
              <i :class="group.icon" :style="{ color: group.color }"></i>
              <InputText
                v-model="group.name"
                class="font-semibold text-sm"
                @input="markAsModified"
              />
            </div>
            <Chip
              :label="group.category"
              class="text-xs"
              :style="{ backgroundColor: group.color + '20', color: group.color }"
            />
            <Textarea
          v-model="group.description"
          placeholder="Описание группы вариаций"
          class="w-96 text-xs"
          rows="1"
          @input="markAsModified"
        />
          </div>
          <div class="flex items-center gap-2">
            <ToggleButton
              v-model="group.enabled"
              on-label="Включено"
              off-label="Отключено"
              class="text-xs"
              @change="markAsModified"
            />
            <Button
              icon="pi pi-plus"
              class="text-xs p-button-outlined"
              @click="addVariationRule(groupIndex)"
              v-tooltip="'Добавить правило'"
            />
            <Button
              icon="pi pi-trash"
              class="text-xs p-button-danger p-button-outlined"
              @click="removeVariationGroup(groupIndex)"
              v-tooltip="'Удалить группу'"
            />
          </div>
        </div>

        <!-- Описание группы -->


        <!-- Правила группы в одну строку -->
        <div class="variation-rules overflow-x-auto">
          <div class="flex gap-2 pb-2" style="min-width: max-content;">
            <div
              v-for="(rule, ruleIndex) in group.rules"
              :key="rule.id"
              class="variation-rule border rounded p-2 bg-gray-50 flex-shrink-0 w-96"
            >
            <!-- Заголовок правила -->
            <div class="rule-header flex items-center justify-between mb-2">
              <div class="flex items-center gap-2">
                <InputText
                  v-model="rule.name"
                  placeholder="Название правила"
                  class="font-medium text-sm"
                  @input="markAsModified"
                />
                <Badge
                  :value="rule.priority"
                  class="text-xs"
                />
              </div>
              <div class="flex items-center gap-2">
                <ToggleButton
                  v-model="rule.enabled"
                  on-icon="pi pi-check"
                  off-icon="pi pi-times"
                  class="text-xs"
                  @change="markAsModified"
                />
                <Button
                  icon="pi pi-trash"
                  class="text-xs p-button-danger p-button-text"
                  @click="removeVariationRule(groupIndex, ruleIndex)"
                />
              </div>
            </div>

            <!-- Описание правила -->
            <InputText
              v-model="rule.description"
              placeholder="Описание правила"
              class="w-full text-xs mb-2"
              @input="markAsModified"
            />

            <!-- Компактная компоновка в 2 колонки -->
            <div class="grid grid-cols-2 gap-2 mb-2">
              <!-- Левая колонка: Целевые элементы -->
              <div class="target-elements">
                <label class="text-xs font-medium text-gray-700 mb-1 block">Целевые элементы:</label>
                <div class="space-y-1">
                  <div>
                    <label class="text-xs text-gray-600">Типы:</label>
                    <MultiSelect
                      v-model="rule.targetElements.types"
                      :options="availableElementTypes"
                      placeholder="Типы"
                      class="text-xs w-full"
                      filter
                      @change="markAsModified"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-gray-600">Теги:</label>
                    <MultiSelect
                      v-model="rule.targetElements.tags"
                      :options="htmlTags"
                      placeholder="HTML теги"
                      class="text-xs w-full"
                      filter
                      @change="markAsModified"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-gray-600">Область:</label>
                    <Dropdown
                      v-model="rule.targetElements.scope"
                      :options="scopeOptions"
                      option-label="label"
                      option-value="value"
                      class="text-xs w-full"
                      @change="markAsModified"
                    />
                  </div>
                </div>
              </div>

              <!-- Правая колонка: Трансформации -->
              <div class="transformations">
                <label class="text-xs font-medium text-gray-700 mb-1 block">Трансформации:</label>
                <div class="space-y-1">
                  <div>
                    <label class="text-xs text-gray-600">Добавить:</label>
                    <MultiSelect
                      v-model="rule.transformations.addClass"
                      :options="availableCSSClasses"
                      option-label="name"
                      option-value="name"
                      placeholder="CSS классы"
                      class="text-xs w-full"
                      filter
                      @change="markAsModified"
                    >
                      <template #option="slotProps">
                        <div class="flex items-center gap-1">
                          <span class="font-mono text-xs">{{ slotProps.option.name }}</span>
                        </div>
                      </template>
                    </MultiSelect>
                    <!-- НОВОЕ: Поле для ввода своих классов -->
                    <InputText
                      v-model="rule.transformations.addCustomClasses"
                      placeholder="Или введите свои классы через пробел"
                      class="w-full text-xs mt-1"
                      @input="markAsModified"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-gray-600">Удалить:</label>
                    <MultiSelect
                      v-model="rule.transformations.removeClass"
                      :options="availableCSSClasses"
                      option-label="name"
                      option-value="name"
                      placeholder="CSS классы"
                      class="text-xs w-full"
                      filter
                      @change="markAsModified"
                    >
                      <template #option="slotProps">
                        <div class="flex items-center gap-1">
                          <span class="font-mono text-xs">{{ slotProps.option.name }}</span>
                        </div>
                      </template>
                    </MultiSelect>
                    <!-- НОВОЕ: Поле для ввода своих классов для удаления -->
                    <InputText
                      v-model="rule.transformations.removeCustomClasses"
                      placeholder="Или введите классы для удаления через пробел"
                      class="w-full text-xs mt-1"
                      @input="markAsModified"
                    />
                  </div>
                  <div>
                    <label class="text-xs text-gray-600">Приоритет:</label>
                    <InputNumber
                      v-model="rule.priority"
                      :min="1"
                      :max="100"
                      class="text-xs w-full"
                      @input="markAsModified"
                    />
                  </div>
                </div>
              </div>
            </div>


            </div>
          </div>
        </div>
        </div>
      </div>
    </div>

    <!-- Превью применения - МНОЖЕСТВЕННЫЕ ЭЛЕМЕНТЫ -->
    <div class="preview-section mt-6 p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <h5 class="text-sm font-semibold mb-3">Превью применения вариаций</h5>
      <div class="preview-content">
        <!-- Если выбраны элементы, показываем превью для каждого -->
        <div v-if="props.selectedElements.length > 0" class="space-y-4">
          <div
            v-for="(element, index) in props.selectedElements.slice(0, 3)"
            :key="element.id || index"
            class="element-preview border rounded-lg p-3 bg-white"
          >
            <div class="text-sm font-medium text-gray-700 mb-2">
              {{ element.title || `Элемент ${index + 1}` }}
            </div>

            <div class="flex gap-4">
              <div class="original flex-1">
                <h6 class="text-xs font-medium mb-2">Оригинал:</h6>
                <iframe
                  :srcdoc="getElementPreviewHtml(element, false)"
                  class="w-full h-24 border rounded"
                  frameborder="0"
                  sandbox="allow-same-origin allow-scripts"
                />
              </div>
              <div class="modified flex-1">
                <h6 class="text-xs font-medium mb-2">С вариациями:</h6>
                <iframe
                  :srcdoc="getElementPreviewHtml(element, true)"
                  class="w-full h-24 border rounded"
                  frameborder="0"
                  sandbox="allow-same-origin allow-scripts"
                />
              </div>
            </div>
          </div>

          <!-- Показываем сколько еще элементов -->
          <div v-if="props.selectedElements.length > 3" class="text-xs text-gray-500 text-center">
            ... и еще {{ props.selectedElements.length - 3 }} элементов
          </div>
        </div>

        <!-- Fallback для демо -->
        <div v-else class="flex gap-4">
          <div class="original flex-1">
            <h6 class="text-xs font-medium mb-2">Оригинал:</h6>
            <div class="preview-box border rounded p-2 bg-white min-h-20">
              <div v-html="previewHtml"></div>
            </div>
          </div>
          <div class="modified flex-1">
            <h6 class="text-xs font-medium mb-2">С вариациями ({{ selectedGroups.length }} групп):</h6>
            <div class="preview-box border rounded p-2 bg-white min-h-20">
              <div v-html="modifiedHtml"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useDirectusItems } from '#imports'
import {
  variationGroups as defaultGroups,
  saveCustomVariationGroups,
  loadCustomVariationGroups,
  applyVariationRules,
  type VariationGroup,
  type VariationRule
} from '~/utils/advanced-variation-mappings'
import { getAllClasses, type CSSClass } from '~/data/css-classes'

// Props
interface Props {
  selectedElements: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['variation-applied'])

// Composables
const toast = useToast()

// Reactive data
const variationGroups = ref<VariationGroup[]>([])
const isModified = ref(false)
const originalHtml = ref('<button class="btn btn-primary">Кнопка</button>')
const isEditMode = ref(false)
const selectedGroups = ref<string[]>([])
const availableElementTypes = ref<string[]>([])
const availableCSSClasses = ref<CSSClass[]>([])
const htmlTags = ref<string[]>([
  'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
  'button', 'a', 'img', 'input', 'textarea', 'select',
  'ul', 'ol', 'li', 'table', 'tr', 'td', 'th',
  'form', 'label', 'section', 'article', 'header', 'footer',
  'nav', 'main', 'aside', 'figure', 'figcaption'
])

// Options
const scopeOptions = [
  { label: 'Корневые элементы', value: 'root' },
  { label: 'Вложенные элементы', value: 'nested' },
  { label: 'Все элементы', value: 'all' }
]

// Computed
const modifiedHtml = computed(() => {
  // Получаем правила из выбранных групп
  const selectedRules = variationGroups.value
    .filter(group => selectedGroups.value.includes(group.id))
    .flatMap(group => group.rules.filter(rule => rule.enabled))
    .map(rule => rule.id)

  if (selectedRules.length === 0) {
    return originalHtml.value
  }

  return applyVariationRules(originalHtml.value, selectedRules)
})

// Улучшенный HTML для превью с реальными элементами
const previewHtml = computed(() => {
  if (props.selectedElements.length > 0) {
    // Используем HTML первого выбранного элемента
    return props.selectedElements[0].html || originalHtml.value
  }
  return originalHtml.value
})

// Функция для создания превью HTML элемента с изолированными стилями
const getElementPreviewHtml = (element: any, applyVariations: boolean = false) => {
  let html = element.html || '<div>Нет HTML</div>'

  // Применяем вариации если нужно
  if (applyVariations) {
    const selectedRules = variationGroups.value
      .filter(group => selectedGroups.value.includes(group.id))
      .flatMap(group => group.rules.filter(rule => rule.enabled))
      .map(rule => rule.id)

    if (selectedRules.length > 0) {
      html = applyVariationRules(html, selectedRules)
    }
  }

  // Создаем полный HTML с изолированными стилями
  return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  
    ${element.css || ''}
  
</head>
<body>
  ${html}
  ${element.js || ''}
</body>
</html>`
}

// Methods
const addVariationGroup = () => {
  const newGroup: VariationGroup = {
    id: `group-${Date.now()}`,
    name: 'Новая группа',
    description: 'Описание новой группы вариаций',
    icon: 'pi pi-palette',
    color: '#3b82f6',
    enabled: true,
    rules: []
  }
  
  variationGroups.value.push(newGroup)
  markAsModified()
}

const removeVariationGroup = (groupIndex: number) => {
  variationGroups.value.splice(groupIndex, 1)
  markAsModified()
}

const addVariationRule = (groupIndex: number) => {
  const newRule: VariationRule = {
    id: `rule-${Date.now()}`,
    name: 'Новое правило',
    description: 'Описание нового правила',
    category: variationGroups.value[groupIndex].id,
    targetElements: {
      types: [],
      tags: [],
      classes: [],
      scope: 'all'
    },
    transformations: {
      addClass: [],
      removeClass: []
    },
    priority: 10,
    enabled: true
  }
  
  variationGroups.value[groupIndex].rules.push(newRule)
  markAsModified()
}

const removeVariationRule = (groupIndex: number, ruleIndex: number) => {
  variationGroups.value[groupIndex].rules.splice(ruleIndex, 1)
  markAsModified()
}

const markAsModified = () => {
  isModified.value = true
}

const saveVariations = async () => {
  try {
    // Сохраняем в файл через API
    await $fetch('/api/save-user-settings', {
      method: 'POST',
      body: {
        type: 'variation-groups',
        data: variationGroups.value,
        filename: `variation-groups-${Date.now()}.json`
      }
    })

    // Также сохраняем в localStorage как fallback
    saveCustomVariationGroups(variationGroups.value)
    isModified.value = false

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Вариации сохранены в файл',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка сохранения:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить вариации в файл',
      life: 3000
    })
  }
}

const loadVariations = async () => {
  try {
    // Пытаемся загрузить из файла
    const response: any = await $fetch('/api/load-user-settings', {
      query: { type: 'variation-groups' }
    })

    if (response.success && response.data) {
      variationGroups.value = response.data
      console.log('Вариации загружены из файла:', response.filename)
    } else {
      // Fallback к localStorage
      const customGroups = loadCustomVariationGroups()
      if (customGroups.length > 0) {
        variationGroups.value = customGroups
      } else {
        variationGroups.value = [...defaultGroups]
      }
    }
  } catch (error) {
    console.error('Ошибка загрузки из файла, используем localStorage:', error)
    // Fallback к localStorage
    const customGroups = loadCustomVariationGroups()
    if (customGroups.length > 0) {
      variationGroups.value = customGroups
    } else {
      variationGroups.value = [...defaultGroups]
    }
  }
}

const toggleGroupSelection = (groupId: string) => {
  const index = selectedGroups.value.indexOf(groupId)
  if (index > -1) {
    selectedGroups.value.splice(index, 1)
  } else {
    selectedGroups.value.push(groupId)
  }
}

const applySelectedVariations = () => {
  const selectedRules = variationGroups.value
    .filter(group => selectedGroups.value.includes(group.id))
    .flatMap(group => group.rules.filter(rule => rule.enabled))

  if (selectedRules.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите хотя бы одну группу вариаций',
      life: 3000
    })
    return
  }

  if (props.selectedElements.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите элементы для применения вариаций',
      life: 3000
    })
    return
  }

  // Применяем вариации к каждому выбранному элементу
  const modifiedElements = props.selectedElements.map(element => {
    const modifiedHtml = applyVariationRules(element.html || '', selectedRules.map(r => r.id))
    return {
      ...element,
      html: modifiedHtml,
      modified: true,
      appliedVariations: selectedRules.map(r => r.name)
    }
  })

  // Эмитим событие с модифицированными элементами
  emit('variation-applied', {
    selectedGroups: selectedGroups.value,
    selectedRules: selectedRules.map(r => r.id),
    originalElements: props.selectedElements,
    modifiedElements,
    rulesApplied: selectedRules.length
  })

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: `Применено ${selectedRules.length} правил к ${props.selectedElements.length} элементам`,
    life: 3000
  })
}

const loadElementTypes = async () => {
  try {
    const { getItems } = useDirectusItems()
    const elements = await getItems({
      collection: 'welem_proto',
      params: {
        fields: ['elem_type'],
        limit: -1
      }
    })

    const types = new Set<string>()
    const elementsArray = Array.isArray(elements) ? elements : ((elements as any)?.data || [])

    elementsArray.forEach((element: any) => {
      if (element.elem_type && Array.isArray(element.elem_type)) {
        element.elem_type.forEach((type: string) => types.add(type))
      }
    })

    availableElementTypes.value = Array.from(types).sort()
  } catch (error) {
    console.error('Ошибка загрузки типов элементов:', error)
  }
}

const loadCSSClasses = () => {
  availableCSSClasses.value = getAllClasses()
}

// Lifecycle
onMounted(async () => {
  loadVariations()
  loadCSSClasses()
  await loadElementTypes()
})
</script>

<style scoped>
.variation-constructor {
  max-height: 80vh;
  overflow-y: auto;
}

.variation-group {
  transition: all 0.2s ease;
}

.variation-group:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.variation-rule {
  transition: all 0.2s ease;
}

.variation-rule:hover {
  background-color: #f8fafc;
}

.preview-box {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style>
