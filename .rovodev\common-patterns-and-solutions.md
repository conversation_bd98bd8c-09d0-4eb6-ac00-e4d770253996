# Общие паттерны и решения

## Часто используемые паттерны

### 1. Массовые операции (Bulk Operations)

Этот паттерн используется во всех основных страницах проекта для выполнения операций над множественными элементами.

```vue
<template>
  <!-- Toolbar с кнопками массовых операций -->
  <div class="flex gap-2">
    <Button
      v-tooltip.bottom="'Массовое добавление'"
      icon="pi pi-plus-circle"
      class="text-xs p-button-success"
      @click="openBulkCreate"
    />
    <Button
      v-tooltip.bottom="'Массовое редактирование'"
      icon="pi pi-pencil"
      class="text-xs p-button-primary"
      :disabled="!selectedItems.length"
      @click="openBulkEdit"
    />
    <Button
      v-tooltip.bottom="'Удалить выбранные'"
      icon="pi pi-trash"
      class="p-button-danger text-xs"
      :disabled="!selectedItems.length"
      @click="bulkDeleteItems"
    />
  </div>

  <!-- Контейнер массовых форм -->
  <BulkFormContainer
    :visible="bulkFormVisible"
    :mode="bulkFormMode"
    :collection="collection"
    :field-config="bulkFieldConfig"
    :selected-items="selectedItems"
    @close="closeBulkForm"
    @saved="onBulkFormSaved"
  />
</template>

<script setup lang="ts">
// Состояние массовых операций
const bulkFormVisible = ref(false)
const bulkFormMode = ref<'create' | 'edit'>('create')
const selectedItems = ref([])

// Конфигурация полей для массовых форм
const bulkFieldConfig = [
  {
    name: 'title',
    type: 'text',
    label: 'Название',
    required: true,
    placeholder: 'Введите название'
  },
  {
    name: 'tags',
    type: 'multiselect',
    label: 'Теги',
    options: tagOptions,
    display: 'chip'
  },
  {
    name: 'description',
    type: 'textarea',
    label: 'Описание',
    rows: 3
  }
]

// Методы массовых операций
const openBulkCreate = () => {
  bulkFormMode.value = 'create'
  bulkFormVisible.value = true
}

const openBulkEdit = () => {
  if (!selectedItems.value.length) return
  bulkFormMode.value = 'edit'
  bulkFormVisible.value = true
}

const closeBulkForm = () => {
  bulkFormVisible.value = false
}

const onBulkFormSaved = async (savedItems: any[]) => {
  toast.add({
    severity: 'success',
    summary: 'Успех',
    detail: `${savedItems.length} элементов сохранено`
  })
  
  await loadData()
  closeBulkForm()
}

const bulkDeleteItems = async () => {
  if (!selectedItems.value.length) return
  
  try {
    await Promise.all(
      selectedItems.value.map(item => deleteItem(item.id))
    )
    
    toast.add({
      severity: 'success',
      summary: 'Успех',
      detail: `${selectedItems.value.length} элементов удалено`
    })
    
    selectedItems.value = []
    await loadData()
  } catch (error) {
    console.error('Ошибка удаления:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось удалить элементы'
    })
  }
}
</script>
```

### 2. Фильтрация и поиск

Универсальный паттерн для фильтрации данных с поддержкой множественных критериев.

```vue
<template>
  <div class="flex gap-2">
    <!-- Глобальный поиск -->
    <InputText
      v-model="globalFilterValue"
      placeholder="Поиск..."
      class="w-full text-xs"
      @input="onGlobalFilterChange"
    />
    
    <!-- Фильтр по тегам -->
    <MultiSelect
      v-model="selectedTags"
      :options="availableTags"
      placeholder="Фильтр по тегам"
      display="chip"
      class="w-64 text-xs"
      @change="applyTagsFilter"
    />
    
    <!-- Фильтр по типам -->
    <MultiSelect
      v-model="selectedTypes"
      :options="availableTypes"
      placeholder="Фильтр по типам"
      display="chip"
      class="w-64 text-xs"
      @change="applyTypesFilter"
    />
  </div>
</template>

<script setup lang="ts">
// Состояние фильтров
const globalFilterValue = ref('')
const selectedTags = ref([])
const selectedTypes = ref([])
const items = ref([])

// Доступные опции для фильтров
const availableTags = computed(() => {
  const tags = new Set()
  items.value.forEach(item => {
    if (item.tags) {
      item.tags.forEach(tag => tags.add(tag))
    }
  })
  return Array.from(tags).sort()
})

const availableTypes = computed(() => {
  const types = new Set()
  items.value.forEach(item => {
    if (item.type) {
      types.add(item.type)
    }
  })
  return Array.from(types).sort()
})

// Фильтрованные элементы
const filteredItems = computed(() => {
  let filtered = items.value

  // Глобальный поиск
  if (globalFilterValue.value) {
    const searchTerm = globalFilterValue.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.title?.toLowerCase().includes(searchTerm) ||
      item.description?.toLowerCase().includes(searchTerm)
    )
  }

  // Фильтр по тегам
  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(item =>
      item.tags?.some(tag => selectedTags.value.includes(tag))
    )
  }

  // Фильтр по типам
  if (selectedTypes.value.length > 0) {
    filtered = filtered.filter(item =>
      selectedTypes.value.includes(item.type)
    )
  }

  return filtered
})

// Debounced поиск для производительности
import { debounce } from 'lodash-es'

const debouncedGlobalFilter = debounce((value: string) => {
  globalFilterValue.value = value
}, 300)

const onGlobalFilterChange = (event: any) => {
  debouncedGlobalFilter(event.target.value)
}

// Методы применения фильтров
const applyTagsFilter = () => {
  // Дополнительная логика если нужна
}

const applyTypesFilter = () => {
  // Дополнительная логика если нужна
}

// Сброс фильтров
const resetFilters = () => {
  globalFilterValue.value = ''
  selectedTags.value = []
  selectedTypes.value = []
}
</script>
```

### 3. Генерация скриншотов

Паттерн для массовой генерации скриншотов с прогресс-индикацией.

```vue
<template>
  <div class="flex gap-2">
    <Button
      v-tooltip.bottom="'Генерировать скриншоты'"
      icon="pi pi-camera"
      class="p-button-warning text-xs"
      :loading="generating"
      :disabled="!selectedItems.length"
      @click="generateScreenshots"
    />
    
    <!-- Прогресс-бар -->
    <ProgressBar
      v-if="generating"
      :value="screenshotProgress"
      :show-value="true"
      class="flex-1"
    />
  </div>
</template>

<script setup lang="ts">
const generating = ref(false)
const screenshotProgress = ref(0)

const generateScreenshots = async () => {
  if (!selectedItems.value.length) return
  
  generating.value = true
  screenshotProgress.value = 0
  
  try {
    const total = selectedItems.value.length
    let completed = 0
    
    // Обработка батчами для производительности
    const BATCH_SIZE = 3
    
    for (let i = 0; i < selectedItems.value.length; i += BATCH_SIZE) {
      const batch = selectedItems.value.slice(i, i + BATCH_SIZE)
      
      const batchPromises = batch.map(async (item) => {
        try {
          const screenshot = await $fetch('/api/capture-html-screenshot', {
            method: 'POST',
            body: {
              html: item.html,
              options: {
                width: 1200,
                height: 800,
                fullPage: false
              }
            }
          })
          
          // Сохранение скриншота
          await updateItem(item.id, {
            screenshot: screenshot.screenshot
          })
          
          completed++
          screenshotProgress.value = Math.round((completed / total) * 100)
          
          return { success: true, item: item.id }
        } catch (error) {
          console.error(`Ошибка генерации скриншота для ${item.id}:`, error)
          return { success: false, item: item.id, error }
        }
      })
      
      await Promise.allSettled(batchPromises)
      
      // Небольшая задержка между батчами
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    toast.add({
      severity: 'success',
      summary: 'Успех',
      detail: `Скриншоты сгенерированы для ${completed} элементов`
    })
    
    await loadData()
    
  } catch (error) {
    console.error('Ошибка генерации скриншотов:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сгенерировать скриншоты'
    })
  } finally {
    generating.value = false
    screenshotProgress.value = 0
  }
}
</script>
```

### 4. Динамические панели с изменяемыми размерами

Паттерн для создания интерфейсов с настраиваемыми панелями.

```vue
<template>
  <div class="flex h-[calc(100vh-1rem)]">
    <!-- Левая панель -->
    <div class="flex" :style="{ width: leftPanelWidth }">
      <div class="w-full bg-surface-50 border-r">
        <!-- Содержимое левой панели -->
      </div>
    </div>

    <!-- Дополнительная панель (условная) -->
    <div
      v-if="showAdditionalPanel"
      class="flex flex-col bg-surface-50 border-r"
      :style="{
        width: additionalPanelWidth,
        minWidth: '250px',
        maxWidth: '400px'
      }"
    >
      <!-- Заголовок панели -->
      <div class="flex justify-between items-center p-2 border-b bg-white">
        <h3 class="text-sm font-semibold">Дополнительная панель</h3>
        <Button
          icon="pi pi-times"
          text
          class="p-button-sm"
          @click="closeAdditionalPanel"
        />
      </div>
      
      <!-- Содержимое дополнительной панели -->
      <div class="flex-1 overflow-hidden">
        <!-- Контент -->
      </div>
    </div>

    <!-- Основная область -->
    <div class="flex flex-col flex-1" :style="{ width: mainAreaWidth }">
      <!-- Содержимое основной области -->
    </div>
  </div>
</template>

<script setup lang="ts">
// Состояние панелей
const leftPanelWidth = ref('400px')
const additionalPanelWidth = ref('300px')
const showAdditionalPanel = ref(false)

// Вычисляемая ширина основной области
const mainAreaWidth = computed(() => {
  let width = '100%'
  
  if (leftPanelWidth.value) {
    width = `calc(${width} - ${leftPanelWidth.value})`
  }
  
  if (showAdditionalPanel.value && additionalPanelWidth.value) {
    width = `calc(${width} - ${additionalPanelWidth.value})`
  }
  
  return width
})

// Методы управления панелями
const openAdditionalPanel = () => {
  showAdditionalPanel.value = true
}

const closeAdditionalPanel = () => {
  showAdditionalPanel.value = false
}

// Сохранение размеров в localStorage
watch(leftPanelWidth, (newWidth) => {
  localStorage.setItem('leftPanelWidth', newWidth)
})

watch(additionalPanelWidth, (newWidth) => {
  localStorage.setItem('additionalPanelWidth', newWidth)
})

// Восстановление размеров при монтировании
onMounted(() => {
  const savedLeftWidth = localStorage.getItem('leftPanelWidth')
  const savedAdditionalWidth = localStorage.getItem('additionalPanelWidth')
  
  if (savedLeftWidth) {
    leftPanelWidth.value = savedLeftWidth
  }
  
  if (savedAdditionalWidth) {
    additionalPanelWidth.value = savedAdditionalWidth
  }
})
</script>
```

### 5. Режимы работы (Work Modes)

Паттерн для компонентов с множественными режимами работы.

```vue
<template>
  <div class="flex flex-col gap-4 p-1">
    <!-- Переключатель режимов -->
    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-purple-50 border rounded-lg">
      <div>
        <h1 class="text-xl font-bold">Название компонента</h1>
        <p class="text-sm text-gray-600">Описание возможностей</p>
      </div>
      <div class="flex gap-2">
        <Button
          v-for="mode in workModes"
          :key="mode.value"
          :label="mode.label"
          :class="[
            'text-xs px-3 py-2',
            currentMode === mode.value ? 'p-button-info' : 'p-button-outlined'
          ]"
          @click="setWorkMode(mode.value)"
        />
      </div>
    </div>

    <!-- Контент для разных режимов -->
    <div v-if="currentMode === 'standard'" class="standard-mode">
      <!-- Стандартный режим -->
    </div>

    <div v-else-if="currentMode === 'advanced'" class="advanced-mode">
      <!-- Продвинутый режим -->
    </div>

    <div v-else-if="currentMode === 'combination'" class="combination-mode">
      <!-- Режим комбинаций -->
    </div>
  </div>
</template>

<script setup lang="ts">
// Определение режимов работы
const workModes = [
  { value: 'standard', label: 'Стандартный', icon: 'pi pi-table' },
  { value: 'advanced', label: 'Продвинутый', icon: 'pi pi-cog' },
  { value: 'combination', label: 'Комбинации', icon: 'pi pi-th-large' }
]

const currentMode = ref('standard')

// Методы переключения режимов
const setWorkMode = (mode: string) => {
  currentMode.value = mode
  
  // Сохранение выбранного режима
  localStorage.setItem('workMode', mode)
  
  // Дополнительная логика при смене режима
  onModeChange(mode)
}

const onModeChange = (mode: string) => {
  // Логика, выполняемая при смене режима
  switch (mode) {
    case 'standard':
      // Инициализация стандартного режима
      break
    case 'advanced':
      // Инициализация продвинутого режима
      break
    case 'combination':
      // Инициализация режима комбинаций
      break
  }
}

// Восстановление режима при монтировании
onMounted(() => {
  const savedMode = localStorage.getItem('workMode')
  if (savedMode && workModes.some(mode => mode.value === savedMode)) {
    currentMode.value = savedMode
  }
})
</script>
```

### 6. Drag & Drop интерфейс

Паттерн для создания интерфейсов с перетаскиванием элементов.

```vue
<template>
  <div class="drag-drop-container">
    <!-- Источник элементов -->
    <div class="source-panel">
      <div
        v-for="item in sourceItems"
        :key="item.id"
        class="draggable-item"
        draggable="true"
        @dragstart="onDragStart($event, item)"
      >
        {{ item.title }}
      </div>
    </div>

    <!-- Область для размещения -->
    <div class="drop-zones">
      <div
        v-for="(zone, zoneIndex) in dropZones"
        :key="zoneIndex"
        class="drop-zone"
        @dragover.prevent
        @drop="onDrop($event, zoneIndex)"
      >
        <div
          v-for="(item, itemIndex) in zone.items"
          :key="item.id"
          class="dropped-item"
          draggable="true"
          @dragstart="onDragStart($event, item, zoneIndex, itemIndex)"
        >
          <span>{{ item.title }}</span>
          <Button
            icon="pi pi-times"
            class="p-button-text p-button-sm"
            @click="removeFromZone(zoneIndex, itemIndex)"
          />
        </div>
        
        <!-- Placeholder для пустой зоны -->
        <div v-if="zone.items.length === 0" class="drop-placeholder">
          Перетащите элементы сюда
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface DragData {
  item: any
  sourceZone?: number
  sourceIndex?: number
}

const sourceItems = ref([])
const dropZones = ref([
  { id: 1, title: 'Зона 1', items: [] },
  { id: 2, title: 'Зона 2', items: [] }
])

const onDragStart = (event: DragEvent, item: any, zoneIndex?: number, itemIndex?: number) => {
  const dragData: DragData = {
    item,
    sourceZone: zoneIndex,
    sourceIndex: itemIndex
  }
  
  event.dataTransfer?.setData('text/plain', JSON.stringify(dragData))
  event.dataTransfer!.effectAllowed = 'move'
}

const onDrop = (event: DragEvent, targetZoneIndex: number) => {
  event.preventDefault()
  
  const dragDataStr = event.dataTransfer?.getData('text/plain')
  if (!dragDataStr) return
  
  const dragData: DragData = JSON.parse(dragDataStr)
  
  // Если элемент перемещается из другой зоны
  if (dragData.sourceZone !== undefined && dragData.sourceIndex !== undefined) {
    // Удаляем из исходной зоны
    dropZones.value[dragData.sourceZone].items.splice(dragData.sourceIndex, 1)
  }
  
  // Добавляем в целевую зону
  dropZones.value[targetZoneIndex].items.push(dragData.item)
  
  // Уведомление об изменении
  emit('zones-updated', dropZones.value)
}

const removeFromZone = (zoneIndex: number, itemIndex: number) => {
  dropZones.value[zoneIndex].items.splice(itemIndex, 1)
  emit('zones-updated', dropZones.value)
}

// Методы для работы с зонами
const clearZone = (zoneIndex: number) => {
  dropZones.value[zoneIndex].items = []
}

const clearAllZones = () => {
  dropZones.value.forEach(zone => {
    zone.items = []
  })
}
</script>

<style scoped>
.drag-drop-container {
  display: flex;
  gap: 1rem;
  height: 100%;
}

.source-panel {
  width: 300px;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
}

.draggable-item {
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  background: #f3f4f6;
  border-radius: 0.25rem;
  cursor: grab;
  user-select: none;
}

.draggable-item:active {
  cursor: grabbing;
}

.drop-zones {
  flex: 1;
  display: flex;
  gap: 1rem;
}

.drop-zone {
  flex: 1;
  min-height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 1rem;
}

.drop-zone:hover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.dropped-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  background: #e5e7eb;
  border-radius: 0.25rem;
}

.drop-placeholder {
  text-align: center;
  color: #9ca3af;
  padding: 2rem;
}
</style>
```

### 7. Предпросмотр с viewport переключением

Паттерн для компонентов с предпросмотром в разных разрешениях.

```vue
<template>
  <div class="preview-container">
    <!-- Toolbar предпросмотра -->
    <div class="flex items-center justify-between mb-3">
      <h3 class="text-lg font-semibold">Предпросмотр</h3>
      <div class="flex gap-2">
        <Button
          v-for="viewport in viewports"
          :key="viewport.name"
          :label="viewport.label"
          :class="[
            'text-xs',
            currentViewport === viewport.name ? 'p-button-primary' : 'p-button-outlined'
          ]"
          @click="setViewport(viewport.name)"
        />
      </div>
    </div>

    <!-- Область предпросмотра -->
    <div class="preview-area">
      <div class="flex justify-center p-4">
        <iframe
          v-if="previewContent"
          :srcdoc="previewContent"
          :style="{ width: currentViewportWidth, height: '600px' }"
          class="border rounded border-surface-300 bg-white"
          frameborder="0"
          sandbox="allow-scripts allow-same-origin allow-forms"
        />
        <div v-else class="flex items-center justify-center h-96 text-surface-500">
          Нет контента для предпросмотра
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Viewport {
  name: string
  label: string
  width: string
}

const viewports: Viewport[] = [
  { name: 'desktop', label: '1440px', width: '1440px' },
  { name: 'tablet', label: '768px', width: '768px' },
  { name: 'mobile', label: '375px', width: '375px' }
]

const currentViewport = ref('desktop')
const previewContent = ref('')

const currentViewportWidth = computed(() => {
  const viewport = viewports.find(v => v.name === currentViewport.value)
  return viewport?.width || '1440px'
})

const setViewport = (viewportName: string) => {
  currentViewport.value = viewportName
  
  // Сохранение выбранного viewport
  localStorage.setItem('previewViewport', viewportName)
  
  // Уведомление о смене viewport
  emit('viewport-changed', viewportName)
}

// Обновление контента предпросмотра
const updatePreview = (content: string) => {
  previewContent.value = content
}

// Восстановление viewport при монтировании
onMounted(() => {
  const savedViewport = localStorage.getItem('previewViewport')
  if (savedViewport && viewports.some(v => v.name === savedViewport)) {
    currentViewport.value = savedViewport
  }
})

// Экспорт методов для родительского компонента
defineExpose({
  updatePreview,
  setViewport
})
</script>

<style scoped>
.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-area {
  flex: 1;
  overflow: auto;
  background: #f8fafc;
  border-radius: 0.5rem;
}
</style>
```

Эти паттерны покрывают основные сценарии использования в проекте и могут быть адаптированы под конкретные нужды новых компонентов.