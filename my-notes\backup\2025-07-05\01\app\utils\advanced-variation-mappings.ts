// Расширенная система маппинга вариаций с визуальным конструктором

export interface VariationRule {
  id: string
  name: string
  description: string
  category: string
  targetElements: {
    types?: string[]        // Типы элементов (button, card, etc.)
    tags?: string[]         // HTML теги (div, span, etc.)
    classes?: string[]      // CSS классы
    selectors?: string[]    // CSS селекторы
    scope: 'root' | 'nested' | 'all'  // Область применения
  }
  transformations: {
    addClass?: string[]     // Добавить классы
    removeClass?: string[]  // Удалить классы
    replaceClass?: { from: string, to: string }[]  // Заменить классы
    addStyle?: Record<string, string>  // Добавить inline стили
    addAttributes?: Record<string, string>  // Добавить атрибуты
    wrapWith?: string      // Обернуть в элемент
    addCustomClasses?: string    // НОВОЕ: Пользовательские классы для добавления
    removeCustomClasses?: string // НОВОЕ: Пользовательские классы для удаления
  }
  conditions?: {
    hasClass?: string[]    // Применять только если есть класс
    hasAttribute?: string[] // Применять только если есть атрибут
    hasParent?: string[]   // Применять только если родитель соответствует
    hasChild?: string[]    // Применять только если есть дочерний элемент
  }
  priority: number         // Приоритет применения (больше = выше)
  enabled: boolean
}

export interface VariationGroup {
  id: string
  name: string
  description: string
  icon: string
  color: string
  rules: VariationRule[]
  enabled: boolean
}

// Предустановленные группы вариаций
export const variationGroups: VariationGroup[] = [
  {
    id: 'colors',
    name: 'Цветовые схемы',
    description: 'Изменение цветов элементов',
    icon: 'pi pi-palette',
    color: '#3b82f6',
    enabled: true,
    rules: [
      {
        id: 'primary-blue',
        name: 'Синяя схема',
        description: 'Применяет синие цвета',
        category: 'colors',
        targetElements: {
          types: ['button', 'card', 'badge'],
          scope: 'all'
        },
        transformations: {
          replaceClass: [
            { from: 'btn-primary', to: 'btn-primary bg-blue-600 hover:bg-blue-700' },
            { from: 'bg-primary', to: 'bg-blue-600' },
            { from: 'text-primary', to: 'text-blue-600' },
            { from: 'border-primary', to: 'border-blue-600' }
          ]
        },
        priority: 10,
        enabled: true
      },
      {
        id: 'primary-green',
        name: 'Зеленая схема',
        description: 'Применяет зеленые цвета',
        category: 'colors',
        targetElements: {
          types: ['button', 'card', 'badge'],
          scope: 'all'
        },
        transformations: {
          replaceClass: [
            { from: 'btn-primary', to: 'btn-success bg-green-600 hover:bg-green-700' },
            { from: 'bg-primary', to: 'bg-green-600' },
            { from: 'text-primary', to: 'text-green-600' },
            { from: 'border-primary', to: 'border-green-600' }
          ]
        },
        priority: 10,
        enabled: true
      }
    ]
  },
  {
    id: 'sizes',
    name: 'Размеры',
    description: 'Изменение размеров элементов',
    icon: 'pi pi-expand',
    color: '#f59e0b',
    enabled: true,
    rules: [
      {
        id: 'size-small',
        name: 'Маленький размер',
        description: 'Уменьшает размеры элементов',
        category: 'sizes',
        targetElements: {
          tags: ['button', 'input', 'select'],
          scope: 'all'
        },
        transformations: {
          addClass: ['text-sm', 'px-2', 'py-1'],
          removeClass: ['text-base', 'text-lg', 'px-4', 'py-2', 'px-6', 'py-3']
        },
        priority: 20,
        enabled: true
      },
      {
        id: 'size-large',
        name: 'Большой размер',
        description: 'Увеличивает размеры элементов',
        category: 'sizes',
        targetElements: {
          tags: ['button', 'input', 'select'],
          scope: 'all'
        },
        transformations: {
          addClass: ['text-lg', 'px-6', 'py-3'],
          removeClass: ['text-sm', 'text-base', 'px-2', 'py-1', 'px-4', 'py-2']
        },
        priority: 20,
        enabled: true
      }
    ]
  },
  {
    id: 'borders',
    name: 'Границы и рамки',
    description: 'Стили границ элементов',
    icon: 'pi pi-stop',
    color: '#8b5cf6',
    enabled: true,
    rules: [
      {
        id: 'border-rounded',
        name: 'Скругленные углы',
        description: 'Добавляет скругленные углы',
        category: 'borders',
        targetElements: {
          types: ['card', 'button', 'input'],
          scope: 'root'
        },
        transformations: {
          addClass: ['rounded-lg'],
          removeClass: ['rounded-none', 'rounded-sm', 'rounded-xl']
        },
        priority: 15,
        enabled: true
      },
      {
        id: 'border-sharp',
        name: 'Острые углы',
        description: 'Убирает скругление углов',
        category: 'borders',
        targetElements: {
          types: ['card', 'button', 'input'],
          scope: 'root'
        },
        transformations: {
          addClass: ['rounded-none'],
          removeClass: ['rounded', 'rounded-sm', 'rounded-lg', 'rounded-xl']
        },
        priority: 15,
        enabled: true
      }
    ]
  },
  {
    id: 'shadows',
    name: 'Тени',
    description: 'Эффекты теней',
    icon: 'pi pi-circle',
    color: '#6b7280',
    enabled: true,
    rules: [
      {
        id: 'shadow-soft',
        name: 'Мягкая тень',
        description: 'Добавляет мягкую тень',
        category: 'shadows',
        targetElements: {
          types: ['card', 'modal', 'dropdown'],
          scope: 'root'
        },
        transformations: {
          addClass: ['shadow-lg'],
          removeClass: ['shadow-none', 'shadow-sm', 'shadow-xl']
        },
        priority: 5,
        enabled: true
      },
      {
        id: 'shadow-strong',
        name: 'Сильная тень',
        description: 'Добавляет выраженную тень',
        category: 'shadows',
        targetElements: {
          types: ['card', 'modal', 'dropdown'],
          scope: 'root'
        },
        transformations: {
          addClass: ['shadow-2xl'],
          removeClass: ['shadow-none', 'shadow-sm', 'shadow-lg']
        },
        priority: 5,
        enabled: true
      }
    ]
  }
]

// Функции для работы с правилами
export function getVariationGroupById(id: string): VariationGroup | undefined {
  return variationGroups.find(group => group.id === id)
}

export function getVariationRuleById(groupId: string, ruleId: string): VariationRule | undefined {
  const group = getVariationGroupById(groupId)
  return group?.rules.find(rule => rule.id === ruleId)
}

export function getAllEnabledRules(): VariationRule[] {
  return variationGroups
    .filter(group => group.enabled)
    .flatMap(group => group.rules.filter(rule => rule.enabled))
    .sort((a, b) => b.priority - a.priority)
}

// Применение правил к HTML
export function applyVariationRules(html: string, selectedRules: string[]): string {
  let modifiedHtml = html

  console.log('Применяем правила к HTML:', selectedRules)

  // ИСПРАВЛЕНО: Получаем правила по ID из всех групп (включая пользовательские)
  const allGroups = getAllVariationGroups()
  const rules = selectedRules
    .map(ruleId => {
      for (const group of allGroups) {
        const rule = group.rules.find(r => r.id === ruleId)
        if (rule) {
          console.log('Найдено правило:', rule.name, rule.transformations)
          return rule
        }
      }
      console.log('Правило не найдено:', ruleId)
      return null
    })
    .filter(Boolean) as VariationRule[]

  console.log('Найдено правил для применения:', rules.length)

  // Сортируем по приоритету
  rules.sort((a, b) => b.priority - a.priority)

  // Применяем каждое правило
  for (const rule of rules) {
    console.log('Применяем правило:', rule.name)
    modifiedHtml = applyVariationRule(modifiedHtml, rule)
  }

  console.log('Итоговый HTML:', modifiedHtml)
  return modifiedHtml
}

function applyVariationRule(html: string, rule: VariationRule): string {
  let modifiedHtml = html

  console.log('Применяем правило:', rule.name, rule.transformations)

  // Применяем замены классов
  if (rule.transformations.replaceClass) {
    for (const replacement of rule.transformations.replaceClass) {
      const regex = new RegExp(`\\b${replacement.from}\\b`, 'g')
      modifiedHtml = modifiedHtml.replace(regex, replacement.to)
    }
  }

  // ИСПРАВЛЕНО: Применяем добавление классов
  if (rule.transformations.addClass && rule.transformations.addClass.length > 0) {
    const classesToAdd = rule.transformations.addClass.join(' ')
    console.log('Добавляем классы:', classesToAdd)

    // Ищем все теги с class атрибутом
    modifiedHtml = modifiedHtml.replace(/class="([^"]*)"/g, (_, existingClasses) => {
      const newClasses = existingClasses ? `${existingClasses} ${classesToAdd}` : classesToAdd
      return `class="${newClasses}"`
    })

    // Если нет class атрибута, добавляем к первому тегу
    if (!modifiedHtml.includes('class=')) {
      modifiedHtml = modifiedHtml.replace(/<(\w+)([^>]*)>/, `<$1$2 class="${classesToAdd}">`)
    }
  }

  // ИСПРАВЛЕНО: Применяем пользовательские классы для добавления
  if (rule.transformations.addCustomClasses) {
    const customClasses = rule.transformations.addCustomClasses.trim()
    if (customClasses) {
      console.log('Добавляем пользовательские классы:', customClasses)

      modifiedHtml = modifiedHtml.replace(/class="([^"]*)"/g, (_, existingClasses) => {
        const newClasses = existingClasses ? `${existingClasses} ${customClasses}` : customClasses
        return `class="${newClasses}"`
      })

      if (!modifiedHtml.includes('class=')) {
        modifiedHtml = modifiedHtml.replace(/<(\w+)([^>]*)>/, `<$1$2 class="${customClasses}">`)
      }
    }
  }

  // ИСПРАВЛЕНО: Применяем удаление классов
  if (rule.transformations.removeClass && rule.transformations.removeClass.length > 0) {
    const classesToRemove = rule.transformations.removeClass
    console.log('Удаляем классы:', classesToRemove)

    for (const classToRemove of classesToRemove) {
      const regex = new RegExp(`\\b${classToRemove}\\b\\s*`, 'g')
      modifiedHtml = modifiedHtml.replace(regex, '')
    }
  }

  // ИСПРАВЛЕНО: Применяем пользовательские классы для удаления
  if (rule.transformations.removeCustomClasses) {
    const customClassesToRemove = rule.transformations.removeCustomClasses.trim().split(/\s+/)
    if (customClassesToRemove.length > 0 && customClassesToRemove[0]) {
      console.log('Удаляем пользовательские классы:', customClassesToRemove)

      for (const classToRemove of customClassesToRemove) {
        const regex = new RegExp(`\\b${classToRemove}\\b\\s*`, 'g')
        modifiedHtml = modifiedHtml.replace(regex, '')
      }
    }
  }

  console.log('Результат применения правила:', modifiedHtml)
  return modifiedHtml
}

// Сохранение и загрузка пользовательских правил
export function saveCustomVariationGroups(groups: VariationGroup[]): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('customVariationGroups', JSON.stringify(groups))
  }
}

export function loadCustomVariationGroups(): VariationGroup[] {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('customVariationGroups')
    return stored ? JSON.parse(stored) : []
  }
  return []
}

export function getAllVariationGroups(): VariationGroup[] {
  const custom = loadCustomVariationGroups()
  return [...variationGroups, ...custom]
}
