import { load } from 'cheerio'

interface AnalyzeResult {
  layout: Set<string>
  elements: Set<string>
  graphics: Set<string>
  features: Set<string>
  treeStructure?: string
}

type Rule = {
  test: ($: ReturnType<typeof load>) => boolean
  field: keyof AnalyzeResult
  value: string
}

const rules: Rule[] = [
  // 1/1 правила
  {
    test: ($) => $('.col-12, .col-sm-12, .col-md-12, .col-lg-12, .col-xl-12, .col-xxl-12').length > 0,
    field: 'layout',
    value: '1/1'
  },
  // Добавляем новые правила для layout с классами .column
  // 1/1 правила для .column
  {
    test: ($) => $('.column.one').length > 0,
    field: 'layout',
    value: '1/1'
  },
  // 1/2 правила
  {
    test: ($) => $('.col-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xl-6, .col-xxl-6').length > 0,
    field: 'layout',
    value: '1/2'
  },
  // 1/2 правила для .column
  {
    test: ($) => $('.column.one-second').length > 0,
    field: 'layout',
    value: '1/2'
  },
  // 1/3 правила
  {
    test: ($) => $('.col-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xl-4, .col-xxl-4').length > 0,
    field: 'layout',
    value: '1/3'
  },
  // 1/3 правила для .column
  {
    test: ($) => $('.column.one-third').length > 0,
    field: 'layout',
    value: '1/3'
  },
  // 2/3 правила
  {
    test: ($) => $('.col-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xl-8, .col-xxl-8').length > 0,
    field: 'layout',
    value: '2/3'
  },
  // 2/3 правила для .column
  {
    test: ($) => $('.column.two-third').length > 0,
    field: 'layout',
    value: '2/3'
  },
  // 1/4 правила
  {
    test: ($) => $('.col-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xl-3, .col-xxl-3').length > 0,
    field: 'layout',
    value: '1/4'
  },
  // 1/4 правила для .column
  {
    test: ($) => $('.column.one-fourth').length > 0,
    field: 'layout',
    value: '1/4'
  },
  // 3/4 правила
  {
    test: ($) => $('.col-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xl-9, .col-xxl-9').length > 0,
    field: 'layout',
    value: '3/4'
  },
  // 3/4 правила для .column
  {
    test: ($) => $('.column.three-fourth').length > 0,
    field: 'layout',
    value: '3/4'
  },
  // 1/6 правила
  {
    test: ($) => $('.col-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xl-2, .col-xxl-2').length > 0,
    field: 'layout',
    value: '1/6'
  },
  // 1/6 правила для .column
  {
    test: ($) => $('.column.one-sixth').length > 0,
    field: 'layout',
    value: '1/6'
  },
  // 5/6 правила
  {
    test: ($) => $('.col-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xl-10, .col-xxl-10').length > 0,
    field: 'layout',
    value: '5/6'
  },
  // 1/12 правила
  {
    test: ($) => $('.col-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xl-1, .col-xxl-1').length > 0,
    field: 'layout',
    value: '1/12'
  },
  // 5/12 правила
  {
    test: ($) => $('.col-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xl-5, .col-xxl-5').length > 0,
    field: 'layout',
    value: '5/12'
  },
  // 7/12 правила
  {
    test: ($) => $('.col-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xl-7, .col-xxl-7').length > 0,
    field: 'layout',
    value: '7/12'
  },
  // 11/12 правила
  {
    test: ($) => $('.col-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xl-11, .col-xxl-11').length > 0,
    field: 'layout',
    value: '11/12'
  },
  // 1/5 правила
  {
    test: ($) => $('.col-1-5, .col-sm-1-5, .col-md-1-5, .col-lg-1-5, .col-xl-1-5, .col-xxl-1-5').length > 0,
    field: 'layout',
    value: '1/5'
  },
  // 1/5 правила для .column
  {
    test: ($) => $('.column.one-fifth').length > 0,
    field: 'layout',
    value: '1/5'
  },
  // 2/5 правила
  {
    test: ($) => $('.col-2-5, .col-sm-2-5, .col-md-2-5, .col-lg-2-5, .col-xl-2-5, .col-xxl-2-5').length > 0,
    field: 'layout',
    value: '2/5'
  },
  // 3/5 правила
  {
    test: ($) => $('.col-3-5, .col-sm-3-5, .col-md-3-5, .col-lg-3-5, .col-xl-3-5, .col-xxl-3-5').length > 0,
    field: 'layout',
    value: '3/5'
  },
  // 4/5 правила
  {
    test: ($) => $('.col-4-5, .col-sm-4-5, .col-md-4-5, .col-lg-4-5, .col-xl-4-5, .col-xxl-4-5').length > 0,
    field: 'layout',
    value: '4/5'
  },
  // Элементы
  // Заголовок
  {
    test: ($) => $('h1, h2, h3, h4, h5, h6').length > 0,
    field: 'elements',
    value: 'Заголовок'
  },
  // Текст
  {
    test: ($) => $('p').length > 0,
    field: 'elements',
    value: 'Текст'
  },
  // Краткий текст
  {
    test: ($) => $('span').length > 0,
    field: 'elements',
    value: 'Краткий-текст'
  },
  // Иконка
  {
    test: ($) => $('i').length > 0,
    field: 'elements',
    value: 'Иконка'
  },
  // Кнопка (расширенное правило)
  {
    test: ($) => $('button, .btn, .button, .mfn-link, .content_link, .icon_bar').length > 0,
    field: 'elements',
    value: 'Кнопка'
  },
  // Аккордеон
  {
    test: ($) => $('.accordion').length > 0,
    field: 'elements',
    value: 'Аккордеон'
  },
  // Оповещение
  {
    test: ($) => $('.alert').length > 0,
    field: 'elements',
    value: 'Оповещение'
  },
  // Бейдж
  {
    test: ($) => $('.badge').length > 0,
    field: 'elements',
    value: 'Бейдж'
  },
  // Навигация
  {
    test: ($) => $('nav, .navbar, .nav').length > 0,
    field: 'elements',
    value: 'Навигация'
  },
  // Хлебные крошки
  {
    test: ($) => $('.breadcrumb').length > 0,
    field: 'elements',
    value: 'Хлебные-крошки'
  },
  // Группа кнопок
  {
    test: ($) => $('.btn-group').length > 0,
    field: 'elements',
    value: 'Группа-кнопок'
  },
  // Карточка
  {
    test: ($) => $('.card').length > 0,
    field: 'elements',
    value: 'Карточка'
  },
  // Dropdown
  {
    test: ($) => $('.dropdown').length > 0,
    field: 'elements',
    value: 'Dropdown'
  },
  // Выделение
  {
    test: ($) => $('.highlight').length > 0,
    field: 'elements',
    value: 'Выделение'
  },
  
  // Image-frame
  {
    test: ($) => $('.image_frame').length > 0,
    field: 'elements',
    value: 'Image-frame'
  },
  
  // Cta-box
  {
    test: ($) => $('.article_box, .promo_box, .call_to_action').length > 0,
    field: 'elements',
    value: 'Cta-box'
  },
  
  // Инфо-лист
  {
    test: ($) => $('.feature_list, .column_list, .get_in_touch, .opening_hours').length > 0,
    field: 'elements',
    value: 'Инфо-лист'
  },
  
  // Таймлайн
  {
    test: ($) => $('.timeline_items').length > 0,
    field: 'elements',
    value: 'Таймлайн'
  },
  
  // Fancy-divider
  {
    test: ($) => $('.fancy-divider').length > 0,
    field: 'elements',
    value: 'Fancy-divider'
  },
  
  // Плитка
  {
    test: ($) => $('.portfolio_grid').length > 0,
    field: 'elements',
    value: 'Плитка'
  },
  
  // Wow-slider
  {
    test: ($) => $('.ws_images').length > 0,
    field: 'elements',
    value: 'Wow-slider'
  },
  // Модальное окно
  {
    test: ($) => $('.modal, .modal-popup-, .subscribe-popup, .popup-').length > 0,
    field: 'elements',
    value: 'Модальное-окно'
  },
  // Табы
  {
    test: ($) => $('.nav-tabs, .nav-pills').length > 0,
    field: 'elements',
    value: 'Табы'
  },
  // Offcanvas
  {
    test: ($) => $('.offcanvas').length > 0,
    field: 'elements',
    value: 'Offcanvas'
  },
  // Пагинация
  {
    test: ($) => $('.pagination').length > 0,
    field: 'elements',
    value: 'Пагинация'
  },
  // Popover
  {
    test: ($) => $('[data-bs-toggle="popover"]').length > 0,
    field: 'elements',
    value: 'Popover'
  },
  // Прогресс бар
  {
    test: ($) => $('.progress-bar, .progress').length > 0,
    field: 'elements',
    value: 'Прогресс-бар'
  },
  // Toast
  {
    test: ($) => $('.toast').length > 0,
    field: 'elements',
    value: 'Toast'
  },
  // Tooltip
  {
    test: ($) => $('[data-bs-toggle="tooltip"]').length > 0,
    field: 'elements',
    value: 'Tooltip'
  },
  // Шапка
  {
    test: ($) => $('header').length > 0,
    field: 'elements',
    value: 'Шапка'
  },
  // Подвал
  {
    test: ($) => $('footer').length > 0,
    field: 'elements',
    value: 'Подвал'
  },
  // iframe
  {
    test: ($) => $('iframe').length > 0,
    field: 'elements',
    value: 'iframe'
  },
  // Видео
  {
    test: ($) => $('video').length > 0,
    field: 'elements',
    value: 'Видео'
  },
  // Картинка
  {
    test: ($) => $('img, picture, figure').length > 0,
    field: 'elements',
    value: 'Картинка'
  },
  // Цитата
  {
    test: ($) => $('blockquote').length > 0,
    field: 'elements',
    value: 'Цитата'
  },
  // svg
  {
    test: ($) => $('svg').length > 0,
    field: 'elements',
    value: 'svg'
  },
  // Таблица
  {
    test: ($) => $('table').length > 0,
    field: 'elements',
    value: 'Таблица'
  },
  // Форма
  {
    test: ($) => $('form').length > 0,
    field: 'elements',
    value: 'Форма'
  },
  // JS
  {
    test: ($) => $('script').length > 0,
    field: 'elements',
    value: 'JS'
  },
  // Флип бокс
  {
    test: ($) => $('.flip-box').length > 0,
    field: 'elements',
    value: 'Флип-бокс'
  },
  // Atropos
  {
    test: ($) => $('.atropos').length > 0,
    field: 'elements',
    value: 'Atropos'
  },
  // iBanner
  {
    test: ($) => $('.interactive-banner-').length > 0,
    field: 'elements',
    value: 'iBanner'
  },
  // Swiper
  {
    test: ($) => $('.swiper').length > 0,
    field: 'elements',
    value: 'Swiper'
  },
  // Логотипы
  {
    test: ($) => $('.customers-style-').length > 0,
    field: 'elements',
    value: 'Логотипы'
  },
  // Countdown
  {
    test: ($) => $('.countdown-style-').length > 0,
    field: 'elements',
    value: 'Countdown'
  },
  // Инфо бокс
  {
    test: ($) => $('.feature-box, .fancy-text-box-style-').length > 0,
    field: 'elements',
    value: 'Инфо-бокс'
  },
  // Счетчик
  {
    test: ($) => $('.counter').length > 0,
    field: 'elements',
    value: 'Счетчик'
  },
  // Разделитель
  {
    test: ($) => $('.divider-style-').length > 0,
    field: 'elements',
    value: 'Разделитель'
  },
  // Буквица
  {
    test: ($) => $('.first-letter').length > 0,
    field: 'elements',
    value: 'Буквица'
  },
  // Fancy text
  {
    test: ($) => $('[data-fancy-text]').length > 0,
    field: 'elements',
    value: 'Fancy-text'
  },
  // Галерея
  {
    test: ($) => $('.image-gallery-style-, .instafeed-grid').length > 0,
    field: 'elements',
    value: 'Галерея'
  },
  // Прайс
  {
    test: ($) => $('.pricing-table-style-').length > 0,
    field: 'elements',
    value: 'Прайс'
  },
  // Процесс
  {
    test: ($) => $('.process-step-style-').length > 0,
    field: 'elements',
    value: 'Процесс'
  },
  // Карусель
  {
    test: ($) => $('.carousel').length > 0,
    field: 'elements',
    value: 'Карусель'
  },
  // Чек лист
  {
    test: ($) => $('.list-group, .list-style-').length > 0,
    field: 'elements',
    value: 'Чек-лист'
  },
  // Диаграмма (расширенное правило)
  {
    test: ($) => $('.chart-percent, .chart_box').length > 0,
    field: 'elements',
    value: 'Диаграмма'
  },
  // Отзывы
  {
    test: ($) => $('.review-style-, .testimonials-style-').length > 0,
    field: 'elements',
    value: 'Отзывы'
  },
  // Rotate box
  {
    test: ($) => $('.rotate-box-style-').length > 0,
    field: 'elements',
    value: 'Rotate-box'
  },
  // Services box
  {
    test: ($) => $('.services-box-style-').length > 0,
    field: 'elements',
    value: 'Services-box'
  },
  // Sliding box
  {
    test: ($) => $('.sliding-box-item').length > 0,
    field: 'elements',
    value: 'Sliding-box'
  },
  // Команда
  {
    test: ($) => $('.team-style-').length > 0,
    field: 'elements',
    value: 'Команда'
  },
  // Rev Slider
  {
    test: ($) => $('.rev_slider').length > 0,
    field: 'elements',
    value: 'Rev-Slider'
  },
  // Фильтр портфолио
  {
    test: ($) => $('.portfolio-filter').length > 0,
    field: 'elements',
    value: 'Фильтр-портфолио'
  },
  // Портфолио
  {
    test: ($) => $('.portfolio-').length > 0,
    field: 'elements',
    value: 'Портфолио'
  },
  // Блог
  {
    test: ($) => $('.blog-').length > 0,
    field: 'elements',
    value: 'Блог'
  },
  // Графика
  {
    test: ($) => $('[style*="background-image"]').length > 0,
    field: 'graphics',
    value: 'Фоновая-картинка'
  },
  {
    test: ($) => $('img').length > 0,
    field: 'graphics',
    value: 'Картинка'
  },
  // Features
  // Адаптивный фон
  {
    test: ($) => $('[data-background]').length > 0 || $('.Adaptive-bg').length > 0,
    field: 'features',
    value: 'Адаптивный-фон'
  },
  // Частицы
  {
    test: ($) => $('[data-particle="true"]').length > 0 || $('[data-particle-options]').length > 0,
    field: 'features',
    value: 'Частицы'
  },
  // Анимация (расширенное правило)
  {
    test: ($) => $('[data-anime], [data-anim-type]').length > 0,
    field: 'features',
    value: 'Анимация'
  },
  // Текст-градиент
  {
    test: ($) => $('.text-gradient-').length > 0,
    field: 'features',
    value: 'Текст-градиент'
  },
  // Parallax-images
  {
    test: ($) => $('[data-parallax-liquid="true"]').length > 0,
    field: 'features',
    value: 'Parallax-images'
  },
  // Скролл-анимация
  {
    test: ($) => $('[data-bottom-top]').length > 0 || $('[data-top-bottom]').length > 0,
    field: 'features',
    value: 'Скролл-анимация'
  },
  // Marquee
  {
    test: ($) => $('.marquee-slide').length > 0,
    field: 'features',
    value: 'Marquee'
  },
  // Parallax
  {
    test: ($) => $('.parallax-scrolling-').length > 0 || $('[data-parallax-background-ratio]').length > 0,
    field: 'features',
    value: 'Parallax'
  },
  // Shape-divider
  {
    test: ($) => $('.page-divider-wrapper').length > 0,
    field: 'features',
    value: 'Shape-divider'
  },
  // Sticky
  {
    test: ($) => $('.position-sticky').length > 0,
    field: 'features',
    value: 'Sticky'
  },
  // Page-loader
  {
    test: ($) => $('.page-loader').length > 0,
    field: 'features',
    value: 'Page-loader'
  },
  // Cookie
  {
    test: ($) => $('.cookie-message').length > 0,
    field: 'features',
    value: 'Cookie'
  },
  // Scroll-top
  {
    test: ($) => $('.scroll-top').length > 0,
    field: 'features',
    value: 'Scroll-top'
  },
  // Custom-cursor
  {
    test: ($) => $('.custom-cursor').length > 0,
    field: 'features',
    value: 'Custom-cursor'
  },
  // Градиент
  {
    test: ($) => $('.bg-gradient-').length > 0 || $('.border-gradient-').length > 0,
    field: 'features',
    value: 'Градиент'
  },
  // Overlay
  {
    test: ($) => $('.bg-transparent').length > 0,
    field: 'features',
    value: 'Overlay'
  },
  // Outside-box
  {
    test: ($) => $('.outside-box').length > 0,
    field: 'features',
    value: 'Outside-box'
  },
  // Absolute
  {
    test: ($) => $('.absolute-').length > 0,
    field: 'features',
    value: 'Absolute'
  }
]

export const generateTreeStructure = (html: string): string => {
  // Настраиваем cheerio для сохранения оригинальных кавычек в атрибутах
  const $ = load(html || '', {
    decodeEntities: false, // Отключаем декодирование HTML-сущностей
    xmlMode: false,       // Не используем XML режим
    _useHtmlParser2: true // Используем htmlparser2 для лучшей совместимости
  })
  let result = ''
  
  // Функция для рекурсивного обхода DOM-дерева
  const processNode = (node: cheerio.Element, level: number = 0): void => {
    // Пропускаем текстовые узлы и комментарии
    if (node.type !== 'tag') return
    
    // Отступ для текущего уровня
    const indent = ' '.repeat(level)
    
    // Получаем имя тега
    const tagName = node.name
    
    // Получаем классы элемента
    const classes = $(node).attr('class') ? 
      '.' + $(node).attr('class').split(/\s+/).join('.') : 
      ''
    
    // Добавляем строку в результат - для div показываем только классы, для остальных тегов - имя тега и классы
    if (tagName === 'div') {
      result += `${indent}${classes || '.div'}\n`
    } else {
      result += `${indent}${tagName}${classes}\n`
    }
    
    // Рекурсивно обрабатываем дочерние элементы
    $(node).children().each((_, child) => {
      processNode(child, level + 1)
    })
  }
  
  // Проверяем, является ли HTML фрагментом или полным документом
  const isFragment = !html.includes('<html') && !html.includes('<!DOCTYPE')
  
  if (isFragment) {
    // Для HTML фрагментов обрабатываем все элементы верхнего уровня
    $($.root().children()).each((_, element) => {
      processNode(element, 0)
    })
  } else {
    // Для полных HTML документов сначала пробуем найти элементы в body
    $('body > *').each((_, element) => {
      processNode(element)
    })
    
    // Если body не найден, обрабатываем корневые элементы
    if (result === '') {
      $(':root > *').each((_, element) => {
        processNode(element)
      })
    }
  }
  
  return result
}

export const analyzeHtml = (html: string): AnalyzeResult => {
  // Настраиваем cheerio для сохранения оригинальных кавычек в атрибутах
  const $ = load(html || '', {
    decodeEntities: false, // Отключаем декодирование HTML-сущностей
    xmlMode: false,       // Не используем XML режим
    _useHtmlParser2: true // Используем htmlparser2 для лучшей совместимости
  })
  const result: AnalyzeResult = {
    layout: new Set(),
    elements: new Set(),
    graphics: new Set(),
    features: new Set(),
    treeStructure: generateTreeStructure(html)
  }

  for (const rule of rules) {
    if (rule.test($)) {
      result[rule.field].add(rule.value)
    }
  }

  return result
}
