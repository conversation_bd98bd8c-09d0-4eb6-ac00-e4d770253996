<template>
  <div style="padding: 0" class="structure-panel p-0 relative">
    <!-- Кнопки управления в правом нижнем углу -->
    <div class="absolute top-0 right-0 flex gap-1 z-10">
      <Button
        v-tooltip="'Развернуть все'"
        icon="pi pi-arrow-up-right-and-arrow-down-left-from-center"
        class="p-button-sm p-button-text"
        @click="expandAll"
      />
      <Button
        v-tooltip="'Свернуть все'"
        icon="pi pi-arrow-down-left-and-arrow-up-right-to-center"
        class="p-button-sm p-button-text"
        @click="collapseAll"
      />
      <Button
        v-tooltip="'Обновить структуру'"
        icon="pi pi-refresh"
        class="p-button-sm p-button-text"
        @click="parseHtmlStructure"
      />
    </div>

    <Tree
      v-model:expanded-keys="expandedKeys"
      v-model:selection-keys="selectedKeys"
      :value="visibleNodes"
      style="padding: 0"
      class="w-full p-0 text-xs"
      selection-mode="single"
      :pt="{
        nodeToggleButton: {
          class: 'p-0 me-0',
          style: 'width: 12px; height: 22px; margin-right: -4px;',
        },
        nodeContent: { style: 'padding: 0' },
        nodeChildren: { style: 'padding-left: 2px' },
      }"
      @node-select="onNodeSelect"
    >
      <template #default="slotProps">
        <div class="flex items-center gap-0 p-0" style="padding: 0">
          <span
            style="padding: 0"
            class="p-0"
            :class="`pi ${getIcon(slotProps.node)}`"
          />
          <span class="p-0 ps-1">
            <span
              v-if="slotProps.node.tag === 'div' && slotProps.node.classes"
              class="text-primary-600"
            >
              div.<span class="text-gray-400">{{
                truncateClasses(slotProps.node.classes)
              }}</span>
            </span>
            <span v-else>
              {{ slotProps.node.label }}
            </span>
            <span v-if="slotProps.node.content" class="text-gray-500 text-xs">
              - {{ truncateContent(slotProps.node.content) }}
            </span>
          </span>
        </div>
      </template>
    </Tree>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, computed } from 'vue'
  import { DOMParser } from 'linkedom'

  interface TreeNode {
    key: string
    label: string
    tag: string
    content?: string
    classes?: string
    children?: TreeNode[]
  }

  const props = defineProps({
    html: {
      type: String,
      required: true,
    },
  })

  const emit = defineEmits(['update:html', 'node-select'])

  const nodes = ref<TreeNode[]>([])
  const selectedKeys = ref({})
  const expandedKeys = ref<Record<string, boolean>>({})

  // Фильтрованные узлы (без корневых html и div)
  const visibleNodes = computed(() => {
    if (!nodes.value.length) return []

    // Получаем корневой html узел
    const htmlNode = nodes.value[0]
    if (!htmlNode.children) return []

    // Получаем div внутри html
    const divNode = htmlNode.children[0]

    // Возвращаем детей div (или пустой массив)
    return divNode?.children || []
  })

  // Обрезаем текст до 30 символов
  const truncateContent = (text: string) => {
    if (!text) return ''
    if (text.length <= 35) return text
    return text.substring(0, 35) + '...'
  }

  // Обрезаем классы до 20 символов
  const truncateClasses = (classes: string) => {
    if (!classes) return ''
    if (classes.length <= 25) return classes
    return classes.substring(0, 25) + '...'
  }

  // Парсинг HTML и построение дерева
  const parseHtmlStructure = () => {
    try {
      if (!props.html.trim()) {
        nodes.value = []
        expandedKeys.value = {}
        return
      }

      const document = new DOMParser().parseFromString(
        `<div>${props.html}</div>`,
        'text/html',
      )

      const root = document.documentElement
      nodes.value = [buildTree(root)]

      // Автоматически раскрываем все узлы
      expandAll()
    } catch (error) {
      console.error('Ошибка парсинга HTML:', error)
      nodes.value = []
      expandedKeys.value = {}
    }
  }

  // Рекурсивное построение дерева с извлечением текста и классов
  const buildTree = (element: Element): TreeNode => {
    const node: TreeNode = {
      key: Math.random().toString(36).substring(2, 9),
      label: element.tagName.toLowerCase(),
      tag: element.tagName.toLowerCase(),
    }

    // Извлекаем текстовое содержимое (если есть)
    const textContent = element.textContent?.trim()
    if (textContent && !element.children.length) {
      node.content = textContent
    }

    // Для div элементов извлекаем классы
    if (
      element.tagName.toLowerCase() === 'div' &&
      element.hasAttribute('class')
    ) {
      node.classes = element.getAttribute('class') || ''
    }

    if (element.children.length > 0) {
      node.children = Array.from(element.children).map((child) =>
        buildTree(child as Element),
      )
    }

    return node
  }

  // Раскрытие всех узлов
  const expandAll = () => {
    const keys: Record<string, boolean> = {}

    const walk = (nodes: TreeNode[]) => {
      nodes.forEach((node) => {
        keys[node.key] = true
        if (node.children) walk(node.children)
      })
    }

    // Раскрываем видимые узлы (детей корневого div)
    walk(visibleNodes.value)
    expandedKeys.value = keys
  }

  // Сворачивание всех узлов
  const collapseAll = () => {
    expandedKeys.value = {}
  }

  // Получение иконки для узла
  const getIcon = (node: TreeNode) => {
    const icons: Record<string, string> = {
      div: 'pi pi-box',
      span: 'pi pi-tag',
      p: 'pi pi-align-left',
      a: 'pi pi-link',
      img: 'pi pi-image',
      ul: 'pi pi-list',
      ol: 'pi pi-list',
      li: 'pi pi-circle-fill',
      h1: 'pi pi-info',
      h2: 'pi pi-info',
      h3: 'pi pi-info',
      h4: 'pi pi-info',
      h5: 'pi pi-info',
      h6: 'pi pi-info',
      form: 'pi pi-table',
      input: 'pi pi-pencil',
      button: 'pi pi-stop-circle',
    }

    return icons[node.tag] || 'pi pi-code'
  }

  // Обработка выбора узла
  const onNodeSelect = (node: TreeNode) => {
    emit('node-select', node)
  }

  // Автоматический парсинг при изменении HTML
  watch(() => props.html, parseHtmlStructure, { immediate: true })

  // Экспортируем функции управления
  defineExpose({
    parseHtmlStructure,
    expandAll,
    collapseAll,
  })
</script>

<style scoped>
  .structure-panel {
    @apply h-full overflow-auto border-r border-gray-200 dark:border-gray-700;
  }

  :deep(.p-tree) {
    @apply border-none;
    font-size: 12px;
  }

  :deep(.p-treenode-content) {
    @apply p-1;
  }

  :deep(.p-treenode-content .p-tree-toggler) {
    @apply mr-1;
    width: 1rem;
    height: 1rem;
  }

  .p-tree-node-content.p-tree-node-selectable {
    padding: 0 !important;
  }
  .p-tree-node-toggle-button {
    width: 16px !important;
    height: 22px !important;
  }
  .p-tree-node-children {
    padding-left: 6px !important;
  }
</style>
