<template>
  <div
    v-if="localVisible"
    class="fixed right-0 top-0 h-screen bg-white border-l border-gray-200 shadow-lg overflow-hidden flex flex-col transition-all duration-300"
    :class="{
      'w-[30rem]': !props.collapsed,
      'w-16': props.collapsed,
    }"
  >
    <div class="flex justify-between items-center p-0 border-b">
      <h4 v-if="!props.collapsed" class="text-xs px-2 font-semibold">
        {{ props.title }}
      </h4>
      <div class="flex gap-2">
        <Button
          :label="props.collapsed ? '◀️' : '▶️'"
          class="p-button-text p-button-sm"
          @click="$emit('toggle-collapse')"
        />
        <Button label="❌" class="p-button-text p-button-sm" @click="$emit('close')" />
      </div>
    </div>

    <div v-if="!props.collapsed" class="flex-1 overflow-y-auto p-1 text-[13px]">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
  import Button from 'primevue/button'
  import { ref, watch, computed } from 'vue'

  const props = withDefaults(
    defineProps<{
      visible: boolean
      collapsed?: boolean
      title?: string
    }>(),
    {
      collapsed: false,
      title: '',
    },
  )

  const emit = defineEmits<{
    (e: 'close'): void
    (e: 'toggle-collapse'): void
    (e: 'update:visible', value: boolean): void
  }>()

  const localVisible = ref(props.visible)

  watch(
    () => props.visible,
    (newVal) => {
      localVisible.value = newVal
    },
  )

  watch(localVisible, (newVal) => {
    emit('update:visible', newVal)
  })
</script>

<style scoped>
  .transition-all {
    transition: width 0.3s ease;
  }
</style>
