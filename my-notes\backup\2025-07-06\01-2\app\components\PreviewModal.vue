<template>
    <div class="modal" v-if="show">
      <div class="modal-content">
        <button @click="close">❌ Закрыть</button>
        <iframe :srcdoc="html" class="preview-frame"></iframe>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({ show: Boolean, html: String });
  const emit = defineEmits(['close']);
  
  const close = () => emit('close');
  </script>
  
  <style scoped>
  .modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); display: flex; justify-content: center; align-items: center; }
  .modal-content { background: white; padding: 20px; width: 80%; height: 80%; }
  .preview-frame { width: 100%; height: 100%; border: none; }
  </style>
  