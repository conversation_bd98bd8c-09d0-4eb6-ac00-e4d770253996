import { defineEventHand<PERSON>, readBody, createError } from 'h3'
import puppeteer from 'puppeteer'
import fs from 'fs/promises'
import path from 'path'
import os from 'os'

interface BlockItem {
  html: string
  css: string
  js: string
  filename: string
}

interface UnifiedScreenshotRequest {
  blocks: BlockItem[]
  sourceFilename: string
}

interface ScreenshotResult {
  fileId: string
  filename: string
  success: boolean
}

interface UnifiedScreenshotResponse {
  results: ScreenshotResult[]
  successCount: number
  totalTime: number
}

export default defineEventHandler(async (event) => {
  const { blocks, sourceFilename }: UnifiedScreenshotRequest = await readBody(event)

  if (!blocks || !Array.isArray(blocks) || blocks.length === 0) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Blocks array is required and must not be empty'
    })
  }

  console.log(`📸 Создание унифицированных скриншотов для ${blocks.length} блоков из файла ${sourceFilename}...`)
  const startTime = Date.now()

  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  })

  let tempFilePath: string | null = null

  try {
    const page = await browser.newPage()
    await page.setViewport({ width: 1400, height: 800 })

    // Создаем единый HTML файл со всеми блоками
    tempFilePath = path.join(os.tmpdir(), `unified-blocks-${Date.now()}.html`)
    console.log(`📁 Создание унифицированного файла: ${tempFilePath}`)

    // Собираем общие CSS и JS из всех блоков
    const allCSS = blocks.map(block => block.css || '').filter(css => css.trim()).join('\n')
    const allJS = blocks.map(block => block.js || '').filter(js => js.trim()).join('\n')

    // Создаем простую HTML структуру без лишней изоляции
    const unifiedHTML = `<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unified Blocks - ${sourceFilename}</title>

    ${allCSS}

</head>
<body>
  ${blocks.map((block, index) => `
    <div id="block-${index + 1}">
      ${block.html || ''}
    </div>
  `).join('\n')}


    ${allJS}

</body>
</html>`

    await fs.writeFile(tempFilePath, unifiedHTML, 'utf8')

    // Настраиваем перехват запросов для лучшей загрузки ресурсов
    await page.setRequestInterception(true)
    page.on('request', (request) => {
      // Разрешаем все запросы (включая внешние ресурсы)
      request.continue()
    })

    // Загружаем унифицированную страницу с расширенными настройками
    console.log(`🌐 Загрузка унифицированной страницы с улучшенной загрузкой ресурсов...`)
    await page.goto(`file://${tempFilePath}`, {
      waitUntil: 'networkidle0',
      timeout: 60000 // Увеличиваем timeout для внешних ресурсов
    })

    // ЭКСТРЕМАЛЬНАЯ базовая задержка для полной загрузки контента
    console.log('⏱️ ЭКСТРЕМАЛЬНАЯ базовая задержка 8000ms для полной загрузки контента...')
    await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 8000)))

    // Ждем загрузки всех изображений с увеличенным timeout
    console.log('🖼️ Ожидание загрузки всех изображений с timeout 10000ms...')
    await page.evaluate(() => {
      return Promise.all(
        Array.from(document.images)
          .filter(img => !img.complete)
          .map(img => new Promise(resolve => {
            img.onload = img.onerror = resolve
            setTimeout(resolve, 10000) // Увеличиваем с 5000 до 10000
          }))
      )
    })

    // ЭКСТРЕМАЛЬНАЯ прокрутка для активации всех элементов
    console.log('📜 ЭКСТРЕМАЛЬНАЯ прокрутка для загрузки всех элементов...')
    await page.evaluate(() => {
      return new Promise((resolve) => {
        const scrollDown = () => {
          window.scrollTo(0, document.body.scrollHeight)
          setTimeout(scrollUp, 5000) // Увеличиваем с 2000 до 5000
        }

        const scrollUp = () => {
          window.scrollTo(0, 0)
          setTimeout(resolve, 5000) // Увеличиваем с 2000 до 5000
        }

        setTimeout(scrollDown, 3000) // Увеличиваем с 1000 до 3000
      })
    })

    // ЭКСТРЕМАЛЬНАЯ финальная стабилизация
    console.log('⏱️ ЭКСТРЕМАЛЬНАЯ финальная стабилизация 8000ms...')
    await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 8000)))

    // ДИАГНОСТИКА: Проверяем состояние страницы перед созданием скриншотов
    console.log('🔍 ДИАГНОСТИКА: Анализ состояния страницы...')
    const pageAnalysis = await page.evaluate(() => {
      const analysis = {
        totalImages: document.images.length,
        loadedImages: Array.from(document.images).filter(img => img.complete).length,
        failedImages: Array.from(document.images).filter(img => !img.complete).length,
        totalElements: document.querySelectorAll('*').length,
        visibleElements: Array.from(document.querySelectorAll('*')).filter(el => {
          const style = window.getComputedStyle(el)
          return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0'
        }).length,
        scriptsLoaded: document.scripts.length,
        stylesheetsLoaded: document.styleSheets.length,
        documentReadyState: document.readyState,
        windowLoaded: document.readyState === 'complete',
        bodyHeight: document.body.scrollHeight,
        viewportHeight: window.innerHeight,
        currentScrollPosition: window.pageYOffset
      }

      // Проверяем конкретные библиотеки
      analysis.libraries = {
        jquery: !!window.jQuery,
        aos: !!window.AOS,
        gsap: !!window.gsap,
        bootstrap: !!window.bootstrap || !!window.Bootstrap
      }

      // Проверяем анимации
      const animatedElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const style = window.getComputedStyle(el)
        return style.animationDuration !== '0s' || style.transitionDuration !== '0s'
      })
      analysis.animatedElements = animatedElements.length

      return analysis
    })

    console.log('📊 ДИАГНОСТИКА РЕЗУЛЬТАТЫ:')
    console.log(`   📷 Изображения: ${pageAnalysis.loadedImages}/${pageAnalysis.totalImages} загружено (${pageAnalysis.failedImages} не загружено)`)
    console.log(`   👁️ Элементы: ${pageAnalysis.visibleElements}/${pageAnalysis.totalElements} видимо (${Math.round(pageAnalysis.visibleElements/pageAnalysis.totalElements*100)}%)`)
    console.log(`   📜 Скрипты: ${pageAnalysis.scriptsLoaded}, Стили: ${pageAnalysis.stylesheetsLoaded}`)
    console.log(`   📄 Состояние документа: ${pageAnalysis.documentReadyState}, Окно загружено: ${pageAnalysis.windowLoaded}`)
    console.log(`   📏 Высота: ${pageAnalysis.bodyHeight}px, Viewport: ${pageAnalysis.viewportHeight}px, Scroll: ${pageAnalysis.currentScrollPosition}px`)
    console.log(`   🎭 Анимированные элементы: ${pageAnalysis.animatedElements}`)
    console.log(`   📚 Библиотеки:`, pageAnalysis.libraries)

    // Создаем скриншоты каждого блока на той же странице
    const results: ScreenshotResult[] = []

    for (let i = 0; i < blocks.length; i++) {
      const block = blocks[i]
      
      try {
        console.log(`📸 Создание скриншота блока ${i + 1}/${blocks.length}: ${block.filename}`)

        // Находим элемент блока на странице
        const blockElement = await page.$(`#block-${i + 1}`)

        if (!blockElement) {
          throw new Error(`Block element #block-${i + 1} not found`)
        }

        // ДИАГНОСТИКА БЛОКА: Анализируем содержимое конкретного блока
        const blockAnalysis = await page.evaluate((blockId) => {
          const blockEl = document.querySelector(`#${blockId}`)
          if (!blockEl) return null

          const analysis = {
            blockId: blockId,
            innerHTML: blockEl.innerHTML.length,
            childElements: blockEl.querySelectorAll('*').length,
            visibleChildren: Array.from(blockEl.querySelectorAll('*')).filter(el => {
              const style = window.getComputedStyle(el)
              return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0'
            }).length,
            imagesInBlock: blockEl.querySelectorAll('img').length,
            loadedImagesInBlock: Array.from(blockEl.querySelectorAll('img')).filter(img => img.complete).length,
            hasBackgroundImages: Array.from(blockEl.querySelectorAll('*')).some(el => {
              const style = window.getComputedStyle(el)
              return style.backgroundImage && style.backgroundImage !== 'none'
            }),
            computedStyles: {
              display: window.getComputedStyle(blockEl).display,
              visibility: window.getComputedStyle(blockEl).visibility,
              opacity: window.getComputedStyle(blockEl).opacity,
              height: window.getComputedStyle(blockEl).height,
              width: window.getComputedStyle(blockEl).width
            }
          }

          return analysis
        }, `block-${i + 1}`)

        console.log(`🔍 ДИАГНОСТИКА БЛОКА ${i + 1}:`)
        if (blockAnalysis) {
          console.log(`   📝 HTML: ${blockAnalysis.innerHTML} символов`)
          console.log(`   🧩 Элементы: ${blockAnalysis.visibleChildren}/${blockAnalysis.childElements} видимо (${Math.round(blockAnalysis.visibleChildren/blockAnalysis.childElements*100)}%)`)
          console.log(`   🖼️ Изображения: ${blockAnalysis.loadedImagesInBlock}/${blockAnalysis.imagesInBlock} загружено`)
          console.log(`   🎨 Фоновые изображения: ${blockAnalysis.hasBackgroundImages ? 'Да' : 'Нет'}`)
          console.log(`   📐 Стили: display=${blockAnalysis.computedStyles.display}, visibility=${blockAnalysis.computedStyles.visibility}, opacity=${blockAnalysis.computedStyles.opacity}`)
          console.log(`   📏 Размеры: ${blockAnalysis.computedStyles.width} x ${blockAnalysis.computedStyles.height}`)
        }

        // Проверяем размеры элемента
        const boundingBox = await blockElement.boundingBox()

        if (!boundingBox || boundingBox.width <= 0 || boundingBox.height <= 0) {
          console.log(`⚠️ Блок ${i + 1} имеет нулевые размеры, пропускаем`)
          results.push({
            fileId: '',
            filename: block.filename,
            success: false
          })
          continue
        }

        console.log(`📏 Блок ${i + 1}: реальные размеры ${boundingBox.width}x${boundingBox.height}px`)

        // КЛЮЧЕВОЕ РЕШЕНИЕ: Прокручиваем К ЭТОМУ КОНКРЕТНОМУ БЛОКУ
        console.log(`📜 Прокрутка К блоку ${i + 1} для активации его анимаций...`)
        await page.evaluate((blockId) => {
          const blockElement = document.querySelector(`#${blockId}`)
          if (blockElement) {
            // Прокручиваем так, чтобы блок был в центре экрана
            blockElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'center'
            })
          }
        }, `block-${i + 1}`)

        // Ждем завершения прокрутки и активации анимаций ЭТОГО блока
        console.log(`⏱️ Ожидание активации анимаций блока ${i + 1} (3000ms)...`)
        await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))

        // Дополнительная проверка: ждем анимации конкретно в этом блоке
        console.log(`🎭 Проверка анимаций в блоке ${i + 1}...`)
        await page.evaluate((blockId) => {
          return new Promise((resolve) => {
            const blockElement = document.querySelector(`#${blockId}`)
            if (!blockElement) {
              resolve()
              return
            }

            // Проверяем анимации в блоке
            const animatedElements = Array.from(blockElement.querySelectorAll('*')).filter(el => {
              const style = window.getComputedStyle(el)
              return style.animationDuration !== '0s' || style.transitionDuration !== '0s'
            })

            if (animatedElements.length === 0) {
              // Нет анимаций - можем продолжать
              setTimeout(resolve, 500)
            } else {
              // Есть анимации - ждем их завершения
              const maxDuration = Math.max(...animatedElements.map(el => {
                const style = window.getComputedStyle(el)
                const animDuration = parseFloat(style.animationDuration) * 1000
                const transDuration = parseFloat(style.transitionDuration) * 1000
                return Math.max(animDuration, transDuration)
              }))

              const waitTime = Math.min(maxDuration + 1000, 5000) // Максимум 5 секунд
              setTimeout(resolve, waitTime)
            }
          })
        }, `block-${i + 1}`)

        // Финальная стабилизация для этого блока
        console.log(`⏱️ Финальная стабилизация блока ${i + 1} (1000ms)...`)
        await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 1000)))

        // Создаем скриншот блока
        console.log(`📸 Создание скриншота блока ${i + 1}...`)
        const screenshot = await blockElement.screenshot({
          type: 'jpeg',
          quality: 90,
          omitBackground: false
        })

        console.log(`📸 Блок ${i + 1}: скриншот создан, размер ${screenshot.length} байт`)

        // Загружаем в Directus
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
        const finalFilename = `${block.filename}_${timestamp}.jpg`
        
        const fileId = await uploadToDirectus(screenshot, finalFilename)
        
        results.push({
          fileId,
          filename: finalFilename,
          success: true
        })

        console.log(`✅ Блок ${i + 1} загружен в Directus: ${fileId}`)

      } catch (error) {
        console.error(`❌ Ошибка создания скриншота блока ${i + 1}:`, error)
        results.push({
          fileId: '',
          filename: block.filename,
          success: false
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const totalTime = Date.now() - startTime

    console.log(`✅ Унифицированные скриншоты завершены: ${successCount}/${blocks.length} успешно за ${totalTime}ms`)

    return {
      results,
      successCount,
      totalTime
    } as UnifiedScreenshotResponse

  } catch (error) {
    console.error('❌ Ошибка создания унифицированных скриншотов:', error)
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to create unified screenshots'
    })
  } finally {
    // Очистка
    if (tempFilePath) {
      try {
        await fs.unlink(tempFilePath)
        console.log(`🗑️ Временный файл удален: ${tempFilePath}`)
      } catch (cleanupError) {
        console.error('⚠️ Ошибка удаления временного файла:', cleanupError)
      }
    }
    
    await browser.close()
  }
})

// Функция загрузки в Directus
async function uploadToDirectus(screenshot: Buffer, filename: string): Promise<string> {
  const formData = new FormData()
  formData.append('title', filename)

  const blob = new Blob([screenshot], { type: 'image/jpeg' })
  formData.append('file', blob, filename)

  const response = await fetch('http://localhost:8055/files', {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error(`Failed to upload to Directus: ${response.statusText}`)
  }

  const data = await response.json()
  return data.data.id
}
