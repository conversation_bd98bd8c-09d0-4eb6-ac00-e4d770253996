<template>
  <div class="html-converter-page p-4">
    <div class="mb-4">
      <h4 class="text-3xl font-bold">Конвертер HTML в шаблоны</h4>
      
    </div>
    
    <Divider />
    
    <HtmlTemplateConverter />
    
    <Divider class="mt-8" />
    
    <div class="mt-4">
      <h2 class="text-xl font-semibold mb-2">Как использовать</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>Вставьте HTML код в левое поле ввода</li>
        <li>Выберите формат шаблона (Handlebars или Pug)</li>
        <li>Нажмите кнопку "Конвертировать"</li>
        <li>Получите готовый шаблон и JSON данные на вкладках справа</li>
      </ol>
      
      <h2 class="text-xl font-semibold mt-6 mb-2">Примечания</h2>
      <ul class="list-disc pl-6 space-y-2">
        <li>Конвертер автоматически извлекает текст, URL, пути к изображениям и другие данные в JSON</li>
        <li>Поддерживается обработка фоновых изображений в стилях</li>
        <li>Для сложных HTML структур может потребоваться ручная доработка шаблонов</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import Divider from 'primevue/divider';
import HtmlTemplateConverter from '../components/HtmlTemplateConverter.vue';
import { useToast } from 'primevue/usetoast';

const toast = useToast();

onMounted(() => {
  toast.add({
    severity: 'info',
    summary: 'Конвертер готов к использованию',
    detail: 'Вставьте HTML код и нажмите кнопку "Конвертировать"',
    life: 3000
  });
});
</script>

<style scoped>
.html-converter-page {
  max-width: 1200px;
  margin: 0 auto;
}
</style>