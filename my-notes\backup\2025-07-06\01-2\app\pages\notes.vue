<template>
    <div class="card">
      <Toast />
      
  
      <div class="flex gap-4">
        <div class="w-1/4">
          <DataTable
            v-model:selection="selectedFile"
            :value="files"
            :loading="loading"
            :paginator="true"
            :rows="30"
            selection-mode="single"
            data-key="path"
            :global-filter-fields="['name', 'path']"
            :sort-field="sortField"
          :sort-order="sortOrder"
            table-style="w-full"
            striped-rows
            class="p-datatable-sm text-[13px]"
          >
          <template #header>
            <div class="flex justify-content-end">
              <span class="p-input-icon-left">
                <i class="pi pi-search" />
                <InputText v-model="globalFilter" placeholder="Поиск..." />
              </span>
            </div>
          </template>
          <Column field="yamlData.number" header="№" :sortable="true"/>
          <Column field="name" header="Название файла" :sortable="true">
            <template #body="{ data }">
              {{ data.name }}
            </template>
          </Column>
  
          <Column header="Действия" style="width: 12rem">
            <template #body="{ data }">
              <Button
                icon="pi pi-eye"
                rounded
                text
                severity="secondary"
                class="mr-2"
                tooltip="Просмотр"
                @click="handleView(data)"
              >
                <i class="pi pi-eye hidden-icon"/> 
              </Button>
              <Button                
                icon="pi pi-pencil"
                rounded
                text
                severity="success"
                class="mr-2"
                tooltip="Редактировать"
                @click="handleEdit(data)"
              >
                <i class="pi pi-pencil hidden-icon"/> 
              </Button>
            </template>
          </Column>

        </DataTable>
    </div>

<div class="w-3/4">
  <div v-if="selectedFile" class="card">
    <div class="flex justify-content-between align-items-center mb-3">
      <div class="flex align-items-center gap-2">
        <span class="font-bold text-xl">{{ selectedFile.name }}</span>
        <Tag
          :value="dialogMode === 'view' ? 'Просмотр' : 'Редактирование'"
          :severity="dialogMode === 'view' ? 'info' : 'warning'"
        />
      </div>
      <div class="flex gap-2">
        <Button
          :label="dialogMode === 'view' ? 'Редактировать' : 'Просмотр'"
          :icon="dialogMode === 'view' ? 'pi pi-pencil' : 'pi pi-eye'"
          :severity="dialogMode === 'view' ? 'success' : 'info'"
          @click="dialogMode = dialogMode === 'view' ? 'edit' : 'view'"
        />
        <Button
          v-if="dialogMode === 'edit'"
          label="Сохранить"
          icon="pi pi-check"
          severity="success"
          @click="handleSave"
        />
      </div>
    </div>

    <div v-if="fileContent" class="p-3 bg-gray-50 h-[70vh] overflow-auto">
        
        <div v-if="dialogMode === 'view'" class="markdown-body">
    <div v-html="renderedMarkdown"/>
  </div>

  <prism-editor 
    v-else 
    v-model="fileContent" 
    class="h-[70vh] text-sm" 
    :highlight="highlightMarkdown" 
    language="markdown" 
    line-numbers
  />
</div>
 
  </div>
  <div v-else class="flex justify-content-center align-items-center h-[70vh] text-500">
    Выберите файл для просмотра
  </div>
</div>
</div>
</div>
</template>
  
  <script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import { PrismEditor } from 'vue-prism-editor'
  import 'vue-prism-editor/dist/prismeditor.min.css'
  import 'prismjs/themes/prism-tomorrow.css'
  import { highlight, languages } from 'prismjs/components/prism-core'
  import Prism from 'prismjs';
import 'prismjs/components/prism-markdown';
import 'github-markdown-css/github-markdown-light.css'

  import { marked } from 'marked'
  
  const loading = ref(false)
  const files = ref([])
  const sortField = ref('yamlData.number')
const sortOrder = ref(1)
  const dialogMode = ref('view')
  const selectedFile = ref(null)
  const fileContent = ref('')
  const globalFilter = ref('')
  const yamlData = ref({})
  const isHovered = ref(false)
  const highlightMarkdown = (code) => {
  try {
    return Prism.highlight(code, Prism.languages.markdown, 'markdown');
  } catch (error) {
    console.error('Error highlighting markdown:', error);
    return code; 
  }
};



  
  const renderedMarkdown = computed(() => {
    marked.setOptions({
  highlight: function(code, lang) {
    if (lang && Prism.languages[lang]) {
      return Prism.highlight(code, Prism.languages[lang], lang);
    } else {
      return code; // Возвращаем код как есть, если язык не поддерживается
    }
  }
});

    return marked(fileContent.value)
  })
  
  const toast = useToast()
  
  const loadFiles = async () => {
    loading.value = true
    try {
      const response = await $fetch('/api/notes')
      files.value = Array.isArray(response) ? response.map(file => ({
        ...file,
        size: Number(file.size) || 0,
        modified: new Date(file.modified).toISOString()
      })) : []
      if (files.value.length === 0) {
        toast.add({ severity: 'info', summary: 'Информация', detail: 'Нет доступных заметок', life: 3000 })
      }
    } catch (error) {
      console.error('Error loading files:', error)
      toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось загрузить заметки', life: 3000 })
      files.value = []
    } finally {
      loading.value = false
    }
  }
  
  const handleView = async (item) => {
    dialogMode.value = 'view'
    selectedFile.value = item
    try {
      const response = await $fetch(`/api/notes/${item.path}`)
      fileContent.value = response.content
      yamlData.value = response.yamlData || {}
    } catch (error) {
      console.error('Error loading file content:', error)
      toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось загрузить содержимое файла', life: 3000 })
    }
  }
  
  const handleEdit = async (item) => {
    dialogMode.value = 'edit'
    selectedFile.value = item
    try {
      const response = await $fetch(`/api/notes/${item.path}`)
      fileContent.value = response.content
    } catch (error) {
      console.error('Error loading file content:', error)
      toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось загрузить содержимое файла', life: 3000 })
    }
  }
  
  const handleSave = async () => {
    if (!selectedFile.value) return
  
    try {
      await $fetch(`/api/notes/${selectedFile.value.path}`, {
        method: 'PUT',
        body: { content: fileContent.value },
      })
      
      toast.add({ severity: 'success', summary: 'Успех', detail: 'Файл успешно сохранен', life: 3000 })
    } catch (error) {
      console.error('Error saving file:', error)
      toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось сохранить файл', life: 3000 })
    }
  }
  
  const onBeforeUpdate = () => {
  // Здесь можно добавить логику, которая должна выполниться перед обновлением компонента
  // Например, обновление подсветки синтаксиса
  Prism.highlightAll();
}

  onMounted(() => {
    loadFiles()
  })

  const renderYaml = (yamlString) => {
  // Экранируем HTML спецсимволы
  const escapedYaml = yamlString.replace(/&/g, '&amp;')
                                .replace(/</g, '&lt;')
                                .replace(/>/g, '&gt;')
                                .replace(/"/g, '&quot;')
                                .replace(/'/g, '&#039;');

  // Оборачиваем в теги <pre> и <code> для форматирования
  return `<pre><code>${escapedYaml}</code></pre>`;
}
  </script>
  
  <style scoped>
  .prism-editor {
    background: #f3f3f3;
    color: #666;
    font-family: Fira code, Fira Mono, Consolas, Menlo, Courier, monospace;
    font-size: 12px;
    line-height: 1.5;
    padding: 5px;
  }
  
  .dark .prism-editor {
    background: #2d2d2d;
    color: #ccc;
  }
  
  /* optional class for removing the outline */
  .prism-editor__textarea:focus {
    outline: none;
  }
  
  /* Стили для режима просмотра Markdown */
  .prose {
    color: inherit;
  }
  
  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: inherit;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
  }
  
  .prose p {
    margin: 1em 0;
  }
  
  .prose code {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 0.9em;
  }
  
  .prose pre {
    background: #2d2d2d;
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
  }
  
  .prose pre code {
    background: none;
    padding: 0;
    font-size: 0.9em;
    color: #ccc;
  }
  
  .dark .prose {
    color: #ccc;
  }
  
  .dark .prose code {
    background: rgba(255, 255, 255, 0.1);
  }
/* Скрываем иконку по умолчанию */
.hidden-icon {
  visibility: hidden; 
}

/* Отображаем иконку при наведении на родительскую кнопку */
.p-button:hover .hidden-icon { 
  visibility: visible; 
}
  
.p-datatable .p-datatable-tbody > tr {
  height: 30px; /*  Настрой высоту строки по своему усмотрению */
}

.p-datatable .p-datatable-tbody > tr > td {
  font-size: 14px; /*  Настрой размер шрифта по своему усмотрению */
}

  </style>
  
  