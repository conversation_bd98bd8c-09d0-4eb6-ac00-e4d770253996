import { htmlToHandlebarsAndJson } from './utils/htmlToTemplate.js';

// Тест с data-атрибутами
const html = `<div data-anime='{"duration":1000}' data-slider-options='{"loop":true}' class="test">
  <span>Текст для замены</span>
</div>`;

// Второй тест с переменными Handlebars в data-атрибутах
const html2 = `<div data-anime='{"duration":{{duration}}}' data-value="{{value}}" class="test">
  <span>{{text}}</span>
</div>`;

// Упрощенный третий тест для проверки data-атрибутов с экранированными кавычками
const html3 = `<div data-anime="simple" data-test="value" class="test">
  <p>Простой текст</p>
</div>`;

// Четвертый тест с экранированными кавычками в JSON
const html4 = `<div data-options="{ &quot;translateY&quot;: [0, 100], &quot;opacity&quot;: [1, 0] }" class="animated">
  <p>Текст с экранированными кавычками</p>
  <a href="test.html">Ссылка</a>
</div>`;

console.log('=== Тест 1: HTML с data-атрибутами ===');
const result = htmlToHandlebarsAndJson(html);
console.log('JSON данные:', JSON.stringify(result.jsonData, null, 2));
console.log('HBS шаблон:', result.hbsTemplate);

console.log('\n=== Тест 2: HTML с переменными Handlebars в data-атрибутах ===');
const result2 = htmlToHandlebarsAndJson(html2);
console.log('JSON данные:', JSON.stringify(result2.jsonData, null, 2));
console.log('HBS шаблон:', result2.hbsTemplate);

console.log('\n=== Тест 3: Упрощенный тест с data-атрибутами ===');
const result3 = htmlToHandlebarsAndJson(html3);
console.log('JSON данные:', JSON.stringify(result3.jsonData, null, 2));
console.log('HBS шаблон:', result3.hbsTemplate);

console.log('\n=== Тест 4: HTML с экранированными кавычками в JSON ===');
const result4 = htmlToHandlebarsAndJson(html4);
console.log('JSON данные:', JSON.stringify(result4.jsonData, null, 2));
console.log('HBS шаблон:', result4.hbsTemplate); 