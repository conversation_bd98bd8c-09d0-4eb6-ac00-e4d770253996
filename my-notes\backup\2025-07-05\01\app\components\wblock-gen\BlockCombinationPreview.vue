<template>
  <div class="block-combination-preview">
    <h4 class="text-md font-semibold mb-3">Превью комбинаций блоков</h4>

    <!-- Упрощенная версия для тестирования -->
    <div class="text-center py-8 text-gray-500">
      <i class="pi pi-objects-column text-4xl mb-4 block"></i>
      <p>Режим комбинаций блоков</p>
      <p class="text-sm">Выбрано блоков: {{ selectedBlocks.length }}</p>
      <div v-if="selectedBlocks.length > 0" class="mt-4">
        <div class="text-sm font-medium mb-2">Выбранные блоки:</div>
        <div class="flex flex-wrap gap-2 justify-center">
          <Badge
            v-for="block in selectedBlocks"
            :key="block.id"
            :value="block.title || block.number"
            severity="info"
            class="text-xs"
          />
        </div>
      </div>
      <div class="mt-4">
        <Button
          label="Функционал в разработке"
          icon="pi pi-cog"
          class="text-xs p-button-outlined"
          disabled
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// PrimeVue компоненты
import Button from 'primevue/button'
import Badge from 'primevue/badge'

// Интерфейсы
interface WBlock {
  id?: string
  number: string
  title: string
  block_type?: string[]
  collection?: string[]
  html?: string
  hbs?: string
  css?: string
  js?: string
}

// Props и emits
const props = defineProps<{
  selectedBlocks: WBlock[]
  collectionOptions: string[]
}>()

const emit = defineEmits<{
  'saved': [data: { savedCount: number }]
}>()

// Заглушка для тестирования
console.log('BlockCombinationPreview загружен, блоков:', props.selectedBlocks.length)
</script>

<style scoped>
.combination-card {
  transition: all 0.2s ease;
}

.combination-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.preview-container {
  transition: all 0.2s ease;
}

.preview-container:hover {
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}
</style>
