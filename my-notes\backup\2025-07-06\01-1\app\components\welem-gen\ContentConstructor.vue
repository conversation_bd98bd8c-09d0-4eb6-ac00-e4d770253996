<template>
  <div class="content-intelligent-selector">
    <!-- Заголовок с режимами -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-3">
        <h4 class="text-lg font-semibold">Интеллектуальный контент</h4>
        <div class="flex bg-gray-100 rounded-lg p-1">
          <Button
            @click="currentMode = 'select'"
            :severity="currentMode === 'select' ? 'primary' : 'secondary'"
            :outlined="currentMode !== 'select'"
            size="small"
            class="text-xs"
          >
            <i class="pi pi-list mr-1"></i>
            Выбор
          </Button>
          <Button
            @click="currentMode = 'create'"
            :severity="currentMode === 'create' ? 'primary' : 'secondary'"
            :outlined="currentMode !== 'create'"
            size="small"
            class="text-xs"
          >
            <i class="pi pi-plus mr-1"></i>
            Создание
          </Button>
        </div>
      </div>

      <div class="flex gap-2">
        <Badge
          v-if="uniqueVariables.length > 0"
          :value="`${uniqueVariables.length} переменных`"
          severity="info"
        />
        <Button
          @click="applyIntelligentContent"
          :disabled="!canApply"
          size="small"
          severity="success"
        >
          <i class="pi pi-magic-wand mr-1"></i>
          Применить ({{ selectedContentSets.length }})
        </Button>
      </div>
    </div>

    <!-- Режим выбора -->
    <div v-if="currentMode === 'select'" class="space-y-4">

      <!-- Анализ переменных -->
      <div v-if="uniqueVariables.length > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium text-blue-800">Найденные переменные:</span>
          <Badge :value="totalVariables" severity="info" />
        </div>
        <div class="flex flex-wrap gap-1">
          <Badge
            v-for="variable in uniqueVariables"
            :key="variable"
            :value="variable"
            :severity="getVariableTypeSeverity(variable)"
            class="text-xs"
          />
        </div>
      </div>

      <!-- Верхняя панель: Конструктор и библиотека -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">

        <!-- Левая панель: Конструктор переменных -->
        <div>
          <h5 class="text-sm font-medium mb-3">Быстрый конструктор</h5>

          <div v-if="uniqueVariables.length > 0" class="space-y-2">
            <div
              v-for="variable in uniqueVariables"
              :key="variable"
              class="flex items-center gap-2"
            >
              <span class="font-mono text-blue-600 text-xs w-20 flex-shrink-0">{{ variable }}:</span>
              <InputText
                v-model="quickVariables[variable]"
                :placeholder="getVariablePlaceholder(variable)"
                class="text-xs flex-1"
                size="small"
                @input="updateCharCount(variable)"
              />
              <span class="text-xs text-gray-500 w-8 text-right">{{ getCharCount(quickVariables[variable]) }}</span>
            </div>

            <div class="flex gap-2 mt-3">
              <Button
                @click="applyQuickVariables"
                size="small"
                severity="success"
                :disabled="Object.keys(quickVariables).filter(k => quickVariables[k]).length === 0"
                class="flex-1"
              >
                <i class="pi pi-check mr-1"></i>
                Применить
              </Button>
              <Button
                @click="clearQuickVariables"
                size="small"
                severity="secondary"
                outlined
              >
                <i class="pi pi-times"></i>
              </Button>
            </div>
          </div>

          <div v-else class="text-center text-gray-500 py-4 text-sm">
            Выберите элементы с HBS шаблонами для анализа переменных
          </div>
        </div>

        <!-- Правая панель: Библиотека контента -->
        <div>
          <div class="flex items-center justify-between mb-3">
            <h5 class="text-sm font-medium">Библиотека контента</h5>
            <Button
              @click="loadContentLibrary"
              size="small"
              text
              severity="secondary"
              :loading="analyzing"
            >
              <i class="pi pi-refresh"></i>
            </Button>
          </div>

          <!-- Фильтры -->
          <div class="mb-3">
            <MultiSelect
              v-model="selectedContentTypes"
              :options="contentTypes"
              optionLabel="label"
              optionValue="value"
              placeholder="Фильтр по типам"
              class="w-full text-xs"
              :maxSelectedLabels="2"
              display="chip"
            />
          </div>

          <div class="mb-3">
            <InputText
              v-model="contentSearchQuery"
              placeholder="Поиск по названию, описанию..."
              class="w-full text-xs"
              size="small"
            />
          </div>

          <!-- Наборы контента -->
          <div class="space-y-2 max-h-64 overflow-y-auto">
            <div
              v-for="contentSet in filteredContentSets"
              :key="contentSet.id"
              @click="toggleContentSet(contentSet)"
              class="p-2 border rounded cursor-pointer transition-all hover:shadow-sm"
              :class="{
                'border-blue-500 bg-blue-50': selectedContentSets.some(s => s.id === contentSet.id),
                'border-gray-200 hover:border-gray-300': !selectedContentSets.some(s => s.id === contentSet.id)
              }"
            >
              <div class="flex items-center justify-between mb-1">
                <span class="font-medium text-xs">{{ contentSet.title }}</span>
                <div class="flex items-center gap-1">
                  <Badge :value="contentSet.type" :severity="getContentTypeSeverity(contentSet.type)" class="text-xs" />
                  <Badge :value="`${contentSet.compatibility}%`"
                    :severity="contentSet.compatibility > 70 ? 'success' : contentSet.compatibility > 40 ? 'warning' : 'secondary'"
                    class="text-xs"
                  />
                  <div
                    v-if="selectedContentSets.some(s => s.id === contentSet.id)"
                    class="w-3 h-3 bg-blue-500 text-white rounded-full flex items-center justify-center"
                  >
                    <i class="pi pi-check text-xs"></i>
                  </div>
                </div>
              </div>

              <div class="text-xs text-gray-600 mb-1">{{ contentSet.description }}</div>

              <!-- Превью переменных -->
              <div class="text-xs bg-gray-50 p-1 rounded">
                <div v-if="contentSet.preview" class="flex flex-wrap gap-1">
                  <span v-for="(value, key) in contentSet.preview" :key="key" class="bg-white px-1 rounded">
                    <span class="font-mono text-blue-600">{{ key }}:</span>
                    <span class="text-gray-700">{{ String(value).substring(0, 15) }}{{ String(value).length > 15 ? '...' : '' }}</span>
                    <span class="text-gray-400">({{ String(value).length }})</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Полноширинное превью -->
      <div v-if="props.selectedElements.length > 0 && (selectedContentSets.length > 0 || Object.keys(quickVariables).filter(k => quickVariables[k]).length > 0)">
        <div class="border rounded-lg overflow-hidden">
          <div class="bg-gray-50 px-4 py-3 border-b flex items-center justify-between">
            <h5 class="text-sm font-medium">Превью результата</h5>
            <div class="flex items-center gap-2">
              <Badge
                v-if="selectedContentSets.length > 0"
                :value="`${selectedContentSets.length} наборов`"
                severity="success"
              />
              <Badge
                v-if="Object.keys(quickVariables).filter(k => quickVariables[k]).length > 0"
                :value="`${Object.keys(quickVariables).filter(k => quickVariables[k]).length} переменных`"
                severity="info"
              />
            </div>
          </div>

          <!-- Превью элементов -->
          <div
            v-for="element in props.selectedElements.slice(0, 2)"
            :key="element.id"
            class="border-b last:border-b-0"
          >
            <div class="bg-gray-100 px-4 py-2 text-sm font-medium">
              {{ element.title }}
            </div>

            <!-- Полноширинное сравнение -->
            <div class="grid grid-cols-2 gap-0 min-h-96">
              <div class="border-r">
                <div class="text-xs text-gray-600 p-3 bg-gray-50 border-b font-medium">Оригинал:</div>
                <div class="p-4">
                  <iframe
                    :srcdoc="getElementPreviewHtml(element, false)"
                    class="w-full h-80 border rounded"
                    frameborder="0"
                    sandbox="allow-same-origin"
                  />
                </div>
              </div>

              <div>
                <div class="text-xs text-gray-600 p-3 bg-blue-50 border-b font-medium">С контентом:</div>
                <div class="p-4">
                  <iframe
                    :srcdoc="getElementPreviewHtml(element, true)"
                    class="w-full h-80 border rounded border-blue-200"
                    frameborder="0"
                    sandbox="allow-same-origin"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Статистика применения -->
          <div class="p-4 bg-green-50 border-t">
            <div class="text-sm font-medium text-green-800 mb-1">Готово к применению:</div>
            <div class="text-xs text-green-700">
              {{ props.selectedElements.length }} элементов × {{ Math.max(selectedContentSets.length, 1) }} наборов =
              <strong>{{ props.selectedElements.length * Math.max(selectedContentSets.length, 1) }} вариантов</strong>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="text-center text-gray-500 py-12 text-sm border rounded-lg">
        <i class="pi pi-eye text-4xl mb-3 block text-gray-300"></i>
        <div class="text-lg font-medium mb-2">Превью контента</div>
        <div>Выберите элементы и наборы контента для превью</div>
      </div>
    </div>

    <!-- Режим создания -->
    <div v-if="currentMode === 'create'" class="space-y-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

        <!-- Левая панель: Создание набора -->
        <div>
          <h5 class="text-sm font-medium mb-4">Создание нового набора контента</h5>

          <div class="space-y-4">
            <!-- Основная информация -->
            <div class="grid grid-cols-2 gap-3">
              <div>
                <label class="block text-sm font-medium mb-1">Название набора</label>
                <InputText
                  v-model="newContentSet.title"
                  placeholder="Например: Первый экран - IT компания"
                  class="w-full"
                />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">Тип блока</label>
                <Dropdown
                  v-model="newContentSet.type"
                  :options="contentTypes"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Выберите тип"
                  class="w-full"
                />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">Описание</label>
              <InputText
                v-model="newContentSet.description"
                placeholder="Краткое описание набора"
                class="w-full"
              />
            </div>

            <!-- Переменные -->
            <div>
              <div class="flex items-center justify-between mb-2">
                <label class="block text-sm font-medium">Переменные</label>
                <Button
                  @click="addNewVariable"
                  size="small"
                  text
                  severity="secondary"
                >
                  <i class="pi pi-plus mr-1"></i>
                  Добавить переменную
                </Button>
              </div>

              <div class="space-y-2 max-h-64 overflow-y-auto">
                <div
                  v-for="(variable, index) in newContentSet.variables"
                  :key="index"
                  class="flex items-center gap-2 p-2 border rounded"
                >
                  <InputText
                    v-model="variable.name"
                    placeholder="Имя переменной"
                    class="w-32 text-xs"
                    size="small"
                  />
                  <InputText
                    v-model="variable.value"
                    :placeholder="getVariablePlaceholder(variable.name)"
                    class="flex-1 text-xs"
                    size="small"
                  />
                  <span class="text-xs text-gray-500 w-8 text-right">{{ getCharCount(variable.value) }}</span>
                  <Button
                    @click="removeVariable(index)"
                    size="small"
                    text
                    severity="danger"
                  >
                    <i class="pi pi-times"></i>
                  </Button>
                </div>
              </div>
            </div>

            <!-- Предустановленные переменные из элементов -->
            <div v-if="uniqueVariables.length > 0">
              <label class="block text-sm font-medium mb-2">Заполнить из найденных переменных</label>
              <div class="flex flex-wrap gap-1">
                <Button
                  v-for="variable in uniqueVariables"
                  :key="variable"
                  @click="addVariableFromElement(variable)"
                  size="small"
                  text
                  severity="info"
                  class="text-xs"
                >
                  <i class="pi pi-plus mr-1"></i>
                  {{ variable }}
                </Button>
              </div>
            </div>

            <!-- Кнопки действий -->
            <div class="flex gap-2 pt-4">
              <Button
                @click="saveNewContentSet"
                severity="success"
                :disabled="!newContentSet.title || newContentSet.variables.length === 0"
                class="flex-1"
              >
                <i class="pi pi-save mr-2"></i>
                Сохранить набор
              </Button>
              <Button
                @click="clearNewContentSet"
                severity="secondary"
                outlined
              >
                <i class="pi pi-times mr-2"></i>
                Очистить
              </Button>
            </div>
          </div>
        </div>

        <!-- Правая панель: Превью создаваемого набора -->
        <div>
          <h5 class="text-sm font-medium mb-4">Превью набора</h5>

          <div v-if="newContentSet.title && newContentSet.variables.length > 0" class="border rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <span class="font-medium text-sm">{{ newContentSet.title }}</span>
              <Badge :value="newContentSet.type || 'Не выбран'" :severity="getContentTypeSeverity(newContentSet.type)" class="text-xs" />
            </div>

            <div class="text-xs text-gray-600 mb-3">{{ newContentSet.description || 'Без описания' }}</div>

            <!-- Превью переменных -->
            <div class="space-y-2">
              <div class="text-xs font-medium text-gray-700 mb-2">Переменные ({{ newContentSet.variables.length }}):</div>
              <div class="space-y-1">
                <div
                  v-for="variable in newContentSet.variables.filter(v => v.name && v.value)"
                  :key="variable.name"
                  class="flex items-center justify-between p-2 bg-gray-50 rounded text-xs"
                >
                  <span class="font-mono text-blue-600">{{ variable.name }}:</span>
                  <span class="text-gray-700 flex-1 mx-2 truncate">{{ variable.value }}</span>
                  <span class="text-gray-400">({{ getCharCount(variable.value) }})</span>
                </div>
              </div>
            </div>

            <!-- Тестовое применение -->
            <div v-if="props.selectedElements.length > 0" class="mt-4 pt-4 border-t">
              <div class="text-xs font-medium text-gray-700 mb-2">Тест на элементе:</div>
              <div class="bg-blue-50 p-2 rounded text-xs">
                <iframe
                  :srcdoc="getTestPreviewHtml()"
                  class="w-full h-24 border rounded border-blue-200"
                  frameborder="0"
                  sandbox="allow-same-origin"
                />
              </div>
            </div>
          </div>

          <div v-else class="text-center text-gray-500 py-12 text-sm border rounded-lg">
            <i class="pi pi-file-edit text-3xl mb-3 block text-gray-300"></i>
            <div>Заполните название и добавьте</div>
            <div>переменные для превью</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Диалог создания нового набора контента -->
    <Dialog
      v-model:visible="showAddContentDialog"
      modal
      header="Создать новый набор контента"
      :style="{ width: '600px' }"
    >
      <div class="space-y-4">
        <!-- Основная информация -->
        <div class="grid grid-cols-2 gap-3">
          <div>
            <label class="block text-sm font-medium mb-1">Название набора</label>
            <InputText
              v-model="newContentSet.title"
              placeholder="Например: Первый экран - IT компания"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-sm font-medium mb-1">Тип блока</label>
            <Dropdown
              v-model="newContentSet.type"
              :options="contentTypes"
              optionLabel="label"
              optionValue="value"
              placeholder="Выберите тип"
              class="w-full"
            />
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium mb-1">Описание</label>
          <InputText
            v-model="newContentSet.description"
            placeholder="Краткое описание набора"
            class="w-full"
          />
        </div>

        <!-- Переменные -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <label class="block text-sm font-medium">Переменные</label>
            <Button
              @click="addNewVariable"
              size="small"
              text
              severity="secondary"
            >
              <i class="pi pi-plus mr-1"></i>
              Добавить переменную
            </Button>
          </div>

          <div class="space-y-2 max-h-48 overflow-y-auto">
            <div
              v-for="(variable, index) in newContentSet.variables"
              :key="index"
              class="flex items-center gap-2 p-2 border rounded"
            >
              <InputText
                v-model="variable.name"
                placeholder="Имя переменной"
                class="w-32 text-xs"
                size="small"
              />
              <InputText
                v-model="variable.value"
                :placeholder="getVariablePlaceholder(variable.name)"
                class="flex-1 text-xs"
                size="small"
              />
              <Button
                @click="removeVariable(index)"
                size="small"
                text
                severity="danger"
              >
                <i class="pi pi-times"></i>
              </Button>
            </div>
          </div>
        </div>

        <!-- Предустановленные переменные из элементов -->
        <div v-if="uniqueVariables.length > 0">
          <label class="block text-sm font-medium mb-2">Заполнить из найденных переменных</label>
          <div class="flex flex-wrap gap-1">
            <Button
              v-for="variable in uniqueVariables"
              :key="variable"
              @click="addVariableFromElement(variable)"
              size="small"
              text
              severity="info"
              class="text-xs"
            >
              <i class="pi pi-plus mr-1"></i>
              {{ variable }}
            </Button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex gap-2">
          <Button
            @click="showAddContentDialog = false"
            severity="secondary"
            outlined
          >
            Отмена
          </Button>
          <Button
            @click="saveNewContentSet"
            severity="success"
            :disabled="!newContentSet.title || newContentSet.variables.length === 0"
          >
            <i class="pi pi-save mr-2"></i>
            Сохранить набор
          </Button>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useDirectusItems } from '#imports'

// Props
interface Props {
  selectedElements: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'content-applied': [data: any]
}>()

// Composables
const { getItems } = useDirectusItems()

// Reactive data
const analyzing = ref(false)
const showContentLibrary = ref(true)
const showAddContentDialog = ref(false)
const currentMode = ref<'select' | 'create'>('select')

// Анализ элементов
const elementAnalysis = ref<any[]>([])
const uniqueVariables = ref<string[]>([])

// Библиотека контента
const contentLibrary = ref<any[]>([])
const selectedContentSets = ref<any[]>([])
const selectedContentTypes = ref<string[]>(['первый-экран', 'текст', 'преимущества', 'услуги-товары'])

// Превью
const previewElements = ref<any[]>([])

// Конструктор переменных
const quickVariables = ref<{ [key: string]: string }>({})

// Поиск и фильтры
const contentSearchQuery = ref('')

// Новый набор контента
const newContentSet = ref({
  title: '',
  description: '',
  type: '',
  variables: [] as Array<{ name: string, value: string }>
})

// Типы блоков/контента (по вашей классификации)
const contentTypes = [
  { label: 'Первый экран', value: 'первый-экран', icon: 'pi pi-star' },
  { label: 'Текст', value: 'текст', icon: 'pi pi-align-left' },
  { label: 'Преимущества', value: 'преимущества', icon: 'pi pi-check-circle' },
  { label: 'Услуги/Товары', value: 'услуги-товары', icon: 'pi pi-shopping-bag' },
  { label: 'О компании', value: 'о-компании', icon: 'pi pi-building' },
  { label: 'Галерея работ', value: 'галерея-работ', icon: 'pi pi-images' },
  { label: 'Цены/Тарифы', value: 'цены-тарифы', icon: 'pi pi-dollar' },
  { label: 'Отзывы', value: 'отзывы', icon: 'pi pi-comments' },
  { label: 'Клиенты/Логотипы', value: 'клиенты-логотипы', icon: 'pi pi-users' },
  { label: 'Схема работы', value: 'схема-работы', icon: 'pi pi-sitemap' },
  { label: 'CTA', value: 'cta', icon: 'pi pi-send' },
  { label: 'Цифры', value: 'цифры', icon: 'pi pi-chart-bar' },
  { label: 'Кейсы/Проекты', value: 'кейсы-проекты', icon: 'pi pi-briefcase' },
  { label: 'Команда', value: 'команда', icon: 'pi pi-user-plus' },
  { label: 'Контакты', value: 'контакты', icon: 'pi pi-phone' },
  { label: 'Форма запроса', value: 'форма-запроса', icon: 'pi pi-file-edit' },
  { label: 'Шапка', value: 'шапка', icon: 'pi pi-window-maximize' },
  { label: 'Подвал', value: 'подвал', icon: 'pi pi-window-minimize' },
  { label: 'Дополнительно', value: 'дополнительно', icon: 'pi pi-plus-circle' }
]

// Computed
const totalVariables = computed(() => {
  return uniqueVariables.value.length
})

const filteredContentSets = computed(() => {
  let filtered = contentLibrary.value

  // Фильтр по типам
  if (selectedContentTypes.value.length > 0) {
    filtered = filtered.filter(set =>
      selectedContentTypes.value.includes(set.type)
    )
  }

  // Поиск по названию и описанию
  if (contentSearchQuery.value) {
    const query = contentSearchQuery.value.toLowerCase()
    filtered = filtered.filter(set =>
      set.title.toLowerCase().includes(query) ||
      set.description.toLowerCase().includes(query)
    )
  }

  return filtered
})

const canApply = computed(() => {
  return selectedContentSets.value.length > 0 &&
         props.selectedElements.length > 0
})

// Methods
const analyzeElements = async () => {
  if (props.selectedElements.length === 0) return

  analyzing.value = true
  try {
    const allVariables = new Set<string>()

    console.log('Анализируем элементы:', props.selectedElements.length)

    for (const element of props.selectedElements) {
      console.log('Анализируем элемент:', element.title, 'HBS:', !!element.hbs)

      if (element.hbs) {
        // Простой анализ переменных из HBS
        const variableRegex = /\{\{\s*([^}]+)\s*\}\}/g
        let match: RegExpExecArray | null
        while ((match = variableRegex.exec(element.hbs)) !== null) {
          const variable = match[1].trim()
          if (!variable.startsWith('#') && !variable.startsWith('/') && !variable.includes(' ')) {
            allVariables.add(variable)
            console.log('Найдена переменная:', variable)
          }
        }
      }
    }

    uniqueVariables.value = Array.from(allVariables)
    console.log('Уникальные переменные:', uniqueVariables.value)

    // Загружаем библиотеку контента
    await loadContentLibrary()

    // Анализируем совместимость
    analyzeCompatibility()

  } catch (error) {
    console.error('Ошибка анализа элементов:', error)
  } finally {
    analyzing.value = false
  }
}

const loadContentLibrary = async () => {
  try {
    // Объединяем и форматируем данные
    const library = []

    // Всегда добавляем встроенные наборы
    library.push(...getBuiltInContentSets())

    try {
      // Пытаемся загрузить из wjson
      const wjsonData = await getItems({
        collection: 'wjson',
        params: {
          limit: 100,
          fields: ['id', 'title', 'description', 'tags', 'json']
        }
      })

      // Добавляем wjson наборы
      wjsonData.forEach((item: any) => {
        if (item.json) {
          const jsonData = typeof item.json === 'string' ? JSON.parse(item.json) : item.json
          library.push({
            id: `wjson-${item.id}`,
            title: item.title || 'Без названия',
            description: item.description || 'Набор из коллекции wjson',
            type: determineContentType(item.tags || ''),
            source: 'wjson',
            data: jsonData,
            preview: getPreviewData(jsonData),
            compatibility: calculateCompatibility(jsonData)
          })
        }
      })
    } catch (error) {
      console.warn('Не удалось загрузить wjson:', error)
    }

    try {
      // Пытаемся загрузить из wblock_proto с json
      const wblockData = await getItems({
        collection: 'wblock_proto',
        params: {
          limit: 50,
          fields: ['id', 'title', 'json'],
          filter: { json: { _nnull: true } }
        }
      })

      // Добавляем wblock_proto наборы
      wblockData.forEach((block: any) => {
        if (block.json) {
          const jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
          library.push({
            id: `wblock-${block.id}`,
            title: block.title || 'Без названия',
            description: 'Набор из блока wblock_proto',
            type: 'дополнительно',
            source: 'wblock_proto',
            data: jsonData,
            preview: getPreviewData(jsonData),
            compatibility: calculateCompatibility(jsonData)
          })
        }
      })
    } catch (error) {
      console.warn('Не удалось загрузить wblock_proto:', error)
    }

    contentLibrary.value = library.sort((a, b) => b.compatibility - a.compatibility)

  } catch (error) {
    console.error('Ошибка загрузки библиотеки контента:', error)
    // В случае ошибки загружаем хотя бы встроенные наборы
    contentLibrary.value = getBuiltInContentSets()
  }
}

const determineContentType = (tags: string | null | undefined): string => {
  if (!tags) return 'дополнительно'

  const tagLower = String(tags).toLowerCase()

  // Определяем тип по типам блоков
  if (tagLower.includes('первый') || tagLower.includes('hero') || tagLower.includes('главный')) return 'первый-экран'
  if (tagLower.includes('текст') || tagLower.includes('text')) return 'текст'
  if (tagLower.includes('преимущества') || tagLower.includes('benefits')) return 'преимущества'
  if (tagLower.includes('услуги') || tagLower.includes('товары') || tagLower.includes('services')) return 'услуги-товары'
  if (tagLower.includes('компании') || tagLower.includes('about')) return 'о-компании'
  if (tagLower.includes('галерея') || tagLower.includes('gallery')) return 'галерея-работ'
  if (tagLower.includes('цены') || tagLower.includes('тарифы') || tagLower.includes('прайс')) return 'цены-тарифы'
  if (tagLower.includes('отзывы') || tagLower.includes('reviews')) return 'отзывы'
  if (tagLower.includes('клиенты') || tagLower.includes('логотипы')) return 'клиенты-логотипы'
  if (tagLower.includes('схема') || tagLower.includes('работы')) return 'схема-работы'
  if (tagLower.includes('cta') || tagLower.includes('призыв')) return 'cta'
  if (tagLower.includes('цифры') || tagLower.includes('статистика')) return 'цифры'
  if (tagLower.includes('кейсы') || tagLower.includes('проекты') || tagLower.includes('портфолио')) return 'кейсы-проекты'
  if (tagLower.includes('команда') || tagLower.includes('сотрудники')) return 'команда'
  if (tagLower.includes('контакты') || tagLower.includes('contacts')) return 'контакты'
  if (tagLower.includes('форма') || tagLower.includes('form')) return 'форма-запроса'
  if (tagLower.includes('шапка') || tagLower.includes('header')) return 'шапка'
  if (tagLower.includes('подвал') || tagLower.includes('footer')) return 'подвал'

  return 'дополнительно'
}

const getPreviewData = (jsonData: any): any => {
  const preview: any = {}
  let count = 0
  for (const [key, value] of Object.entries(jsonData)) {
    if (count >= 3) break
    preview[key] = value
    count++
  }
  return preview
}

const calculateCompatibility = (jsonData: any): number => {
  if (uniqueVariables.value.length === 0) {
    // Если переменные еще не найдены, возвращаем базовую совместимость
    return Object.keys(jsonData).length * 10 // Чем больше переменных, тем выше совместимость
  }

  const contentVars = Object.keys(jsonData)
  const matches = uniqueVariables.value.filter(v => contentVars.includes(v))
  const compatibility = Math.round((matches.length / uniqueVariables.value.length) * 100)

  console.log('Совместимость:', {
    uniqueVariables: uniqueVariables.value,
    contentVars,
    matches,
    compatibility
  })

  return compatibility
}

const getBuiltInContentSets = () => {
  return [
    // Первый экран
    {
      id: 'builtin-hero-1',
      title: 'Первый экран - Бизнес',
      description: 'Главный экран для бизнес-сайта',
      type: 'первый-экран',
      source: 'builtin',
      data: {
        title: 'Развиваем ваш бизнес',
        title2: 'Профессиональные решения для роста компании',
        text: 'Помогаем предпринимателям достигать новых высот с помощью современных технологий и проверенных стратегий',
        text2: 'Начать сотрудничество',
        text3: 'Узнать больше',
        image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800',
        image2: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=1200',
        url: '#contact',
        url2: '#about'
      },
      preview: { title: 'Развиваем ваш бизнес', title2: 'Профессиональные решения...', text2: 'Начать сотрудничество' },
      compatibility: 0
    },

    // Текст
    {
      id: 'builtin-text-1',
      title: 'Текстовый блок - О компании',
      description: 'Информационный текст о компании',
      type: 'текст',
      source: 'builtin',
      data: {
        title: 'О нашей компании',
        title2: 'Надежный партнер для вашего бизнеса',
        text: 'Мы работаем на рынке уже более 10 лет и за это время помогли сотням клиентов достичь своих целей. Наша команда состоит из опытных специалистов, которые постоянно совершенствуют свои навыки.',
        text2: 'Качество и результат - наши главные приоритеты',
        excerpt: 'Более 10 лет на рынке'
      },
      preview: { title: 'О нашей компании', text: 'Мы работаем на рынке уже более 10 лет...', title2: 'Надежный партнер...' },
      compatibility: 0
    },

    // Преимущества
    {
      id: 'builtin-benefits-1',
      title: 'Преимущества - Почему выбирают нас',
      description: 'Ключевые преимущества компании',
      type: 'преимущества',
      source: 'builtin',
      data: {
        title: 'Почему выбирают нас',
        title2: 'Наши конкурентные преимущества',
        title3: 'Опыт работы',
        title4: 'Качество услуг',
        title5: 'Индивидуальный подход',
        text: 'Более 10 лет успешной работы на рынке',
        text2: 'Высокие стандарты качества во всех проектах',
        text3: 'Персональные решения для каждого клиента',
        image: 'https://images.unsplash.com/photo-1553484771-371a605b060b?w=400',
        image2: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400',
        image3: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400'
      },
      preview: { title: 'Почему выбирают нас', title3: 'Опыт работы', title4: 'Качество услуг' },
      compatibility: 0
    },

    // Услуги/Товары
    {
      id: 'builtin-services-1',
      title: 'Услуги - Наши предложения',
      description: 'Каталог услуг компании',
      type: 'услуги-товары',
      source: 'builtin',
      data: {
        title: 'Наши услуги',
        title2: 'Полный спектр профессиональных услуг',
        title3: 'Консультации',
        title4: 'Разработка стратегии',
        title5: 'Внедрение решений',
        text: 'Экспертные консультации по развитию бизнеса',
        text2: 'Создание индивидуальной стратегии развития',
        text3: 'Полное сопровождение внедрения проектов',
        text4: 'от 5 000 ₽',
        text5: 'от 25 000 ₽',
        text6: 'от 50 000 ₽',
        image: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400',
        image2: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=400',
        image3: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400'
      },
      preview: { title: 'Наши услуги', title3: 'Консультации', text4: 'от 5 000 ₽' },
      compatibility: 0
    },

    // Отзывы
    {
      id: 'builtin-reviews-1',
      title: 'Отзывы клиентов',
      description: 'Отзывы довольных клиентов',
      type: 'отзывы',
      source: 'builtin',
      data: {
        title: 'Отзывы наших клиентов',
        title2: 'Что говорят о нас',
        title3: 'Иван Петров',
        title4: 'Мария Сидорова',
        title5: 'Алексей Козлов',
        text: 'Отличная работа! Результат превзошел все ожидания. Рекомендую всем!',
        text2: 'Профессиональный подход и качественное выполнение всех задач.',
        text3: 'Быстро, качественно, в срок. Будем обращаться еще!',
        excerpt: 'Директор ООО "Успех"',
        excerpt2: 'Маркетолог',
        excerpt3: 'Предприниматель',
        image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        image2: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
        image3: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100'
      },
      preview: { title: 'Отзывы наших клиентов', title3: 'Иван Петров', text: 'Отличная работа!...' },
      compatibility: 0
    },

    // CTA
    {
      id: 'builtin-cta-1',
      title: 'Призыв к действию',
      description: 'Блок призыва к действию',
      type: 'cta',
      source: 'builtin',
      data: {
        title: 'Готовы начать?',
        text: 'Свяжитесь с нами прямо сейчас и получите бесплатную консультацию',
        text2: 'Получить консультацию',
        text3: 'Позвонить',
        text4: '+7 (495) 123-45-67',
        text5: '<EMAIL>',
        linkText: 'Получить консультацию',
        linkText2: 'Позвонить',
        url: '#contact',
        url2: 'tel:+74951234567',
        image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=1200'
      },
      preview: { title: 'Готовы начать?', text: 'Свяжитесь с нами прямо сейчас...', text2: 'Получить консультацию' },
      compatibility: 0
    }
  ]
}

const analyzeCompatibility = () => {
  contentLibrary.value.forEach(set => {
    set.compatibility = calculateCompatibility(set.data)
  })

  // Пересортируем по совместимости
  contentLibrary.value.sort((a, b) => b.compatibility - a.compatibility)
}

const toggleContentType = (type: string) => {
  const index = selectedContentTypes.value.indexOf(type)
  if (index > -1) {
    selectedContentTypes.value.splice(index, 1)
  } else {
    selectedContentTypes.value.push(type)
  }
}

const toggleContentSet = (contentSet: any) => {
  const index = selectedContentSets.value.findIndex(s => s.id === contentSet.id)
  if (index > -1) {
    selectedContentSets.value.splice(index, 1)
  } else {
    selectedContentSets.value.push(contentSet)
  }

  // Обновляем превью
  updatePreview()
}

const updatePreview = () => {
  if (props.selectedElements.length === 0 || selectedContentSets.value.length === 0) {
    previewElements.value = []
    return
  }

  // Создаем превью для первых элементов с первым набором контента
  const previews = props.selectedElements.slice(0, 2).map(element => {
    const appliedSets = selectedContentSets.value.slice(0, 1) // Берем первый набор для превью

    return {
      ...element,
      appliedContentSets: appliedSets
    }
  })

  previewElements.value = previews
}

const getVariableTypeSeverity = (variable: string) => {
  if (variable.includes('image') || variable.includes('Image')) return 'info'
  if (variable.includes('url') || variable.includes('Url')) return 'warning'
  if (variable.includes('title') || variable.includes('Title')) return 'success'
  return 'secondary'
}

const getContentTypeSeverity = (type: string) => {
  switch (type) {
    case 'первый-экран': return 'danger'
    case 'текст': return 'info'
    case 'преимущества': return 'success'
    case 'услуги-товары': return 'warning'
    case 'о-компании': return 'info'
    case 'галерея-работ': return 'secondary'
    case 'цены-тарифы': return 'success'
    case 'отзывы': return 'warning'
    case 'клиенты-логотипы': return 'secondary'
    case 'схема-работы': return 'info'
    case 'cta': return 'danger'
    case 'цифры': return 'success'
    case 'кейсы-проекты': return 'warning'
    case 'команда': return 'info'
    case 'контакты': return 'secondary'
    case 'форма-запроса': return 'warning'
    case 'шапка': return 'contrast'
    case 'подвал': return 'contrast'
    default: return 'secondary'
  }
}

// Методы для конструктора переменных
const getVariablePlaceholder = (variable: string): string => {
  if (!variable) return 'Значение'

  const varLower = variable.toLowerCase()
  if (varLower.includes('title')) return 'Заголовок'
  if (varLower.includes('text')) return 'Текст описания'
  if (varLower.includes('button') || varLower.includes('linktext')) return 'Текст кнопки'
  if (varLower.includes('image')) return 'URL изображения'
  if (varLower.includes('url') || varLower.includes('link')) return 'Ссылка'
  if (varLower.includes('price')) return 'Цена'
  if (varLower.includes('name')) return 'Название'
  if (varLower.includes('phone')) return 'Телефон'
  if (varLower.includes('email')) return 'Email'
  if (varLower.includes('excerpt')) return 'Краткое описание'

  return 'Значение'
}

const getCharCount = (text: string | undefined): string => {
  if (!text) return '0'
  return String(text.length)
}

const updateCharCount = (_variable: string) => {
  // Метод вызывается автоматически при изменении input
}

const applyQuickVariables = () => {
  if (Object.keys(quickVariables.value).filter(k => quickVariables.value[k]).length === 0) return

  // Создаем временный набор контента из быстрых переменных
  const quickContentSet = {
    id: `quick-${Date.now()}`,
    title: 'Быстрый набор',
    description: 'Созданный через конструктор переменных',
    type: 'дополнительно',
    source: 'quick',
    data: { ...quickVariables.value },
    preview: Object.fromEntries(Object.entries(quickVariables.value).slice(0, 3)),
    compatibility: 100
  }

  // Добавляем в выбранные наборы
  selectedContentSets.value = [quickContentSet]

  // Обновляем превью
  updatePreview()
}

const clearQuickVariables = () => {
  quickVariables.value = {}
}

const applyIntelligentContent = () => {
  if (selectedContentSets.value.length === 0 && Object.keys(quickVariables.value).filter(k => quickVariables.value[k]).length === 0) return

  // Создаем модифицированные элементы для каждого набора контента
  const modifiedElements: any[] = []

  props.selectedElements.forEach(element => {
    // Определяем источник контента
    const contentSources = selectedContentSets.value.length > 0 ? selectedContentSets.value : [
      {
        id: 'quick',
        title: 'Быстрые переменные',
        data: quickVariables.value
      }
    ]

    contentSources.forEach(contentSet => {
      // Генерируем HTML с контентом
      let html = element.hbs || element.html
      Object.entries(contentSet.data).forEach(([variable, value]) => {
        const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
        html = html.replace(regex, String(value))
      })

      modifiedElements.push({
        ...element,
        html,
        appliedContentSet: contentSet,
        contentData: contentSet.data
      })
    })
  })

  emit('content-applied', {
    modifiedElements,
    contentSets: selectedContentSets.value.length > 0 ? selectedContentSets.value : [{ title: 'Быстрые переменные' }],
    totalVariants: modifiedElements.length
  })
}

// Методы для диалога создания набора
const addNewVariable = () => {
  newContentSet.value.variables.push({ name: '', value: '' })
}

const removeVariable = (index: number) => {
  newContentSet.value.variables.splice(index, 1)
}

const addVariableFromElement = (variable: string) => {
  const exists = newContentSet.value.variables.some(v => v.name === variable)
  if (!exists) {
    newContentSet.value.variables.push({
      name: variable,
      value: getVariablePlaceholder(variable)
    })
  }
}

const saveNewContentSet = () => {
  // Создаем объект данных из переменных
  const data: { [key: string]: string } = {}
  newContentSet.value.variables.forEach(variable => {
    if (variable.name && variable.value) {
      data[variable.name] = variable.value
    }
  })

  // Создаем новый набор контента
  const contentSet = {
    id: `custom-${Date.now()}`,
    title: newContentSet.value.title,
    description: newContentSet.value.description,
    type: newContentSet.value.type,
    source: 'custom',
    data,
    preview: Object.fromEntries(Object.entries(data).slice(0, 3)),
    compatibility: calculateCompatibility(data)
  }

  // Добавляем в библиотеку
  contentLibrary.value.unshift(contentSet)

  // Автоматически выбираем созданный набор
  selectedContentSets.value = [contentSet]

  // Обновляем превью
  updatePreview()

  // Закрываем диалог и очищаем форму
  showAddContentDialog.value = false
  newContentSet.value = {
    title: '',
    description: '',
    type: '',
    variables: []
  }
}

const getElementPreviewHtml = (element: any, withContent: boolean): string => {
  let html = element.html || ''
  let css = element.css || ''
  let js = element.js || ''

  if (withContent) {
    // Определяем источник контента
    let contentData: any = {}

    // Приоритет: быстрые переменные > выбранные наборы
    const quickVars = Object.keys(quickVariables.value).filter(k => quickVariables.value[k])
    if (quickVars.length > 0) {
      contentData = { ...quickVariables.value }
    } else if (selectedContentSets.value.length > 0) {
      contentData = selectedContentSets.value[0].data
    }

    // Применяем контент к HBS или HTML
    if (element.hbs && Object.keys(contentData).length > 0) {
      html = element.hbs
      Object.entries(contentData).forEach(([variable, value]) => {
        const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
        html = html.replace(regex, String(value))
      })
    }
  }

  // Формируем полный HTML с CSS и JS
  let fullHtml = '<!DOCTYPE html><html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1"><style>body { margin: 0; padding: 8px; font-family: system-ui, -apple-system, sans-serif; }'

  if (css) {
    fullHtml += css
  }

  fullHtml += '</style></head><body>' + html

  if (js) {
    fullHtml += '<script>' + js + '<\/script>'
  }

  fullHtml += '</body></html>'

  return fullHtml
}

// Watchers
watch(() => props.selectedElements, () => {
  if (props.selectedElements.length > 0) {
    analyzeElements()
  } else {
    uniqueVariables.value = []
    contentLibrary.value = []
  }
}, { immediate: true })

watch(() => selectedContentSets.value, () => {
  updatePreview()
}, { deep: true })

// Автоматический анализ при изменении переменных
watch(() => uniqueVariables.value, () => {
  if (uniqueVariables.value.length > 0) {
    analyzeCompatibility()
  }
}, { deep: true })

// Lifecycle
onMounted(() => {
  if (props.selectedElements.length > 0) {
    analyzeElements()
  }
})

const clearNewContentSet = () => {
  newContentSet.value = {
    title: '',
    description: '',
    type: '',
    variables: []
  }
}

const getTestPreviewHtml = (): string => {
  if (props.selectedElements.length === 0 || newContentSet.value.variables.length === 0) {
    return '<div>Нет данных для тестирования</div>'
  }

  const element = props.selectedElements[0]
  const testData: { [key: string]: string } = {}

  newContentSet.value.variables.forEach(variable => {
    if (variable.name && variable.value) {
      testData[variable.name] = variable.value
    }
  })

  let html = element.hbs || element.html
  Object.entries(testData).forEach(([variable, value]) => {
    const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
    html = html.replace(regex, String(value))
  })

  // Формируем полный HTML с CSS
  let fullHtml = '<!DOCTYPE html><html><head><meta charset="utf-8"><style>body { margin: 0; padding: 8px; font-family: system-ui, -apple-system, sans-serif; }'

  if (element.css) {
    fullHtml += element.css
  }

  fullHtml += '</style></head><body>' + html

  if (element.js) {
    fullHtml += '<script>' + element.js + '<\/script>'
  }

  fullHtml += '</body></html>'

  return fullHtml
}

// Watchers
watch(() => props.selectedElements, () => {
  if (props.selectedElements.length > 0) {
    analyzeElements()
  } else {
    uniqueVariables.value = []
    contentLibrary.value = []
  }
}, { immediate: true })

watch(() => selectedContentSets.value, () => {
  updatePreview()
}, { deep: true })

// Автоматический анализ при изменении переменных
watch(() => uniqueVariables.value, () => {
  if (uniqueVariables.value.length > 0) {
    analyzeCompatibility()
  }
}, { deep: true })

// Lifecycle
onMounted(() => {
  if (props.selectedElements.length > 0) {
    analyzeElements()
  }
})
</script>
