<template>
  <div class="block-content-constructor">
   
    
    <!-- Режимы работы -->
    
    <div class="flex gap-2 items-center justify-between mb-3">
      <h3 class="text-lg font-semibold mb-3">Интеллектуальный конструктор контента для блоков</h3>
      <div class="flex items-center gap-2">
        <Button
        v-for="mode in modes"
        :key="mode.value"
        :label="mode.label"
        :class="[
          'text-xs px-3 py-2',
          currentMode === mode.value ? 'p-button-info' : 'p-button-outlined'
        ]"
        @click="currentMode = mode.value"
      />

          </div>
      
    </div>

    <!-- Режим выбора -->
    <div v-if="currentMode === 'select'" class="grid grid-cols-3 gap-4">
      <!-- Быстрый конструктор (1/3) -->
      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg p-3">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-semibold text-blue-800 dark:text-blue-200">⚡ Быстрый конструктор</h4>
          <div class="text-xs text-blue-700 mb-2">
            Найдено {{ uniqueVariables.length }} переменных
          </div>
          <Button
            v-if="Object.keys(quickVariables).some(k => quickVariables[k])"
            icon="pi pi-check"
            class="text-xs p-button-success"
            @click="applyQuickVariables"
          />
          <Button
            v-else
            icon="pi pi-times"
            class="text-xs p-button-outlined"
            disabled
          />
        </div>

        <div v-if="analyzing" class="text-center py-4">
          <i class="pi pi-spinner pi-spin text-blue-600"/>
          <div class="text-xs text-blue-700 mt-2">Анализируем блоки...</div>
        </div>

        <div v-else-if="uniqueVariables.length > 0" class="space-y-2">
          

          <!-- Сетка переменных в 3 колонки -->
          <div class="grid grid-cols-1 gap-1 max-h-[550px] overflow-y-auto">
            <div
              v-for="variable in uniqueVariables.slice(0, 30)"
              :key="variable"
              class="flex items-center gap-1"
            >
              <span class="text-blue-600 text-xs w-[90px] truncate text-right">{{ variable }}:</span>
              <InputText
                v-model="quickVariables[variable]"
                :placeholder="getVariablePlaceholder(variable)"
                class="text-xs flex-1"
                size="small"
              />
              <span class="text-xs text-gray-500 w-5 text-right">{{ getCharCount(quickVariables[variable]) }}</span>
            </div>
          </div>

          <div v-if="uniqueVariables.length > 30" class="text-xs text-gray-500 text-center">
            +{{ uniqueVariables.length - 30 }} переменных (показаны первые 30)
          </div>
        </div>

        <div v-else class="text-center py-4 text-gray-500">
          <i class="pi pi-info-circle text-2xl mb-2 block"/>
          <div class="text-xs">Выберите блоки с HBS шаблонами</div>
          <div class="text-xs mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
            <div><strong>Отладка:</strong></div>
            <div>Блоков выбрано: {{ props.selectedBlocks.length }}</div>
            <div>Блоки с HBS: {{ props.selectedBlocks.filter(b => b.hbs).length }}</div>
            <div v-if="props.selectedBlocks.length > 0">
              Первый блок: {{ props.selectedBlocks[0].title }}
            </div>
          </div>
        </div>
      </div>

      <!-- Библиотека контента (2/3) -->
      <div class="col-span-2 bg-white border rounded-lg p-3 max-h-[620px] overflow-y-auto">
        <div class="flex items-center justify-between mb-2 gap-2">
          <h4 class="text-sm font-semibold">📚 Библиотека контента</h4>
          <InputText
            v-model="searchQuery"
            placeholder="Поиск..."
            class="text-xs flex-1"
            @input="filterContentLibrary"
          />
          <MultiSelect
            v-model="contentTypeFilter"
            :options="contentTypes"
            option-label="label"
            option-value="value"
            placeholder="Тип контента"
            class="text-xs w-60"
            @change="filterContentLibrary"
          />
          <MultiSelect
            v-model="contentSourceFilter"
            :options="contentSources"
            option-label="label"
            option-value="value"
            placeholder="Источник"
            class="text-xs w-40"
            @change="filterContentLibrary"
          />
          <div class="flex items-center gap-2">
            
            <Button
              v-tooltip.top="'Принудительный анализ'"
              icon="pi pi-search"
              class="text-xs p-button-outlined"
              @click="analyzeBlocks"
            />
            <Button
              icon="pi pi-refresh"
              class="text-xs p-button-outlined"
              @click="loadContentLibrary"
            />
            <Button
            v-if="selectedContentSets.length > 0 || Object.keys(quickVariables).some(k => quickVariables[k])"
            label="Применить"
            icon="pi pi-check"
            class="text-xs p-button-success"
            @click="applyAllContent"
          />
          </div>
        </div>

        <!-- Фильтры -->
        <div class="flex items-center gap-2 mb-3"/>

        <!-- Наборы контента в 4 колонки -->
        <div class="grid grid-cols-4 gap-3 mb-4">
          <div
            v-for="contentSet in filteredContentSets"
            :key="contentSet.id"
            class="content-card border rounded-lg p-2 cursor-pointer transition-all"
            :class="{
              'border-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedContentSets.some(s => s.id === contentSet.id),
              'border-gray-200 hover:border-gray-300': !selectedContentSets.some(s => s.id === contentSet.id)
            }"
            @click="toggleContentSet(contentSet)"
          >
            <div class="flex items-center justify-between mb-2">
              <h6 class="text-xs font-medium truncate">{{ contentSet.title }}</h6>
              <Checkbox
                :model-value="selectedContentSets.some(s => s.id === contentSet.id)"
                binary
                class="text-xs"
                @click.stop
                @change="toggleContentSet(contentSet)"
              />
            </div>
            
            <div class="text-xs text-gray-600 mb-2 line-clamp-2">
              {{ contentSet.description }}
            </div>
            
            <div class="flex items-center justify-between text-xs">
              <div class="flex gap-1">
                <Badge :value="contentSet.type" severity="info" class="text-xs" />
                <Badge :value="contentSet.source" severity="secondary" class="text-xs" />
              </div>
              <div class="text-gray-500">
                {{ getCompatibilityScore(contentSet) }}%
              </div>
            </div>

            <!-- Разворачиваемый JSON -->
            <div v-if="expandedSets.includes(contentSet.id)" class="mt-2 pt-2 border-t">
              <div class="text-xs font-medium text-gray-700 mb-1">Переменные:</div>
              <div class="space-y-1 max-h-32 overflow-y-auto">
                <div 
                  v-for="(value, key) in contentSet.data" 
                  :key="key"
                  class="flex items-center gap-2"
                >
                  <span class="font-mono text-blue-600 w-16 text-xs">{{ key }}:</span>
                  <span class="text-gray-700 flex-1 truncate text-xs">{{ value }}</span>
                  <span class="text-gray-400 text-xs">({{ getCharCount(value) }})</span>
                </div>
              </div>
            </div>

            <Button
              :icon="expandedSets.includes(contentSet.id) ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
              class="text-xs p-button-text w-full mt-2"
              @click.stop="toggleExpanded(contentSet.id)"
            />
          </div>
        </div>

        <div v-if="filteredContentSets.length === 0" class="text-center py-8 text-gray-500">
          <i class="pi pi-inbox text-2xl mb-2 block"/>
          <div class="text-sm">Наборы контента не найдены</div>
          <div class="text-xs">Попробуйте изменить фильтры</div>
        </div>
      </div>
    </div>

    <!-- Режим создания -->
    <div v-else-if="currentMode === 'create'" class="space-y-4">
      <h4 class="text-sm font-semibold">🛠 Создание наборов контента</h4>
      
      <!-- Горизонтальная прокрутка наборов -->
      <div class="flex gap-4 overflow-x-auto pb-4" style="scroll-snap-type: x mandatory;">
        <div
          v-for="(contentSet, index) in editableContentSets"
          :key="contentSet.id"
          class="content-set-editor flex-shrink-0 w-[400px] border rounded-lg p-3 bg-white"
          style="scroll-snap-align: start;"
        >
          <div class="flex items-center justify-between mb-3">
            <h5 class="text-sm font-medium">Набор {{ index + 1 }}</h5>
            <div class="flex gap-2">
              <Button
                icon="pi pi-plus"
                class="text-xs p-button-outlined"
                @click="addVariable(index)"
              />
              <Button
                icon="pi pi-trash"
                class="text-xs p-button-danger p-button-outlined"
                @click="removeContentSet(index)"
              />
            </div>
          </div>

          <!-- Основные поля -->
          <div class="grid grid-cols-2 gap-2 mb-3">
            <InputText
              v-model="contentSet.title"
              placeholder="Название набора"
              class="text-xs"
            />
            <Dropdown
              v-model="contentSet.type"
              :options="blockTypes"
              option-label="label"
              option-value="value"
              placeholder="Тип блока"
              class="text-xs"
            />
          </div>

          <InputText
            v-model="contentSet.description"
            placeholder="Описание набора"
            class="text-xs w-full mb-3"
          />

          <!-- Быстрое добавление из найденных переменных -->
          <div v-if="uniqueVariables.length > 0" class="mb-3">
            <label class="block text-xs font-medium mb-1">
              Быстрое добавление из блоков ({{ uniqueVariables.length }}):
            </label>
            <div class="flex flex-wrap gap-1">
              <Button
                v-for="variable in uniqueVariables"
                :key="variable"
                size="small"
                text
                severity="info"
                class="text-xs"
                @click="addVariableFromBlock(index, variable)"
              >
                <i class="pi pi-plus mr-1"/>
                {{ variable }}
              </Button>
            </div>
          </div>
          
          <!-- Отладочная информация -->
          <div v-else class="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
            <div class="text-yellow-800">
              <strong>Отладка:</strong> Переменные не найдены
            </div>
            <div class="text-yellow-700 mt-1">
              Блоков выбрано: {{ props.selectedBlocks.length }}
            </div>
            <div class="text-yellow-700">
              Блоки с HBS: {{ props.selectedBlocks.filter(block => block.hbs).length }}
            </div>
          </div>

          <!-- Переменные -->
          <div class="space-y-2">
            <div
              v-for="(variable, varIndex) in contentSet.variables"
              :key="varIndex"
              class="flex items-center gap-2"
            >
              <Dropdown
                v-model="variable.name"
                :options="predefinedVariables"
                option-label="label"
                option-value="value"
                placeholder="Переменная"
                class="text-xs w-32"
                editable
                :filter="true"
                filter-placeholder="Поиск или ввод своей..."
              />
              <InputText
                v-model="variable.value"
                :placeholder="getVariablePlaceholder(variable.name)"
                class="text-xs flex-1"
              />
              <span class="text-xs text-gray-500 w-8 text-right">{{ getCharCount(variable.value) }}</span>
              <Button
                icon="pi pi-trash"
                class="text-xs p-button-danger p-button-text"
                @click="removeVariable(index, varIndex)"
              />
            </div>
          </div>

          <!-- Превью набора -->
          <div v-if="contentSet.variables.some(v => v.name && v.value)" class="mt-3 pt-3 border-t">
            <div class="text-xs font-medium text-gray-700 mb-2">Превью набора:</div>
            <div class="bg-gray-50 p-2 rounded text-xs space-y-1">
              <div 
                v-for="variable in contentSet.variables.filter(v => v.name && v.value)" 
                :key="variable.name"
                class="flex items-center gap-2"
              >
                <span class="font-mono text-blue-600 w-16">{{ variable.name }}:</span>
                <span class="text-gray-700 flex-1 truncate">{{ variable.value }}</span>
                <span class="text-gray-400">({{ getCharCount(variable.value) }})</span>
              </div>
            </div>
            
            <!-- Тест на блоке -->
            <div v-if="props.selectedBlocks.length > 0" class="mt-2">
              <div class="text-xs font-medium text-gray-700 mb-1">Тест на блоке:</div>
              <iframe
                :srcdoc="getTestPreviewHtml(index)"
                class="w-full h-20 border rounded border-blue-200"
                frameborder="0"
                sandbox="allow-same-origin allow-scripts"
              />
            </div>
          </div>

          <!-- Кнопка сохранения -->
          <div class="mt-3 pt-3 border-t">
            <Button
              label="Сохранить набор"
              icon="pi pi-save"
              class="text-xs w-full"
              @click="saveContentSet(index)"
            />
          </div>
        </div>

        <!-- Кнопка добавления нового набора -->
        <div class="flex-shrink-0 w-[200px] border-2 border-dashed border-gray-300 rounded-lg p-3 flex items-center justify-center">
          <Button
            label="Добавить набор"
            icon="pi pi-plus"
            class="text-xs p-button-outlined"
            @click="addNewContentSet"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useDirectusItems } from '#imports'
// @ts-ignore
import handlebars from 'handlebars/dist/handlebars.min.js'

// PrimeVue компоненты
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Dropdown from 'primevue/dropdown'
import Checkbox from 'primevue/checkbox'
import Badge from 'primevue/badge'

// Интерфейсы
interface WBlock {
  id?: string
  number: string
  title: string
  block_type?: string[]
  collection?: string[]
  json?: string
  hbs?: string
  html?: string
  sketch?: string
  css?: string
  js?: string
}

interface ContentSet {
  id: string
  title: string
  description: string
  type: string
  source: string
  data: { [key: string]: string }
  compatibility?: number
}

interface EditableContentSet {
  id: string
  title: string
  description: string
  type: string
  variables: { name: string; value: string }[]
}

// Props и emits
const props = defineProps<{
  selectedBlocks: WBlock[]
}>()

const emit = defineEmits<{
  'content-applied': [data: any]
}>()

// Composables
const toast = useToast()
const { getItems } = useDirectusItems()

// Настройка Handlebars для правильной обработки отсутствующих переменных
handlebars.registerHelper('default', function(value, defaultValue) {
  return value || defaultValue || ''
})

// Настройка для замены отсутствующих переменных на пустые строки
handlebars.registerHelper('missing', function() {
  return ''
})

// Реактивные данные
const currentMode = ref('select')
const analyzing = ref(false)

// Режимы работы
const modes = [
  { label: 'Выбор', value: 'select' },
  { label: 'Создание', value: 'create' }
]

// Данные для анализа
const uniqueVariables = ref<string[]>([])
const quickVariables = ref<{ [key: string]: string }>({})

// Данные для библиотеки
const contentLibrary = ref<ContentSet[]>([])
const selectedContentSets = ref<ContentSet[]>([])
const expandedSets = ref<string[]>([])

// Фильтры
const contentTypeFilter = ref('')
const contentSourceFilter = ref('')
const searchQuery = ref('')

// Данные для создания
const editableContentSets = ref<EditableContentSet[]>([])
const blockTypes = ref<any[]>([])

// Предопределенные переменные СТРОГО по системе htmlToTemplate
const predefinedVariables = [
  { label: 'title', value: 'title' },
  { label: 'title2', value: 'title2' },
  { label: 'title3', value: 'title3' },
  { label: 'text', value: 'text' },
  { label: 'text2', value: 'text2' },
  { label: 'text3', value: 'text3' },
  { label: 'image', value: 'image' },
  { label: 'image2', value: 'image2' },
  { label: 'image3', value: 'image3' },
  { label: 'imageAlt', value: 'imageAlt' },
  { label: 'imageAlt2', value: 'imageAlt2' },
  { label: 'imageBackground', value: 'imageBackground' },
  { label: 'imageBackground2', value: 'imageBackground2' },
  { label: 'url', value: 'url' },
  { label: 'url2', value: 'url2' },
  { label: 'url3', value: 'url3' },
  { label: 'linkText', value: 'linkText' },
  { label: 'linkText2', value: 'linkText2' },
  { label: 'linkText3', value: 'linkText3' },
  { label: 'excerpt', value: 'excerpt' },
  { label: 'excerpt2', value: 'excerpt2' },
  { label: 'excerpt3', value: 'excerpt3' },
  { label: 'cssUrl', value: 'cssUrl' },
  { label: 'cssUrl2', value: 'cssUrl2' },
  { label: 'icon', value: 'icon' },
  { label: 'icon2', value: 'icon2' },
  { label: 'icon3', value: 'icon3' }
]

// Computed свойства
const contentTypes = computed(() => [
  { label: 'Все типы', value: '' },
  { label: 'Первый экран', value: 'первый-экран' },
  { label: 'О компании', value: 'о-компании' },
  { label: 'Услуги', value: 'услуги' },
  { label: 'Преимущества', value: 'преимущества' },
  { label: 'Команда', value: 'команда' },
  { label: 'Отзывы', value: 'отзывы' },
  { label: 'Контакты', value: 'контакты' },
  { label: 'CTA', value: 'cta' }
])

const contentSources = computed(() => [
  { label: 'Все источники', value: '' },
  { label: 'Встроенные', value: 'builtin' },
  { label: 'wjson', value: 'wjson' },
  { label: 'wblock_proto', value: 'wblock_proto' },
  { label: 'Файлы', value: 'files' },
  { label: 'Быстрые', value: 'quick' },
  { label: 'Пользовательские', value: 'custom' }
])

const filteredContentSets = computed(() => {
  let filtered = contentLibrary.value

  if (contentTypeFilter.value) {
    filtered = filtered.filter(set => set.type === contentTypeFilter.value)
  }

  if (contentSourceFilter.value) {
    filtered = filtered.filter(set => set.source === contentSourceFilter.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(set =>
      set.title.toLowerCase().includes(query) ||
      set.description.toLowerCase().includes(query)
    )
  }

  return filtered
})

// Методы для анализа блоков
const analyzeBlocks = async () => {
  if (props.selectedBlocks.length === 0) {
    uniqueVariables.value = []
    return
  }

  analyzing.value = true
  try {
    const allVariables = new Set<string>()

    console.log('🔍 Анализируем блоки:', props.selectedBlocks.length)

    for (const block of props.selectedBlocks) {
      console.log('📝 Блок:', block.title || block.id, {
        hasHbs: !!block.hbs,
        hbsLength: block.hbs?.length || 0,
        hbsPreview: block.hbs?.substring(0, 100) + '...'
      })

      if (block.hbs && typeof block.hbs === 'string') {
        // Ищем все переменные Handlebars {{variable}}
        const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
        let match: RegExpExecArray | null

        while ((match = variableRegex.exec(block.hbs)) !== null) {
          const variable = match[1].trim()

          // Исключаем Handlebars хелперы и блоки
          if (!variable.startsWith('#') &&
              !variable.startsWith('/') &&
              !variable.startsWith('^') &&
              !variable.includes(' ') &&
              !variable.includes('.') &&
              variable.length > 0) {
            allVariables.add(variable)
            console.log('✅ Найдена переменная:', variable)
          }
        }
      }
    }

    const variablesArray = Array.from(allVariables).sort()
    uniqueVariables.value = variablesArray

    console.log('🎯 Найдено уникальных переменных:', variablesArray.length, variablesArray)

    // Инициализируем быстрые переменные пустыми значениями
    const newQuickVariables: { [key: string]: string } = {}
    variablesArray.forEach(variable => {
      newQuickVariables[variable] = ''
    })
    quickVariables.value = newQuickVariables

    // Загружаем библиотеку контента
    await loadContentLibrary()

    // Анализируем совместимость
    analyzeCompatibility()

  } catch (error) {
    console.error('❌ Ошибка анализа блоков:', error)
  } finally {
    analyzing.value = false
  }
}

const analyzeCompatibility = () => {
  contentLibrary.value.forEach(contentSet => {
    const contentVariables = Object.keys(contentSet.data)
    const matchingVariables = uniqueVariables.value.filter(v => contentVariables.includes(v))
    const compatibility = uniqueVariables.value.length > 0
      ? Math.round((matchingVariables.length / uniqueVariables.value.length) * 100)
      : 0
    contentSet.compatibility = compatibility
  })

  // Сортируем по совместимости
  contentLibrary.value.sort((a, b) => (b.compatibility || 0) - (a.compatibility || 0))
}

// Методы для работы с библиотекой
const loadContentLibrary = async () => {
  try {
    console.log('📚 Загружаем библиотеку контента...')

    // Загружаем встроенные наборы
    const builtinSets: ContentSet[] = [
      {
        id: 'it-company-hero',
        title: 'IT компания - Первый экран',
        description: 'Набор для главного экрана IT компании',
        type: 'первый-экран',
        source: 'builtin',
        data: {
          title: 'Развиваем ваш бизнес с помощью IT',
          title2: 'Профессиональные IT решения',
          text: 'Помогаем компаниям достигать новых высот с помощью современных технологий и инновационных решений',
          text2: 'Более 10 лет опыта в разработке',
          image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop',
          linkText: 'Получить консультацию',
          url: '/consultation'
        }
      },
      {
        id: 'web-development-services',
        title: 'Веб-разработка - Услуги',
        description: 'Услуги веб-разработки',
        type: 'услуги',
        source: 'builtin',
        data: {
          title: 'Веб-разработка',
          text: 'Создаем современные веб-сайты и приложения',
          image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop',
          linkText: 'Подробнее',
          url: '/services/web-development'
        }
      }
    ]

    contentLibrary.value = [...builtinSets]

    // Загружаем из wjson коллекции
    try {
      const wjsonItems = await getItems({
        collection: 'wjson',
        params: {
          limit: -1,
          fields: ['id', 'title', 'description', 'tags', 'json']
        }
      })

      if (wjsonItems && Array.isArray(wjsonItems)) {
        const wjsonSets = wjsonItems.map((item: any) => ({
          id: `wjson-${item.id}`,
          title: item.title || `Набор ${item.id}`,
          description: item.description || 'Набор из коллекции wjson',
          type: item.tags?.[0] || 'общий',
          source: 'wjson',
          data: typeof item.json === 'string' ? JSON.parse(item.json) : (item.json || {})
        }))
        contentLibrary.value.push(...wjsonSets)
        console.log('✅ Загружено из wjson:', wjsonSets.length)
      }
    } catch (error) {
      console.log('⚠️ Не удалось загрузить из wjson:', error)
    }

    // Загружаем из wblock_proto коллекции
    try {
      const wblockItems = await getItems({
        collection: 'wblock_proto',
        params: {
          limit: -1,
          fields: ['id', 'title', 'json', 'block_type'],
          filter: {
            json: {
              _nnull: true
            }
          }
        }
      })

      if (wblockItems && Array.isArray(wblockItems)) {
        const wblockSets = wblockItems
          .filter(item => item.json) // Фильтруем только элементы с JSON
          .map((item: any) => {
            let parsedData = {}
            try {
              if (typeof item.json === 'string') {
                parsedData = JSON.parse(item.json)
              } else if (typeof item.json === 'object') {
                parsedData = item.json
              }
            } catch (error) {
              console.warn(`Ошибка парсинга JSON для блока ${item.id}:`, error)
              return null
            }

            return {
              id: `wblock-${item.id}`,
              title: item.title || `Блок ${item.id}`,
              description: `Контент из блока ${item.title}`,
              type: item.block_type?.[0] || 'блок',
              source: 'wblock_proto',
              data: parsedData
            }
          })
          .filter(Boolean) // Удаляем null элементы

        contentLibrary.value.push(...wblockSets)
        console.log('✅ Загружено из wblock_proto:', wblockSets.length)
      }
    } catch (error) {
      console.log('⚠️ Не удалось загрузить из wblock_proto:', error)
    }

    // Загружаем из файлов
    try {
      const fileResponse = await $fetch('/api/load-user-settings', {
        method: 'POST',
        body: { type: 'content-sets' }
      })

      if (fileResponse.success && fileResponse.data) {
        const fileSets = fileResponse.data.map((set: any) => ({
          ...set,
          source: 'files'
        }))
        contentLibrary.value.push(...fileSets)
        console.log('✅ Загружено из файлов:', fileSets.length)
      }
    } catch (error) {
      console.log('⚠️ Файлы контента не найдены')
    }

    console.log('📚 Всего загружено наборов контента:', contentLibrary.value.length)

  } catch (error) {
    console.error('❌ Ошибка загрузки библиотеки контента:', error)
  }
}

const filterContentLibrary = () => {
  // Фильтрация происходит через computed свойство filteredContentSets
}

// Методы для работы с выбором
const toggleContentSet = (contentSet: ContentSet) => {
  const index = selectedContentSets.value.findIndex(s => s.id === contentSet.id)
  if (index > -1) {
    selectedContentSets.value.splice(index, 1)
  } else {
    selectedContentSets.value.push(contentSet)
  }
}

const toggleExpanded = (setId: string) => {
  const index = expandedSets.value.indexOf(setId)
  if (index > -1) {
    expandedSets.value.splice(index, 1)
  } else {
    expandedSets.value.push(setId)
  }
}

// Методы применения контента
const applyQuickVariables = () => {
  const quickVars = Object.keys(quickVariables.value).filter(k => quickVariables.value[k])
  if (quickVars.length === 0) return

  const modifiedBlocks: any[] = []

  props.selectedBlocks.forEach(block => {
    let html = ''

    if (block.hbs) {
      try {
        // Используем Handlebars для правильной компиляции
        const template = handlebars.compile(block.hbs)
        html = template(quickVariables.value)
      } catch (error) {
        console.error('Ошибка компиляции HBS:', error)
        // Fallback к простой замене
        html = block.hbs
        Object.entries(quickVariables.value).forEach(([variable, value]) => {
          if (value) {
            const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
            html = html.replace(regex, String(value))
          }
        })
        // Удаляем оставшиеся переменные
        html = html.replace(/\{\{[^}]+\}\}/g, '')
      }
    } else {
      html = block.html || ''
    }

    modifiedBlocks.push({
      ...block,
      id: `${block.id}-quick`,
      number: `${block.number}-quick`,
      title: `${block.title} (Быстрые переменные)`,
      html,
      appliedContentSet: { id: 'quick', title: 'Быстрые переменные', data: quickVariables.value },
      contentData: quickVariables.value
    })
  })

  emit('content-applied', {
    modifiedBlocks,
    contentSets: [{ id: 'quick', title: 'Быстрые переменные', data: quickVariables.value }],
    totalVariants: modifiedBlocks.length
  })
}

const applySelectedContent = () => {
  if (selectedContentSets.value.length === 0) return

  const modifiedBlocks: any[] = []

  props.selectedBlocks.forEach((block, blockIndex) => {
    selectedContentSets.value.forEach((contentSet, setIndex) => {
      let html = ''

      if (block.hbs) {
        try {
          // Используем Handlebars для правильной компиляции
          const template = handlebars.compile(block.hbs)
          html = template(contentSet.data)
        } catch (error) {
          console.error('Ошибка компиляции HBS:', error)
          // Fallback к простой замене
          html = block.hbs
          Object.entries(contentSet.data).forEach(([variable, value]) => {
            const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
            html = html.replace(regex, String(value || ''))
          })
          // Удаляем оставшиеся переменные
          html = html.replace(/\{\{[^}]+\}\}/g, '')
        }
      } else {
        html = block.html || ''
      }

      // Создаем уникальный ID и название с префиксом
      const contentPrefix = contentSet.title.toLowerCase().replace(/[^a-z0-9]/g, '-').substring(0, 10)
      const uniqueId = `${block.id}-${contentPrefix}-${blockIndex + 1}-${setIndex + 1}`
      const uniqueNumber = `${block.number}-${contentPrefix}-${String(blockIndex + 1).padStart(2, '0')}`
      const uniqueTitle = `${block.title} (${contentSet.title})`

      modifiedBlocks.push({
        ...block,
        id: uniqueId,
        number: uniqueNumber,
        title: uniqueTitle,
        html,
        appliedContentSet: contentSet,
        contentData: contentSet.data
      })
    })
  })

  emit('content-applied', {
    modifiedBlocks,
    contentSets: selectedContentSets.value,
    totalVariants: modifiedBlocks.length
  })
}

// Объединенная функция применения контента
const applyAllContent = () => {
  const modifiedBlocks: any[] = []
  const appliedContentSets: ContentSet[] = []

  // 1. Применяем быстрые переменные (если заполнены)
  const hasQuickVariables = Object.keys(quickVariables.value).some(k => quickVariables.value[k])
  if (hasQuickVariables) {
    props.selectedBlocks.forEach((block, blockIndex) => {
      let html = ''

      if (block.hbs) {
        try {
          const template = handlebars.compile(block.hbs)
          html = template(quickVariables.value)
        } catch (error) {
          console.error('Ошибка компиляции HBS (быстрые переменные):', error)
          html = block.hbs
          Object.entries(quickVariables.value).forEach(([variable, value]) => {
            if (value) {
              const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
              html = html.replace(regex, String(value))
            }
          })
          html = html.replace(/\{\{[^}]+\}\}/g, '')
        }
      } else {
        html = block.html || ''
      }

      modifiedBlocks.push({
        ...block,
        id: `${block.id}-quick`,
        number: `${block.number}-quick`,
        title: `${block.title} (Быстрые переменные)`,
        html,
        appliedContentSet: { id: 'quick', title: 'Быстрые переменные', data: quickVariables.value },
        contentData: quickVariables.value
      })
    })

    appliedContentSets.push({ id: 'quick', title: 'Быстрые переменные', data: quickVariables.value } as ContentSet)
  }

  // 2. Применяем выбранные наборы из библиотеки
  if (selectedContentSets.value.length > 0) {
    props.selectedBlocks.forEach((block, blockIndex) => {
      selectedContentSets.value.forEach((contentSet, setIndex) => {
        let html = ''

        if (block.hbs) {
          try {
            const template = handlebars.compile(block.hbs)
            html = template(contentSet.data)
          } catch (error) {
            console.error('Ошибка компиляции HBS (библиотека):', error)
            html = block.hbs
            Object.entries(contentSet.data).forEach(([variable, value]) => {
              const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
              html = html.replace(regex, String(value || ''))
            })
            html = html.replace(/\{\{[^}]+\}\}/g, '')
          }
        } else {
          html = block.html || ''
        }

        const contentPrefix = contentSet.title.toLowerCase().replace(/[^a-z0-9]/g, '-').substring(0, 10)
        const uniqueId = `${block.id}-${contentPrefix}-${blockIndex + 1}-${setIndex + 1}`
        const uniqueNumber = `${block.number}-${contentPrefix}-${String(blockIndex + 1).padStart(2, '0')}`
        const uniqueTitle = `${block.title} (${contentSet.title})`

        modifiedBlocks.push({
          ...block,
          id: uniqueId,
          number: uniqueNumber,
          title: uniqueTitle,
          html,
          appliedContentSet: contentSet,
          contentData: contentSet.data
        })
      })
    })

    appliedContentSets.push(...selectedContentSets.value)
  }

  console.log('🎯 Применение контента завершено:', {
    quickVariables: hasQuickVariables,
    libraryContent: selectedContentSets.value.length,
    totalVariants: modifiedBlocks.length
  })

  emit('content-applied', {
    modifiedBlocks,
    contentSets: appliedContentSets,
    totalVariants: modifiedBlocks.length
  })
}

// Утилиты
const getVariablePlaceholder = (variable: string): string => {
  const placeholders: { [key: string]: string } = {
    title: 'Заголовок',
    title2: 'Подзаголовок',
    title3: 'Дополнительный заголовок',
    text: 'Основной текст',
    text2: 'Дополнительный текст',
    text3: 'Третий текст',
    image: 'URL изображения',
    image2: 'URL второго изображения',
    image3: 'URL третьего изображения',
    url: 'Ссылка',
    url2: 'Вторая ссылка',
    linkText: 'Текст кнопки',
    linkText2: 'Текст второй кнопки',
    excerpt: 'Краткое описание',
    excerpt2: 'Второе описание'
  }
  return placeholders[variable] || `Значение для ${variable}`
}

const getCharCount = (value: string | undefined): number => {
  return value ? value.length : 0
}

const getCompatibilityScore = (contentSet: ContentSet): number => {
  return contentSet.compatibility || 0
}

// Методы для режима создания
const loadBlockTypes = async () => {
  // Используем fallback типы сразу, так как коллекция block_type недоступна
  blockTypes.value = [
    { label: 'Первый экран', value: 'первый-экран' },
    { label: 'О компании', value: 'о-компании' },
    { label: 'Услуги', value: 'услуги' },
    { label: 'Преимущества', value: 'преимущества' },
    { label: 'Команда', value: 'команда' },
    { label: 'Отзывы', value: 'отзывы' },
    { label: 'Контакты', value: 'контакты' },
    { label: 'CTA', value: 'cta' },
    { label: 'Товары', value: 'товары' },
    { label: 'Портфолио', value: 'портфолио' },
    { label: 'FAQ', value: 'faq' },
    { label: 'Блог', value: 'блог' }
  ]
  console.log('✅ Загружено fallback типов блоков:', blockTypes.value.length)
}

const addNewContentSet = () => {
  editableContentSets.value.push({
    id: `new-${Date.now()}`,
    title: '',
    description: '',
    type: '',
    variables: [{ name: '', value: '' }]
  })
}

const removeContentSet = (index: number) => {
  if (editableContentSets.value.length > 1) {
    editableContentSets.value.splice(index, 1)
  }
}

const addVariable = (setIndex: number) => {
  editableContentSets.value[setIndex].variables.push({ name: '', value: '' })
}

const removeVariable = (setIndex: number, varIndex: number) => {
  editableContentSets.value[setIndex].variables.splice(varIndex, 1)
}

const addVariableFromBlock = (setIndex: number, variable: string) => {
  const contentSet = editableContentSets.value[setIndex]
  const exists = contentSet.variables.some((v: any) => v.name === variable)
  if (!exists) {
    contentSet.variables.push({
      name: variable,
      value: getVariablePlaceholder(variable)
    })
  }
}

const getTestPreviewHtml = (setIndex: number): string => {
  if (props.selectedBlocks.length === 0) {
    return '<div style="padding: 8px; text-align: center; color: #666;">Нет блоков для тестирования</div>'
  }

  const block = props.selectedBlocks[0]
  const contentSet = editableContentSets.value[setIndex]
  const testData: { [key: string]: string } = {}

  contentSet.variables.forEach((variable: any) => {
    if (variable.name && variable.value) {
      testData[variable.name] = variable.value
    }
  })

  let html = block.hbs || block.html
  Object.entries(testData).forEach(([variable, value]) => {
    const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
    html = html.replace(regex, String(value))
  })

  // Формируем полный HTML с CSS
  let fullHtml = '<!DOCTYPE html><html><head><meta charset="utf-8">'

  if (block.css) {
    fullHtml += block.css
  }

  fullHtml += '</head><body>' + html

  if (block.js) {
    fullHtml += '' + block.js + ''
  }

  fullHtml += '</body></html>'

  return fullHtml
}

const saveContentSet = async (index: number) => {
  const contentSet = editableContentSets.value[index]

  if (!contentSet.title || contentSet.variables.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните название и добавьте переменные',
      life: 3000
    })
    return
  }

  const newSet = {
    id: contentSet.id,
    title: contentSet.title,
    description: contentSet.description,
    type: contentSet.type,
    source: 'custom',
    data: {} as { [key: string]: string }
  }

  // Собираем переменные
  contentSet.variables.forEach(variable => {
    if (variable.name && variable.value) {
      newSet.data[variable.name] = variable.value
    }
  })

  try {
    await $fetch('/api/save-user-settings', {
      method: 'POST',
      body: {
        type: 'content-sets',
        filename: `content-set-${Date.now()}.json`,
        data: newSet
      }
    })

    toast.add({
      severity: 'success',
      summary: 'Успех',
      detail: 'Набор контента сохранен',
      life: 3000
    })

    // Перезагружаем библиотеку
    await loadContentLibrary()
  } catch (error) {
    console.error('Ошибка сохранения набора:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить набор',
      life: 3000
    })
  }
}

// Watchers
watch(() => props.selectedBlocks, async () => {
  console.log('🔄 Изменились выбранные блоки:', props.selectedBlocks.length)
  if (props.selectedBlocks.length > 0) {
    await analyzeBlocks()
    // Автоматически загружаем библиотеку контента при выборе блоков
    if (contentLibrary.value.length === 0) {
      await loadContentLibrary()
    }
  } else {
    uniqueVariables.value = []
    contentLibrary.value = []
    quickVariables.value = {}
  }
}, { immediate: true, deep: true })

watch(() => uniqueVariables.value, () => {
  if (uniqueVariables.value.length > 0) {
    analyzeCompatibility()
  }
}, { deep: true })

// Lifecycle
onMounted(async () => {
  console.log('🚀 BlockContentConstructor mounted')
  console.log('📊 Блоков получено:', props.selectedBlocks.length)
  console.log('📝 Блоки:', props.selectedBlocks.map(b => ({ id: b.id, title: b.title, hasHbs: !!b.hbs })))

  await loadBlockTypes()

  // Принудительно анализируем блоки при монтировании
  if (props.selectedBlocks.length > 0) {
    console.log('🔍 Принудительный анализ при монтировании')
    await analyzeBlocks()
  } else {
    console.log('⚠️ Нет блоков для анализа')
  }

  // Инициализируем режим создания с одним пустым набором
  editableContentSets.value = [{
    id: 'new-1',
    title: '',
    description: '',
    type: '',
    variables: [{ name: '', value: '' }]
  }]

  console.log('✅ BlockContentConstructor инициализирован')
})
</script>

<style scoped>
.block-content-constructor {
  max-height: 80vh;
  overflow-y: auto;
}

.content-card {
  transition: all 0.2s ease;
}

.content-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.content-set-editor {
  transition: all 0.2s ease;
}

.content-set-editor:hover {
  background-color: #f8fafc;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
