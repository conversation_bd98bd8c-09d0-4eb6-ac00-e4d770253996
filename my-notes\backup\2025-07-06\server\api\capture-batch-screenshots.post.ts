import { defineEventHand<PERSON>, readBody, createError } from 'h3'
import puppeteer from 'puppeteer'
import fs from 'fs/promises'
import path from 'path'
import os from 'os'

interface ScreenshotItem {
  html: string
  filename: string
  width?: number
  height?: number
}

interface BatchScreenshotRequest {
  screenshots: ScreenshotItem[]
}

interface ScreenshotResult {
  fileId: string
  filename: string
  success: boolean
  error?: string
}

interface BatchScreenshotResponse {
  results: ScreenshotResult[]
  totalTime: number
  successCount: number
  errorCount: number
}

export default defineEventHandler(async (event) => {
  const startTime = Date.now()
  const { screenshots } = await readBody<BatchScreenshotRequest>(event)

  if (!screenshots || !Array.isArray(screenshots) || screenshots.length === 0) {
    throw createError({
      statusCode: 400,
      message: 'Screenshots array is required and must not be empty',
    })
  }

  console.log(`🔄 Создание ${screenshots.length} скриншотов в batch режиме...`)

  // Запускаем браузер ОДИН РАЗ для всех скриншотов с максимальными оптимизациями
  const browser = await puppeteer.launch({
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage', // Оптимизация памяти
      '--disable-gpu', // Отключаем GPU для стабильности
      '--no-first-run', // Ускоряем запуск
      '--disable-default-apps', // Ускоряем запуск
      '--disable-background-timer-throttling', // Ускоряем анимации
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--disable-background-networking',
      '--disable-sync',
      '--disable-extensions',
      '--disable-plugins',
      // Убираем --disable-images для корректной работы анимаций
    ]
  })

  const results: ScreenshotResult[] = []

  try {
    // Обрабатываем скриншоты параллельно (но ограничиваем количество одновременных)
    const CONCURRENT_LIMIT = 2 // Ограничиваем для стабильности

    for (let i = 0; i < screenshots.length; i += CONCURRENT_LIMIT) {
      const batch = screenshots.slice(i, i + CONCURRENT_LIMIT)

      const batchPromises = batch.map(async (item, batchIndex) => {
        const globalIndex = i + batchIndex
        return await processScreenshot(browser, item, globalIndex + 1)
      })

      const batchResults = await Promise.allSettled(batchPromises)

      // Обрабатываем результаты batch
      batchResults.forEach((result, batchIndex) => {
        const globalIndex = i + batchIndex
        if (result.status === 'fulfilled') {
          results.push(result.value)
        } else {
          console.error(`❌ Ошибка скриншота ${globalIndex + 1}:`, result.reason)
          results.push({
            fileId: '',
            filename: screenshots[globalIndex].filename,
            success: false,
            error: result.reason?.message || 'Unknown error'
          })
        }
      })
    }

  } catch (error) {
    console.error('❌ Критическая ошибка batch обработки:', error)
    throw createError({
      statusCode: 500,
      message: (error as Error).message || 'Failed to process batch screenshots',
    })
  } finally {
    // Закрываем браузер
    await browser.close()
    console.log('🔒 Браузер закрыт')
  }

  const totalTime = Date.now() - startTime
  const successCount = results.filter(r => r.success).length
  const errorCount = results.length - successCount

  console.log(`✅ Batch обработка завершена: ${successCount}/${results.length} успешно за ${totalTime}ms`)

  return {
    results,
    totalTime,
    successCount,
    errorCount
  } as BatchScreenshotResponse
})

// Функция обработки одного скриншота
async function processScreenshot(browser: any, item: ScreenshotItem, index: number): Promise<ScreenshotResult> {
  const { html, filename, width = 1400, height = 800 } = item
  let tempFilePath: string | null = null

  console.log(`📸 Обработка скриншота ${index}: ${filename}...`)

  try {
    // Создаем новую страницу для каждого скриншота
    const page = await browser.newPage()

    try {
      // Устанавливаем размер окна браузера
      await page.setViewport({ width, height })

      // Создаем временный HTML файл для полной загрузки ресурсов
      tempFilePath = path.join(os.tmpdir(), `temp-screenshot-${Date.now()}-${index}.html`)
      console.log(`📁 Скриншот ${index}: создание временного файла ${tempFilePath}`)

      try {
        await fs.writeFile(tempFilePath, html, 'utf8')

        // Загружаем как реальную страницу для полной загрузки ресурсов
        console.log(`🌐 Скриншот ${index}: загрузка страницы как file:// URL...`)
        await page.goto(`file://${tempFilePath}`, {
          waitUntil: 'networkidle0', // Ждем полной загрузки всех ресурсов
          timeout: 30000 // Увеличиваем timeout для очень сложных страниц
        })
      } catch (fileError) {
        console.error(`❌ Ошибка создания временного файла для скриншота ${index}:`, fileError)
        tempFilePath = null // Сбрасываем путь если файл не создался
        // Fallback к setContent если не удалось создать файл
        await page.setContent(html, {
          waitUntil: 'networkidle0',
          timeout: 30000
        })
      }

      // Оптимизированная базовая задержка для полной загрузки контента
      console.log(`⏱️ Скриншот ${index}: базовая задержка 3000ms для полной загрузки контента...`)
      await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))

      // Ждем загрузки всех изображений с увеличенным timeout
      console.log(`🖼️ Скриншот ${index}: ожидание загрузки всех изображений с timeout 10000ms...`)
      await page.evaluate(() => {
        return Promise.all(
          Array.from(document.images)
            .filter(img => !img.complete)
            .map(img => new Promise(resolve => {
              img.onload = img.onerror = resolve
              // Увеличенный timeout для изображений
              setTimeout(resolve, 10000)
            }))
        )
      })

      // КРИТИЧНО: СНАЧАЛА прокрутка для активации scroll-анимаций
      console.log(`📜 Скриншот ${index}: умная прокрутка для активации scroll-анимаций...`)
      await page.evaluate(() => {
        return new Promise((resolve) => {
          // Имитируем ручной процесс: медленная прокрутка вниз, затем быстро вверх
          const scrollToBottom = () => {
            // Медленная прокрутка вниз по частям для активации всех scroll-триггеров
            let currentScroll = 0
            const maxScroll = document.body.scrollHeight
            const scrollStep = Math.max(200, maxScroll / 10) // Прокручиваем по частям

            const scrollInterval = setInterval(() => {
              currentScroll += scrollStep
              window.scrollTo(0, Math.min(currentScroll, maxScroll))

              if (currentScroll >= maxScroll) {
                clearInterval(scrollInterval)
                // Пауза внизу для активации анимаций
                setTimeout(scrollToTop, 2000)
              }
            }, 300) // Медленная прокрутка для активации всех триггеров
          }

          // Быстрая прокрутка вверх
          const scrollToTop = () => {
            window.scrollTo(0, 0)
            setTimeout(resolve, 1500) // Пауза вверху для стабилизации
          }

          // Начинаем процесс
          setTimeout(scrollToBottom, 1000)
        })
      })

      // ЗАТЕМ анализ анимаций (после активации scroll-анимаций)
      const animationInfo = await page.evaluate(() => {
        let maxDuration = 0
        let hasAnimations = false
        let animationCount = 0

        // Проверяем CSS анимации и transitions
        const elements = document.querySelectorAll('*')
        for (const el of elements) {
          const styles = window.getComputedStyle(el)

          // Проверяем анимации
          if (styles.animationDuration !== '0s') {
            hasAnimations = true
            animationCount++
            const duration = parseFloat(styles.animationDuration) * 1000
            maxDuration = Math.max(maxDuration, duration)
          }

          // Проверяем transitions
          if (styles.transitionDuration !== '0s') {
            hasAnimations = true
            animationCount++
            const duration = parseFloat(styles.transitionDuration) * 1000
            maxDuration = Math.max(maxDuration, duration)
          }
        }

        // Проверяем наличие популярных анимационных библиотек и их элементов
        const hasAnimationLibs = !!(
          window.AOS || // AOS library
          document.querySelector('[data-aos]') || // AOS elements
          document.querySelector('.animate__animated') || // Animate.css
          document.querySelector('[class*="fade"]') || // Fade animations
          document.querySelector('[class*="slide"]') || // Slide animations
          document.querySelector('[class*="zoom"]') || // Zoom animations
          document.querySelector('[class*="bounce"]') || // Bounce animations
          document.querySelector('[class*="rotate"]') || // Rotate animations
          document.querySelector('.wow') // WOW.js library
        )

        // Проверяем JavaScript анимации (GSAP, jQuery и др.)
        const hasJSAnimations = !!(
          window.gsap || // GSAP
          window.TweenMax || // TweenMax
          window.anime || // Anime.js
          (window.jQuery && window.jQuery.fn.animate) // jQuery animations
        )

        return {
          hasAnimations: hasAnimations || hasAnimationLibs || hasJSAnimations,
          maxDuration: Math.min(maxDuration, 4000), // Увеличиваем максимум до 4 секунд
          hasLibs: hasAnimationLibs,
          hasJSAnimations: hasJSAnimations,
          animationCount: animationCount
        }
      })

      // Адаптивная задержка в зависимости от контента (после базовой задержки)
      let additionalWaitTime = 0

      if (animationInfo.hasAnimations) {
        if (animationInfo.maxDuration > 0) {
          // Ждем максимальную продолжительность анимации + буфер
          additionalWaitTime = Math.min(animationInfo.maxDuration + 500, 3000)
        } else if (animationInfo.hasJSAnimations) {
          // Для JavaScript анимаций даем больше времени
          additionalWaitTime = 1500
        } else if (animationInfo.hasLibs) {
          // Для библиотек анимаций используем стандартную задержку
          additionalWaitTime = 1200
        } else {
          additionalWaitTime = 800
        }

        // Дополнительное время для множественных анимаций
        if (animationInfo.animationCount > 5) {
          additionalWaitTime += 300
        }
      }



      if (additionalWaitTime > 0) {
        console.log(`⏱️ Скриншот ${index}: дополнительное ожидание ${additionalWaitTime}ms для анимаций...`)
        await page.evaluate((delay: number) => new Promise(resolve => setTimeout(resolve, delay)), additionalWaitTime)
      }

      // ПРИНУДИТЕЛЬНАЯ активация всех анимаций
      console.log(`🎯 Скриншот ${index}: принудительная активация всех анимаций...`)
      await page.evaluate(() => {
        // Принудительно активируем AOS анимации
        if (window.AOS && window.AOS.refresh) {
          window.AOS.refresh()
        }

        // Принудительно активируем WOW анимации
        if (window.WOW) {
          try {
            new window.WOW().init()
          } catch (e) { }
        }

        // Принудительно активируем все элементы с data-aos
        const aosElements = document.querySelectorAll('[data-aos]')
        aosElements.forEach(el => {
          if (el instanceof HTMLElement) {
            el.classList.add('aos-animate')
          }
        })

        // Принудительно активируем все элементы с классами анимаций
        const animatedElements = document.querySelectorAll('.animate__animated, [class*="fade"], [class*="slide"], [class*="zoom"], [class*="bounce"]')
        animatedElements.forEach(el => {
          if (el instanceof HTMLElement) {
            el.style.opacity = '1'
            el.style.visibility = 'visible'
            el.style.transform = 'none'
          }
        })

        // Принудительно показываем скрытые элементы
        const hiddenElements = document.querySelectorAll('[style*="opacity: 0"], [style*="visibility: hidden"]')
        hiddenElements.forEach(el => {
          if (el instanceof HTMLElement) {
            el.style.opacity = '1'
            el.style.visibility = 'visible'
          }
        })
      })

      // Оптимизированная финальная стабилизация после всех операций
      console.log(`⏱️ Скриншот ${index}: финальная стабилизация 2000ms...`)
      await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 2000)))

      // ДИАГНОСТИКА: Проверяем состояние страницы перед созданием скриншота
      console.log(`🔍 Скриншот ${index}: ДИАГНОСТИКА: Анализ состояния страницы...`)
      const pageAnalysis = await page.evaluate(() => {
        const analysis = {
          totalImages: document.images.length,
          loadedImages: Array.from(document.images).filter(img => img.complete).length,
          failedImages: Array.from(document.images).filter(img => !img.complete).length,
          totalElements: document.querySelectorAll('*').length,
          visibleElements: Array.from(document.querySelectorAll('*')).filter(el => {
            const style = window.getComputedStyle(el)
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0'
          }).length,
          documentReadyState: document.readyState,
          bodyHeight: document.body.scrollHeight,
          viewportHeight: window.innerHeight,
          currentScrollPosition: window.pageYOffset
        }

        // Проверяем конкретные библиотеки
        analysis.libraries = {
          jquery: !!window.jQuery,
          aos: !!window.AOS,
          gsap: !!window.gsap,
          bootstrap: !!window.bootstrap || !!window.Bootstrap
        }

        return analysis
      })

      console.log(`📊 Скриншот ${index}: ДИАГНОСТИКА РЕЗУЛЬТАТЫ:`)
      console.log(`   📷 Изображения: ${pageAnalysis.loadedImages}/${pageAnalysis.totalImages} загружено (${pageAnalysis.failedImages} не загружено)`)
      console.log(`   👁️ Элементы: ${pageAnalysis.visibleElements}/${pageAnalysis.totalElements} видимо (${Math.round(pageAnalysis.visibleElements / pageAnalysis.totalElements * 100)}%)`)
      console.log(`   📄 Состояние документа: ${pageAnalysis.documentReadyState}`)
      console.log(`   📏 Высота: ${pageAnalysis.bodyHeight}px, Viewport: ${pageAnalysis.viewportHeight}px, Scroll: ${pageAnalysis.currentScrollPosition}px`)
      console.log(`   📚 Библиотеки:`, pageAnalysis.libraries)
      console.log(`⏱️ Анимации - ${animationInfo.hasAnimations}, JS анимации - ${animationInfo.hasJSAnimations}, библиотеки - ${animationInfo.hasLibs}, количество - ${animationInfo.animationCount}, макс. длительность - ${animationInfo.maxDuration}ms`)

      // Создаем скриншот body элемента для правильной высоты (восстанавливаем оригинальную логику)
      console.log(`📸 Скриншот ${index}: Поиск body элемента для правильной высоты...`)
      const bodyElement = await page.$('body')

      if (!bodyElement) {
        throw new Error('Body element not found')
      }

      // Получаем размеры body элемента
      const boundingBox = await bodyElement.boundingBox()

      if (!boundingBox) {
        throw new Error('Could not get body element dimensions')
      }

      console.log(`📏 Скриншот ${index}: реальная высота контента ${boundingBox.height}px (ширина: ${boundingBox.width}px)`)

      // Делаем скриншот body элемента для правильной высоты
      const screenshot = await bodyElement.screenshot({
        type: 'jpeg',
        quality: 90,
        omitBackground: false
      })

      console.log(`📸 Скриншот ${index} создан, размер: ${screenshot.length} байт`)

      // Генерируем имя файла с временной меткой
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
      const finalFilename = `${filename}_${timestamp}.jpg`

      // Загружаем в Directus
      const fileId = await uploadToDirectus(screenshot, finalFilename)

      console.log(`✅ Скриншот ${index} загружен в Directus: ${fileId}`)

      return {
        fileId,
        filename: finalFilename,
        success: true
      }

    } finally {
      // Удаляем временный файл если он был создан
      if (tempFilePath) {
        try {
          await fs.unlink(tempFilePath).catch(() => { }) // Игнорируем ошибки удаления
          console.log(`🗑️ Скриншот ${index}: временный файл удален`)
        } catch (cleanupError) {
          // Игнорируем ошибки очистки
        }
      }

      // Закрываем страницу
      await page.close()
    }

  } catch (error) {
    console.error(`❌ Ошибка скриншота ${index}:`, error)
    throw error
  }
}

// Функция загрузки в Directus
async function uploadToDirectus(screenshot: Buffer, filename: string): Promise<string> {
  const formData = new FormData()
  formData.append('title', filename)

  const blob = new Blob([screenshot], { type: 'image/jpeg' })
  formData.append('file', blob, filename)

  const response = await fetch('http://localhost:8055/files', {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error(`Failed to upload to Directus: ${response.statusText}`)
  }

  const data = await response.json()
  return data.data.id
}
