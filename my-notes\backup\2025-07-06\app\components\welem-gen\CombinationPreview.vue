<template>
  <div class="combination-preview">
    
    
    <!-- Настройки сетки -->
    <div class="grid-settings p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg mb-4">
      <div class="flex items-center justify-between mb-3">
        <h4 class="text-sm font-semibold">Конструктор комбинаций</h4>
        <div class="flex items-center gap-2">
          <Button
            label="Сохранить шаблон"
            icon="pi pi-bookmark"
            class="text-xs p-button-outlined"
            @click="saveLayoutTemplate"
          />
          <Dropdown
            v-model="selectedTemplate"
            :options="layoutTemplates"
            option-label="name"
            option-value="id"
            placeholder="Загрузить шаблон"
            class="text-xs w-40"
            @change="loadLayoutTemplate"
          />
          <Button
            v-tooltip="'Отменить'"
            icon="pi pi-undo"
            class="text-xs p-button-outlined p-1"
            :disabled="!canUndo"
            @click="undo"
          />
          <Button
            v-tooltip="'Повторить'"
            icon="pi pi-redo"
            class="text-xs p-button-outlined p-1"
            :disabled="!canRedo"
            @click="redo"
          />
          <span class="text-xs text-gray-500">{{ historyPosition + 1 }}/{{ history.length }}</span>
        </div>
      </div>

      <!-- Ряды -->
      <div class="rows-container">
        <div
          v-for="(row, rowIndex) in gridSettings.rows"
          :key="row.id"
          class="row-item mb-3 p-3 border rounded bg-white"
          draggable="true"
          @dragstart="onRowDragStart($event, rowIndex)"
          @dragover.prevent
          @drop="onRowDrop($event, rowIndex)"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center gap-2">
              <span class="text-sm font-medium">Ряд {{ rowIndex + 1 }}</span>
              <Dropdown
                v-model="row.containerType"
                :options="containerOptions"
                option-label="label"
                option-value="value"
                class="text-xs w-32"
                @change="saveState(); updatePreview()"
              />
              <InputText
                v-model="row.customClasses"
                placeholder="Кастомные классы"
                class="text-xs w-32"
                @input="saveState(); updatePreview()"
              />
            </div>
            <div class="flex gap-1">
              <Button
                v-tooltip="'Дублировать ряд'"
                icon="pi pi-copy"
                class="text-xs p-button-outlined p-1"
                @click="duplicateRow(rowIndex)"
              />
              <Button
                v-tooltip="'Переместить вверх'"
                icon="pi pi-arrow-up"
                class="text-xs p-button-outlined p-1"
                :disabled="rowIndex === 0"
                @click="moveRowUp(rowIndex)"
              />
              <Button
                v-tooltip="'Переместить вниз'"
                icon="pi pi-arrow-down"
                class="text-xs p-button-outlined p-1"
                :disabled="rowIndex === gridSettings.rows.length - 1"
                @click="moveRowDown(rowIndex)"
              />
              <Button
                v-tooltip="'Добавить колонку'"
                icon="pi pi-plus"
                class="text-xs p-button-outlined p-1"
                @click="addColumn(rowIndex)"
              />
              <Button
                v-tooltip="'Удалить ряд'"
                icon="pi pi-trash"
                class="text-xs p-button-danger p-1"
                @click="removeRow(rowIndex)"
              />
            </div>
          </div>

          <!-- Кастомные классы как чипы -->
          

          <!-- Колонки с правильной Bootstrap-like логикой -->
          <div class="columns-container row" style="margin: 0;">
            <div
              v-for="(column, colIndex) in row.columns"
              :key="column.id"
              :class="['column-item', 'p-2', 'border', 'rounded', 'bg-gray-50', 'relative', 'group', 'transition-all', getBootstrapColumnClass(column.width)]"
              draggable="true"
              @dragstart="onColumnDragStart($event, rowIndex, colIndex)"
              @dragend="onColumnDragEnd"
              @dragover.prevent="onColumnDragOver($event, rowIndex, colIndex)"
              @dragleave="onColumnDragLeave($event, rowIndex, colIndex)"
              @drop="onColumnDrop($event, rowIndex, colIndex)"
            >
              <!-- Микро-панель управления колонкой (по центру сверху) -->
              <div class="absolute -top-5 left-1/2 transform -translate-x-1/2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity z-10 bg-white p-1 rounded">
                <Button
                  v-tooltip="'Дублировать'"
                  icon="pi pi-copy"
                  class="text-xs p-button-text p-0 w-5 h-5 bg-white border shadow-sm rounded"
                  @click="duplicateColumn(rowIndex, colIndex)"
                />
                <Button
                  v-tooltip="'Влево'"
                  icon="pi pi-arrow-left"
                  class="text-xs p-button-text p-0 w-5 h-5 bg-white border shadow-sm rounded"
                  :disabled="colIndex === 0"
                  @click="moveColumnLeft(rowIndex, colIndex)"
                />
                <Button
                  v-tooltip="'Вправо'"
                  icon="pi pi-arrow-right"
                  class="text-xs p-button-text p-0 w-5 h-5 bg-white border shadow-sm rounded"
                  :disabled="colIndex === row.columns.length - 1"
                  @click="moveColumnRight(rowIndex, colIndex)"
                />
                <Button
                  v-tooltip="'Удалить'"
                  icon="pi pi-times"
                  class="text-xs p-button-danger p-0 w-5 h-5 bg-white border shadow-sm rounded"
                  @click="removeColumn(rowIndex, colIndex)"
                />
              </div>

              <!-- Компактная строка управления -->
              <div class="flex gap-1 mb-2">
                <Dropdown
                  v-model="column.width"
                  :options="columnOptions"
                  option-label="label"
                  option-value="value"
                  class="text-xs flex-1"
                  @change="saveState(); updatePreview()"
                />
                <InputText
                  v-model="column.customClasses"
                  placeholder="Классы"
                  class="text-xs flex-1"
                  @input="saveState(); updatePreview()"
                />
              </div>

              <!-- Кастомные классы как чипы -->
              

              <!-- Выбор элементов -->
              <MultiSelect
                v-model="column.elements"
                :options="availableElements"
                option-label="displayName"
                option-value="id"
                placeholder="Выберите элементы"
                class="text-xs w-full mb-2"
                @change="saveState(); updatePreview()"
              >
                <template #option="slotProps">
                  <div class="flex items-center gap-2 p-1">
                    <img
                      v-if="slotProps.option.sketch"
                      :src="`http://localhost:8055/assets/${slotProps.option.sketch}`"
                      :alt="slotProps.option.displayName"
                      class="w-8 h-8 object-cover rounded border flex-shrink-0"
                    >
                    <div
                      v-else
                      class="w-8 h-8 bg-gray-200 rounded border flex-shrink-0 flex items-center justify-center"
                    >
                      <i class="pi pi-image text-xs text-gray-400"/>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="font-medium truncate">{{ slotProps.option.displayName }}</div>
                      <div class="text-xs text-gray-500 truncate">{{ slotProps.option.title }}</div>
                    </div>
                  </div>
                </template>
                <template #value="slotProps">
                  <div v-if="slotProps.value && slotProps.value.length > 0" class="flex flex-wrap gap-1">
                    <div
                      v-for="elementId in slotProps.value"
                      :key="elementId"
                      class="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                    >
                      <img
                        v-if="getElementById(elementId)?.sketch"
                        :src="`http://localhost:8055/assets/${getElementById(elementId).sketch}`"
                        :alt="getElementDisplayName(elementId)"
                        class="w-4 h-4 object-cover rounded"
                      >
                      <div
                        v-else
                        class="w-4 h-4 bg-gray-200 rounded flex items-center justify-center"
                      >
                        <i class="pi pi-image text-xs text-gray-400"/>
                      </div>
                      <span class="truncate max-w-20">{{ getElementDisplayName(elementId) }}</span>
                    </div>
                  </div>
                  <span v-else class="text-gray-500">Выберите элементы</span>
                </template>
              </MultiSelect>

              <!-- Список выбранных элементов (без фона) -->
              <div v-if="column.elements.length > 0" class="selected-elements">
                <div
                  v-for="(elementId, elemIndex) in column.elements"
                  :key="`${elementId}-${elemIndex}`"
                  class="element-item relative p-1 mb-1 text-xs border-l-2 border-blue-300 pl-2 group cursor-move transition-all"
                  draggable="true"
                  @dragstart="onElementDragStart($event, rowIndex, colIndex, elemIndex)"
                  @dragend="onElementDragEnd"
                  @dragover.prevent="onElementDragOver"
                  @drop="onElementDrop($event, rowIndex, colIndex, elemIndex)"
                >
                  <div class="flex items-center gap-2 pr-8">
                    <img
                      v-if="getElementById(elementId)?.sketch"
                      :src="`http://localhost:8055/assets/${getElementById(elementId).sketch}`"
                      :alt="getElementDisplayName(elementId)"
                      class="w-6 h-6 object-cover rounded border flex-shrink-0"
                    >
                    <div
                      v-else
                      class="w-6 h-6 bg-gray-200 rounded border flex-shrink-0 flex items-center justify-center"
                    >
                      <i class="pi pi-image text-xs text-gray-400"/>
                    </div>
                    <span class="truncate">{{ getElementDisplayName(elementId) }}</span>
                  </div>

                  <!-- МАКСИМАЛЬНО КОМПАКТНЫЕ иконки управления 16x16px -->
                  <div class="absolute -right-1 top-0 flex opacity-0 group-hover:opacity-100 transition-opacity bg-white border border-gray-200 rounded shadow-sm">
                    <button
                      class="w-4 h-4 flex items-center justify-center text-red-500 hover:bg-red-50 border-r border-gray-200"
                      style="font-size: 8px; line-height: 1;"
                      title="Удалить"
                      @click="removeElementFromColumn(rowIndex, colIndex, elemIndex)"
                    >
                      ×
                    </button>
                    <button
                      class="w-4 h-4 flex items-center justify-center text-blue-500 hover:bg-blue-50 border-r border-gray-200"
                      style="font-size: 8px; line-height: 1;"
                      title="Дублировать"
                      @click="duplicateElement(rowIndex, colIndex, elemIndex)"
                    >
                      ⧉
                    </button>
                    <button
                      class="w-4 h-4 flex items-center justify-center text-gray-500 hover:bg-gray-50 border-r border-gray-200"
                      style="font-size: 8px; line-height: 1;"
                      :disabled="elemIndex === 0"
                      title="Вверх"
                      @click="moveElementUp(rowIndex, colIndex, elemIndex)"
                    >
                      ↑
                    </button>
                    <button
                      class="w-4 h-4 flex items-center justify-center text-gray-500 hover:bg-gray-50"
                      style="font-size: 8px; line-height: 1;"
                      :disabled="elemIndex === column.elements.length - 1"
                      title="Вниз"
                      @click="moveElementDown(rowIndex, colIndex, elemIndex)"
                    >
                      ↓
                    </button>
                  </div>
                </div>
              </div>


            </div>
          </div>
        </div>

        <!-- Кнопка добавления ряда -->
        <Button
          label="Добавить ряд"
          icon="pi pi-plus"
          class="text-xs p-button-outlined w-full"
          @click="addRow"
        />
      </div>
    </div>

    <!-- Панель управления блоком -->
    <div class="block-controls p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg mb-4">
      <div class="flex items-center gap-2">
        <InputText
          v-model="blockData.number"
          placeholder="Номер блока"
          class="w-24 text-xs"
        />
        <InputText
          v-model="blockData.title"
          placeholder="Название блока"
          class="w-48 text-xs"
        />
        <MultiSelect
          v-model="blockData.wblock_type"
          :options="blockTypeOptions"
          display="chip"
          class="text-xs w-40"
          filter
          placeholder="Тип"
          panel-class="text-xs"
        />
        <MultiSelect
          v-model="blockData.collection"
          :options="collectionOptions"
          display="chip"
          class="text-xs w-40"
          filter
          placeholder="Коллекция"
          panel-class="text-xs"
        />

        <!-- Viewport controls -->
        <div class="flex items-center gap-1 border rounded p-1 ml-auto">
          <Button
            v-for="viewport in viewports"
            :key="viewport.name"
            :label="viewport.name"
            :class="[
              'text-xs p-1 px-2',
              currentViewport.name === viewport.name ? 'p-button-primary' : 'p-button-outlined'
            ]"
            @click="setViewport(viewport)"
          />
        </div>

        <Button
          v-tooltip.top="'Обновить превью'"
          icon="pi pi-refresh"
          class="p-button-outlined text-xs"
          @click="updatePreview"
        />

        <Button
          v-tooltip.top="'Сохранить как блок'"
          icon="pi pi-save"
          class="p-button-success text-xs"
          :loading="saving"
          :disabled="!combinedHtml"
          @click="saveAsBlock"
        />
      </div>
    </div>

    <!-- АДАПТИВНОЕ превью с подстройкой высоты под контент -->
    <div class="preview-container">
      <div class="preview-frame border rounded-lg bg-white relative">
        <!-- Индикатор viewport -->
        <div class="viewport-indicator">
          {{ currentViewport.name }}: {{ currentViewport.width }} × {{ iframeHeight }}px
        </div>

        <iframe
          ref="previewFrame"
          :style="{
            width: currentViewport.width,
            height: iframeHeight + 'px',
            display: 'block',
            border: '1px solid #e5e7eb',
            borderRadius: '4px',
            overflow: 'hidden'
          }"
          class="mx-auto"
          frameborder="0"
          sandbox="allow-same-origin allow-scripts"
          @load="adjustIframeHeight"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'

// Импорт системы шаблонов
import {
  getAllTemplates,
  saveCustomTemplate,
  generateTemplateId,
  type GridTemplate
} from '~/data/grid-templates'

// Props
interface Props {
  selectedElements: any[]
  collectionOptions: string[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['saved'])

// Composables
const { createItems, updateItem } = useDirectusItems()
const toast = useToast()

// Refs
const previewFrame = ref<HTMLIFrameElement>()
const saving = ref(false)
const iframeHeight = ref(400)

// Данные блока
const blockData = ref({
  number: '',
  title: '',
  wblock_type: [] as string[],
  collection: [] as string[]
})

// Настройки сетки с уникальными ID
const gridSettings = ref({
  rows: [
    {
      id: generateId(),
      containerType: 'row',
      customClasses: '',
      columns: [
        {
          id: generateId(),
          width: 'col-md-12',
          customClasses: '',
          elements: [] as number[]
        }
      ]
    }
  ]
})

// История для undo/redo
const history = ref<any[]>([])
const historyPosition = ref(-1)

// Шаблоны сеток
const layoutTemplates = ref<GridTemplate[]>([])
const selectedTemplate = ref<string | null>(null)

// ИСПРАВЛЕННЫЕ Viewport настройки с правильными размерами
const viewports = ref([
  { name: 'Desktop', width: '1400px', height: '900px', maxHeight: '900px' },
  { name: 'Tablet', width: '768px', height: '1024px', maxHeight: '800px' },
  { name: 'Mobile', width: '375px', height: '667px', maxHeight: '600px' }
])
const currentViewport = ref(viewports.value[0])

// Drag & Drop состояние
const dragState = ref({
  type: '',
  data: null as any
})

// Опции
const containerOptions = ref([
  { label: 'div.row', value: 'row' },
  { label: 'div.row-fluid', value: 'row-fluid' },
  { label: 'div.container', value: 'container' },
  { label: 'div.container-fluid', value: 'container-fluid' }
])

const columnOptions = ref([
  { label: '1/1', value: 'col-md-12' },
  { label: '1/2', value: 'col-md-6' },
  { label: '1/3', value: 'col-md-4' },
  { label: '2/3', value: 'col-md-8' },
  { label: '1/4', value: 'col-md-3' },
  { label: '3/4', value: 'col-md-9' },
  { label: '1/6', value: 'col-md-2' },
  { label: '5/6', value: 'col-md-10' },
  { label: '1/12', value: 'col-md-1' },
  { label: '5/12', value: 'col-md-5' },
  { label: '7/12', value: 'col-md-7' },
  { label: '11/12', value: 'col-md-11' },
  { label: '1/5', value: 'col-md-1-5' },  
  { label: '2/5', value: 'col-md-2-5' },  
  { label: '3/5', value: 'col-md-3-5' },  
  { label: '4/5', value: 'col-md-4-5' }  
])

const blockTypeOptions = ref<string[]>([])

// Загрузка типов блоков из базы
const loadBlockTypes = async () => {
  try {
    const { getItems } = useDirectusItems()
    const blocks = await getItems({
      collection: 'wblock_proto',
      query: {
        fields: ['block_type'],
        limit: -1
      }
    })

    const types = new Set<string>()
    blocks.forEach((block: any) => {
      if (block.block_type && Array.isArray(block.block_type)) {
        block.block_type.forEach((type: string) => types.add(type))
      }
    })

    blockTypeOptions.value = Array.from(types).sort()

    // Добавляем стандартные типы если их нет
    const standardTypes = ['header', 'hero', 'features', 'about', 'services', 'portfolio',
      'testimonials', 'team', 'pricing', 'contact', 'footer', 'комбинация']

    standardTypes.forEach(type => {
      if (!blockTypeOptions.value.includes(type)) {
        blockTypeOptions.value.push(type)
      }
    })
  } catch (error) {
    console.error('Ошибка загрузки типов блоков:', error)
    // Fallback к стандартным типам
    blockTypeOptions.value = ['header', 'hero', 'features', 'about', 'services', 'portfolio',
      'testimonials', 'team', 'pricing', 'contact', 'footer', 'комбинация']
  }
}

// Вычисляемые свойства
const combinedHtml = computed(() => {
  return generateCombinationHtml()
})

const availableElements = computed(() => {
  return props.selectedElements.map(el => ({
    ...el,
    displayName: `${el.number || 'N/A'} - ${el.title || 'Без названия'}`,
    sketch: el.sketch // Убеждаемся что sketch передается
  }))
})

const canUndo = computed(() => historyPosition.value > 0)
const canRedo = computed(() => historyPosition.value < history.value.length - 1)

// Утилиты
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// История и undo/redo
const saveState = () => {
  const state = JSON.parse(JSON.stringify(gridSettings.value))

  // Удаляем состояния после текущей позиции
  if (historyPosition.value < history.value.length - 1) {
    history.value = history.value.slice(0, historyPosition.value + 1)
  }

  history.value.push(state)
  historyPosition.value = history.value.length - 1

  // Ограничиваем историю 50 состояниями
  if (history.value.length > 50) {
    history.value.shift()
    historyPosition.value--
  }
}

const undo = () => {
  if (canUndo.value) {
    historyPosition.value--
    gridSettings.value = JSON.parse(JSON.stringify(history.value[historyPosition.value]))
    updatePreview()
  }
}

const redo = () => {
  if (canRedo.value) {
    historyPosition.value++
    gridSettings.value = JSON.parse(JSON.stringify(history.value[historyPosition.value]))
    updatePreview()
  }
}

// Viewport управление
const setViewport = (viewport: any) => {
  currentViewport.value = viewport
}

// Методы управления сеткой
const addRow = () => {
  saveState()
  gridSettings.value.rows.push({
    id: generateId(),
    containerType: 'row',
    customClasses: '',
    columns: [{
      id: generateId(),
      width: 'col-12',
      customClasses: '',
      elements: []
    }]
  })
  updatePreview()
}

const removeRow = (index: number) => {
  if (gridSettings.value.rows.length > 1) {
    saveState()
    gridSettings.value.rows.splice(index, 1)
    updatePreview()
  }
}

const duplicateRow = (index: number) => {
  saveState()
  const row = JSON.parse(JSON.stringify(gridSettings.value.rows[index]))
  row.id = generateId()
  row.columns.forEach((col: any) => {
    col.id = generateId()
  })
  gridSettings.value.rows.splice(index + 1, 0, row)
  updatePreview()
}

const moveRowUp = (index: number) => {
  if (index > 0) {
    saveState()
    const row = gridSettings.value.rows.splice(index, 1)[0]
    gridSettings.value.rows.splice(index - 1, 0, row)
    updatePreview()
  }
}

const moveRowDown = (index: number) => {
  if (index < gridSettings.value.rows.length - 1) {
    saveState()
    const row = gridSettings.value.rows.splice(index, 1)[0]
    gridSettings.value.rows.splice(index + 1, 0, row)
    updatePreview()
  }
}

const addColumn = (rowIndex: number) => {
  saveState()
  gridSettings.value.rows[rowIndex].columns.push({
    id: generateId(),
    width: 'col-md-6',
    customClasses: '',
    elements: []
  })
  updatePreview()
}

const removeColumn = (rowIndex: number, colIndex: number) => {
  const row = gridSettings.value.rows[rowIndex]
  if (row.columns.length > 1) {
    saveState()
    row.columns.splice(colIndex, 1)
    updatePreview()
  }
}

const duplicateColumn = (rowIndex: number, colIndex: number) => {
  saveState()
  const column = JSON.parse(JSON.stringify(gridSettings.value.rows[rowIndex].columns[colIndex]))
  column.id = generateId()
  gridSettings.value.rows[rowIndex].columns.splice(colIndex + 1, 0, column)
  updatePreview()
}

const moveColumnLeft = (rowIndex: number, colIndex: number) => {
  if (colIndex > 0) {
    saveState()
    const columns = gridSettings.value.rows[rowIndex].columns
    const column = columns.splice(colIndex, 1)[0]
    columns.splice(colIndex - 1, 0, column)
    updatePreview()
  }
}

const moveColumnRight = (rowIndex: number, colIndex: number) => {
  const columns = gridSettings.value.rows[rowIndex].columns
  if (colIndex < columns.length - 1) {
    saveState()
    const column = columns.splice(colIndex, 1)[0]
    columns.splice(colIndex + 1, 0, column)
    updatePreview()
  }
}

// Методы для работы с элементами
const duplicateElement = (rowIndex: number, colIndex: number, elemIndex: number) => {
  saveState()
  const elements = gridSettings.value.rows[rowIndex].columns[colIndex].elements
  const elementId = elements[elemIndex]
  elements.splice(elemIndex + 1, 0, elementId)
  updatePreview()
}

const moveElementUp = (rowIndex: number, colIndex: number, elemIndex: number) => {
  if (elemIndex > 0) {
    saveState()
    const elements = gridSettings.value.rows[rowIndex].columns[colIndex].elements
    const temp = elements[elemIndex]
    elements[elemIndex] = elements[elemIndex - 1]
    elements[elemIndex - 1] = temp
    updatePreview()
  }
}

const moveElementDown = (rowIndex: number, colIndex: number, elemIndex: number) => {
  const elements = gridSettings.value.rows[rowIndex].columns[colIndex].elements
  if (elemIndex < elements.length - 1) {
    saveState()
    const temp = elements[elemIndex]
    elements[elemIndex] = elements[elemIndex + 1]
    elements[elemIndex + 1] = temp
    updatePreview()
  }
}

const getElementDisplayName = (elementId: number): string => {
  const element = props.selectedElements.find(el => el.id === elementId)
  return element ? `${element.number || 'N/A'} - ${element.title || 'Без названия'}` : `Элемент ${elementId}`
}

const getElementById = (elementId: number) => {
  return props.selectedElements.find(el => el.id === elementId)
}

const removeElementFromColumn = (rowIndex: number, colIndex: number, elemIndex: number) => {
  saveState()
  gridSettings.value.rows[rowIndex].columns[colIndex].elements.splice(elemIndex, 1)
  updatePreview()
}

// Методы для работы с кастомными классами
const getClassChips = (classString: string): string[] => {
  return classString.split(' ').filter(cls => cls.trim())
}

const removeCustomClass = (rowIndex: number, className: string) => {
  saveState()
  const row = gridSettings.value.rows[rowIndex]
  const classes = row.customClasses.split(' ').filter(cls => cls.trim() && cls !== className)
  row.customClasses = classes.join(' ')
  updatePreview()
}

const removeColumnCustomClass = (rowIndex: number, colIndex: number, className: string) => {
  saveState()
  const column = gridSettings.value.rows[rowIndex].columns[colIndex]
  const classes = column.customClasses.split(' ').filter(cls => cls.trim() && cls !== className)
  column.customClasses = classes.join(' ')
  updatePreview()
}

// ИСПРАВЛЕННАЯ система ширины колонок с правильными процентами для 1/5, 2/5, 3/5, 4/5
const getColumnWidthPercent = (colClass: string): number => {
  const widthMap: Record<string, number> = {
    'col-md-1': 8.33, 'col-1': 8.33,
    'col-md-1-5': 20,    // 1/5 = 20% (ИСПРАВЛЕНО!)
    'col-md-2': 16.66, 'col-2': 16.66,
    'col-md-2-5': 40,    // 2/5 = 40% (ИСПРАВЛЕНО!)
    'col-md-3': 25, 'col-3': 25,
    'col-md-3-5': 60,    // 3/5 = 60% (ИСПРАВЛЕНО!)
    'col-md-4': 33.33, 'col-4': 33.33,
    'col-md-4-5': 80,    // 4/5 = 80% (ИСПРАВЛЕНО!)
    'col-md-5': 41.66, 'col-5': 41.66,
    'col-md-6': 50, 'col-6': 50,
    'col-md-7': 58.33, 'col-7': 58.33,
    'col-md-8': 66.66, 'col-8': 66.66,
    'col-md-9': 75, 'col-9': 75,
    'col-md-10': 83.33, 'col-10': 83.33,
    'col-md-11': 91.66, 'col-11': 91.66,
    'col-md-12': 100, 'col-12': 100
  }
  return widthMap[colClass] || 100
}

// Функция для правильного Bootstrap класса в нативном конструкторе
const getBootstrapColumnClass = (colClass: string): string => {
  // Возвращаем оригинальный класс для Bootstrap логики
  return colClass
}

const getRowContainerStyle = (row: any): Record<string, string> => {
  // Вычисляем общую ширину всех колонок в ряду
  const totalWidth = row.columns.reduce((sum: number, col: any) => {
    return sum + getColumnWidthPercent(col.width)
  }, 0)

  // Если общая ширина больше 100%, используем flex-wrap для переноса
  if (totalWidth > 100) {
    return {
      display: 'flex',
      flexWrap: 'wrap',
      gap: '8px',
      alignItems: 'flex-start'
    }
  } else {
    return {
      display: 'flex',
      gap: '8px',
      alignItems: 'flex-start'
    }
  }
}

const getColumnStyle = (column: any, row: any): Record<string, string> => {
  const widthPercent = getColumnWidthPercent(column.width)

  // Вычисляем общую ширину всех колонок в ряду
  const totalWidth = row.columns.reduce((sum: number, col: any) => {
    return sum + getColumnWidthPercent(col.width)
  }, 0)

  // Если общая ширина больше 100%, корректируем ширину для переноса
  if (totalWidth > 100) {
    // Используем flex-basis для правильного переноса
    return {
      flexBasis: `calc(${widthPercent}% - 4px)`,
      minWidth: '200px', // Минимальная ширина для читаемости
      maxWidth: `${widthPercent}%`
    }
  } else {
    return {
      width: `${widthPercent}%`,
      flexShrink: '0'
    }
  }
}

// Drag & Drop методы
const onRowDragStart = (event: DragEvent, rowIndex: number) => {
  dragState.value = { type: 'row', data: { rowIndex } }
  event.dataTransfer!.effectAllowed = 'move'
}

const onRowDrop = (event: DragEvent, targetRowIndex: number) => {
  event.preventDefault()
  if (dragState.value.type === 'row') {
    const sourceRowIndex = dragState.value.data.rowIndex
    if (sourceRowIndex !== targetRowIndex) {
      saveState()
      const row = gridSettings.value.rows.splice(sourceRowIndex, 1)[0]
      gridSettings.value.rows.splice(targetRowIndex, 0, row)
      updatePreview()
    }
  }
  dragState.value = { type: '', data: null }
}

const onColumnDragStart = (event: DragEvent, rowIndex: number, colIndex: number) => {
  dragState.value = { type: 'column', data: { rowIndex, colIndex } }
  event.dataTransfer!.effectAllowed = 'move'
  event.dataTransfer!.setData('text/plain', 'column')

  // Добавляем визуальный индикатор
  const target = event.target as HTMLElement
  target.style.opacity = '0.5'
}

const onColumnDragOver = (event: DragEvent, rowIndex: number, colIndex: number) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }

  // Добавляем визуальный индикатор для drop зоны
  const target = event.currentTarget as HTMLElement
  target.style.backgroundColor = 'rgba(59, 130, 246, 0.1)'
  target.style.borderColor = '#3b82f6'
}

const onColumnDragLeave = (event: DragEvent, rowIndex: number, colIndex: number) => {
  const target = event.currentTarget as HTMLElement
  target.style.backgroundColor = ''
  target.style.borderColor = ''
}

const onColumnDragEnd = (event: DragEvent) => {
  const target = event.target as HTMLElement
  target.style.opacity = '1'
}

const onColumnDrop = (event: DragEvent, targetRowIndex: number, targetColIndex: number) => {
  event.preventDefault()

  // Очищаем визуальные индикаторы
  const target = event.currentTarget as HTMLElement
  target.style.backgroundColor = ''
  target.style.borderColor = ''

  if (dragState.value.type === 'column') {
    const { rowIndex: sourceRowIndex, colIndex: sourceColIndex } = dragState.value.data
    if (sourceRowIndex !== targetRowIndex || sourceColIndex !== targetColIndex) {
      saveState()
      const column = gridSettings.value.rows[sourceRowIndex].columns.splice(sourceColIndex, 1)[0]
      gridSettings.value.rows[targetRowIndex].columns.splice(targetColIndex, 0, column)
      updatePreview()
    }
  }
  dragState.value = { type: '', data: null }
}

const onElementDragStart = (event: DragEvent, rowIndex: number, colIndex: number, elemIndex: number) => {
  dragState.value = { type: 'element', data: { rowIndex, colIndex, elemIndex } }
  event.dataTransfer!.effectAllowed = 'move'
  event.dataTransfer!.setData('text/plain', 'element')

  // Добавляем визуальный индикатор
  const target = event.target as HTMLElement
  target.style.opacity = '0.5'
}

const onElementDragEnd = (event: DragEvent) => {
  const target = event.target as HTMLElement
  target.style.opacity = '1'
}

const onElementDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

const onElementDrop = (event: DragEvent, targetRowIndex: number, targetColIndex: number, targetElemIndex: number) => {
  event.preventDefault()
  if (dragState.value.type === 'element') {
    const { rowIndex: sourceRowIndex, colIndex: sourceColIndex, elemIndex: sourceElemIndex } = dragState.value.data

    if (sourceRowIndex !== targetRowIndex || sourceColIndex !== targetColIndex || sourceElemIndex !== targetElemIndex) {
      saveState()
      const elementId = gridSettings.value.rows[sourceRowIndex].columns[sourceColIndex].elements.splice(sourceElemIndex, 1)[0]
      gridSettings.value.rows[targetRowIndex].columns[targetColIndex].elements.splice(targetElemIndex, 0, elementId)
      updatePreview()
    }
  }
  dragState.value = { type: '', data: null }
}

// Методы для работы с шаблонами
const saveLayoutTemplate = async () => {
  const templateName = prompt('Введите название шаблона:')
  if (!templateName) return

  const template: GridTemplate = {
    id: generateTemplateId(),
    name: templateName,
    description: 'Пользовательский шаблон',
    category: 'custom',
    preview: '|████████████|',
    structure: {
      container: gridSettings.value.container,
      customClasses: gridSettings.value.customClasses,
      rows: gridSettings.value.rows.map(row => ({
        id: row.id,
        container: row.container,
        customClasses: row.customClasses,
        columns: row.columns.map(col => ({
          id: col.id,
          width: col.width,
          customClasses: col.customClasses,
          elements: []
        }))
      }))
    },
    tags: ['пользовательский'],
    created: new Date().toISOString(),
    updated: new Date().toISOString()
  }

  saveCustomTemplate(template)
  loadTemplatesFromStorage()

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: `Шаблон "${templateName}" сохранен`,
    life: 3000
  })
}

const loadLayoutTemplate = () => {
  if (!selectedTemplate.value) return

  const template = layoutTemplates.value.find(t => t.id === selectedTemplate.value)
  if (template) {
    saveState()

    // Загружаем структуру из шаблона
    gridSettings.value = {
      container: template.structure.container,
      customClasses: template.structure.customClasses,
      rows: template.structure.rows.map(row => ({
        id: row.id,
        container: row.container,
        customClasses: row.customClasses,
        columns: row.columns.map(col => ({
          id: col.id,
          width: col.width,
          customClasses: col.customClasses,
          elements: []
        }))
      }))
    }

    updatePreview()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Шаблон "${template.name}" загружен`,
      life: 3000
    })
  }
}

const loadTemplatesFromStorage = () => {
  layoutTemplates.value = getAllTemplates()
}

const generateCombinationHtml = (): string => {
  if (!props.selectedElements.length) return ''

  let html = `<section>\n`

  for (const row of gridSettings.value.rows) {
    const containerClass = row.containerType
    const customClasses = row.customClasses ? ` ${row.customClasses}` : ''

    html += `  <div class="${containerClass}${customClasses}">\n`

    for (const col of row.columns) {
      const columnClasses = col.customClasses ? `${col.width} ${col.customClasses}` : col.width
      html += `    <div class="${columnClasses}">\n`

      // Добавляем элементы в колонку в заданном порядке
      if (col.elements.length > 0) {
        for (const elementId of col.elements) {
          const element = props.selectedElements.find(el => el.id === elementId)
          if (element?.html) {
            html += `      ${element.html}\n`
          }
        }
      } else {
        // Если элементы не назначены, используем автоматическое распределение
        const totalAssignedElements = gridSettings.value.rows
          .flatMap(r => r.columns)
          .reduce((sum, c) => sum + c.elements.length, 0)

        if (totalAssignedElements === 0) {
          // Автоматическое распределение только если нет назначенных элементов
          const elementsPerColumn = Math.ceil(props.selectedElements.length / getTotalColumns())
          const startIndex = getColumnStartIndex(row, col)

          for (let i = 0; i < elementsPerColumn; i++) {
            const elementIndex = startIndex + i
            if (elementIndex < props.selectedElements.length) {
              const element = props.selectedElements[elementIndex]
              if (element?.html) {
                html += `      ${element.html}\n`
              }
            }
          }
        }
      }

      html += `    </div>\n`
    }

    html += `  </div>\n`
  }

  html += `</section>`

  return html
}

const getColumnStartIndex = (currentRow: any, currentCol: any): number => {
  let index = 0

  for (const row of gridSettings.value.rows) {
    for (const col of row.columns) {
      if (row === currentRow && col === currentCol) {
        return index
      }
      index += Math.ceil(props.selectedElements.length / getTotalColumns())
    }
  }

  return index
}

const getTotalColumns = (): number => {
  return gridSettings.value.rows.reduce((total, row) => total + row.columns.length, 0)
}

const adjustIframeHeight = () => {
  if (!previewFrame.value) return

  try {
    const iframe = previewFrame.value
    const doc = iframe.contentDocument || iframe.contentWindow?.document

    if (doc) {
      // АДАПТИВНАЯ ВЫСОТА - подстраиваем под контент БЕЗ лишних стилей
      setTimeout(() => {
        const body = doc.body
        const html = doc.documentElement

        if (body && html) {
          // Получаем реальную высоту контента
          const contentHeight = Math.max(
            body.scrollHeight,
            body.offsetHeight,
            html.clientHeight,
            html.scrollHeight,
            html.offsetHeight
          )

          // Устанавливаем высоту под контент с минимумом
          const newHeight = Math.max(contentHeight, 300)
          iframeHeight.value = newHeight

          console.log(`Превью ${currentViewport.value.name}: ${currentViewport.value.width} × ${newHeight}px (контент: ${contentHeight}px)`)
        }
      }, 500) // Увеличиваем задержку для полной загрузки
    }
  } catch (error) {
    console.warn('Не удалось настроить iframe:', error)
    iframeHeight.value = 400 // fallback
  }
}

const updatePreview = async () => {
  if (!previewFrame.value) return

  const html = generateCombinationHtml()
  if (!html) return

  // Собираем CSS и JS от всех элементов
  const allCss = props.selectedElements.map(el => el.css || '').join('\n')
  const allJs = props.selectedElements.map(el => el.js || '').join('\n')

  const fullHtml = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Combination Preview</title>
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
      <style>
        body { padding: 20px; background: #f8f9fa; }
        .container-fluid { background: white; padding: 20px; border-radius: 8px; }
      </style>
      ${allCss}
    </head>
    <body>
      ${html}
      ${allJs}
    </body>
    </html>
  `

  // Обновляем iframe
  const iframe = previewFrame.value
  iframe.srcdoc = fullHtml

  // Настраиваем высоту после загрузки
  iframe.onload = adjustIframeHeight
}

const saveAsBlock = async () => {
  if (!combinedHtml.value) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Нет данных для сохранения',
      life: 3000
    })
    return
  }

  saving.value = true

  try {
    // Собираем CSS и JS от всех элементов
    const allCss = props.selectedElements.map(el => el.css || '').join('\n')
    const allJs = props.selectedElements.map(el => el.js || '').join('\n')

    // Создаем данные блока
    const newBlock = {
      number: blockData.value.number,
      title: blockData.value.title,
      collection: blockData.value.collection,
      wblock_type: blockData.value.wblock_type,
      html: combinedHtml.value,
      css: allCss,
      js: allJs,
      status: 'draft'
    }

    // Сохраняем блок
    const savedBlock: any = await createItems({
      collection: 'wblock_proto',
      items: [newBlock]
    })

    const blockId = savedBlock[0].id
    console.log('✅ Блок создан с ID:', blockId)

    // 1. Анализируем HTML
    const analysisResult: any = await $fetch('/api/batch-analyze-html', {
      method: 'POST',
      body: { records: [{ id: blockId, html: combinedHtml.value }] }
    })
    const results = analysisResult.results || []

    // 2. Генерируем скриншот КАК В wblock-proto2
    console.log('Генерируем скриншот для блока:', blockId)

    // Создаем полный HTML для скриншота
    const blockFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${blockData.value.title || 'Combination Block'}</title>
  

    ${allCss}
  
</head>
<body>
  ${combinedHtml.value}
  ${allJs}
</body>
</html>`

    // Создаем скриншот через temp API
    const response = await $fetch('/api/capture-html-screenshot-temp', {
      method: 'POST',
      body: {
        html: blockFullHtml,
        width: 1400,
        height: 800
      },
      responseType: 'blob'
    })

    // Загружаем скриншот в Directus
    const blob = response as Blob
    const sanitizedTitle = (blockData.value.title || 'combination').replace(/[^a-zA-Z0-9\-_]/g, '_')
    const file = new File([blob], `block_${blockData.value.number || 'combo'}_${sanitizedTitle}.png`, { type: 'image/png' })

    const formData = new FormData()
    formData.append('file', file)

    const uploadResponse = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!uploadResponse.ok) {
      throw new Error(`Ошибка загрузки файла для блока ${blockId}`)
    }

    const { data: fileData } = await uploadResponse.json()
    console.log('✅ Скриншот загружен в Directus:', fileData.id)

    // 3. Конвертируем в HBS шаблон
    const templateResult: any = await $fetch('/api/html-to-template', {
      method: 'POST',
      body: { html: combinedHtml.value }
    })

    // 4. Обновляем блок с результатами анализа
    const updateData: any = {
      status: 'published'
    }

    if (results.length > 0) {
      updateData.block_type = results[0].blockTypes || blockData.value.wblock_type
      updateData.composition = results[0].treeStructure
      updateData.layout = results[0].layout
      updateData.elements = results[0].elements
      updateData.features = results[0].features
      updateData.graphics = results[0].graphics
    } else {
      // Если анализ не удался, используем данные из формы
      updateData.block_type = blockData.value.wblock_type
    }

    // Обновляем блок с sketch ID
    updateData.sketch = fileData.id
    console.log('✅ УСПЕШНО: Добавляем sketch в updateData:', fileData.id)

    if (templateResult.success) {
      updateData.hbs = templateResult.hbs
      updateData.json = JSON.stringify(templateResult.variables, null, 2)
    }

    console.log('🔄 Обновляем блок с ID:', blockId)
    console.log('🔄 Данные для обновления:', JSON.stringify(updateData, null, 2))

    const updatedBlock = await updateItem({
      collection: 'wblock_proto',
      id: blockId,
      item: updateData
    })

    console.log('✅ Блок обновлен:', updatedBlock)

    // Проверяем что sketch действительно сохранился
    const { getItems } = useDirectusItems()
    const checkBlock = await getItems({
      collection: 'wblock_proto',
      params: {
        filter: { id: { _eq: blockId } },
        fields: ['id', 'sketch', 'title']
      }
    })
    console.log('🔍 Проверка сохраненного блока:', checkBlock)

    // 5. Создаем связи с элементами через junction table
    const elementIds = [...new Set(
      gridSettings.value.rows
        .flatMap(row => row.columns)
        .flatMap(col => col.elements)
    )]

    if (elementIds.length > 0) {
      const junctionItems = elementIds.map(elementId => ({
        wblock_proto_id: blockId,
        welem_proto_id: elementId
      }))

      await createItems({
        collection: 'wblock_proto_welem_proto',
        items: junctionItems
      })
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Комбинация сохранена и обработана',
      life: 3000
    })

    emit('saved', savedBlock[0])

  } catch (error) {
    console.error('Ошибка сохранения блока:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блок',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}

// Watchers
watch(() => props.selectedElements, () => {
  updatePreview()
}, { deep: true })

// Lifecycle
onMounted(async () => {
  // Инициализируем историю
  saveState()

  // Загружаем шаблоны
  loadTemplatesFromStorage()

  // Загружаем типы блоков
  await loadBlockTypes()

  // Обновляем превью
  updatePreview()
})
</script>

<style scoped>
.combination-preview {
  max-width: 100%;
}

.header-section {
  background: #f8f9fa;
}

.grid-settings {
  background: #f8f9fa;
}

.row-item {
  background: white;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.row-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.column-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  min-height: 150px;
  transition: all 0.2s ease;
  position: relative;
}

.column-item:hover {
  border-color: #28a745;
  box-shadow: 0 2px 4px rgba(40,167,69,0.1);
}

.element-item {
  cursor: move;
  transition: all 0.2s ease;
  background: transparent;
}

.element-item:hover {
  background: rgba(59, 130, 246, 0.05);
}

.custom-classes-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.preview-frame {
  background: white;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

/* Кнопки видны только при наведении */
.group .opacity-0 {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.group:hover .opacity-0 {
  opacity: 1;
}

/* Компактные кнопки без рамок */
:deep(.p-button-text) {
  border: none !important;
  background: transparent !important;
  padding: 2px !important;
}

:deep(.p-button-text:hover) {
  background: rgba(59, 130, 246, 0.1) !important;
}

/* Drag & Drop визуальные эффекты */
.row-item[draggable="true"]:hover {
  cursor: move;
}

.column-item[draggable="true"]:hover {
  cursor: move;
}

.element-item[draggable="true"]:hover {
  cursor: move;
}

/* Полноразмерный превью контейнер */
.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: visible; /* Убираем ограничения overflow */
}

.preview-frame {
  display: flex;
  flex-direction: column;
  overflow: visible; /* Убираем ограничения overflow */
  background: white;
  border-radius: 8px;
}

/* Viewport indicators */
.viewport-indicator {
  position: absolute;
  top: -20px;
  left: 0;
  font-size: 10px;
  color: #6c757d;
  background: white;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #dee2e6;
}

/* Bootstrap CSS для нативного конструктора */
.columns-container.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.columns-container .column-item {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

/* Стандартные Bootstrap колонки */
.columns-container .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.columns-container .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.columns-container .col-md-3 { flex: 0 0 25%; max-width: 25%; }
.columns-container .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.columns-container .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.columns-container .col-md-6 { flex: 0 0 50%; max-width: 50%; }
.columns-container .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.columns-container .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.columns-container .col-md-9 { flex: 0 0 75%; max-width: 75%; }
.columns-container .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.columns-container .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.columns-container .col-md-12 { flex: 0 0 100%; max-width: 100%; }

/* Дробные колонки 1/5, 2/5, 3/5, 4/5 */
.columns-container .col-md-1-5 { flex: 0 0 20%; max-width: 20%; }    /* 1/5 = 20% */
.columns-container .col-md-2-5 { flex: 0 0 40%; max-width: 40%; }    /* 2/5 = 40% */
.columns-container .col-md-3-5 { flex: 0 0 60%; max-width: 60%; }    /* 3/5 = 60% */
.columns-container .col-md-4-5 { flex: 0 0 80%; max-width: 80%; }    /* 4/5 = 80% */

/* Дробные Bootstrap колонки для превью */
:deep(.col-md-1-5) { flex: 0 0 20%; max-width: 20%; }    /* 1/5 = 20% */
:deep(.col-md-2-5) { flex: 0 0 40%; max-width: 40%; }    /* 2/5 = 40% */
:deep(.col-md-3-5) { flex: 0 0 60%; max-width: 60%; }    /* 3/5 = 60% */
:deep(.col-md-4-5) { flex: 0 0 80%; max-width: 80%; }    /* 4/5 = 80% */

/* Адаптивные варианты для превью */
@media (min-width: 768px) {
  :deep(.col-md-1-5) { flex: 0 0 20%; max-width: 20%; }
  :deep(.col-md-2-5) { flex: 0 0 40%; max-width: 40%; }
  :deep(.col-md-3-5) { flex: 0 0 60%; max-width: 60%; }
  :deep(.col-md-4-5) { flex: 0 0 80%; max-width: 80%; }
}
</style>
