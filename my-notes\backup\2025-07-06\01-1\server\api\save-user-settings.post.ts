import { promises as fs } from 'fs'
import { join } from 'path'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { type, data, filename } = body

    if (!type || !data || !filename) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Отсутствуют обязательные параметры: type, data, filename'
      })
    }

    // Определяем директорию для сохранения
    const baseDir = join(process.cwd(), 'storage', 'user-settings')
    
    // Создаем директорию если не существует
    try {
      await fs.access(baseDir)
    } catch {
      await fs.mkdir(baseDir, { recursive: true })
    }

    // Создаем поддиректорию по типу
    const typeDir = join(baseDir, type)
    try {
      await fs.access(typeDir)
    } catch {
      await fs.mkdir(typeDir, { recursive: true })
    }

    // Сохраняем файл
    const filePath = join(typeDir, filename)
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8')

    console.log(`✅ Настройки сохранены: ${filePath}`)

    return {
      success: true,
      message: 'Настройки успешно сохранены',
      filename,
      path: filePath,
      type
    }

  } catch (error: any) {
    console.error('❌ Ошибка сохранения настроек:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: `Ошибка сохранения настроек: ${error.message}`
    })
  }
})
