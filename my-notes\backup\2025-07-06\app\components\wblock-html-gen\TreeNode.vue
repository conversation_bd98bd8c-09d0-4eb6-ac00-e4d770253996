<template>
  <div class="pl-[calc(var(--level)*1rem)] mb-2" draggable="true" @dragstart="onDragStart" @dragover.prevent @drop="onDrop">
    <div class="flex items-center gap-2 p-2 bg-white rounded shadow-sm hover:bg-gray-50">
      <div class="flex-1" @click="$emit('select', node)">
        {{ node.data.name }}
      </div>
      <div class="flex gap-1">
        <Button
          icon="pi pi-arrow-up"
          class="p-button-text p-button-sm"
          :disabled="!canMoveUp"
          @click="$emit('move', node, 'up')"
        />
        <Button
          icon="pi pi-arrow-down"
          class="p-button-text p-button-sm"
          :disabled="!canMoveDown"
          @click="$emit('move', node, 'down')"
        />
        <Button
          icon="pi pi-angle-double-right"
          class="p-button-text p-button-sm"
          :disabled="!canIncreaseLevel"
          @click="$emit('increase-level', node)"
        />
        <Button
          icon="pi pi-angle-double-left"
          class="p-button-text p-button-sm"
          :disabled="!canDecreaseLevel"
          @click="$emit('decrease-level', node)"
        />
      </div>
    </div>
    
    <div v-if="node.children && node.children.length > 0">
      <TreeNode
        v-for="child in node.children"
        :key="child.key"
        :node="child"
        :can-move-up="canMoveUp"
        :can-move-down="canMoveDown"
        :can-increase-level="canIncreaseLevel"
        :can-decrease-level="canDecreaseLevel"
        @select="$emit('select', $event)"
        @move="(node, direction) => $emit('move', node, direction)"
        @increase-level="$emit('increase-level', $event)"
        @decrease-level="$emit('decrease-level', $event)"
      />
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(['select', 'move', 'increase-level', 'decrease-level']);

const props = defineProps({
  node: {
    type: Object,
    required: true
  },
  canMoveUp: {
    type: Boolean,
    default: false
  },
  canMoveDown: {
    type: Boolean,
    default: false
  },
  canIncreaseLevel: {
    type: Boolean,
    default: false
  },
  canDecreaseLevel: {
    type: Boolean,
    default: false
  }
});

function onDragStart(event) {
  event.dataTransfer.setData('text/plain', JSON.stringify(props.node));
}

function onDrop(event) {
  event.preventDefault();
  const droppedNode = JSON.parse(event.dataTransfer.getData('text/plain'));
  
  if (droppedNode.key === props.node.key) return;
  
  // Определяем позицию дропа относительно центра элемента
  const rect = event.currentTarget.getBoundingClientRect();
  const mouseY = event.clientY;
  const elementCenterY = rect.top + rect.height / 2;
  
  if (mouseY < elementCenterY) {
    // Вставить перед текущим элементом
    emit('move', droppedNode, 'up');
  } else {
    // Вставить после текущего элемента
    emit('move', droppedNode, 'down');
  }
}

</script>

<style scoped>
.pl-\[calc\(var\(--level\)\*1rem\)\] {
  --level: v-bind('node.data.level');
}
</style>