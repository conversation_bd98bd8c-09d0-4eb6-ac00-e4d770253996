<template>
  <div class="content-selector">
    <h4 class="text-md font-semibold mb-3">Выбор и настройка контента</h4>
    
    <!-- Выбор типа и варианта контента -->
    <div class="content-type-selection mb-4">
      <div class="flex gap-2 mb-3">
        <Dropdown
          v-model="selectedContentType"
          :options="contentTypes"
          option-label="label"
          option-value="value"
          placeholder="Тип контента"
          class="text-xs"
          @change="updateContentLibrary"
        />
        <Dropdown
          v-model="selectedContentVariant"
          :options="contentVariants"
          option-label="label"
          option-value="value"
          placeholder="Вариант длины"
          class="text-xs"
          @change="updateContentLibrary"
        />
      </div>
    </div>

    <!-- Библио<PERSON>е<PERSON>а контента -->
    <div v-if="selectedContentType" class="content-library mb-4">
      <h5 class="text-sm font-semibold mb-2">Библиотека контента ({{ currentContentLibrary.length }} вариантов)</h5>
      
      <div class="content-items grid grid-cols-2 gap-2 max-h-64 overflow-y-auto">
        <div
          v-for="(item, index) in currentContentLibrary"
          :key="index"
          class="content-item p-2 border rounded cursor-pointer transition-all"
          :class="{
            'border-blue-500 bg-blue-50': selectedContentItems.includes(index),
            'border-gray-200 hover:border-gray-300': !selectedContentItems.includes(index)
          }"
          @click="toggleContentItem(index)"
        >
          <div class="text-xs font-medium mb-1">{{ getContentTitle(item, index) }}</div>
          <div class="text-xs text-gray-500 line-clamp-2">{{ getContentPreview(item) }}</div>
        </div>
      </div>
      
      <div class="flex gap-2 mt-2">
        <Button
          label="Выбрать все"
          class="text-xs p-button-outlined"
          @click="selectAllContent"
        />
        <Button
          label="Очистить выбор"
          class="text-xs p-button-outlined"
          @click="clearContentSelection"
        />
        <Button
          label="Случайный выбор (5)"
          class="text-xs p-button-outlined"
          @click="selectRandomContent"
        />
      </div>
    </div>

    <!-- Кастомный контент -->
    <div class="custom-content mb-4">
      <div class="flex items-center gap-2 mb-2">
        <h5 class="text-sm font-semibold">Кастомный контент</h5>
        <Button
          icon="pi pi-plus"
          class="p-button-outlined text-xs p-1"
          @click="showCustomContentEditor = !showCustomContentEditor"
        />
      </div>
      
      <div v-if="showCustomContentEditor" class="custom-content-editor p-3 border rounded bg-white">
        <div class="grid grid-cols-1 gap-3">
          <!-- Тип кастомного контента -->
          <div>
            <label class="text-xs font-medium block mb-1">Тип контента</label>
            <Dropdown
              v-model="customContent.type"
              :options="contentTypes"
              option-label="label"
              option-value="value"
              class="text-xs w-full"
            />
          </div>
          
          <!-- Контент в зависимости от типа -->
          <div v-if="customContent.type === 'headings'">
            <label class="text-xs font-medium block mb-1">Заголовки (по одному на строку)</label>
            <Textarea
              v-model="customContent.headings"
              placeholder="Заголовок 1&#10;Заголовок 2&#10;Заголовок 3"
              class="text-xs w-full"
              rows="4"
            />
          </div>
          
          <div v-if="customContent.type === 'paragraphs'">
            <label class="text-xs font-medium block mb-1">Параграфы (разделенные пустой строкой)</label>
            <Textarea
              v-model="customContent.paragraphs"
              placeholder="Первый параграф текста.&#10;&#10;Второй параграф текста."
              class="text-xs w-full"
              rows="6"
            />
          </div>
          
          <div v-if="customContent.type === 'buttons'">
            <label class="text-xs font-medium block mb-1">Тексты кнопок (по одному на строку)</label>
            <Textarea
              v-model="customContent.buttons"
              placeholder="Кнопка 1&#10;Кнопка 2&#10;Кнопка 3"
              class="text-xs w-full"
              rows="4"
            />
          </div>
          
          <div v-if="customContent.type === 'images'">
            <label class="text-xs font-medium block mb-1">URL изображений (по одному на строку)</label>
            <Textarea
              v-model="customContent.images"
              placeholder="https://example.com/image1.jpg&#10;https://example.com/image2.jpg"
              class="text-xs w-full"
              rows="4"
            />
          </div>
          
          <div v-if="customContent.type === 'lists'">
            <label class="text-xs font-medium block mb-1">Списки (элементы через запятую, списки через пустую строку)</label>
            <Textarea
              v-model="customContent.lists"
              placeholder="Элемент 1, Элемент 2, Элемент 3&#10;&#10;Пункт А, Пункт Б, Пункт В"
              class="text-xs w-full"
              rows="6"
            />
          </div>
          
          <!-- Действия -->
          <div class="flex gap-2">
            <Button
              label="Добавить в библиотеку"
              class="text-xs"
              @click="addCustomContent"
            />
            <Button
              label="Применить к элементам"
              class="text-xs p-button-outlined"
              @click="applyCustomContent"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Предпросмотр переменных -->
    <div v-if="selectedContentType && selectedContentItems.length > 0" class="content-preview mb-4">
      <h5 class="text-sm font-semibold mb-2">Предпросмотр переменных</h5>
      <div class="preview-variables p-2 border rounded bg-gray-50 text-xs">
        <div class="grid grid-cols-2 gap-2">
          <div v-for="(value, key) in previewVariables" :key="key">
            <span class="font-medium">{{ key }}:</span>
            <span class="text-gray-600 ml-1">{{ getVariablePreview(value) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Кнопки действий -->
    <div class="flex gap-2">
      <Button
        label="Создать с выбранным контентом"
        class="text-xs"
        :disabled="!selectedContentType || selectedContentItems.length === 0"
        @click="createContentElements"
      />
      <Button
        label="Генерировать случайный контент"
        class="text-xs p-button-outlined"
        @click="generateRandomContent"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { contentLibrary, generateContentData, universalVariables } from '~/utils/content-mappings'

// Props
interface Props {
  selectedElements: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['contentCreated'])

// Данные
const contentTypes = ref([
  { label: 'Заголовки', value: 'headings' },
  { label: 'Параграфы', value: 'paragraphs' },
  { label: 'Кнопки', value: 'buttons' },
  { label: 'Изображения', value: 'images' },
  { label: 'Списки', value: 'lists' }
])

const contentVariants = ref([
  { label: 'Короткий', value: 'short' },
  { label: 'Средний', value: 'medium' },
  { label: 'Длинный', value: 'long' }
])

const selectedContentType = ref('')
const selectedContentVariant = ref('medium')
const selectedContentItems = ref<number[]>([])
const currentContentLibrary = ref<any[]>([])
const showCustomContentEditor = ref(false)

const customContent = ref({
  type: 'headings',
  headings: '',
  paragraphs: '',
  buttons: '',
  images: '',
  lists: ''
})

// Вычисляемые свойства
const previewVariables = computed(() => {
  if (!selectedContentType.value || selectedContentItems.value.length === 0) {
    return {}
  }
  
  const selectedItems = selectedContentItems.value.map(index => currentContentLibrary.value[index])
  return generateContentDataFromSelected(selectedItems)
})

// Методы
const updateContentLibrary = () => {
  if (selectedContentType.value && selectedContentVariant.value) {
    currentContentLibrary.value = contentLibrary[selectedContentType.value]?.[selectedContentVariant.value] || []
    selectedContentItems.value = []
  }
}

const toggleContentItem = (index: number) => {
  const itemIndex = selectedContentItems.value.indexOf(index)
  if (itemIndex > -1) {
    selectedContentItems.value.splice(itemIndex, 1)
  } else {
    selectedContentItems.value.push(index)
  }
}

const selectAllContent = () => {
  selectedContentItems.value = currentContentLibrary.value.map((_, index) => index)
}

const clearContentSelection = () => {
  selectedContentItems.value = []
}

const selectRandomContent = () => {
  const count = Math.min(5, currentContentLibrary.value.length)
  const shuffled = [...Array(currentContentLibrary.value.length).keys()].sort(() => 0.5 - Math.random())
  selectedContentItems.value = shuffled.slice(0, count)
}

const getContentTitle = (item: any, index: number): string => {
  if (Array.isArray(item)) {
    return `Список ${index + 1}`
  }
  return `${selectedContentType.value} ${index + 1}`
}

const getContentPreview = (item: any): string => {
  if (Array.isArray(item)) {
    return item.join(', ')
  }
  return item.toString().substring(0, 100) + (item.toString().length > 100 ? '...' : '')
}

const getVariablePreview = (value: any): string => {
  if (Array.isArray(value)) {
    return `[${value.slice(0, 2).join(', ')}${value.length > 2 ? '...' : ''}]`
  }
  return value.toString().substring(0, 30) + (value.toString().length > 30 ? '...' : '')
}

const generateContentDataFromSelected = (selectedItems: any[]) => {
  const baseData = { ...universalVariables }
  
  if (selectedItems.length === 0) return baseData
  
  // Используем первый выбранный элемент как основу
  const firstItem = selectedItems[0]
  
  switch (selectedContentType.value) {
    case 'headings':
      baseData.title = firstItem
      baseData.heading = firstItem
      baseData.subtitle = selectedItems[1] || firstItem
      break
    case 'paragraphs':
      baseData.text = firstItem
      baseData.description = firstItem
      baseData.content = firstItem
      break
    case 'buttons':
      baseData.button = firstItem
      baseData.buttonText = firstItem
      break
    case 'images':
      baseData.image = firstItem
      baseData.imageUrl = firstItem
      break
    case 'lists':
      baseData.list = firstItem
      baseData.items = firstItem
      break
  }
  
  return baseData
}

const addCustomContent = () => {
  const type = customContent.value.type
  const content = customContent.value[type]
  
  if (!content) return
  
  let parsedContent: any[]
  
  switch (type) {
    case 'headings':
    case 'buttons':
      parsedContent = content.split('\n').filter(line => line.trim())
      break
    case 'paragraphs':
      parsedContent = content.split('\n\n').filter(para => para.trim())
      break
    case 'images':
      parsedContent = content.split('\n').filter(url => url.trim())
      break
    case 'lists':
      parsedContent = content.split('\n\n').map(list => 
        list.split(',').map(item => item.trim()).filter(item => item)
      ).filter(list => list.length > 0)
      break
    default:
      return
  }
  
  // Добавляем в текущую библиотеку
  currentContentLibrary.value.push(...parsedContent)
  
  // Очищаем форму
  customContent.value[type] = ''
  showCustomContentEditor.value = false
}

const applyCustomContent = () => {
  addCustomContent()
  // Выбираем добавленные элементы
  const startIndex = currentContentLibrary.value.length - 1
  selectedContentItems.value = [startIndex]
  createContentElements()
}

const generateRandomContent = () => {
  if (!selectedContentType.value) return
  
  const randomVariant = contentVariants.value[Math.floor(Math.random() * contentVariants.value.length)].value
  selectedContentVariant.value = randomVariant
  updateContentLibrary()
  selectRandomContent()
}

const createContentElements = () => {
  if (!selectedContentType.value || selectedContentItems.value.length === 0) return
  
  const selectedItems = selectedContentItems.value.map(index => currentContentLibrary.value[index])
  
  emit('contentCreated', {
    contentType: selectedContentType.value,
    contentVariant: selectedContentVariant.value,
    selectedItems,
    contentData: generateContentDataFromSelected(selectedItems)
  })
}

// Watchers
watch([selectedContentType, selectedContentVariant], () => {
  updateContentLibrary()
})
</script>

<style scoped>
.content-selector {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.content-item {
  min-height: 60px;
}

.content-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.custom-content-editor {
  background: #ffffff;
  border: 1px solid #dee2e6;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.preview-variables {
  max-height: 150px;
  overflow-y: auto;
}
</style>
