<template>
  <div class="flex h-screen">
    <!-- Основной контент -->
    <div
      class="flex-1 overflow-hidden flex flex-col"
      :class="{ 'pr-[30rem]': sidebarVisible }"
    >
      <div class="flex justify-between mb-1 p-1">
        <div class="flex gap-2">
          <Button
            v-tooltip.top="'Обновить данные'"
            icon="pi pi-refresh"
            class="p-button-rounded p-button-text p-button-sm"
            :disabled="loading"
            aria-label="Обновить"
            @click="loadData"
          />
          <span class="p-input-icon-left">
            <InputText
              v-model="globalFilterValue"
              placeholder="Поиск..."
              icon="pi pi-search"
              class="w-full"
              style="font-size: 12px"
              @input="onGlobalFilterChange"
            />
          </span>
          <MultiSelect
            v-model="selectedElemTypes"
            :options="availableElemTypes"
            placeholder="Фильтр по типам элементов"
            display="chip"
            class="w-64 text-xs"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
            @change="applyElemTypesFilter"
          />
          <MultiSelect
            v-model="selectedCollections"
            :options="availableCollections"
            placeholder="Фильтр по коллекциям"
            display="chip"
            class="w-64 text-xs"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
            @change="applyCollectionsFilter"
          />
        </div>
        <div class="flex gap-2">
          <Button
            v-tooltip.bottom="'Массовое добавление'"
            icon="pi pi-plus-circle"
            class="text-xs p-button-success"
            @click="openBulkCreate"
          />
          <Button
            v-tooltip.bottom="'Массовое редактирование'"
            icon="pi pi-pencil"
            class="text-xs p-button-primary"
            :disabled="!selectedItems.length"
            @click="openBulkEdit"
          />
          <Button
            v-tooltip.bottom="'Создать'"
            icon="pi pi-plus"
            class="p-button-success text-xs"
            @click="openCreateDialog"
          />
          <Button
            v-tooltip.bottom="'Анализ'"
            icon="pi pi-cog"
            class="p-button-warning text-xs"
            :disabled="!selectedItems.length"
            @click="bulkAnalyzeHtml"
          />
          <Button
            v-tooltip.bottom="'Эскизы'"
            icon="pi pi-images"
            class="p-button-info text-xs"
            :disabled="!selectedItems.length"
            @click="bulkUploadSketches"
          />
          <Button
            v-tooltip.bottom="'Скрины'"
            icon="pi pi-camera"
            class="p-button-secondary text-xs"
            :disabled="!selectedItems.length"
            @click="bulkGenerateScreenshots"
          />
          <Button
            v-tooltip.bottom="'Дублировать выбранные'"
            icon="pi pi-copy"
            class="p-button-primary text-xs"
            :disabled="!selectedItems.length"
            @click="bulkDuplicateItems"
          />
          <Button
            v-tooltip.bottom="'Удалить выбранные'"
            icon="pi pi-trash"
            class="p-button-danger text-xs"
            :disabled="!selectedItems.length"
            @click="bulkDeleteItems"
          />
          <ProgressBar
            v-if="loading"
            mode="indeterminate"
            style="height: 6px"
            class="mt-2"
          />
        </div>
      </div>

      <!-- Область массовых форм -->
      <BulkFormContainer
        :visible="bulkFormVisible"
        :mode="bulkFormMode"
        collection="welem_proto"
        :field-config="welemBulkFieldConfig"
        :selected-items="selectedItems"
        @close="closeBulkForm"
        @saved="onBulkFormSaved"
      />

      <div class="flex-1 overflow-auto">
        <DataTable
          v-model:selection="selectedItems"
          v-model:sort-field="sortField"
          v-model:sort-order="sortOrder"
          :value="filteredItems"
          selection-mode="multiple"
          scrollable
          scroll-height="calc(100vh - 70px)"
          :virtual-scroller-options="{ itemSize: 44 }"
          filter-display="menu"
          :global-filter-fields="globalFilterFields"
          :loading="loading"
          data-key="id"
          striped-rows
          responsive-layout="scroll"
          class="p-datatable-sm text-[13px]"
          style="
            --highlight-bg: var(--primary-50);
            padding: 1px;
            font-size: 11px;
          "
          @row-select="onRowSelect"
        >
          <Column
            selection-mode="multiple"
            style="font-size: 9px; padding: 1px; width: 50px"
          />
          <Column
            field="number"
            header="№"
            :sortable="true"
            style="font-size: 9px; padding: 1px; width: 50px"
          >
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.number }}</span>
              </div>
            </template>
          </Column>

          <Column field="sketch" header="Эскиз" style="padding: 1px; width: 120px">
            <template #body="{ data }">
              <Image
                v-if="data.sketch"
                :src="`http://localhost:8055/assets/${data.sketch}`"
                alt="Эскиз"
                width="100"
                class="my"
                preview
                style="max-width: 200px; max-height: 300px; object-fit: cover; object-position: top;"
              />
              <span v-else>-</span>
            </template>
          </Column>

          <Column field="title" header="Название" :sortable="true" style="padding: 1px; width: 120px">
            <template #body="{ data }">
              <span>{{ data.title }}</span>
            </template>
          </Column>

          <Column
            field="description"
            header="Описание"
            style="padding: 1px; font-size: 9px; width: 150px"
          >
            <template #body="{ data }">
              <span class="description-cell">{{ data.description }}</span>
            </template>
          </Column>

          <Column
            field="composition"
            header="Композиция"
            style="padding: 1px; font-size: 9px; width: 350px"
          >
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span class="composition-cell">{{ data.composition }}</span>
                <div class="flex-col items-center gap-2">
                  <Button
                    label="⿻ HTML"
                    class="p-button-text p-button-sm"
                    style="width: 70px; font-size: 10px;padding:1px"
                    @click="copyToClipboard(data.html)"
                  />
                  <Button
                    label="⿻ HBS"
                    class="p-button-text p-button-sm"
                    style="width: 70px; font-size: 10px;padding:1px"
                    @click="copyToClipboard(data.hbs)"
                  />
                  <Button
                    label="⿻ JSON"
                    class="p-button-text p-button-sm"
                    style="width: 70px; font-size: 10px;padding:1px"
                    @click="copyToClipboard(data.json)"
                  />
                </div>
              </div>
            </template>
          </Column>

          <Column
            field="elem_type"
            header="Тип элемента"
            :sortable="true"
            style="padding: 1px; width: 200px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="type in data.elem_type"
                  :key="type"
                  :value="type"
                  style="padding: 0 3px; font-size: 11px"
                />
              </div>
            </template>
          </Column>

          <Column
            field="collection"
            header="Коллекция"
            :sortable="true"
            style="padding: 1px; width: 100px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="collection in data.collection"
                  :key="collection"
                  :value="collection"
                  style="padding: 0 3px; font-size: 11px"
                />
              </div>
            </template>
          </Column>

          <Column header="Действия" :exportable="false" style="width: 180px">
            <template #body="{ data }">
              <div class="flex gap-1">
                <Button
                  icon="pi pi-pencil"
                  class="p-button-text p-button-sm"
                  @click="editItem(data)"
                />
                <Button
                  icon="pi pi-copy"
                  class="p-button-text p-button-sm"
                  @click="duplicateItem(data)"
                />
                <Button
                  icon="pi pi-trash"
                  class="p-button-text p-button-sm p-button-danger"
                  @click="confirmDelete(data)"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Сайдбар редактирования -->
    <WcontSidebar
      v-model:visible="sidebarVisible"
      :collapsed="false"
      title="Редактирование элемента"
      @close="sidebarVisible = false"
      @toggle-collapse="() => {}"
    >
      <div class="p-fluid">
        <!-- Базовая информация -->
        <div class="space-y-2">
          <div class="flex gap-2">
            <div class="field w-1/4">
              <InputText
                id="number"
                v-model="editingItem.number"
                required
                class="w-full"
                placeholder="Номер элемента*"
                style="padding: 6px; font-size: 10px"
              />
            </div>

            <div class="field w-3/4">
              <InputText
                id="title"
                v-model="editingItem.title"
                required
                class="w-full"
                placeholder="Название*"
                style="padding: 6px; font-size: 11px"
              />
            </div>
          </div>

          <div class="field">
            <Textarea
              id="description"
              v-model="editingItem.description"
              rows="2"
              class="w-full text-xs [&>textarea]:text-xs"
              placeholder="Описание"
              style="padding: 4px; font-size: 10px"
            />
          </div>

          <div class="field mb-0" style="margin-top: 0">
            <Textarea
              id="composition"
              v-model="editingItem.composition"
              rows="4"
              class="w-full text-xs [&>textarea]:text-xs"
              placeholder="Композиция"
              style="padding: 4px; font-size: 10px"
            />
          </div>

          <div class="field mb-2" style="margin-top: 0">
            <MultiSelect
              v-model="editingItem.elem_type"
              :options="elemTypeOptions"
              placeholder="Выберите типы элементов"
              display="chip"
              class="text-xs w-full p-0"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.collection"
              :options="collectionOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите коллекции"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.wblock_proto"
              :options="wblockOptions"
              option-label="label"
              option-value="value"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите связанные блоки"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-2">
            <div class="flex gap-2">
              <Image
                v-if="editingItem.sketch"
                :src="`http://localhost:8055/assets/${editingItem.sketch}`"
                alt="Эскиз"
                width="200"
                class="my"
                preview
              />
              <FileUpload
                mode="basic"
                :auto="true"
                accept="image/*"
                :max-file-size="1000000"
                choose-label="Эскиз"
                class="p-button-sm"
                @select="onSketchSelect"
              />
            </div>
          </div>

          <div class="field mb-0">
            <TabView
              class="text-xs"
              :pt="{
                panelcontainer: { style: 'padding:0' },
              }"
            >
              <TabPanel
                header="HTML/CSS/JS"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <div class="space-y-1">
                  <PrismEditorWithCopy
                    v-model="editingItem.html"
                    editor-class="my-editor text-xs"
                    :highlight="highlightHtml"
                    placeholder="Введите HTML код"
                    field-name="HTML"
                    max-height="120px"
                  />
                  <div class="p-0 grid grid-cols-2 gap-4">
                    <div class="flex flex-col h-full">
                      <PrismEditorWithCopy
                        v-model="editingItem.css"
                        editor-class="my-editor text-xs w-full"
                        :highlight="highlightCss"
                        placeholder="CSS код"
                        field-name="CSS"
                        max-height="60px !important"
                      />
                    </div>
                    <div class="flex flex-col h-full">
                      <PrismEditorWithCopy
                        v-model="editingItem.js"
                        editor-class="my-editor text-xs w-full"
                        :highlight="highlightJs"
                        placeholder="JS код"
                        field-name="JavaScript"
                        max-height="60px !important"
                      />
                    </div>
                  </div>
                </div>
              </TabPanel>
              <TabPanel
                header="HBS"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <PrismEditorWithCopy
                  v-model="editingItem.hbs"
                  editor-class="my-editor text-xs"
                  :highlight="highlightHtml"
                  placeholder="Введите HBS код"
                  field-name="HBS"
                  max-height="200px"
                />
              </TabPanel>
              <TabPanel
                header="JSON"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <PrismEditorWithCopy
                  v-model="editingItem.json"
                  editor-class="my-editor text-xs"
                  :highlight="highlightJson"
                  placeholder="Введите JSON код"
                  field-name="JSON"
                  max-height="200px"
                />
              </TabPanel>
            </TabView>
          </div>

          <!-- Связанные блоки -->
          <div class="field mb-2" v-if="relatedBlocks.length > 0">
            <h5 class="text-xs font-semibold mb-2">Связанные блоки:</h5>
            <DataTable
              :value="relatedBlocks"
              class="p-datatable-sm text-[11px]"
              :paginator="false"
              scrollable
              scroll-height="150px"
            >
              <Column field="number" header="№" style="width: 80px">
                <template #body="{ data }">
                  <span class="text-xs">{{ data.number }}</span>
                </template>
              </Column>
              <Column field="title" header="Название">
                <template #body="{ data }">
                  <span class="text-xs">{{ data.title }}</span>
                </template>
              </Column>
            </DataTable>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-0">
          <Button
            label="Отмена"
            icon="pi pi-times"
            class="p-button-sm"
            @click="closeSidebar"
          />
          <Button
            label="Сохранить"
            icon="pi pi-check"
            class="p-button-sm"
            :loading="saving"
            @click="saveItem"
          />
        </div>
      </div>
    </WcontSidebar>

    <!-- Диалог подтверждения удаления -->
    <ConfirmDialog />

    <!-- Уведомления -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
  import 'vue-prism-editor/dist/prismeditor.min.css'
  import Prism from 'prismjs'
  import 'prismjs/components/prism-clike'
  import 'prismjs/components/prism-markup'
  import 'prismjs/components/prism-css'
  import 'prismjs/components/prism-javascript'
  import 'prismjs/components/prism-json'
  import 'prismjs/themes/prism-tomorrow.css'

  import { ref, computed, onMounted } from 'vue'
  import { useDirectusItems } from '#imports'
  import { useConfirm } from 'primevue/useconfirm'
  import { useToast } from 'primevue/usetoast'
  import Image from 'primevue/image'
  import Button from 'primevue/button'
  import InputText from 'primevue/inputtext'
  import Textarea from 'primevue/textarea'
  import Dropdown from 'primevue/dropdown'
  import MultiSelect from 'primevue/multiselect'
  import FileUpload from 'primevue/fileupload'
  import Tag from 'primevue/tag'
  import DataTable from 'primevue/datatable'
  import Column from 'primevue/column'
  import ProgressBar from 'primevue/progressbar'
  import ConfirmDialog from 'primevue/confirmdialog'
  import Toast from 'primevue/toast'
  import TabView from 'primevue/tabview'
  import TabPanel from 'primevue/tabpanel'
  import WcontSidebar from '~/components/WcontSidebar.vue'
  import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'
  import BulkFormContainer from '~/components/BulkFormContainer.vue'

  interface WElem {
    id?: string
    number: string
    title: string
    description?: string
    elem_type?: string[]
    html?: string
    css?: string
    js?: string
    hbs?: string
    json?: string
    composition?: string
    collection?: string[]
    sketch?: string
    wblock_proto?: string[]
    date_created?: string
    user_created?: string
    date_updated?: string
    user_updated?: string
  }

  // API и утилиты
  const { getItems, createItems, updateItem, deleteItems } = useDirectusItems()
  const confirm = useConfirm()
  const toast = useToast()

  // Состояние
  const items = ref<WElem[]>([])
  const loading = ref(false)
  const saving = ref(false)
  const sidebarVisible = ref(false)
  const selectedItems = ref<WElem[]>([])
  const globalFilterValue = ref('')
  const selectedElemTypes = ref<string[]>([])
  const selectedCollections = ref<string[]>([])

  // Состояние для массовых форм
  const bulkFormVisible = ref(false)
  const bulkFormMode = ref<'create' | 'edit'>('create')

  // Редактирование
  const editingItem = ref<WElem>({
    number: '',
    title: '',
    description: '',
    elem_type: [],
    html: '',
    css: '',
    js: '',
    hbs: '',
    json: '',
    composition: '',
    collection: [],
    sketch: '',
    wblock_proto: [],
  })

  // Сортировка
  const sortField = ref('number')
  const sortOrder = ref(1)

  // Опции для фильтров
  const elemTypeOptions = ref<string[]>([])
  const collectionOptions = ref<string[]>([])
  const wblockOptions = ref([])
  const relatedBlocks = ref([])

  // Вычисляемые свойства
  const availableElemTypes = computed(() => {
    const types = new Set<string>()
    items.value.forEach((item) => {
      item.elem_type?.forEach((type) => types.add(type))
    })
    return Array.from(types)
  })

  const availableCollections = computed(() => {
    const collections = new Set<string>()
    items.value.forEach((item) => {
      item.collection?.forEach((collection) => collections.add(collection))
    })
    return Array.from(collections)
  })

  const filteredItems = computed(() => {
    let result = [...items.value]

    // Фильтр по типам элементов
    if (selectedElemTypes.value.length > 0) {
      result = result.filter((item) => {
        return selectedElemTypes.value.some((type) =>
          item.elem_type?.includes(type)
        )
      })
    }

    // Фильтр по коллекциям
    if (selectedCollections.value.length > 0) {
      result = result.filter((item) => {
        return selectedCollections.value.some((collection) =>
          item.collection?.includes(collection)
        )
      })
    }

    // Глобальный поиск
    if (globalFilterValue.value) {
      const searchValue = globalFilterValue.value.toLowerCase()
      result = result.filter(
        (item) =>
          (item.title?.toLowerCase() || '').includes(searchValue) ||
          (item.number?.toLowerCase() || '').includes(searchValue) ||
          (item.description?.toLowerCase() || '').includes(searchValue) ||
          (item.composition?.toLowerCase() || '').includes(searchValue),
      )
    }

    return result
  })

  const globalFilterFields = ['title', 'number', 'description', 'composition']

  // Конфигурация полей для массовых форм welem-proto
  const welemBulkFieldConfig = computed(() => [
    {
      name: 'elem_type',
      type: 'multiselect' as const,
      placeholder: 'Типы элементов',
      options: availableElemTypes.value,
      class: 'field-full'
    },
    {
      name: 'collection',
      type: 'multiselect' as const,
      placeholder: 'Коллекции',
      options: availableCollections.value,
      class: 'field-full'
    },
    {
      name: 'html',
      type: 'prism' as const,
      placeholder: 'HTML код',
      class: 'field-full'
    },
    {
      name: 'css',
      type: 'prism' as const,
      placeholder: 'CSS код',
      class: 'field-full'
    },
    {
      name: 'js',
      type: 'prism' as const,
      placeholder: 'JavaScript код',
      class: 'field-full'
    },
    {
      name: 'sketch',
      type: 'file' as const,
      placeholder: 'Эскиз (изображение)',
      class: 'field-full'
    }
  ])

  // Методы для Prism подсветки
  const highlightHtml = (code: string) => {
    return Prism.highlight(code, Prism.languages.markup, 'html')
  }

  const highlightCss = (code: string) => {
    return Prism.highlight(code, Prism.languages.css, 'css')
  }

  const highlightJs = (code: string) => {
    return Prism.highlight(code, Prism.languages.javascript, 'javascript')
  }

  const highlightJson = (code: string) => {
    return Prism.highlight(code, Prism.languages.json, 'json')
  }

  // Методы
  const onRowSelect = (event: { data: WElem }) => {
    // Удалено автоматическое открытие сайдбара при выборе строки
  }

  const loadData = async () => {
    loading.value = true
    try {
      const elements = await getItems({
        collection: 'welem_proto',
        params: {
          limit: -1,
          sort: [sortField.value],
          fields: ['*'],
        },
      })

      console.log('Loaded elements:', elements)
      items.value = elements
    } catch (error) {
      console.error('Error loading data:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить данные элементов',
        life: 3000,
      })
    } finally {
      loading.value = false
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text || '')
    toast.add({
      severity: 'info',
      summary: 'Скопировано',
      detail: 'Текст скопирован в буфер обмена',
      life: 3000,
    })
  }

  const onGlobalFilterChange = () => {
    // Обработка изменения глобального фильтра
  }

  const applyElemTypesFilter = () => {
    // Обработка изменения фильтра по типам элементов
  }

  const applyCollectionsFilter = () => {
    // Обработка изменения фильтра по коллекциям
  }

  const openCreateDialog = () => {
    editingItem.value = {
      number: '',
      title: '',
      description: '',
      elem_type: [],
      html: '',
      css: '',
      js: '',
      hbs: '',
      json: '',
      composition: '',
      collection: [],
      sketch: '',
      wblock_proto: [],
    }
    sidebarVisible.value = true
  }

  const closeSidebar = () => {
    sidebarVisible.value = false
    relatedBlocks.value = []
    editingItem.value = {
      number: '',
      title: '',
      description: '',
      elem_type: [],
      html: '',
      css: '',
      js: '',
      hbs: '',
      json: '',
      composition: '',
      collection: [],
      sketch: '',
      wblock_proto: [],
    }
  }

  const saveItem = async () => {
    saving.value = true
    try {
      const { id, wblock_proto, ...saveData } = editingItem.value

      if (id) {
        // Обновление существующего элемента
        await updateItem({
          collection: 'welem_proto',
          id,
          item: saveData,
        })
        await saveRelations(id, wblock_proto || [])
      } else {
        // Создание нового элемента
        const result = await createItems({
          collection: 'welem_proto',
          items: [saveData],
        })
        const newId = Array.isArray(result) ? result[0]?.id : result?.id
        if (newId && wblock_proto?.length) {
          await saveRelations(newId, wblock_proto)
        }
      }

      await loadData()
      closeSidebar()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: id ? 'Элемент обновлен' : 'Элемент создан',
        life: 3000,
      })
    } catch (error) {
      console.error('Save error:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось сохранить элемент',
        life: 5000,
      })
    } finally {
      saving.value = false
    }
  }

  const editItem = async (item: WElem) => {
    try {
      const [elementData] = await getItems({
        collection: 'welem_proto',
        params: {
          filter: { id: { _eq: item.id } },
          fields: ['*'],
          limit: 1,
        },
      })

      const relations = await loadElementRelations(item.id!)
      const relatedBlocksData = await loadRelatedBlocksDetails(relations)

      editingItem.value = {
        ...elementData,
        wblock_proto: relations,
      }
      relatedBlocks.value = relatedBlocksData
      sidebarVisible.value = true
    } catch (error) {
      console.error('Error loading element:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить данные элемента',
        life: 3000,
      })
    }
  }

  const loadRelatedBlocksDetails = async (blockIds: string[]) => {
    if (!blockIds.length) return []

    try {
      const blocks = await getItems({
        collection: 'wblock_proto',
        params: {
          filter: { id: { _in: blockIds } },
          fields: ['id', 'number', 'title'],
        },
      })
      return blocks || []
    } catch (error) {
      console.error('Error loading related blocks:', error)
      return []
    }
  }

  const duplicateItem = async (item: WElem) => {
    const duplicate = { ...item }
    delete duplicate.id
    duplicate.number = `${duplicate.number}-copy`
    duplicate.title = `${duplicate.title} (копия)`

    try {
      await createItems({
        collection: 'welem_proto',
        items: [duplicate],
      })
      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Элемент скопирован',
      })
    } catch (error) {
      console.error('Error duplicating item:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось скопировать элемент',
      })
    }
  }

  const confirmDelete = (item: WElem) => {
    confirm.require({
      message: 'Вы уверены, что хотите удалить этот элемент?',
      header: 'Подтверждение удаления',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteItem(item),
    })
  }

  const deleteItem = async (item: WElem) => {
    try {
      await deleteItems({
        collection: 'welem_proto',
        items: [item.id!],
      })
      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Элемент удален',
      })
    } catch (error) {
      console.error('Error deleting item:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось удалить элемент',
      })
    }
  }

  const onSketchSelect = async (event: any) => {
    const file = event.files[0]
    if (file) {
      try {
        const formData = new FormData()
        formData.append('file', file)

        const response = await fetch('http://localhost:8055/files', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          throw new Error('Ошибка загрузки файла')
        }

        const data = await response.json()
        editingItem.value.sketch = data.data.id
      } catch (error) {
        console.error('Ошибка при загрузке файла:', error)
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Не удалось загрузить файл',
        })
      }
    }
  }

  // Методы для работы с M2M связями
  const loadElementRelations = async (elementId: string) => {
    try {
      const relations = await getItems({
        collection: 'wblock_proto_welem_proto',
        params: {
          filter: { welem_proto_id: { _eq: elementId } },
          fields: ['wblock_proto_id'],
        },
      })
      return relations.map((r) => r.wblock_proto_id)
    } catch (error) {
      console.error('Error loading relations:', error)
      return []
    }
  }

  const saveRelations = async (elementId: string, wblockIds: string[]) => {
    try {
      // Удаляем старые связи
      const currentRelations = await getItems({
        collection: 'wblock_proto_welem_proto',
        params: {
          filter: { welem_proto_id: { _eq: elementId } },
          fields: ['id'],
        },
      })

      if (currentRelations.length > 0) {
        await deleteItems({
          collection: 'wblock_proto_welem_proto',
          items: currentRelations.map((r) => r.id),
        })
      }

      // Добавляем новые
      if (wblockIds.length > 0) {
        await createItems({
          collection: 'wblock_proto_welem_proto',
          items: wblockIds.map((id) => ({
            welem_proto_id: elementId,
            wblock_proto_id: id,
          })),
        })
      }
    } catch (error) {
      console.error('Error saving relations:', error)
      throw error
    }
  }

  // Загрузка опций
  const loadOptions = async () => {
    try {
      // Загружаем опции для elem_type и collection из существующих записей
      const elements = await getItems({
        collection: 'welem_proto',
        params: {
          limit: -1,
          fields: ['elem_type', 'collection'],
        },
      })

      if (Array.isArray(elements)) {
        const elemTypes = new Set()
        const collections = new Set()

        elements.forEach((item) => {
          item.elem_type?.forEach((type) => elemTypes.add(type))
          item.collection?.forEach((collection) => collections.add(collection))
        })

        elemTypeOptions.value = Array.from(elemTypes)
        collectionOptions.value = Array.from(collections)
      }
    } catch (error) {
      console.error('Error loading options:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить справочники',
      })
    }
  }

  const loadWblockOptions = async () => {
    try {
      const blocks = await getItems({
        collection: 'wblock_proto',
        params: {
          limit: -1,
          fields: ['id', 'title', 'number'],
        },
      })

      if (Array.isArray(blocks)) {
        wblockOptions.value = blocks.map((block) => ({
          value: block.id,
          label: `${block.number} - ${block.title}` || block.id,
        }))
      }
    } catch (error) {
      console.error('Ошибка загрузки блоков:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить блоки для выбора',
        life: 3000,
      })
    }
  }

  const bulkAnalyzeHtml = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true

      // Получаем полные данные выбранных записей
      const selectedIds = selectedItems.value.map((item) => item.id)

      const fullRecords = await getItems({
        collection: 'welem_proto',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: [
            'id',
            'number',
            'title',
            'html',
            'elem_type',
            'collection',
            'composition',
          ],
        },
      })

      // Отправляем на анализ только записи с HTML
      const recordsWithHtml = fullRecords.filter((item) => item.html)

      if (recordsWithHtml.length === 0) {
        toast.add({
          severity: 'warn',
          summary: 'Предупреждение',
          detail: 'У выбранных записей отсутствует HTML для анализа',
          life: 3000,
        })
        return
      }

      // Анализируем каждую запись
      for (const record of recordsWithHtml) {
        try {
          // Подготавливаем данные для обновления
          const updateData: any = {}

          // Генерируем HBS и JSON для записи
          if (record.html?.trim()) {
            try {
              const templateResult = await $fetch('/api/convert-html-template', {
                method: 'POST',
                body: {
                  html: record.html,
                  format: 'handlebars',
                  blockName: record.title || '',
                  blockNumber: record.number || ''
                }
              })

              if (templateResult.success) {
                updateData.hbs = templateResult.template
                updateData.json = JSON.stringify(templateResult.jsonData, null, 2)
              }
            } catch (templateError) {
              console.warn(`Ошибка генерации HBS/JSON для записи ${record.id}:`, templateError)
            }
          }

          // Анализируем HTML для определения типов элементов
          try {
            const analysisResult = await $fetch('/api/analyze-html-element', {
              method: 'POST',
              body: { html: record.html }
            })

            if (analysisResult.success) {
              updateData.elem_type = [...new Set(analysisResult.elementTypes)]
              updateData.composition = analysisResult.treeStructure
            }
          } catch (analysisError) {
            console.warn(`Ошибка анализа HTML для записи ${record.id}:`, analysisError)
          }

          // Обновляем запись, если есть что обновлять
          if (Object.keys(updateData).length > 0) {
            await updateItem({
              collection: 'welem_proto',
              id: record.id,
              item: updateData,
            })
          }
        } catch (recordError) {
          console.error(`Ошибка обработки записи ${record.id}:`, recordError)
        }
      }

      await loadData()
      selectedItems.value = []
      toast.add({
        severity: 'success',
        summary: 'Готово',
        detail: `Обновлено ${recordsWithHtml.length} записей с анализом HTML и генерацией HBS/JSON`,
        life: 3000,
      })
    } catch (error) {
      console.error('Ошибка массового анализа:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось выполнить анализ',
        life: 5000,
      })
    } finally {
      loading.value = false
    }
  }

  const bulkUploadSketches = async () => {
    if (!selectedItems.value.length) return

    // Создаем невидимый input для выбора файлов
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.multiple = true
    fileInput.accept = 'image/*'

    fileInput.onchange = async (event) => {
      const files = event.target.files
      if (!files || files.length === 0) return

      try {
        loading.value = true

        // Получаем ID выбранных элементов
        const selectedIds = selectedItems.value.map(item => item.id)

        // Загружаем файлы и обновляем записи
        let successCount = 0
        let errorCount = 0

        for (let i = 0; i < Math.min(files.length, selectedIds.length); i++) {
          const file = files[i]
          const itemId = selectedIds[i]

          try {
            // Загружаем файл в Directus
            const formData = new FormData()
            formData.append('file', file)

            const response = await fetch('http://localhost:8055/files', {
              method: 'POST',
              body: formData,
            })

            if (!response.ok) {
              throw new Error('Ошибка загрузки файла')
            }

            const data = await response.json()

            // Обновляем запись с новым sketch
            await updateItem({
              collection: 'welem_proto',
              id: itemId,
              item: { sketch: data.data.id },
            })

            successCount++
          } catch (fileError) {
            console.error(`Ошибка загрузки файла для элемента ${itemId}:`, fileError)
            errorCount++
          }
        }

        await loadData()
        selectedItems.value = []

        toast.add({
          severity: successCount > 0 ? 'success' : 'error',
          summary: 'Результат загрузки',
          detail: `Успешно: ${successCount}, Ошибок: ${errorCount}`,
          life: 3000,
        })
      } catch (error) {
        console.error('Ошибка массовой загрузки эскизов:', error)
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Не удалось загрузить эскизы',
        })
      } finally {
        loading.value = false
      }
    }

    // Открываем диалог выбора файлов
    fileInput.click()
  }

  const bulkGenerateScreenshots = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true

      // Получаем полные данные выбранных записей
      const selectedIds = selectedItems.value.map((item) => item.id)
      const fullRecords = await getItems({
        collection: 'welem_proto',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: ['id', 'number', 'title', 'html', 'css', 'js'],
        },
      })

      // Отправляем на генерацию скриншотов только записи с HTML
      const recordsWithHtml = fullRecords.filter((item) => item.html)

      if (recordsWithHtml.length === 0) {
        toast.add({
          severity: 'warn',
          summary: 'Предупреждение',
          detail: 'У выбранных записей отсутствует HTML для генерации скриншотов',
          life: 3000,
        })
        return
      }

      // Генерируем скриншоты для каждой записи (как в wblock-proto2.vue)
      let successCount = 0
      for (const record of recordsWithHtml) {
        try {
          const elementTitle = record.title || 'Untitled Element'
          const elementNumber = record.number || 'element'

          // Создаем полный HTML для скриншота (как в wblock-proto2.vue)
          const elementFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${elementTitle}</title>
  
    ${record.css || ''}
  
</head>
<body>
  ${record.html}
  
    ${record.js || ''}
  
</body>
</html>`

          // Создаем скриншот используя существующий API (как в wblock-proto2.vue)
          const response = await $fetch('/api/capture-html-screenshot-temp', {
            method: 'POST',
            body: {
              html: elementFullHtml,
              width: 1400,
              height: 800
            },
            responseType: 'blob'
          })

          // Загружаем blob в Directus
          const formData = new FormData()
          const filename = `element_${elementNumber}_${elementTitle.replace(/[^a-zA-Z0-9\-_]/g, '_')}.png`
          formData.append('file', response, filename)

          const uploadResponse = await fetch('http://localhost:8055/files', {
            method: 'POST',
            body: formData,
          })

          if (!uploadResponse.ok) {
            throw new Error('Ошибка загрузки скриншота в Directus')
          }

          const uploadData = await uploadResponse.json()

          // Обновляем запись с новым sketch
          await updateItem({
            collection: 'welem_proto',
            id: record.id,
            item: { sketch: uploadData.data.id },
          })

          successCount++
          console.log(`✅ Скриншот для элемента ${elementTitle} создан успешно`)

        } catch (elementError) {
          console.error(`❌ Ошибка генерации скриншота для элемента ${record.id}:`, elementError)
        }
      }

      await loadData()
      selectedItems.value = []
      toast.add({
        severity: 'success',
        summary: 'Готово',
        detail: `Сгенерировано скриншотов: ${successCount} из ${recordsWithHtml.length}`,
        life: 3000,
      })
    } catch (error) {
      console.error('Ошибка генерации скриншотов:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось сгенерировать скриншоты',
        life: 5000,
      })
    } finally {
      loading.value = false
    }
  }

  const bulkDuplicateItems = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true

      // Получаем полные данные выбранных записей
      const selectedIds = selectedItems.value.map(item => item.id)
      const fullRecords = await getItems({
        collection: 'welem_proto',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: ['*'],
        },
      })

      // Подготавливаем данные для дублирования
      const duplicates = fullRecords.map(item => {
        const duplicate = { ...item }
        delete duplicate.id
        duplicate.number = `${duplicate.number}-copy`
        duplicate.title = `${duplicate.title} (копия)`
        return duplicate
      })

      // Создаем дубликаты
      await createItems({
        collection: 'welem_proto',
        items: duplicates,
      })

      await loadData()
      selectedItems.value = []
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Дублировано ${duplicates.length} элементов`,
      })
    } catch (error) {
      console.error('Error duplicating items:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось дублировать элементы',
      })
    } finally {
      loading.value = false
    }
  }

  const bulkDeleteItems = () => {
    if (!selectedItems.value.length) return

    confirm.require({
      message: `Вы уверены, что хотите удалить ${selectedItems.value.length} выбранных элементов?`,
      header: 'Подтверждение удаления',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          loading.value = true
          const selectedIds = selectedItems.value.map(item => item.id)

          await deleteItems({
            collection: 'welem_proto',
            items: selectedIds,
          })

          await loadData()
          selectedItems.value = []

          toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: `Удалено ${selectedIds.length} элементов`,
          })
        } catch (error) {
          console.error('Error deleting items:', error)
          toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось удалить элементы',
          })
        } finally {
          loading.value = false
        }
      }
    })
  }

  // Функции для массовых форм
  function openBulkCreate() {
    bulkFormMode.value = 'create'
    bulkFormVisible.value = true
  }

  function openBulkEdit() {
    if (selectedItems.value.length === 0) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Выберите элементы для редактирования',
        life: 3000
      })
      return
    }
    bulkFormMode.value = 'edit'
    bulkFormVisible.value = true
  }

  function closeBulkForm() {
    bulkFormVisible.value = false
  }

  function onBulkFormSaved(savedItems) {
    // Обновляем список элементов
    loadData()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Обработано ${savedItems.length} элементов`,
      life: 3000
    })
  }

  // Инициализация
  onMounted(async () => {
    await Promise.all([loadData(), loadOptions(), loadWblockOptions()])
  })
</script>
