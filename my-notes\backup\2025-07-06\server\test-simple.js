// test-simple.js
import { htmlToHandlebarsAndJson, decodeHtmlEntities } from './utils/htmlToTemplate.js';

// Тест функции декодирования HTML-сущностей
const testString = '{ &quot;translateY&quot;: [0, 100] }';
console.log('Оригинальная строка:', testString);
console.log('Декодированная строка:', decodeHtmlEntities(testString));

// Тест с data-атрибутами и JSON
const html = `<div data-anime="{ &quot;translateY&quot;: [0, 100] }" data-value="test" class="test">
  <p>Test text</p>
</div>`;

const result = htmlToHandlebarsAndJson(html);

console.log('\nJSON данные:');
console.log(JSON.stringify(result.jsonData, null, 2));

console.log('\nHBS шаблон:');
console.log(result.hbsTemplate); 