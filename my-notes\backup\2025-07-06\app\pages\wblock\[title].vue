<template> 
  <div> 
      <PageHeader/> 
      <div class="container mx-auto p-4">              
          <div v-if="item"> 
              <div class="flex"> 
                  <h2 class="flex-1 font-medium mb-2 text-2xl text-surface-900 dark:text-surface-0">{{ item.title }}</h2> 
                  <button class="bg-gradient-to-r flex-1 flex-none from-indigo-500 mb-2 px-2 py-0 rounded-full text-white text-xs to-pink-500 transition via-purple-500 hover:bg-blue-700" @click="generateHtml"> 
                      🔄 HBS-to-HTML
                  </button> 
                  <button class="bg-gradient-to-r flex-1 flex-none from-indigo-500 mb-2 ml-2 px-2 py-0 rounded-full text-white text-xs to-pink-500 transition via-purple-500 hover:bg-blue-700" @click="renderPug"> 
                      🔄 PUG-to-HTML
                  </button> 
                  <button class="bg-gradient-to-r flex-1 flex-none from-green-500 mb-2 ml-2 px-2 py-0 rounded-full text-white text-xs to-blue-500 transition via-teal-500 hover:bg-green-700" @click="parsesHtml"> 
                      🛠 HTML-to-PUG
                  </button>  
              </div>                 
              <div class="flex space-y-4"> 
                  <!-- JSON -->                     
                  <div v-if="item.json" class="flex-1 bg-gray-50 order-1 p-0 rounded"> 
                      <h3 class="font-semibold mb-0 mr-1 text-neutral-400 text-right text-xs">JSON</h3> 
                      <pre class="max-h-80 overflow-y-scroll text-gray-600 text-xs whitespace-pre-wrap">{{ formattedJson }}</pre> 
                  </div>                     
                  <!-- HBS -->                     
                  <div v-if="item.hbs" class="flex-1 bg-gray-50 order-2 p-0 rounded"> 
                      <h3 class="font-semibold mb-0 mr-1 text-neutral-400 text-right text-xs">HBS</h3> 
                      <pre class="max-h-80 overflow-y-scroll text-gray-600 text-xs whitespace-pre-wrap">{{ item.hbs }}</pre> 
                  </div>                     
                  <!-- PUG -->                     
                  <div v-if="item.pug" class="flex-1 bg-gray-50 order-3 p-0 rounded"> 
                      <h3 class="font-semibold mb-0 mr-1 text-neutral-400 text-right text-xs">PUG</h3> 
                      <pre class="max-h-80 overflow-y-scroll text-gray-600 text-xs whitespace-pre-wrap">{{ item.pug }}</pre> 
                  </div>
                  <!-- HTML -->                     
                  <div v-if="item.html" class="flex-1 bg-gray-50 order-3 p-0 rounded"> 
                      <h3 class="font-semibold mb-0 mr-1 text-neutral-400 text-right text-xs">HTML</h3> 
                      <pre class="max-h-80 overflow-y-scroll text-gray-600 text-xs whitespace-pre-wrap">{{ item.html }}</pre> 
                  </div>                       
              </div>
              <div class="grid grid-cols-4 gap-4">
                <!-- Поле для Pug -->
                <div class="col-span-3">
                  <h3 class="text-lg font-medium">Pug</h3>
                  <prism-editor
                      v-model="pugCode"
                      class="w-full h-96 text-xs p-2 border rounded my-editor"
                      :highlight="highlightPug"
                      language="pug"
                      preview="false"
                      line-numbers 
                      :tab-size="1" 
                    />
                  
                </div>
                <!-- Переменные -->              
                <div class="col-span-1">
                  <div class="flex mt-5 mb-3">
                    <button class="bg-gradient-to-r flex-1 flex-none from-indigo-500 mb-2 ml-2 px-4 py-2 rounded-full text-white text-xs to-pink-500 transition via-purple-500 hover:bg-blue-700" @click="parsePug"> 
                        🔄 Parse PUG
                    </button> 
                    <button class="bg-gradient-to-r flex-1 flex-none from-green-500 mb-2 ml-2 px-4 py-2 rounded-full text-white text-xs to-blue-500 transition via-teal-500 hover:bg-green-700" @click="savePugAndJson"> 
                        🛠 Save PUG+JSON
                    </button>
                  </div>
                  <h3 class="text-lg font-medium">Переменные</h3>
                  <ul>
                    <li v-for="variable in allVariables" :key="variable">
                    <span
                      class="text-xs"
                      :class="{ 'text-xs': usedVariables.includes(variable) }"
                    >
                      {{ variable }}
                    </span>
                    <button @click="copyVariable(variable)"> ⿻</button>
                  </li>
                </ul>
                </div>
              <!-- Поле для JSON -->
              <div class="col-span-3">
                <h3 class="text-lg font-medium">JSON</h3>
                <prism-editor
                    v-model="jsonCode"
                    class="w-full h-96 text-xs p-2 border rounded my-editor"
                    :highlight="highlightJson"
                    preview="false"
                    line-numbers 
                    :tab-size="1" 
                  />
                <!-- <textarea v-model="jsonCode" class="w-full h-40 p-2 border rounded"/> -->
              </div>
            
            <div class="col-span-1">
              <h3 class="text-lg font-medium">Используемые переменные</h3>
              <ul>
                <li v-for="variable in usedVariables" :key="variable">
                  {{ variable }}
                </li>
              </ul>
            </div>
          </div>
            <div class="flex space-x-4 mt-4"/>
          </div>             
          <div v-else class="text-center py-8 text-gray-600"> 
              Загрузка данных...
          </div>             
      </div>         
  </div>     
</template> 

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useDirectusItems } from '#imports';
import { useRoute } from 'vue-router';
import Handlebars from 'handlebars/dist/handlebars.min.js';
import { PrismEditor } from 'vue-prism-editor';
// import "prismjs/themes/prism.min.css";
import 'vue-prism-editor/dist/prismeditor.min.css';
import 'prismjs/themes/prism-tomorrow.css';
import { highlight, languages } from 'prismjs/components/prism-core';
import 'prismjs/components/prism-pug';
import Prism from 'prismjs';
import 'prismjs/components/prism-json';




const item = ref<any | null>(null);
const route = useRoute();
const { getItems, updateItem } = useDirectusItems();

const pugCode = ref('');
const jsonCode = ref('');

const allVariables = [
  'title',
  'subtitle',
  'excerpt',
  'text',
  'url',
  'linkText',
  'number',
  'inscription',
  'icon',
  'product',
  'category',
  'additional',
  'imageBackground',
  'image',
  'imageLogo',
  'imageIcon',
  'imageGallery',
  'items.title',
  'items.subtitle',
  'items.excerpt',
  'items.text',
  'items.url',
  'items.linkText',
  'items.number',
  'items.inscription',
  'items.icon',
  'items.product',
  'items.category',
  'items.additional',
  'items.imageBackground',
  'items.image',
  'items.imageLogo',
  'items.imageIcon',
  'items.imageGallery',
];

const usedVariables = computed(() => {
  const regex = /#{([^{}]+)}/g;
  let match;
  const variables = [];
  while ((match = regex.exec(pugCode.value))) {
    variables.push(match[1].trim());
  }
  return variables;
});

// Загрузка данных
onMounted(async () => {
  Prism.highlightAll();
  try {
    console.log('Запрос к записи с title:', route.params.title);
    const response = await getItems({
      collection: 'wblock',
      params: {
        filter: { title: { _eq: route.params.title } },
        fields: ['*']
      }
    });

    if (Array.isArray(response) && response.length > 0) {
      item.value = response[0];
      pugCode.value = item.value.pug || '';
      jsonCode.value = item.value.json || '{}';
    } else {
      console.warn('Запись не найдена:', route.params.title);
    }
  } catch (error) {
    console.error('Ошибка при загрузке записи:', error);
  }
});

const highlightPug = (code) => highlight(code, languages.pug, 'pug');

const highlightJson = (code) => {
  return Prism.highlight(
    code, 
    Prism.languages.json, 
    'json'
  )
}

const copyVariable = (variable) => {
  navigator.clipboard.writeText(`#{${variable}}`);
  // Дополнительная обработка: отображение уведомления об успешном копировании
};


// Преобразуем JSON в читаемый вид
const formattedJson = computed(() => {
  try {
    return typeof item.value?.json === 'string' 
      ? JSON.stringify(JSON.parse(item.value.json), null, 2) 
      : JSON.stringify(item.value?.json, null, 2);
  } catch {
    return item.value?.json;
  }
});


// Функция генерации HTML
const generateHtml = async () => {
  if (!item.value?.hbs || !item.value?.json) {
    console.warn('Отсутствуют данные для сборки HTML.');
    return;
  }

  try {
    const template = Handlebars.compile(item.value.hbs);
    const jsonData = typeof item.value.json === 'string' 
      ? JSON.parse(item.value.json) 
      : item.value.json;
    
    const generatedHtml = template(jsonData);
    console.log('Отправляем в Directus:', { html: generatedHtml });

    await updateItem({
      collection: 'wblock',
      id: item.value.id,
      item: { html: generatedHtml }
    });

    item.value.html = generatedHtml;
    console.log('✅ HTML успешно сгенерирован и сохранён!');
  } catch (error) {
    console.error('❌ Ошибка при генерации HTML:', error);
  }
};

// рендеринг pug to html
const renderPug = async () => {
  if (!item.value?.pug || !item.value?.json) {
    console.warn('❌ Отсутствует шаблон Pug или JSON данные');
    return;
  }

  try {
    const response = await fetch('/api/render-pug', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        template: item.value.pug,
        data: JSON.parse(item.value.json)
      })
    });

    const result = await response.json();
    if (result.error) {
      throw new Error(result.error);
    }

    // --- Форматируем HTML через API ---
    const formatResponse = await fetch('/api/format-html', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ html: result.html })
    });

    if (!formatResponse.ok) {
      throw new Error(`Ошибка HTTP: ${formatResponse.status}`);
    }

    const formatData = await formatResponse.json();
    const formattedHtml = formatData.html;

    // Отправляем отформатированный HTML обратно в Directus
    updateItem({
      collection: 'wblock',
      id: item.value.id,
      item: { html: formattedHtml }
    })
    .then(() => {
      // Обновляем локальные данные
      item.value.html = formattedHtml;
    })
    .catch(error => {
      console.error('❌ Ошибка при обновлении Directus:', error);
    });

  } catch (error) {
    console.error('❌ Ошибка при рендеринге Pug:', error);
  }
};

// конвертация html to pug
const parsesHtml = async () => {
  if (!item.value?.html) {
    console.warn('❌ Отсутствует HTML для преобразования');
    return;
  }

  try {
    // --- Конвертируем HTML в Pug через API ---
    const convertResponse = await fetch('/api/convert-html', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ html: item.value.html })
    });

    if (!convertResponse.ok) {
      throw new Error(`Ошибка HTTP: ${convertResponse.status}`);
    }

    const convertData = await convertResponse.json();
    const pugCode = convertData.pug;

    // Отправляем сгенерированный Pug код в Directus
    updateItem({
      collection: 'wblock',
      id: item.value.id,
      item: { pug: pugCode }
    })
    .then(() => {
      // Обновляем локальные данные
      item.value.pug = pugCode;
    })
    .catch(error => {
      console.error('❌ Ошибка при обновлении Directus:', error);
    });

  } catch (error) {
    console.error('❌ Ошибка при преобразовании HTML в Pug:', error);
  }
};

const parsePug = async () => {
  if (!pugCode.value) {
    console.warn('❌ Pug-код отсутствует');
    return;
  }

  try {
    const response = await fetch('/api/parse-pug', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ template: pugCode.value })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Проверяем, что получили валидный результат
    if (!result.pug || typeof result.pug !== 'string') {
      throw new Error('Некорректный результат парсинга');
    }

    // Обновляем поля редактора
    pugCode.value = result.pug;
    
    // Форматируем JSON перед обновлением
    const formattedJson = JSON.stringify(result.json, null, 2);
    jsonCode.value = formattedJson;

    console.log('✅ Pug успешно обработан', { 
      pugLength: result.pug.length,
      jsonKeys: Object.keys(result.json)
    });

  } catch (error) {
    console.error('❌ Ошибка при парсинге Pug:', error);
    // Восстанавливаем предыдущее значение pugCode если произошла ошибка
    if (item.value?.pug) {
      pugCode.value = item.value.pug;
    }
  }
};

const savePugAndJson = async () => {
  if (!item.value?.id) {
    console.warn('❌ Запись не найдена');
    return;
  }

  try {
    await updateItem({
      collection: 'wblock',
      id: item.value.id,
      item: { 
        pug: pugCode.value, 
        json: jsonCode.value
      }
    });

    console.log('✅ Изменения успешно сохранены в базе');
  } catch (error) {
    console.error('❌ Ошибка при сохранении:', error);
  }
};



</script>

<style scoped>
.my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family: Fira code, Fira Mono, Consolas, Menlo, Courier, monospace;
    font-size: 12px;
    line-height: 1.5;
    padding: 5px;
  }

  /* optional class for removing the outline */
  .prism-editor__textarea:focus {
    outline: none;
  }
</style>