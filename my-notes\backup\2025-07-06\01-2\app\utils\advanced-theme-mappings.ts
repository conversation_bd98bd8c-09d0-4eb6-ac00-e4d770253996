// Расширенная система маппинга тем (аналог theme.json для WordPress)

export interface ThemeColorPalette {
  primary: string
  secondary: string
  accent: string
  background: string
  surface: string
  text: string
  textSecondary: string
  border: string
  success: string
  warning: string
  error: string
  info: string
}

export interface ThemeTypography {
  fontFamily: {
    primary: string
    secondary: string
    monospace: string
  }
  fontSize: {
    xs: string
    sm: string
    base: string
    lg: string
    xl: string
    '2xl': string
    '3xl': string
  }
  fontWeight: {
    light: number
    normal: number
    medium: number
    semibold: number
    bold: number
  }
  lineHeight: {
    tight: number
    normal: number
    relaxed: number
  }
}

export interface ThemeSpacing {
  xs: string
  sm: string
  md: string
  lg: string
  xl: string
  '2xl': string
}

export interface ThemeBorderRadius {
  none: string
  sm: string
  md: string
  lg: string
  xl: string
  full: string
}

export interface ThemeShadows {
  sm: string
  md: string
  lg: string
  xl: string
  '2xl': string
}

export interface ThemeRule {
  id: string
  name: string
  description: string
  selector: string
  targetElements: {
    types?: string[]
    tags?: string[]
    classes?: string[]
    attributes?: Record<string, string>
    scope: 'root' | 'nested' | 'all'
  }
  styles: Record<string, string>
  classReplacements?: { from: string, to: string }[]
  conditions?: {
    hasClass?: string[]
    hasAttribute?: string[]
    hasParent?: string[]
  }
  priority: number
  enabled: boolean
}

export interface ThemeDefinition {
  id: string
  name: string
  description: string
  version: string
  author: string
  preview: string
  colors: ThemeColorPalette
  typography: ThemeTypography
  spacing: ThemeSpacing
  borderRadius: ThemeBorderRadius
  shadows: ThemeShadows
  rules: ThemeRule[]
  customCSS?: string
  enabled: boolean
}

// Предустановленные темы
export const themeDefinitions: ThemeDefinition[] = [
  {
    id: 'modern-blue',
    name: 'Современный синий',
    description: 'Современная тема с синими акцентами',
    version: '1.0.0',
    author: 'System',
    preview: '/themes/modern-blue-preview.jpg',
    enabled: true,
    colors: {
      primary: '#3b82f6',
      secondary: '#6b7280',
      accent: '#10b981',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#e5e7eb',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    },
    typography: {
      fontFamily: {
        primary: 'Inter, system-ui, sans-serif',
        secondary: 'Inter, system-ui, sans-serif',
        monospace: 'JetBrains Mono, monospace'
      },
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem'
      },
      fontWeight: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      },
      lineHeight: {
        tight: 1.25,
        normal: 1.5,
        relaxed: 1.75
      }
    },
    spacing: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      '2xl': '3rem'
    },
    borderRadius: {
      none: '0',
      sm: '0.25rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem',
      full: '9999px'
    },
    shadows: {
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
      '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
    },
    rules: [
      {
        id: 'button-primary',
        name: 'Основные кнопки',
        description: 'Стилизация основных кнопок',
        selector: '.btn, button, [role="button"]',
        targetElements: {
          types: ['button'],
          tags: ['button'],
          classes: ['btn', 'btn-primary'],
          scope: 'all'
        },
        styles: {
          'background-color': '#3b82f6',
          'color': '#ffffff',
          'border': 'none',
          'border-radius': '0.5rem',
          'padding': '0.75rem 1.5rem',
          'font-weight': '600',
          'transition': 'all 0.2s ease'
        },
        classReplacements: [
          { from: 'btn-primary', to: 'bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all' }
        ],
        priority: 10,
        enabled: true
      },
      {
        id: 'card-modern',
        name: 'Современные карточки',
        description: 'Стилизация карточек',
        selector: '.card',
        targetElements: {
          types: ['card'],
          classes: ['card'],
          scope: 'root'
        },
        styles: {
          'background-color': '#ffffff',
          'border': '1px solid #e5e7eb',
          'border-radius': '0.75rem',
          'box-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          'padding': '1.5rem'
        },
        classReplacements: [
          { from: 'card', to: 'bg-white border border-gray-200 rounded-xl shadow-md p-6' }
        ],
        priority: 8,
        enabled: true
      }
    ]
  },
  {
    id: 'elegant-dark',
    name: 'Элегантная темная',
    description: 'Элегантная темная тема',
    version: '1.0.0',
    author: 'System',
    preview: '/themes/elegant-dark-preview.jpg',
    enabled: true,
    colors: {
      primary: '#8b5cf6',
      secondary: '#a78bfa',
      accent: '#06b6d4',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f1f5f9',
      textSecondary: '#94a3b8',
      border: '#334155',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4'
    },
    typography: {
      fontFamily: {
        primary: 'Poppins, system-ui, sans-serif',
        secondary: 'Poppins, system-ui, sans-serif',
        monospace: 'Fira Code, monospace'
      },
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem'
      },
      fontWeight: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      },
      lineHeight: {
        tight: 1.25,
        normal: 1.5,
        relaxed: 1.75
      }
    },
    spacing: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      '2xl': '3rem'
    },
    borderRadius: {
      none: '0',
      sm: '0.25rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem',
      full: '9999px'
    },
    shadows: {
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.4)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.4)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.4)',
      '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.5)'
    },
    rules: [
      {
        id: 'button-dark',
        name: 'Темные кнопки',
        description: 'Стилизация кнопок для темной темы',
        selector: '.btn, button',
        targetElements: {
          types: ['button'],
          tags: ['button'],
          scope: 'all'
        },
        styles: {
          'background-color': '#8b5cf6',
          'color': '#ffffff',
          'border': 'none',
          'border-radius': '0.5rem',
          'padding': '0.75rem 1.5rem',
          'font-weight': '600'
        },
        classReplacements: [
          { from: 'btn-primary', to: 'bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg' }
        ],
        priority: 10,
        enabled: true
      }
    ]
  }
]

// Функции для работы с темами
export function getThemeById(id: string): ThemeDefinition | undefined {
  return themeDefinitions.find(theme => theme.id === id)
}

export function getAllEnabledThemes(): ThemeDefinition[] {
  return themeDefinitions.filter(theme => theme.enabled)
}

export function applyThemeToHtml(html: string, theme: ThemeDefinition): { html: string, css: string } {
  let modifiedHtml = html
  let generatedCSS = ''

  // Применяем замены классов
  for (const rule of theme.rules.filter(r => r.enabled)) {
    if (rule.classReplacements) {
      for (const replacement of rule.classReplacements) {
        const regex = new RegExp(`\\b${replacement.from}\\b`, 'g')
        modifiedHtml = modifiedHtml.replace(regex, replacement.to)
      }
    }

    // Генерируем CSS для правила
    if (Object.keys(rule.styles).length > 0) {
      generatedCSS += `${rule.selector} {\n`
      for (const [property, value] of Object.entries(rule.styles)) {
        generatedCSS += `  ${property}: ${value};\n`
      }
      generatedCSS += `}\n\n`
    }
  }

  // Добавляем кастомный CSS
  if (theme.customCSS) {
    generatedCSS += theme.customCSS
  }

  return { html: modifiedHtml, css: generatedCSS }
}

// Сохранение и загрузка пользовательских тем
export function saveCustomThemes(themes: ThemeDefinition[]): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('customThemes', JSON.stringify(themes))
  }
}

export function loadCustomThemes(): ThemeDefinition[] {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('customThemes')
    return stored ? JSON.parse(stored) : []
  }
  return []
}

export function getAllThemes(): ThemeDefinition[] {
  const custom = loadCustomThemes()
  return [...themeDefinitions, ...custom]
}
