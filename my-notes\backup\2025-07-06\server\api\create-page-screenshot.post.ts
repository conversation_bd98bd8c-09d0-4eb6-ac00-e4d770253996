import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody } from 'h3'
import puppeteer from 'puppeteer'
import fs from 'fs/promises'
import path from 'path'
import os from 'os'

export default defineEventHandler(async (event) => {
  const { html, filename } = await readBody(event)
  
  const browser = await puppeteer.launch({ headless: 'new' })
  const page = await browser.newPage()
  await page.setViewport({ width: 1400, height: 800 })
  
  const tempFilePath = path.join(os.tmpdir(), `page-${Date.now()}.html`)
  await fs.writeFile(tempFilePath, html, 'utf8')
  
  await page.goto(`file://${tempFilePath}`)
  await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))
  
  const screenshot = await page.screenshot({
    type: 'jpeg',
    quality: 90,
    fullPage: true
  })
  
  await browser.close()
  await fs.unlink(tempFilePath)
  
  // Загрузка в Directus
  const formData = new FormData()
  const blob = new Blob([screenshot], { type: 'image/jpeg' })
  formData.append('file', blob, `page-${filename}.jpg`)
  
  const response = await fetch('http://localhost:8055/files', {
    method: 'POST',
    body: formData,
  })
  
  const data = await response.json()
  return data.data.id
})