// server/api/extract-html-elements-with-screenshots.js
import { defineEventHand<PERSON>, readBody, createError } from 'h3';
import { load } from 'cheerio';
import { analyzeHtml, generateTreeStructure } from '../utils/htmlAnalyzer';
import { htmlToHandlebarsAndJson, decodeHtmlEntities } from '../utils/htmlToTemplate.js';
import { getElementTypes } from '../utils/elementTypeMapping';

// URL Directus сервера
const DIRECTUS_URL = 'http://localhost:8055';

// Функция для извлечения элементов из HTML с данными для скриншотов
function extractHtmlElementsWithScreenshotData(html, splitOption = 1) {
  console.log('🔪 Начало извлечения элементов из HTML с данными для скриншотов...');
  console.log(`🔧 Используется вариант разделения: ${splitOption}`);

  if (!html || html.length < 100) {
    console.warn('⚠️ HTML пустой или слишком короткий для извлечения элементов');
    return { elements: [], cssContent: '', jsContent: '', cleanedHtml: '' };
  }

  // Очищаем HTML от ненужных элементов
  console.log('🧹 Очистка HTML...');
  const cleanedHtml = cleanHtml(html);
  console.log(`✅ HTML очищен, итоговая длина: ${cleanedHtml.length} символов`);

  // Определяем префикс пути в зависимости от splitOption
  let pathPrefix;
  if (splitOption === 2) {
    pathPrefix = 'https://fm-demo.ru/html2/';
  } else if (splitOption === 3) {
    pathPrefix = 'https://fm-demo.ru/html2/';
  } else {
    pathPrefix = 'https://fm-demo.ru/html/';
  }

  console.log(`🔗 Используется префикс для CSS/JS: ${pathPrefix}`);

  // Сначала извлечем CSS и JavaScript код перед удалением тегов
  let cssContent = '';
  let jsContent = '';

  try {
    // Создаем отдельный экземпляр cheerio для извлечения CSS и JS
    const $extract = load(cleanedHtml, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    });

    console.log('🔍 Извлечение CSS из тегов link и style...');

    // Извлекаем CSS из тегов link и обрабатываем относительные пути
    $extract('link[rel="stylesheet"], link[rel="preconnect"]').each((i, el) => {
      const href = $extract(el).attr('href');

      if (href && !href.startsWith('http://') && !href.startsWith('https://') && !href.startsWith('//')) {
        $extract(el).attr('href', `${pathPrefix}${href}`);
        console.log(`🔗 Добавлен префикс к CSS пути: ${href} -> ${pathPrefix}${href}`);
      }

      cssContent += $extract.html(el) + '\n';
    });

    // Извлекаем CSS из тегов style
    $extract('style').each((i, el) => {
      cssContent += $extract.html(el) + '\n';
    });

    console.log(`✅ Извлечено ${cssContent.length} символов CSS`);

    console.log('🔍 Извлечение JavaScript из тегов script...');

    // Извлекаем JavaScript из тегов script и обрабатываем относительные пути
    $extract('script').each((i, el) => {
      const src = $extract(el).attr('src');

      if (src && !src.startsWith('http://') && !src.startsWith('https://') && !src.startsWith('//')) {
        $extract(el).attr('src', `${pathPrefix}${src}`);
        console.log(`🔗 Добавлен префикс к JS пути: ${src} -> ${pathPrefix}${src}`);
      }

      jsContent += $extract.html(el) + '\n';
    });

    console.log(`✅ Извлечено ${jsContent.length} символов JavaScript`);
  } catch (extractError) {
    console.error('❌ Ошибка при извлечении CSS и JavaScript:', extractError);
  }

  try {
    // Настраиваем cheerio для сохранения оригинальных кавычек в атрибутах
    const $ = load(cleanedHtml, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    });

    // Удаляем теги script и link для анализа элементов
    console.log('🗑️ Удаление тегов script и link...');
    $('script').remove();
    $('link').remove();

    // Массив для хранения найденных элементов
    const elements = [];

    console.log('🔍 Поиск элементов по типам...');

    // Извлекаем все элементы, которые могут быть интересными
    const allElements = $('*').toArray();
    console.log(`🔍 Найдено ${allElements.length} элементов для анализа`);

    // Обрабатываем каждый элемент
    allElements.forEach((element, index) => {
      try {
        const $element = $(element);
        const elementHtml = $.html(element);

        // Пропускаем слишком маленькие элементы
        if (!elementHtml || elementHtml.length < 20) {
          return;
        }

        // Получаем информацию об элементе
        const tagName = element.name.toLowerCase();
        const classes = $element.attr('class') || '';
        const id = $element.attr('id') || '';

        // Определяем типы элемента (может быть несколько)
        const elementTypes = getElementTypes($element, $);

        // Пропускаем элементы, для которых не найдено ни одного типа
        if (elementTypes.length === 0) {
          return;
        }

        // Проверяем, не добавляли ли мы уже этот элемент (точное совпадение HTML)
        const isDuplicate = elements.some(existingElement =>
          existingElement.html === elementHtml
        );

        if (!isDuplicate) {
          // Создаем более точный селектор для скриншота
          let selector = tagName;

          // Добавляем ID если есть (наиболее точный селектор)
          if (id) {
            selector = `#${id}`;
          } else if (classes) {
            // Добавляем классы
            selector = `${tagName}.${classes.replace(/\s+/g, '.')}`;
          }

          // Для более точного поиска добавляем nth-child если нет уникальных идентификаторов
          if (!id && (!classes || classes.split(/\s+/).length < 2)) {
            const siblings = $element.parent().children(tagName);
            if (siblings.length > 1) {
              const elementIndex = siblings.index($element) + 1;
              selector = `${selector}:nth-child(${elementIndex})`;
            }
          }

          console.log(`✅ Найден элемент типов "${elementTypes.join(', ')}" (селектор: ${selector})`);

          elements.push({
            html: elementHtml,
            types: elementTypes, // Сохраняем все найденные типы
            selector: selector // Улучшенный селектор для точного поиска
          });
        }
      } catch (e) {
        console.warn(`⚠️ Проблема при обработке элемента ${index}:`, e.message);
      }
    });

    console.log(`🏁 Итоговое количество найденных элементов: ${elements.length}`);

    // Возвращаем объект с элементами, извлеченным CSS/JS и очищенным HTML
    return {
      elements,
      cssContent,
      jsContent,
      cleanedHtml: cleanedHtml
    };

  } catch (error) {
    console.error('❌ Ошибка при извлечении элементов:', error);
    return {
      elements: [],
      cssContent: '',
      jsContent: '',
      cleanedHtml: ''
    };
  }
}

// Функция для очистки HTML (используем ту же, что и в split-html-files.js)
function cleanHtml(html) {
  if (!html) {
    console.warn('⚠️ Входной HTML пустой или не определен');
    return '';
  }

  // Удаляем DOCTYPE, если есть
  const withoutDoctype = html.replace(/<!DOCTYPE[^>]*>/i, '');

  // Проверяем наличие тегов html, head и body
  if (!withoutDoctype.includes('<html') && !withoutDoctype.includes('<body')) {
    console.log('🔧 HTML не содержит тегов html или body, оборачиваем в базовую структуру');
    return `<html><head><title>Generated</title></head><body>${withoutDoctype}</body></html>`;
  }

  return withoutDoctype;
}

// Функция для создания полного HTML документа для скриншотов
function createFullHtmlDocument(htmlContent, cssContent, jsContent, title) {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  ${cssContent || ''}
</head>
<body>
  ${htmlContent}
  ${jsContent || ''}
</body>
</html>`;
}

export default defineEventHandler(async (event) => {
  try {
    const { fileIds, splitOption = 1, sourceCollection = 'directus_files' } = await readBody(event);

    console.log('Получены fileIds для извлечения элементов с скриншотами:', fileIds);
    console.log('Выбран вариант разделения:', splitOption);
    console.log('Источник данных:', sourceCollection);

    // Результаты обработки
    const results = [];
    let processedFiles = 0;
    let totalElements = 0;

    // Для каждого файла
    for (const fileId of fileIds) {
      try {
        console.log(`🔍 Обработка записи с ID: ${fileId}`);

        let htmlContent = '';
        let fileName = '';
        let fileNumber = '';
        let fileTags = [];
        let cssContent = '';
        let jsContent = '';

        if (sourceCollection === 'wpage') {
          // Работаем с коллекцией wpage
          console.log(`📥 Запрос данных из коллекции wpage: ${DIRECTUS_URL}/items/wpage/${fileId}`);
          const pageResponse = await fetch(`${DIRECTUS_URL}/items/wpage/${fileId}`);

          if (!pageResponse.ok) {
            console.error(`❌ Не удалось получить данные страницы: HTTP ${pageResponse.status}`);
            results.push({
              fileId,
              success: false,
              error: `Не удалось получить данные страницы (HTTP ${pageResponse.status})`
            });
            continue;
          }

          const pageData = await pageResponse.json();
          const page = pageData.data;

          htmlContent = page.html || '';
          fileName = page.title || `page-${fileId}`;
          fileNumber = page.number || fileId;
          fileTags = page.tags || [];
          cssContent = page.css || '';
          jsContent = page.js || '';

          console.log(`📋 Получена информация о странице: title=${fileName}, number=${fileNumber}, tags=${fileTags.join(', ')}`);
          console.log(`📄 Получено HTML содержимое, длина: ${htmlContent.length} символов`);
          console.log(`🎨 CSS содержимое, длина: ${cssContent.length} символов`);
          console.log(`⚡ JS содержимое, длина: ${jsContent.length} символов`);
        } else {
          // Работаем с коллекцией directus_files (оригинальная логика)
          console.log(`📥 Запрос содержимого файла: ${DIRECTUS_URL}/assets/${fileId}`);
          const fileContentResponse = await fetch(`${DIRECTUS_URL}/assets/${fileId}`);

          if (!fileContentResponse.ok) {
            console.error(`❌ Не удалось получить содержимое файла: HTTP ${fileContentResponse.status}`);
            results.push({
              fileId,
              success: false,
              error: `Не удалось получить содержимое файла (HTTP ${fileContentResponse.status})`
            });
            continue;
          }

          // Получаем информацию о файле из Directus для получения title, number и tags
          console.log(`📥 Запрос информации о файле: ${DIRECTUS_URL}/files/${fileId}`);
          const fileInfoResponse = await fetch(`${DIRECTUS_URL}/files/${fileId}`);

          if (fileInfoResponse.ok) {
            const fileInfo = await fileInfoResponse.json();
            fileName = fileInfo.data.title || `file-${fileId}`;
            fileNumber = fileInfo.data.number || fileId;
            fileTags = fileInfo.data.tags || [];
            console.log(`📋 Получена информация о файле: title=${fileName}, number=${fileNumber}, tags=${fileTags.join(', ')}`);
          } else {
            fileName = `file-${fileId}`;
            fileNumber = fileId;
            console.warn(`⚠️ Не удалось получить информацию о файле, используем ID: ${fileName}`);
          }

          // Получаем HTML содержимое
          htmlContent = await fileContentResponse.text();
          console.log(`📄 Получено HTML содержимое, длина: ${htmlContent.length} символов`);
        }

        // 4. Извлекаем элементы из HTML
        console.log('✂️ Извлечение элементов из HTML...');
        let extractedCss = '';
        let extractedJs = '';
        let cleanedHtml = '';

        if (sourceCollection === 'wpage') {
          // Для wpage используем CSS и JS из полей записи
          extractedCss = cssContent;
          extractedJs = jsContent;
          // Для wpage используем HTML как есть, но очищаем его
          cleanedHtml = cleanHtml(htmlContent);
        } else {
          // Для files извлекаем CSS и JS из HTML
          const extractResult = extractHtmlElementsWithScreenshotData(htmlContent, splitOption);
          extractedCss = extractResult.cssContent;
          extractedJs = extractResult.jsContent;
          cleanedHtml = extractResult.cleanedHtml;
        }

        const { elements } = extractHtmlElementsWithScreenshotData(htmlContent, splitOption);
        console.log(`✅ HTML обработан, найдено ${elements.length} элементов`);
        console.log(`✅ Используется CSS: ${extractedCss.length} символов, JS: ${extractedJs.length} символов`);

        if (elements.length === 0) {
          console.warn('⚠️ Не найдено элементов для извлечения в HTML');
          results.push({
            fileId,
            fileName,
            success: true,
            elementsExtracted: 0,
            elementsAdded: 0,
            message: 'Не найдено элементов для извлечения'
          });
          continue;
        }

        // 5. Создаем полный HTML документ для скриншотов
        const fullHtmlDocument = createFullHtmlDocument(cleanedHtml, extractedCss, extractedJs, fileName);

        // 6. Подготавливаем элементы для создания
        const elementsToCreate = [];

        // Счетчики для уникальных названий
        const typeCounters = {};
        const titleCounters = {};

        // 7. Обрабатываем каждый элемент
        for (let i = 0; i < elements.length; i++) {
          const element = elements[i];

          // Пропускаем пустые или слишком маленькие элементы
          if (!element.html.trim() || element.html.length < 10) {
            console.log(`⏭️ Пропускаем элемент ${i + 1}: слишком маленький (${element.html.length} символов)`);
            continue;
          }

          console.log(`🔎 Анализ элемента ${i + 1} типов "${element.types.join(', ')}"...`);

          // Генерируем структуру DOM дерева для поля composition
          const treeStructure = generateTreeStructure(element.html);

          // Используем первый тип для нумерации (основной тип)
          const primaryType = element.types[0];

          // Инициализируем счетчики для типа элемента
          if (!typeCounters[primaryType]) {
            typeCounters[primaryType] = 0;
          }
          typeCounters[primaryType]++;

          // Создаем базовое название
          const baseTitle = `${fileName} ${primaryType}`;

          // Инициализируем счетчик для названия
          if (!titleCounters[baseTitle]) {
            titleCounters[baseTitle] = 0;
          }
          titleCounters[baseTitle]++;

          // Форматируем порядковые номера как двузначные числа
          const typeNumber = String(typeCounters[primaryType]).padStart(2, '0');
          const instanceNumber = String(titleCounters[baseTitle]).padStart(2, '0');

          // Создаем уникальные title и number
          const elementTitle = titleCounters[baseTitle] > 1
            ? `${baseTitle} ${instanceNumber}`
            : baseTitle;

          const elementNumber = titleCounters[baseTitle] > 1
            ? `${fileNumber}-${typeNumber}-${instanceNumber}`
            : `${fileNumber}-${typeNumber}`;

          // Создаем новый элемент для welem_proto
          const newElement = {
            title: elementTitle,
            number: elementNumber,
            status: 'idea', // По умолчанию "Идея"
            html: decodeHtmlEntities(element.html), // Декодируем HTML-сущности в атрибутах
            composition: treeStructure || '', // Структура DOM дерева
            elem_type: element.types, // Все типы элемента (массив для поля типа "теги")
            date_updated: new Date().toISOString(),
            // Добавляем CSS и JS
            css: extractedCss || '',
            js: extractedJs || '',
            // Устанавливаем collection на основе тегов файла
            collection: fileTags.length > 0 ? fileTags : [],
            tags: fileTags,
            file: fileId,
            // Добавляем информацию для создания скриншота
            _screenshotData: {
              fullHtmlDocument: fullHtmlDocument,
              selector: element.selector,
              elementTitle: elementTitle,
              elementNumber: elementNumber
            }
          };

          // Пробуем сконвертировать HTML в handlebars и JSON
          try {
            console.log(`🔄 Генерация handlebars и JSON для элемента ${elementTitle}...`);
            const result = htmlToHandlebarsAndJson(element.html, elementTitle, elementNumber);

            if (result.success) {
              newElement.hbs = result.hbsTemplate || '';
              newElement.json = JSON.stringify(result.jsonData || {}, null, 2);
              console.log(`✅ Успешно сгенерирован шаблон и JSON для элемента ${elementTitle}`);
            } else {
              console.warn(`⚠️ Проблема при генерации шаблона для элемента ${elementTitle}: ${result.error || 'Неизвестная ошибка'}`);
            }
          } catch (convErr) {
            console.error(`❌ Ошибка при конвертации элемента ${elementTitle}:`, convErr);
          }

          console.log(`🏷️ Установлена коллекция: ${newElement.collection.join(', ') || 'не указана'}`);
          console.log(`📝 Подготовлен элемент для создания: ${newElement.title}`);

          // Добавляем элемент в список для возврата клиенту
          elementsToCreate.push(newElement);
          totalElements++;
        }

        // Возвращаем элементы клиенту
        results.push({
          fileId,
          fileName,
          success: true,
          elementsExtracted: elements.length,
          elementsToCreate: elementsToCreate,
        });

        processedFiles++;

      } catch (fileError) {
        console.error(`❌ Ошибка при обработке файла ${fileId}:`, fileError);
        results.push({
          fileId,
          success: false,
          error: fileError.message
        });
      }
    }

    console.log(`🏁 Итоговый результат: обработано ${processedFiles} файлов, извлечено ${totalElements} элементов`);

    return {
      success: true,
      processedFiles,
      results
    };

  } catch (error) {
    console.error('❌ Общая ошибка при извлечении элементов:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message
    });
  }
});
