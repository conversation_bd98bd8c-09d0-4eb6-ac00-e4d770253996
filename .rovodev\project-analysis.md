# Анализ проекта для эффективной работы с Rovo Dev

## Обзор проекта

Это комплексная система управления контентом и генерации веб-компонентов, построенная на Nuxt 3 + PrimeVue. Проект представляет собой мощный инструмент для создания, редактирования и управления различными типами веб-контента с возможностями автоматической генерации скриншотов, анализа HTML и преобразования в шаблоны.

## Архитектура проекта

### Технологический стек
- **Frontend**: Nuxt 3, Vue 3 (Composition API), TypeScript
- **UI Framework**: PrimeVue 4.x с темизацией
- **Стилизация**: TailwindCSS + UnoCSS
- **Backend**: Nuxt Server API (Nitro)
- **CMS**: Directus (headless CMS)
- **Инструменты**: Puppeteer (скриншоты), Cheerio (HTML парсинг), Handlebars (шаблоны)

### Структура данных (основные коллекции)

1. **directus_files** - файлы и медиа-контент
2. **wpage** - страницы сайтов
3. **wblock_proto** - прототипы блоков
4. **welem_proto** - прототипы элементов
5. **wjson** - JSON-данные для контента

## Ключевые страницы и их функциональность

### 1. files2.vue - Управление файлами
**Назначение**: Центральная система управления файлами с расширенными возможностями обработки

**Основные функции**:
- Массовое редактирование файлов
- Разделение HTML-файлов на блоки
- Извлечение элементов из HTML
- Генерация скриншотов
- Сохранение как страницы

**Ключевые особенности**:
- Поддержка различных вариантов разделения (simple, advanced, semantic)
- Интеграция с BulkFormContainer для массовых операций
- Фильтрация по тегам и типам файлов

### 2. wpages.vue - Управление страницами
**Назначение**: Управление веб-страницами с возможностями анализа и генерации

**Основные функции**:
- CRUD операции для страниц
- Создание страниц по URL
- Генерация эскизов и скриншотов
- Разделение на блоки и элементы
- Массовое дублирование

**Архитектурные особенности**:
- Использует UniversalDataTable для отображения
- Интегрирован с системой тегов и типов страниц
- Поддержка фильтрации и поиска

### 3. wblock-proto2.vue - Прототипы блоков
**Назначение**: Управление прототипами блоков с расширенными фильтрами

**Основные функции**:
- Создание и редактирование блоков
- Анализ HTML-контента
- Извлечение элементов
- Система статусов и категоризации

**Уникальные возможности**:
- UniversalFilterPanel с множественными фильтрами
- Поддержка различных режимов просмотра
- Интеграция с системой концептов и стилей

### 4. welem-proto.vue - Прототипы элементов
**Назначение**: Управление отдельными UI-элементами

**Основные функции**:
- Каталогизация элементов по типам
- Анализ и генерация скриншотов
- Группировка по коллекциям

### 5. wjson.vue - Управление JSON-данными
**Назначение**: Работа с структурированными данными и контентом

**Уникальные особенности**:
- Двухэкранный режим с конструктором контента
- ContentLibraryConstructor для работы с примитивами
- Система применения контента к формам

### 6. graphics.vue - Генератор графики
**Назначение**: Создание и управление графическими элементами

**Режимы работы**:
- Стандартный режим
- Режим комбинаций
- Массовые операции с формами

### 7. wblock-html-gen.vue - Генератор HTML-блоков
**Назначение**: Визуальный конструктор HTML-блоков

**Ключевые компоненты**:
- ElementsPanel для управления элементами
- Preview с поддержкой различных viewport
- Интеграция с PrismEditor для редактирования кода
- Двойной режим: блок/страница

**Архитектурные решения**:
- Система Undo/Redo
- Динамическое изменение размеров панелей
- Поддержка CSS/JS кастомизации

### 8. wblock-page-gen.vue - Генератор страниц из блоков
**Назначение**: Создание страниц путем комбинирования блоков

**Особенности**:
- Drag & Drop интерфейс
- Предпросмотр в реальном времени
- Система viewport переключений
- Интеграция с BlockForm для редактирования

### 9. wblock-gen2.vue - Расширенный генератор блоков
**Назначение**: Многорежимный генератор с продвинутыми возможностями

**Режимы работы**:
- **Standard**: Базовая генерация JSON/HBS
- **Combination**: Создание комбинаций блоков на досках
- **Content**: Интеллектуальный конструктор контента
- **Prototyping**: Быстрое прототипирование страниц

**Инновационные функции**:
- Система досок для комбинирования
- Предпросмотр в iframe с различными viewport
- Автоматическая генерация скриншотов
- Сохранение страниц и блоков одновременно

### 10. welem-gen2.vue - Расширенный генератор элементов
**Назначение**: Продвинутая работа с элементами

**Режимы работы**:
- **Combination**: Создание комбинаций элементов
- **Variation**: Генерация вариаций элементов
- **Theming**: Применение тем к элементам
- **Content**: Наполнение элементов контентом

## Универсальные компоненты

### BulkFormContainer.vue
**Назначение**: Универсальный контейнер для массовых операций

**Возможности**:
- Создание множественных форм
- Горизонтальная прокрутка форм
- Индивидуальное сохранение
- Поддержка различных коллекций

### UniversalDataTable.vue
**Назначение**: Стандартизированная таблица данных

### ContentLibraryConstructor.vue
**Назначение**: Конструктор для работы с библиотекой контента

**Функции**:
- Управление примитивами контента
- Фильтрация по типам и подтипам
- Применение к формам

## Серверная архитектура

### API Endpoints (ключевые)
- `html-to-template.post.ts` - Конвертация HTML в HBS+JSON
- `capture-*-screenshot.post.ts` - Генерация скриншотов
- `analyze-*.post.ts` - Анализ HTML контента
- `extract-*.post.ts` - Извлечение элементов
- `split-html-files.js` - Разделение HTML файлов

### Утилиты
- `htmlToTemplate.js` - Основная логика конвертации
- `htmlAnalyzer.ts` - Анализ HTML структуры
- `elementTypeMapping.ts` - Маппинг типов элементов

## Паттерны и архитектурные решения

### 1. Композиция API
Все компоненты используют `<script setup>` с Composition API:
```typescript
// Стандартный паттерн состояния
const loading = ref(false)
const selectedItems = ref([])
const filteredItems = computed(() => /* логика фильтрации */)
```

### 2. Универсальные фильтры
Система фильтрации через MultiSelect компоненты:
```vue
<MultiSelect
  v-model="selectedTags"
  :options="availableTags"
  display="chip"
  class="text-xs"
/>
```

### 3. Массовые операции
Паттерн для массовых операций:
```typescript
const bulkOperation = async (items: any[]) => {
  loading.value = true
  try {
    for (const item of items) {
      await processItem(item)
    }
  } finally {
    loading.value = false
  }
}
```

### 4. Динамические панели
Система изменяемых размеров панелей:
```typescript
const leftSidebarWidth = ref('400px')
const canvasWidth = computed(() => `calc(100% - ${leftSidebarWidth.value})`)
```

### 5. Режимы работы
Переключение между режимами через reactive состояние:
```typescript
const currentMode = ref('standard')
const workModes = [
  { value: 'standard', label: 'Стандартный' },
  { value: 'combination', label: 'Комбинации' }
]
```

## Интеграции и зависимости

### Directus CMS
- Использование `nuxt-directus` модуля
- Composables: `useDirectusItems`, `useDirectusFiles`
- Автоматическая типизация коллекций

### PrimeVue компоненты
Основные используемые компоненты:
- DataTable (с виртуализацией)
- MultiSelect (для фильтров)
- Button, InputText, Textarea
- TabView, Card, Image
- Toast для уведомлений

### Puppeteer для скриншотов
- Серверная генерация скриншотов
- Поддержка различных viewport
- Batch обработка

## Особенности кодовой базы

### 1. Мультиязычность
- Интерфейс на русском языке
- Комментарии и документация на русском
- Английские названия переменных и функций

### 2. Типизация
- Частичная типизация TypeScript
- Интерфейсы в `app/types/`
- Строгий режим отключен

### 3. Стилизация
- Utility-first подход с TailwindCSS
- Кастомные CSS классы для специфичных компонентов
- Темная тема через PrimeVue

### 4. Производительность
- Виртуализация в DataTable
- Debounced поиск
- Lazy loading изображений

## Рекомендации для работы с проектом

### 1. При добавлении новых страниц
- Следовать паттерну существующих страниц
- Использовать BulkFormContainer для массовых операций
- Интегрировать с системой фильтров

### 2. При работе с API
- Выносить сложную логику в серверные endpoints
- Использовать существующие утилиты для HTML обработки
- Следовать паттерну error handling

### 3. При создании компонентов
- Использовать Composition API
- Следовать naming conventions
- Интегрировать с PrimeVue темизацией

### 4. При работе с данными
- Использовать Directus composables
- Кешировать тяжелые операции
- Валидировать входные данные

## Потенциальные области для улучшения

1. **Типизация**: Усиление TypeScript типизации
2. **Тестирование**: Добавление unit/integration тестов
3. **Документация**: Расширение JSDoc комментариев
4. **Производительность**: Оптимизация больших списков
5. **Accessibility**: Улучшение доступности интерфейса

## Заключение

Проект представляет собой мощную и гибкую систему для управления веб-контентом с уникальными возможностями генерации и анализа. Архитектура хорошо продумана и масштабируема, с четким разделением ответственности между компонентами. Для эффективной работы важно понимать паттерны и следовать установленным конвенциям.