<template>
  <div class="theme-selector">
    <h4 class="text-md font-semibold mb-3">Выбор и настройка тем</h4>
    
    <!-- Готовые темы -->
    <div class="predefined-themes mb-4">
      <h5 class="text-sm font-semibold mb-2">Готовые темы</h5>
      <div class="grid grid-cols-3 gap-3">
        <div
          v-for="theme in availableThemes"
          :key="theme.name"
          class="theme-card p-3 border rounded cursor-pointer transition-all"
          :class="{
            'border-blue-500 bg-blue-50': selectedThemes.includes(theme.name),
            'border-gray-200 hover:border-gray-300': !selectedThemes.includes(theme.name)
          }"
          @click="toggleTheme(theme.name)"
        >
          <div class="text-sm font-medium mb-1">{{ theme.label }}</div>
          <div class="text-xs text-gray-500 mb-2">{{ theme.description }}</div>
          <div class="flex gap-1 mb-2">
            <div
              v-for="color in theme.colors"
              :key="color"
              class="w-4 h-4 rounded border"
              :style="{ backgroundColor: color }"
            />
          </div>
          <div class="text-xs text-gray-600">{{ theme.fontFamily }}</div>
        </div>
      </div>
    </div>

    <!-- Кастомная тема -->
    <div class="custom-theme mb-4">
      <div class="flex items-center gap-2 mb-2">
        <h5 class="text-sm font-semibold">Кастомная тема</h5>
        <Button
          icon="pi pi-plus"
          class="p-button-outlined text-xs p-1"
          @click="showCustomThemeEditor = !showCustomThemeEditor"
        />
      </div>
      
      <div v-if="showCustomThemeEditor" class="custom-theme-editor p-3 border rounded bg-white">
        <div class="grid grid-cols-2 gap-4">
          <!-- Основные настройки -->
          <div>
            <label class="text-xs font-medium block mb-1">Название темы</label>
            <InputText
              v-model="customTheme.name"
              placeholder="Моя тема"
              class="text-xs w-full"
            />
          </div>
          
          <div>
            <label class="text-xs font-medium block mb-1">Описание</label>
            <InputText
              v-model="customTheme.description"
              placeholder="Описание темы"
              class="text-xs w-full"
            />
          </div>
          
          <!-- Цветовая палитра -->
          <div class="col-span-2">
            <label class="text-xs font-medium block mb-1">Цветовая палитра</label>
            <div class="flex gap-2">
              <div
                v-for="(color, index) in customTheme.colors"
                :key="index"
                class="flex flex-col items-center gap-1"
              >
                <input
                  v-model="customTheme.colors[index]"
                  type="color"
                  class="w-8 h-8 rounded border cursor-pointer"
                />
                <Button
                  icon="pi pi-trash"
                  class="p-button-text p-button-danger text-xs p-1"
                  @click="removeColor(index)"
                />
              </div>
              <Button
                icon="pi pi-plus"
                class="p-button-outlined text-xs"
                @click="addColor"
              />
            </div>
          </div>
          
          <!-- Шрифт -->
          <div>
            <label class="text-xs font-medium block mb-1">Семейство шрифтов</label>
            <Dropdown
              v-model="customTheme.fontFamily"
              :options="fontFamilies"
              option-label="label"
              option-value="value"
              class="text-xs w-full"
            />
          </div>
          
          <!-- Замены классов -->
          <div>
            <label class="text-xs font-medium block mb-1">Замены классов</label>
            <Button
              label="Настроить"
              class="text-xs p-button-outlined"
              @click="showClassReplacements = !showClassReplacements"
            />
          </div>
          
          <!-- Детальные замены классов -->
          <div v-if="showClassReplacements" class="col-span-2">
            <div class="class-replacements p-2 border rounded bg-gray-50">
              <div
                v-for="(replacement, className) in customTheme.classReplacements"
                :key="className"
                class="flex items-center gap-2 mb-2"
              >
                <InputText
                  :model-value="className"
                  placeholder="Исходный класс"
                  class="text-xs flex-1"
                  readonly
                />
                <span class="text-xs">→</span>
                <InputText
                  v-model="customTheme.classReplacements[className]"
                  placeholder="Новый класс"
                  class="text-xs flex-1"
                />
                <Button
                  icon="pi pi-trash"
                  class="p-button-text p-button-danger text-xs p-1"
                  @click="removeClassReplacement(className)"
                />
              </div>
              <div class="flex gap-2">
                <InputText
                  v-model="newClassReplacement.from"
                  placeholder="Исходный класс"
                  class="text-xs flex-1"
                />
                <InputText
                  v-model="newClassReplacement.to"
                  placeholder="Новый класс"
                  class="text-xs flex-1"
                />
                <Button
                  icon="pi pi-plus"
                  class="p-button-outlined text-xs"
                  @click="addClassReplacement"
                />
              </div>
            </div>
          </div>
          
          <!-- CSS переопределения -->
          <div class="col-span-2">
            <label class="text-xs font-medium block mb-1">CSS переопределения</label>
            <Textarea
              v-model="customTheme.cssOverrides"
              placeholder="Дополнительные CSS стили..."
              class="text-xs w-full"
              rows="4"
            />
          </div>
          
          <!-- Действия -->
          <div class="col-span-2 flex gap-2">
            <Button
              label="Сохранить тему"
              class="text-xs"
              @click="saveCustomTheme"
            />
            <Button
              label="Применить к элементам"
              class="text-xs p-button-outlined"
              @click="applyCustomTheme"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Кнопки действий -->
    <div class="flex gap-2">
      <Button
        label="Создать с выбранными темами"
        class="text-xs"
        :disabled="selectedThemes.length === 0"
        @click="createThemedElements"
      />
      <Button
        label="Выбрать все"
        class="text-xs p-button-outlined"
        @click="selectAllThemes"
      />
      <Button
        label="Очистить выбор"
        class="text-xs p-button-outlined"
        @click="clearSelection"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getAllThemes, type ThemeMapping } from '~/utils/theme-mappings'

// Props
interface Props {
  selectedElements: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['themesCreated'])

// Данные
const availableThemes = ref<ThemeMapping[]>(getAllThemes())
const selectedThemes = ref<string[]>([])
const showCustomThemeEditor = ref(false)
const showClassReplacements = ref(false)

const customTheme = ref({
  name: '',
  label: '',
  description: '',
  colors: ['#007bff', '#6c757d', '#28a745'],
  fontFamily: 'Arial, sans-serif',
  classReplacements: {
    'btn-primary': 'btn-primary custom-btn',
    'btn-secondary': 'btn-outline-primary custom-btn',
    'bg-primary': 'bg-primary custom-bg',
    'text-primary': 'text-primary custom-text'
  } as Record<string, string>,
  cssOverrides: ''
})

const newClassReplacement = ref({
  from: '',
  to: ''
})

const fontFamilies = ref([
  { label: 'Arial, sans-serif', value: 'Arial, sans-serif' },
  { label: 'Inter, sans-serif', value: 'Inter, sans-serif' },
  { label: 'Roboto, sans-serif', value: 'Roboto, sans-serif' },
  { label: 'Poppins, sans-serif', value: 'Poppins, sans-serif' },
  { label: 'Playfair Display, serif', value: 'Playfair Display, serif' },
  { label: 'Georgia, serif', value: 'Georgia, serif' },
  { label: 'Courier New, monospace', value: 'Courier New, monospace' }
])

// Методы
const toggleTheme = (themeName: string) => {
  const index = selectedThemes.value.indexOf(themeName)
  if (index > -1) {
    selectedThemes.value.splice(index, 1)
  } else {
    selectedThemes.value.push(themeName)
  }
}

const addColor = () => {
  customTheme.value.colors.push('#000000')
}

const removeColor = (index: number) => {
  if (customTheme.value.colors.length > 1) {
    customTheme.value.colors.splice(index, 1)
  }
}

const addClassReplacement = () => {
  if (newClassReplacement.value.from && newClassReplacement.value.to) {
    customTheme.value.classReplacements[newClassReplacement.value.from] = newClassReplacement.value.to
    newClassReplacement.value = { from: '', to: '' }
  }
}

const removeClassReplacement = (className: string) => {
  delete customTheme.value.classReplacements[className]
}

const saveCustomTheme = () => {
  // Добавляем кастомную тему к доступным темам
  const newTheme: ThemeMapping = {
    name: customTheme.value.name || 'custom',
    label: customTheme.value.label || customTheme.value.name || 'Кастомная тема',
    description: customTheme.value.description || 'Пользовательская тема',
    colors: customTheme.value.colors,
    fontFamily: customTheme.value.fontFamily,
    classReplacements: customTheme.value.classReplacements,
    cssOverrides: customTheme.value.cssOverrides
  }
  
  // Проверяем, не существует ли уже тема с таким именем
  const existingIndex = availableThemes.value.findIndex(t => t.name === newTheme.name)
  if (existingIndex > -1) {
    availableThemes.value[existingIndex] = newTheme
  } else {
    availableThemes.value.push(newTheme)
  }
  
  showCustomThemeEditor.value = false
}

const applyCustomTheme = () => {
  saveCustomTheme()
  const themeName = customTheme.value.name || 'custom'
  if (!selectedThemes.value.includes(themeName)) {
    selectedThemes.value.push(themeName)
  }
  createThemedElements()
}

const selectAllThemes = () => {
  selectedThemes.value = availableThemes.value.map(t => t.name)
}

const clearSelection = () => {
  selectedThemes.value = []
}

const createThemedElements = () => {
  emit('themesCreated', {
    selectedThemes: selectedThemes.value,
    availableThemes: availableThemes.value
  })
}
</script>

<style scoped>
.theme-selector {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.theme-card {
  min-height: 120px;
}

.theme-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.custom-theme-editor {
  background: #ffffff;
  border: 1px solid #dee2e6;
}

.class-replacements {
  max-height: 200px;
  overflow-y: auto;
}
</style>
