import { defineEventHandler, readBody, createError } from 'h3'
import puppeteer from 'puppeteer'
import { load } from 'cheerio'
import { decodeHtmlEntities } from '../utils/htmlToTemplate.js'

interface ExtractPageFromUrlRequest {
  url: string
  splitOption?: number
}

interface ExtractPageFromUrlResponse {
  success: boolean
  data?: {
    bodyHtml: string
    cssContent: string
    jsContent: string
    fullHtml: string
    bodyTag: string
  }
  error?: string
}

export default defineEventHandler(async (event) => {
  const { url, splitOption = 1 } = await readBody<ExtractPageFromUrlRequest>(event)

  console.log(`🔍 Извлечение контента из URL: ${url}`)
  console.log(`🔧 Вариант разделения: ${splitOption}`)

  if (!url) {
    throw createError({
      statusCode: 400,
      statusMessage: 'URL is required'
    })
  }

  try {
    // 1. Получаем HTML контент страницы с помощью Puppeteer (используем проверенный подход)
    console.log('🌐 Загружаем страницу с помощью Puppeteer...')
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })

    let htmlContent = ''
    try {
      const page = await browser.newPage()
      await page.setViewport({ width: 1400, height: 800 })
      
      // Загружаем страницу с увеличенным timeout и лучшим ожиданием
      await page.goto(url, { waitUntil: 'networkidle0', timeout: 60000 })
      
      // Базовая задержка для полной загрузки (как в capture-screenshots)
      console.log('⏱️ Базовая задержка 3000ms для полной загрузки контента...')
      await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))

      // Ждем загрузки всех изображений
      console.log('🖼️ Ожидание загрузки всех изображений...')
      await page.evaluate(() => {
        return Promise.all(
          Array.from(document.images)
            .filter(img => !img.complete)
            .map(img => new Promise(resolve => {
              img.onload = img.onerror = resolve
              setTimeout(resolve, 10000)
            }))
        )
      })
      
      // Получаем полный HTML
      htmlContent = await page.content()
      console.log(`✅ HTML загружен, размер: ${htmlContent.length} символов`)
      
    } finally {
      await browser.close()
    }

    if (!htmlContent || htmlContent.length < 100) {
      throw new Error('Не удалось получить HTML контент или контент слишком мал')
    }

    // 2. Извлекаем контент с учетом варианта разделения
    console.log('🔍 Извлекаем контент страницы...')
    const { bodyHtml, cssContent, jsContent, bodyTag } = await extractPageContent(htmlContent, url, splitOption)

    console.log(`✅ Контент извлечен успешно`)

    return {
      success: true,
      data: {
        bodyHtml,
        cssContent,
        jsContent,
        fullHtml: htmlContent,
        bodyTag
      }
    } as ExtractPageFromUrlResponse

  } catch (error) {
    console.error('❌ Ошибка извлечения контента из URL:', error)
    return {
      success: false,
      error: error.message || 'Unknown error'
    } as ExtractPageFromUrlResponse
  }
})

// Функция извлечения контента страницы (с преобразованием всех ссылок)
async function extractPageContent(html: string, baseUrl: string, splitOption: number) {
  // Определяем префикс пути в зависимости от splitOption
  let pathPrefix: string
  if (splitOption === 2) {
    pathPrefix = 'https://fm-demo.ru/html2/'
  } else if (splitOption === 3) {
    pathPrefix = 'https://fm-demo.ru/html2/'
  } else {
    pathPrefix = 'https://fm-demo.ru/html/'
  }

  console.log(`🔗 Используется префикс для CSS/JS: ${pathPrefix}`)

  // Извлекаем CSS и JavaScript код
  let cssContent = ''
  let jsContent = ''

  try {
    const $extract = load(html, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    })

    console.log('🔍 Извлечение CSS из тегов link и style...')

    // Извлекаем CSS из тегов link и обрабатываем пути
    $extract('link[rel="stylesheet"], link[rel="preconnect"]').each((i, el) => {
      const href = $extract(el).attr('href')

      if (href && !href.startsWith('http://') && !href.startsWith('https://') && !href.startsWith('//')) {
        // ИСПРАВЛЕНО: Используем pathPrefix вместо baseUrl
        const newHref = pathPrefix + href.replace(/^\.?\//, '')
        $extract(el).attr('href', newHref)
        console.log(`🔗 Преобразован CSS путь: ${href} -> ${newHref}`)
      }

      cssContent += $extract.html(el) + '\n'
    })

    // Извлекаем CSS из тегов style
    $extract('style').each((i, el) => {
      cssContent += $extract.html(el) + '\n'
    })

    console.log(`✅ Извлечено ${cssContent.length} символов CSS`)

    console.log('🔍 Извлечение JavaScript из тегов script...')

    // Извлекаем JavaScript из тегов script и обрабатываем пути
    $extract('script').each((i, el) => {
      const src = $extract(el).attr('src')

      if (src && !src.startsWith('http://') && !src.startsWith('https://') && !src.startsWith('//')) {
        // ИСПРАВЛЕНО: Используем pathPrefix вместо baseUrl
        const newSrc = pathPrefix + src.replace(/^\.?\//, '')
        $extract(el).attr('src', newSrc)
        console.log(`🔗 Преобразован JS путь: ${src} -> ${newSrc}`)
      }

      jsContent += $extract.html(el) + '\n'
    })

    console.log(`✅ Извлечено ${jsContent.length} символов JavaScript`)
  } catch (extractError) {
    console.error('❌ Ошибка при извлечении CSS и JavaScript:', extractError)
  }

  // Извлекаем HTML контент и преобразуем все относительные ссылки
  try {
    const $ = load(html, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    })

    // Извлекаем тег body (только открывающий тег) ДО удаления тегов
    console.log('🏷️ Извлечение тега body...')
    let bodyTag = ''
    const bodyElement = $('body')
    if (bodyElement.length > 0) {
      const bodyHtml = $.html(bodyElement)
      const bodyTagMatch = bodyHtml.match(/<body[^>]*>/i)
      bodyTag = bodyTagMatch ? bodyTagMatch[0] : '<body>'
      console.log(`✅ Тег body извлечен: ${bodyTag}`)
    }

    // Преобразуем все относительные ссылки в абсолютные ПЕРЕД удалением тегов
    console.log('🔗 Преобразование всех относительных ссылок в абсолютные...')

    // Обрабатываем атрибуты href
    $('[href]').each((i, el) => {
      const href = $(el).attr('href')
      if (href && !href.startsWith('http://') && !href.startsWith('https://') && !href.startsWith('//') && !href.startsWith('#') && !href.startsWith('mailto:') && !href.startsWith('tel:')) {
        try {
          const absoluteUrl = new URL(href, baseUrl).href
          $(el).attr('href', absoluteUrl)
          console.log(`🔗 href: ${href} -> ${absoluteUrl}`)
        } catch (e) {
          console.warn(`⚠️ Не удалось преобразовать href: ${href}`)
        }
      }
    })

    // Обрабатываем атрибуты src
    $('[src]').each((i, el) => {
      const src = $(el).attr('src')
      if (src && !src.startsWith('http://') && !src.startsWith('https://') && !src.startsWith('//') && !src.startsWith('data:')) {
        try {
          const absoluteUrl = new URL(src, baseUrl).href
          $(el).attr('src', absoluteUrl)
          console.log(`🔗 src: ${src} -> ${absoluteUrl}`)
        } catch (e) {
          console.warn(`⚠️ Не удалось преобразовать src: ${src}`)
        }
      }
    })

    // Обрабатываем атрибуты srcset
    $('[srcset]').each((i, el) => {
      const srcset = $(el).attr('srcset')
      if (srcset) {
        const newSrcset = srcset.split(',').map(item => {
          const parts = item.trim().split(' ')
          const url = parts[0]
          if (url && !url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('//') && !url.startsWith('data:')) {
            try {
              const absoluteUrl = new URL(url, baseUrl).href
              parts[0] = absoluteUrl
              console.log(`🔗 srcset: ${url} -> ${absoluteUrl}`)
            } catch (e) {
              console.warn(`⚠️ Не удалось преобразовать srcset: ${url}`)
            }
          }
          return parts.join(' ')
        }).join(', ')
        $(el).attr('srcset', newSrcset)
      }
    })

    // Обрабатываем background-image в style атрибутах
    $('[style*="background"]').each((i, el) => {
      const style = $(el).attr('style')
      if (style) {
        const newStyle = style.replace(/background-image\s*:\s*url\(['"]?([^'")]+)['"]?\)/gi, (match, url) => {
          if (url && !url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('//') && !url.startsWith('data:')) {
            try {
              const absoluteUrl = new URL(url, baseUrl).href
              console.log(`🔗 background-image: ${url} -> ${absoluteUrl}`)
              return `background-image: url('${absoluteUrl}')`
            } catch (e) {
              console.warn(`⚠️ Не удалось преобразовать background-image: ${url}`)
            }
          }
          return match
        })
        $(el).attr('style', newStyle)
      }
    })

    // Обрабатываем другие атрибуты с URL
    const urlAttributes = ['data-src', 'data-background', 'data-bg', 'poster', 'action']
    urlAttributes.forEach(attr => {
      $(`[${attr}]`).each((i, el) => {
        const value = $(el).attr(attr)
        if (value && !value.startsWith('http://') && !value.startsWith('https://') && !value.startsWith('//') && !value.startsWith('data:') && !value.startsWith('#')) {
          try {
            const absoluteUrl = new URL(value, baseUrl).href
            $(el).attr(attr, absoluteUrl)
            console.log(`🔗 ${attr}: ${value} -> ${absoluteUrl}`)
          } catch (e) {
            console.warn(`⚠️ Не удалось преобразовать ${attr}: ${value}`)
          }
        }
      })
    })

    // ТЕПЕРЬ удаляем теги script и link ПОСЛЕ преобразования ссылок
    console.log('🗑️ Удаление тегов script и link...')
    $('script').remove()
    $('link').remove()

    // Извлекаем HTML без head и script
    let bodyHtml = ''
    if (bodyElement.length > 0) {
      bodyHtml = bodyElement.html() || ''
    } else {
      $('head').remove()
      bodyHtml = $.html()
    }

    // Декодируем HTML-сущности в атрибутах
    const decodedBodyHtml = decodeHtmlEntities(bodyHtml)

    console.log(`✅ Извлечен HTML контент, длина: ${decodedBodyHtml.length} символов`)
    console.log(`✅ Все относительные ссылки преобразованы в абсолютные`)

    return {
      bodyHtml: decodedBodyHtml,
      cssContent: cssContent,
      jsContent: jsContent,
      bodyTag: bodyTag
    }

  } catch (error) {
    console.error('❌ Ошибка при извлечении HTML контента:', error)
    throw new Error('Failed to extract page content')
  }
}
