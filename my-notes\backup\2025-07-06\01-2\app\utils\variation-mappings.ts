// Маппинги для интеллектуальных вариаций элементов

export interface VariationMapping {
  name: string
  description: string
  classReplacements: Record<string, string>
  cssOverrides?: string
}

// Цветовые схемы
export const colorVariations: VariationMapping[] = [
  {
    name: 'primary',
    description: 'Основная цветовая схема',
    classReplacements: {
      'btn-secondary': 'btn-primary',
      'bg-secondary': 'bg-primary',
      'text-secondary': 'text-primary',
      'border-secondary': 'border-primary',
      'btn-outline-secondary': 'btn-outline-primary'
    }
  },
  {
    name: 'success',
    description: 'Зеленая цветовая схема',
    classReplacements: {
      'btn-primary': 'btn-success',
      'btn-secondary': 'btn-success',
      'bg-primary': 'bg-success',
      'bg-secondary': 'bg-success',
      'text-primary': 'text-success',
      'text-secondary': 'text-success',
      'border-primary': 'border-success',
      'border-secondary': 'border-success'
    }
  },
  {
    name: 'warning',
    description: 'Желтая цветовая схема',
    classReplacements: {
      'btn-primary': 'btn-warning',
      'btn-secondary': 'btn-warning',
      'bg-primary': 'bg-warning',
      'bg-secondary': 'bg-warning',
      'text-primary': 'text-warning',
      'text-secondary': 'text-warning',
      'border-primary': 'border-warning',
      'border-secondary': 'border-warning'
    }
  },
  {
    name: 'danger',
    description: 'Красная цветовая схема',
    classReplacements: {
      'btn-primary': 'btn-danger',
      'btn-secondary': 'btn-danger',
      'bg-primary': 'bg-danger',
      'bg-secondary': 'bg-danger',
      'text-primary': 'text-danger',
      'text-secondary': 'text-danger',
      'border-primary': 'border-danger',
      'border-secondary': 'border-danger'
    }
  },
  {
    name: 'info',
    description: 'Синяя цветовая схема',
    classReplacements: {
      'btn-primary': 'btn-info',
      'btn-secondary': 'btn-info',
      'bg-primary': 'bg-info',
      'bg-secondary': 'bg-info',
      'text-primary': 'text-info',
      'text-secondary': 'text-info',
      'border-primary': 'border-info',
      'border-secondary': 'border-info'
    }
  }
]

// Размерные вариации
export const sizeVariations: VariationMapping[] = [
  {
    name: 'xs',
    description: 'Очень маленький размер',
    classReplacements: {
      'btn-lg': 'btn-sm',
      'btn': 'btn btn-sm',
      'h1': 'h6',
      'h2': 'h6',
      'h3': 'h6',
      'display-1': 'h6',
      'display-2': 'h6',
      'display-3': 'h6',
      'display-4': 'h6',
      'p-3': 'p-1',
      'p-4': 'p-2',
      'p-5': 'p-2',
      'm-3': 'm-1',
      'm-4': 'm-2',
      'm-5': 'm-2'
    }
  },
  {
    name: 'sm',
    description: 'Маленький размер',
    classReplacements: {
      'btn-lg': 'btn',
      'h1': 'h5',
      'h2': 'h5',
      'display-1': 'h5',
      'display-2': 'h5',
      'display-3': 'h5',
      'display-4': 'h5',
      'p-4': 'p-3',
      'p-5': 'p-3',
      'm-4': 'm-3',
      'm-5': 'm-3'
    }
  },
  {
    name: 'lg',
    description: 'Большой размер',
    classReplacements: {
      'btn': 'btn btn-lg',
      'btn-sm': 'btn-lg',
      'h6': 'h3',
      'h5': 'h3',
      'h4': 'h2',
      'h3': 'h1',
      'p-1': 'p-3',
      'p-2': 'p-4',
      'm-1': 'm-3',
      'm-2': 'm-4'
    }
  },
  {
    name: 'xl',
    description: 'Очень большой размер',
    classReplacements: {
      'btn': 'btn btn-lg',
      'btn-sm': 'btn-lg',
      'h6': 'h2',
      'h5': 'h2',
      'h4': 'h1',
      'h3': 'display-4',
      'h2': 'display-3',
      'h1': 'display-2',
      'p-1': 'p-4',
      'p-2': 'p-5',
      'p-3': 'p-5',
      'm-1': 'm-4',
      'm-2': 'm-5',
      'm-3': 'm-5'
    }
  }
]

// Стили границ
export const borderVariations: VariationMapping[] = [
  {
    name: 'rounded',
    description: 'Скругленные углы',
    classReplacements: {
      'rounded-0': 'rounded',
      'rounded-1': 'rounded-3',
      'rounded-2': 'rounded-3'
    },
    cssOverrides: `
      .variation-rounded * {
        border-radius: 0.5rem !important;
      }
    `
  },
  {
    name: 'pill',
    description: 'Таблетка (полностью скругленные)',
    classReplacements: {
      'rounded': 'rounded-pill',
      'rounded-1': 'rounded-pill',
      'rounded-2': 'rounded-pill',
      'rounded-3': 'rounded-pill'
    },
    cssOverrides: `
      .variation-pill * {
        border-radius: 50px !important;
      }
    `
  },
  {
    name: 'sharp',
    description: 'Острые углы',
    classReplacements: {
      'rounded': 'rounded-0',
      'rounded-1': 'rounded-0',
      'rounded-2': 'rounded-0',
      'rounded-3': 'rounded-0',
      'rounded-pill': 'rounded-0'
    },
    cssOverrides: `
      .variation-sharp * {
        border-radius: 0 !important;
      }
    `
  },
  {
    name: 'thick-border',
    description: 'Толстые границы',
    classReplacements: {
      'border': 'border border-3',
      'border-1': 'border-3',
      'border-2': 'border-4'
    },
    cssOverrides: `
      .variation-thick-border * {
        border-width: 3px !important;
      }
    `
  }
]

// Теневые эффекты
export const shadowVariations: VariationMapping[] = [
  {
    name: 'light-shadow',
    description: 'Легкая тень',
    classReplacements: {},
    cssOverrides: `
      .variation-light-shadow * {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
      }
    `
  },
  {
    name: 'medium-shadow',
    description: 'Средняя тень',
    classReplacements: {},
    cssOverrides: `
      .variation-medium-shadow * {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
      }
    `
  },
  {
    name: 'heavy-shadow',
    description: 'Сильная тень',
    classReplacements: {},
    cssOverrides: `
      .variation-heavy-shadow * {
        box-shadow: 0 8px 16px rgba(0,0,0,0.2) !important;
      }
    `
  },
  {
    name: 'colored-shadow',
    description: 'Цветная тень',
    classReplacements: {},
    cssOverrides: `
      .variation-colored-shadow * {
        box-shadow: 0 4px 8px rgba(0,123,255,0.3) !important;
      }
    `
  }
]

// Функция применения вариации к HTML
export function applyVariationToHtml(html: string, hbs: string, variation: VariationMapping): { html: string, hbs: string } {
  let modifiedHtml = html
  let modifiedHbs = hbs

  // Применяем замены классов к HTML
  for (const [oldClass, newClass] of Object.entries(variation.classReplacements)) {
    const regex = new RegExp(`\\b${oldClass}\\b`, 'g')
    modifiedHtml = modifiedHtml.replace(regex, newClass)
    modifiedHbs = modifiedHbs.replace(regex, newClass)
  }

  // Добавляем CSS overrides если есть
  if (variation.cssOverrides) {
    const wrapperClass = `variation-${variation.name}`
    modifiedHtml = `<div class="${wrapperClass}">${modifiedHtml}</div>`
  }

  return { html: modifiedHtml, hbs: modifiedHbs }
}

// Функция получения CSS для вариации
export function getVariationCSS(variation: VariationMapping): string {
  return variation.cssOverrides || ''
}

// Функция получения всех вариаций по типу
export function getVariationsByType(type: string): VariationMapping[] {
  switch (type) {
    case 'colors':
      return colorVariations
    case 'sizes':
      return sizeVariations
    case 'borders':
      return borderVariations
    case 'shadows':
      return shadowVariations
    default:
      return []
  }
}
