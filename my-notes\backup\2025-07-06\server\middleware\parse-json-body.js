export default defineEventHandler(async (event) => {
    try {
      if (event.req.headers['content-type'] === 'application/json') {
        const body = await new Promise((resolve, reject) => {
          let data = '';
          event.req.on('data', (chunk) => {
            data += chunk;
          });
  
          event.req.on('end', () => {
            try {
              resolve(JSON.parse(data));
            } catch (error) {
              reject(error);
            }
          });
  
          event.req.on('error', (error) => {
            reject(error);
          });
        });
  
        event.req.body = body;
      }
    } catch (error) {
      console.error('Ошибка при парсинге JSON тела запроса:', error);
      // Можно вернуть ошибку клиенту, если необходимо
    }
  });
  