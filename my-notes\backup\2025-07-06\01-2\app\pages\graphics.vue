<template>
  <div class="graphics-page flex h-screen">
    <!-- Основной контент -->
    <div class="flex-1 overflow-hidden flex flex-col" :class="{ 'pr-[30rem]': sidebarVisible }">
      <!-- 1. Верхняя панель с переключателями режимов -->
      <div class="top-panel flex items-center justify-between p-2 border-b bg-surface-0 dark:bg-surface-900">
        <h1 class="text-lg font-bold">🎨 Генератор графики</h1>

        <div class="flex items-center gap-2">
          <!-- Переключатель режимов -->
          <div class="flex gap-1 mr-2">
            <Button
              v-tooltip.bottom="'Стандартный режим'"
              :class="['text-xs', workMode === 'standard' ? 'p-button-info' : 'p-button-outlined']"
              icon="pi pi-table"
              
              @click="workMode = 'standard'"
            />
            <Button
              v-tooltip.bottom="'Режим комбинаций'"
              :class="['text-xs', workMode === 'combinations' ? 'p-button-info' : 'p-button-outlined']"
              icon="pi pi-th-large"
              
              @click="workMode = 'combinations'"
            />
          </div>

          <!-- Массовые операции -->
          <Button
            v-tooltip.bottom="'Массовое добавление'"
            icon="pi pi-plus-circle"
            class="text-xs p-button-success"
            @click="openBulkCreate"
          />
          <Button
            v-tooltip.bottom="'Массовое редактирование'"
            icon="pi pi-pencil"
            class="text-xs p-button-primary"
            :disabled="!selectedItems.length"
            @click="openBulkEdit"
          />
        </div>
      </div>

    <!-- 2. Область массовых форм (горизонтальная прокрутка) -->
    <div v-if="bulkFormVisible" class="bulk-forms-area bg-surface-50 dark:bg-surface-800 border-b p-1">
      <div class="flex items-center justify-between mb-1">
        <h3 class="text-sm font-semibold">📝 Массовое {{ bulkFormMode === 'create' ? 'добавление' : 'редактирование' }}</h3>
        <div class="flex gap-2">
          <Button            
            icon="pi pi-check"
            class="p-button-sm p-button-text p-button-success"
            @click="saveBulkForms"
          />
          <Button
            icon="pi pi-times"
            class="p-button-sm p-button-text"
            @click="closeBulkForms"
          />
        </div>
      </div>
      
      <!-- Горизонтальная прокрутка форм -->
      <div class="forms-scroll overflow-x-auto overflow-y-hidden" style="height: 400px;">
        <div class="flex gap-2 h-full" style="min-width: fit-content;">
          <div
            v-for="(form, index) in bulkForms"
            :key="form.id"
            class="bulk-form-container flex-shrink-0 bg-white dark:bg-surface-900 border rounded overflow-y-auto"
            style="width: 420px; height: 100%; padding: 8px;"
          >
            <!-- Заголовок формы -->
            <div class="flex items-center justify-between mb-1">
              <span class="text-xs font-semibold">Форма {{ index + 1 }}</span>
              <div class="flex gap-1">
                <Button
                  v-tooltip="'Сохранить'"
                  icon="pi pi-save"
                  class="p-button-text p-button-sm"
                  style="width: 16px; height: 16px; padding: 0"
                  @click="saveSingleForm(form)"
                />
                <Button
                  v-tooltip="'Удалить форму'"
                  icon="pi pi-trash"
                  class="p-button-text p-button-sm p-button-danger"
                  style="width: 16px; height: 16px; padding: 0"
                  @click="removeBulkForm(index)"
                />
              </div>
            </div>

            <!-- Поля формы -->
            <div class="space-y-1">
              <!-- Первая строка: image -->
              <div class="flex gap-1">
                <div class="w-1/3">
                  <InputText
                    v-model="form.art"
                    placeholder="Артикул"
                    class="w-full"
                    style="font-size: 9px; padding: 2px; height: 20px"
                  />
                </div>
                <div class="w-2/3">
                  <InputText
                    v-model="form.title"
                    placeholder="Название"
                    class="w-full"
                    style="font-size: 9px; padding: 2px; height: 20px"
                  />
                </div>
              </div>

              <!-- Вторая строка: tags, collection -->
              <div class="flex gap-1">
                <div class="w-1/2">
                  <MultiSelect
                    v-model="form.tags"
                    :options="tagOptions"
                    placeholder="Теги"
                    display="chip"
                    class="w-full"
                    style="font-size: 9px; min-height: 20px"
                  />
                </div>
                <div class="w-1/2">
                  <MultiSelect
                    v-model="form.collection"
                    :options="collectionOptions"
                    placeholder="Коллекция"
                    display="chip"
                    class="w-full"
                    style="font-size: 9px; min-height: 20px"
                  />
                </div>
              </div>

              <!-- Третья строка: description -->
              <div>
                <Textarea
                  v-model="form.description"
                  placeholder="Описание"
                  rows="1"
                  class="w-full"
                  style="font-size: 9px; padding: 2px; min-height: 20px"
                />
              </div>

              <!-- Табы -->
              <TabView
              class="text-xs compact-tabs"
              :pt="{
                panelcontainer: { style: 'padding:0' },
              }">
                <TabPanel
                header="JSON" value="json"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                  <PrismEditorWithCopy
                    v-model="form.json"
                    :highlight="highlightJson"
                    placeholder='{"image": "url", "imageBackground": "url"}'
                    field-name="JSON"
                    
                    class="my-editor"
                    @update:model-value="updateFormFields(form)"
                  />
                </TabPanel>

                <TabPanel
                header="Редактор" value="editor"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                  <div class="space-y-1">
                    <!-- Быстрое добавление переменных -->
                    <div class="flex gap-1 mb-2">
                      <Button
                        label="+ image"
                        class="p-button-sm p-button-outlined"
                        style="font-size: 9px; padding: 1px 4px"
                        @click="addQuickVariable(form, 'image')"
                      />
                      <Button
                        label="+ imageBackground"
                        class="p-button-sm p-button-outlined"
                        style="font-size: 9px; padding: 1px 4px"
                        @click="addQuickVariable(form, 'imageBackground')"
                      />
                    </div>
                    
                    <!-- Поля редактора -->
                    <div v-for="(field, fieldIndex) in form.fields" :key="fieldIndex" class="flex gap-1 items-start mb-1">
                      <div class="w-1/4">
                        <InputText
                          v-model="field.key"
                          placeholder="Название"
                          class="w-full"
                          style="font-size: 9px; padding: 1px; height: 18px"
                          @input="updateFormJson(form)"
                        />
                      </div>
                      <div class="w-1/3 flex gap-1">
                        <InputText
                          :value="getBulkFieldInputValue(field)"
                          placeholder="Значение"
                          class="flex-1"
                          style="font-size: 9px; padding: 1px; height: 18px"
                          :class="fieldParseErrors[`${form.id}_${fieldIndex}`] ? 'border-red-500' : ''"
                          @input="handleFieldInput(form, field, $event.target.value, fieldIndex)"
                        />
                        <Button
                          v-tooltip="'Копировать'"
                          icon="pi pi-copy"
                          class="p-button-text p-button-sm"
                          style="width: 24px; height: 18px; padding: 0"
                          @click="copyToClipboard(field.value)"
                        />
                      </div>
                      <div class="w-1/6">
                        <Image
                          v-if="field.value && isValidImageUrl(field.value)"
                          :src="field.value"
                          alt="Preview"
                          width="30"
                          height="18"
                          preview
                          class="border rounded"
                        />
                      </div>
                      <div class="w-1/6 flex gap-1">
                        <Button
                          v-tooltip="'Вверх'"
                          icon="pi pi-arrow-up"
                          class="p-button-text p-button-sm"
                          style="width: 12px; height: 18px; padding: 0"
                          :disabled="fieldIndex === 0"
                          @click="moveFieldUp(form, fieldIndex)"
                        />
                        <Button
                          v-tooltip="'Вниз'"
                          icon="pi pi-arrow-down"
                          class="p-button-text p-button-sm"
                          style="width: 12px; height: 18px; padding: 0"
                          :disabled="fieldIndex === form.fields.length - 1"
                          @click="moveFieldDown(form, fieldIndex)"
                        />
                        <Button
                          v-tooltip="'Дублировать'"
                          icon="pi pi-clone"
                          class="p-button-text p-button-sm"
                          style="width: 12px; height: 18px; padding: 0"
                          @click="duplicateField(form, fieldIndex)"
                        />
                        <Button
                          v-tooltip="'Удалить'"
                          icon="pi pi-trash"
                          class="p-button-text p-button-sm p-button-danger"
                          style="width: 12px; height: 18px; padding: 0"
                          @click="removeField(form, fieldIndex)"
                        />
                      </div>
                    </div>

                    <Button
                      label="+ Добавить поле"
                      class="p-button-sm p-button-text w-full"
                      @click="addField(form)"
                    />
                  </div>
                </TabPanel>

                <TabPanel
header="Галерея" value="gallery"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                  <div class="grid grid-cols-3 gap-1">
                    <div
                      v-for="(imageField, imgIndex) in getImageFields(form)"
                      :key="imgIndex"
                      class="flex flex-col"
                    >
                      <div class="aspect-square mb-1" style="height: 50px;">
                        <Image
                          v-if="imageField.value && isValidImageUrl(imageField.value)"
                          :src="imageField.value"
                          :alt="imageField.key"
                          width="50"
                          height="50"
                          preview
                          class="w-full h-full object-cover border rounded"
                        />
                        <div v-else class="w-full h-full bg-gray-100 border rounded flex items-center justify-center">
                          <span class="text-xs text-gray-400">{{ imageField.key }}</span>
                        </div>
                      </div>
                      <div class="flex gap-1">
                        <InputText
                          v-model="imageField.value"
                          class="flex-1"
                          style="font-size: 8px; padding: 1px; height: 16px"
                          @input="updateFormJson(form)"
                        />
                        <Button
                          v-tooltip="'Копировать URL'"
                          icon="pi pi-copy"
                          class="p-button-text p-button-sm"
                          style="width: 16px; height: 16px; padding: 0"
                          @click="copyToClipboard(imageField.value)"
                        />
                      </div>
                    </div>
                  </div>
                </TabPanel>
              </TabView>
            </div>
          </div>

          <!-- Кнопка добавления новой формы -->
          <div class="flex-shrink-0 flex items-center justify-center" style="width: 100px;">
            <Button
              icon="pi pi-plus"
              class="p-button-rounded p-button-outlined"
              @click="addBulkForm"
            />
          </div>
        </div>
      </div>
    </div>

      <!-- 3. Область быстрого добавления в существующие записи -->
      <div class="quick-add-area bg-surface-100 dark:bg-surface-800 border-b p-2">
        <div class="flex items-center gap-2">
          <span class="text-sm font-semibold">⚡ Быстрое добавление:</span>

          <!-- image поле -->
          <div class="flex flex-col gap-1">
            <div v-for="(img, idx) in quickAddImages" :key="'img'+idx" class="flex items-center gap-1">
              <span class="text-xs font-medium">image{{idx > 0 ? idx+1 : ''}}:</span>
              <InputText
                v-model="quickAddImages[idx]"
                placeholder="URL изображения"
                class="w-48 text-xs"
                style="font-size: 10px; padding: 4px"
              />
              <Image
                v-if="img && isValidImageUrl(img)"
                :src="img"
                alt="Preview"
                width="30"
                height="20"
                preview
                class="border rounded"
              />
              <Button v-if="quickAddImages.length > 1" icon="pi pi-times" class="p-button-text p-button-sm" @click="removeQuickAddImage(idx)" />
              <Button v-if="idx === quickAddImages.length-1" icon="pi pi-plus" class="p-button-text p-button-sm" @click="addQuickAddImage" />
            </div>
          </div>

          <!-- imageBackground поле -->
          <div class="flex flex-col gap-1">
            <div v-for="(bg, idx) in quickAddImageBackgrounds" :key="'bg'+idx" class="flex items-center gap-1">
              <span class="text-xs font-medium">imageBackground{{idx > 0 ? idx+1 : ''}}:</span>
              <InputText
                v-model="quickAddImageBackgrounds[idx]"
                placeholder="URL фона"
                class="w-48 text-xs"
                style="font-size: 10px; padding: 4px"
              />
              <Image
                v-if="bg && isValidImageUrl(bg)"
                :src="bg"
                alt="Preview"
                width="30"
                height="20"
                preview
                class="border rounded"
              />
              <Button v-if="quickAddImageBackgrounds.length > 1" icon="pi pi-times" class="p-button-text p-button-sm" @click="removeQuickAddImageBackground(idx)" />
              <Button v-if="idx === quickAddImageBackgrounds.length-1" icon="pi pi-plus" class="p-button-text p-button-sm" @click="addQuickAddImageBackground" />
            </div>
          </div>

          <Button
            label="Добавить"
            icon="pi pi-plus"
            class="p-button-sm p-button-success"
            :disabled="!selectedItems.length"
            @click="applyQuickAdd"
          />
        </div>
      </div>

      <!-- 4. Основная область с плиткой записей -->
      <div class="main-content flex-1 overflow-hidden">
        <!-- Панель фильтров -->
        <div class="filters-panel flex items-center justify-between p-2 border-b bg-surface-50 dark:bg-surface-800">
          <!-- Быстрые фильтры -->
          <div class="flex gap-2 items-center">
            <Button
              v-tooltip.top="'Обновить записи'"
              icon="pi pi-refresh"
              class="p-button-rounded p-button-text p-button-sm mr-2"
              @click="loadData"
            />
            <MultiSelect
              v-model="selectedTagsFilter"
              :options="tagOptions"
              placeholder="Фильтр по тегам"
              display="chip"
              class="w-48 text-xs"
              @change="applyFilters"
            />
            <MultiSelect
              v-model="selectedCollectionFilter"
              :options="collectionOptions"
              placeholder="Фильтр по коллекции"
              display="chip"
              class="w-48 text-xs"
              @change="applyFilters"
            />
            <Button
            v-tooltip.top="'Сбросить все фильтры'"
        icon="pi pi-times"
        class="p-button-outlined text-xs"
              @click="resetFilters"
            />
            <Button
            v-tooltip.top="'Показать только выбранные'"
        icon="pi pi-filter"
        class="text-xs"
              :class="showOnlySelected ? 'p-button-info' : 'p-button-outlined'"
              @click="showOnlySelected = !showOnlySelected"
            />
            <Button
            v-tooltip.top="'Снять все отметки'"
        icon="pi pi-check-square"
        class="p-button-outlined text-xs"
              @click="clearSelection"
            />
          </div>

          <!-- Панель применения к выбранным -->
          <div class="flex gap-2 items-center">
            <MultiSelect
              v-model="applyTags"
              :options="tagOptions"
              placeholder="Применить теги"
              display="chip"
              class="w-48 text-xs"
            />
            <MultiSelect
              v-model="applyCollection"
              :options="collectionOptions"
              placeholder="Применить коллекцию"
              display="chip"
              class="w-48 text-xs"
            />
            <Button
              label="Применить"
              class="p-button-sm p-button-primary"
              :disabled="!selectedItems.length"
              @click="applyToSelected"
            />
          </div>
        </div>

        <!-- Плитка карточек -->
        <div class="cards-grid overflow-y-auto p-4" style="height: calc(100vh - 200px);">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            <div
              v-for="item in filteredItems"
              :key="item.id"
              class="card bg-white dark:bg-surface-900 border rounded-lg overflow-hidden relative cursor-pointer"
              :class="{ 'ring-2 ring-blue-500': isSelected(item) }"
              @click="toggleSelection(item)"
            >
              <!-- Чекбокс -->
              <div class="absolute top-2 left-2 z-10">
                <Checkbox
                  :model-value="isSelected(item)"
                  class="bg-white/80 rounded"
                  @click.stop="toggleSelection(item)"
                  @update:model-value="toggleSelection(item)"
                />
              </div>

              <!-- Кнопка редактирования -->
              <div class="absolute top-1 right-1 z-10">
                <Button
                icon="pi pi-pencil"
                class="absolute bottom-1 group-hover:opacity-100 transition-opacity p-button-sm p-button-rounded p-button-secondary"
                style="width: 24px; height: 24px; opacity: 0.5;"
                  @click.stop="editItem(item)"
                />
              </div>

              <!-- Интеллектуальная галерея-карусель -->
              <div class="gallery-area bg-gray-100">
                <div v-if="getImageUrls(item).length === 0" class="h-24 flex items-center justify-center"/>

                <div v-else-if="getImageUrls(item).length <= 6" class="grid grid-cols-2 grid-rows-3 gap-1 p-2 h-48">
                  <div
                    v-for="(imageUrl, imgIndex) in getImageUrls(item).slice(0, 6)"
                    :key="imgIndex"
                    class="bg-gray-200 rounded overflow-hidden"
                  >
                    <Image
                      :src="imageUrl"
                      alt="Preview"
                      width="100%"
                      height="100%"
                      preview
                      class="w-full object-cover"
                    />
                    <div class="absolute bottom-1 right-0 z-10">
                <Button
                  icon="pi pi-copy"
                  class="group-hover:opacity-100 transition-opacity p-button-sm p-button-text"
                  style="width: 14px; height: 14px; padding: 0; z-index: 10; background: rgba(255,255,255,0.5); right: 4px; top: 4px; opacity: 0.5;"
                  @click="copyToClipboard(imageUrl)"
                />
              </div>
                  </div>
                  <!-- Заполняем пустые ячейки если меньше 6 -->
                  
                </div>

                <div v-else class="h-48">
                  <Carousel
                    :value="getImageUrls(item)"
                    :num-visible="6"
                    :num-scroll="3"
                    :show-indicators="false"
                    :show-navigators="true"
                    class="h-full"
                  >
                    <template #item="{ data }">
                      <div class="p-1">
                        <Image
                          :src="data"
                          alt="Preview"
                          width="100%"
                          height="120"
                          preview
                          class="w-full h-full object-cover rounded"
                        />
                      </div>
                    </template>
                  </Carousel>
                </div>
              </div>

              <!-- Информация о карточке -->
              <div class="p-3">
                <div class="flex items-center gap-2 mb-1">
                  <span class="text-xs font-mono bg-gray-100 px-1 rounded">{{ item.art }}</span>
                  <span class="text-xs">{{ item.title }}</span>
                </div>

                <!-- Теги -->
                <div class="flex flex-wrap gap-1 mb-2">
                  <Tag
                    v-for="tag in (item.tags || [])"
                    :key="tag"
                    :value="tag"
                    severity="info"
                    class="text-xs"
                    style="padding: 2px; font-size: 9px; height: 14px;"
                  />
                  <Tag
                    v-for="collection in (item.collection || [])"
                    :key="collection"
                    :value="collection"
                    severity="success"
                    class="text-xs"
                    style="padding: 2px; font-size: 9px; height: 14px;"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 5. Сайдбар редактирования (как в wjson) -->
    <WcontSidebar
      v-model:visible="sidebarVisible"
      :collapsed="false"
      title="Редактирование графики"
      @close="sidebarVisible = false"
    >
      <div class="p-fluid">
        <!-- Базовая информация -->
        <div class="space-y-2">
          <div class="flex gap-2">
            <div class="field w-1/3">
              <InputText
                v-model="editingItem.art"
                required
                class="w-full"
                placeholder="Артикул*"
                style="padding: 6px; font-size: 10px"
              />
            </div>
            <div class="field w-2/3">
              <InputText
                v-model="editingItem.title"
                required
                class="w-full"
                placeholder="Название*"
                style="padding: 6px; font-size: 11px"
              />
            </div>
          </div>
          <div class="field mb-0">
            <Textarea
              v-model="editingItem.description"
              rows="1"
              class="w-full text-xs [&>textarea]:text-xs"
              placeholder="Описание"
              style="padding: 4px; font-size: 10px"
            />
          </div>
          <div class="field mt-0 mb-2">
            <MultiSelect
              v-model="editingItem.tags"
              :options="tagOptions"
              placeholder="Выберите теги"
              display="chip"
              class="text-xs w-full p-0"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{ item: { class: 'text-xs' }, header: { class: 'text-xs' } }"
            />
          </div>
          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.collection"
              :options="collectionOptions"
              placeholder="Выберите коллекции"
              display="chip"
              class="text-xs w-full p-0"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{ item: { class: 'text-xs' }, header: { class: 'text-xs' } }"
            />
          </div>
          
          <div class="field mb-0">
            <TabView class="text-xs" :pt="{ panelcontainer: { style: 'padding:0' } }">
              <TabPanel header="JSON" :pt="{ header: { class: 'p-0' }, headerAction: { class: 'text-xs p-0' }, content: { class: 'p-0' } }">
                <PrismEditorWithCopy
                  v-model="editingItem.json"
                  editor-class="my-editor text-xs"
                  :highlight="highlightJson"
                  placeholder="Введите JSON данные"
                  field-name="JSON"
                  style="min-height:600px;"
                />
              </TabPanel>
              <TabPanel header="Редактор" :pt="{ header: { class: 'p-0' }, headerAction: { class: 'text-xs p-0' }, content: { class: 'p-0' } }">
                <div>
                  <div class="quick-variables flex flex-wrap gap-1 mb-2">
                    <div v-for="varType in quickVariableTypes" :key="varType" class="relative">
                      <Button
                        :label="varType"
                        class="p-button-sm p-button-outlined"
                        style="font-size: 10px; padding: 2px 6px"
                        @click="addQuickVariableSidebar(varType)"
                      />
                      <span
v-if="getVariableCountSidebar(varType) > 0"
                        class="absolute -top-1 -right-1 bg-blue-500 text-white rounded-full text-xs w-4 h-4 flex items-center justify-center"
                        style="font-size: 8px; min-width: 16px; min-height: 16px"
                      >
                        {{ getVariableCountSidebar(varType) }}
                      </span>
                    </div>
                  </div>
                  <div v-for="(field, index) in sidebarFields" :key="index" class="flex gap-1 items-start">
                    <div class="w-1/5 mb-0">
                      <InputText
                        v-model="field.key"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px"
                        placeholder="Переменная"
                        @input="updateSidebarJsonFromFields"
                      />
                    </div>
                    <div class="w-3/5 relative group mb-0">
                      <Textarea
                        v-if="isTextVariable(field.key)"
                        v-model="field.value"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px; padding-right: 5px;"
                        placeholder="Значение"
                        rows="2"
                        @input="updateSidebarJsonFromFields"
                      />
                      <InputText
                        v-else
                        v-model="field.value"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px; padding-right: 5px;"
                        placeholder="Значение"
                        @input="updateSidebarJsonFromFields"
                      />
                      
                    </div>
                    <div class="w-6">
                      <Button
                        icon="pi pi-copy"
                        class="group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                        style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); right: 4px; top: 4px;"
                        @click="copyToClipboard(field.value)"
                      />
                    </div>
                    <div class="w-1/12 text-xs text-gray-500 text-center pt-1">
                      {{ field.value ? field.value.length : 0 }}
                    </div>
                    <div class="w-1/12 flex gap-1 pt-1">
                      <Button icon="pi pi-clone" class="p-button-text p-button-sm" style="width: 16px; height: 16px; padding: 0" @click="duplicateSidebarField(index)" />
                      <Button icon="pi pi-trash" class="p-button-text p-button-sm p-button-danger" style="width: 16px; height: 16px; padding: 0" @click="removeSidebarField(index)" />
                    </div>
                  </div>
                  <div class="flex justify-between items-center mt-2">
                    <Button label="+ Добавить поле" class="p-button-sm p-button-text" @click="addSidebarField" />
                    <span class="text-xs text-gray-500">Всего переменных: {{ sidebarFields.length }}</span>
                  </div>
                </div>
              </TabPanel>
              <TabPanel header="Картинки" :pt="{ header: { class: 'p-0' }, headerAction: { class: 'text-xs p-0' }, content: { class: 'p-0' } }">
                <div class="grid grid-cols-2 gap-1 h-[550px]">
                  <div v-for="(imageData, index) in sidebarExtractedImages" :key="index" class="border rounded p-1 overflow-hidden">
                    <Image
                      :src="imageData.url"
                      alt="Изображение из JSON"
                      width="100%"
                      height="100%"
                      preview
                      class="w-full object-cover  overflow-hidden"
                    />
                    <div class="space-y-1 z-10 bg-white absolute bottom-1 w-full">
                      <div class="text-xs text-gray-700" style="font-size: 10px;"> {{ imageData.variable }}</div>
                      <div class="flex">
                      <InputText
                        v-model="imageData.url"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 2px; height:20px;"
                        placeholder="URL изображения"
                        @input="updateSidebarImageInJson(imageData.variable, imageData.url)"
                      />
                      <Button
                        icon="pi pi-copy"
                        class="group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                        style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); right: 4px; top: 4px;"
                        @click="copyToClipboard(imageData.url)"
                      />
                      </div>
                    </div>
                  </div>
                  <p v-if="!sidebarExtractedImages.length" class="text-xs text-gray-500">Изображения не найдены в JSON (поиск по полям image, imageBackground)</p>
                </div>
              </TabPanel>
            </TabView>
          </div>
        </div>
        <div class="flex justify-end gap-2 mt-4">
          <Button label="Отмена" icon="pi pi-times" class="p-button-sm" @click="sidebarVisible = false" />
          <Button label="Сохранить" icon="pi pi-check" class="p-button-sm" :loading="loading" @click="saveSidebarItem" />
        </div>
      </div>
    </WcontSidebar>

    <!-- Уведомления -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'
import Prism from 'prismjs'
import 'prismjs/components/prism-json'

// Компоненты
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import MultiSelect from 'primevue/multiselect'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import Image from 'primevue/image'
import Carousel from 'primevue/carousel'
import Tag from 'primevue/tag'
import Checkbox from 'primevue/checkbox'
import Toast from 'primevue/toast'
import WcontSidebar from '~/components/WcontSidebar.vue'
import WcontSidebarForm from '~/components/WcontSidebarForm.vue'
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'

// Интерфейсы
interface WJson {
  id?: string
  art: string
  title: string
  description?: string
  tags?: string[]
  collection?: string[]
  json?: string
}

interface BulkForm {
  id: string
  art: string
  title: string
  description: string
  tags: string[]
  collection: string[]
  json: string
  fields: Array<{ key: string; value: string }>
}

// API и утилиты
const { getItems, createItems, updateItem } = useDirectusItems()
const toast = useToast()

// Состояние
const workMode = ref<'standard' | 'combinations'>('standard')
const items = ref<WJson[]>([])
const selectedItems = ref<WJson[]>([])
const loading = ref(false)

// Массовые формы
const bulkFormVisible = ref(false)
const bulkFormMode = ref<'create' | 'edit'>('create')
const bulkForms = ref<BulkForm[]>([])

// Быстрое добавление
const quickAddImages = ref<string[]>([''])
const quickAddImageBackgrounds = ref<string[]>([''])

// Фильтры
const tagOptions = ref<string[]>([])
const collectionOptions = ref<string[]>([])
const selectedTagsFilter = ref<string[]>([])
const selectedCollectionFilter = ref<string[]>([])
const showOnlySelected = ref(false)

// Применение к выбранным
const applyTags = ref<string[]>([])
const applyCollection = ref<string[]>([])

// Сайдбар
const sidebarVisible = ref(false)
const editingItem = ref<WJson | null>(null)

// --- Сайдбар: состояния для редактора переменных ---
const sidebarFields = ref<Array<{ key: string; value: string }>>([])
const sidebarExtractedImages = ref<Array<{ variable: string; url: string }>>([])





// Парсинг JSON в массив полей для редактора
const parseSidebarJsonToFields = (json: string) => {
  try {
    const parsed = JSON.parse(json || '{}')
    return Object.entries(parsed).map(([key, value]) => ({
      key,
      value: typeof value === 'object' ? JSON.stringify(value) : String(value)
    }))
  } catch {
    return []
  }
}

// Синхронизация массива полей с JSON
const updateSidebarJsonFromFields = () => {
  try {
    if (!editingItem.value) return;
    const jsonObj: Record<string, any> = {}
    sidebarFields.value.forEach(field => {
      if (field.key) {
        try {
          jsonObj[field.key] = JSON.parse(field.value)
        } catch {
          jsonObj[field.key] = field.value
        }
      }
    })
    editingItem.value.json = JSON.stringify(jsonObj, null, 2)
    updateSidebarExtractedImages()
  } catch (e) {
    // Можно добавить логирование или toast при необходимости
  }
}

// При изменении JSON — обновлять массив полей
watch(
  () => editingItem.value && editingItem.value.json,
  (val) => {
    if (!editingItem.value) return;
    sidebarFields.value = parseSidebarJsonToFields(val);
    updateSidebarExtractedImages();
  },
  { immediate: true }
)

// Методы для полей
const addSidebarField = () => {
  sidebarFields.value.push({ key: '', value: '' })
}
const removeSidebarField = (idx: number) => {
  sidebarFields.value.splice(idx, 1)
  updateSidebarJsonFromFields()
}
const duplicateSidebarField = (idx: number) => {
  const field = sidebarFields.value[idx]
  sidebarFields.value.splice(idx + 1, 0, { ...field })
  updateSidebarJsonFromFields()
}
const addQuickVariableSidebar = (varType: string) => {
  // Находим следующий уникальный номер для переменной
  const existingKeys = sidebarFields.value.map(f => f.key)
  let newKey = varType
  let counter = 2
  while (existingKeys.includes(newKey)) {
    newKey = `${varType}${counter}`
    counter++
  }
  sidebarFields.value.push({ key: newKey, value: '' })
  updateSidebarJsonFromFields()
}

// --- Работа с картинками во вкладке "Картинки" ---
const updateSidebarExtractedImages = () => {
  const fields = sidebarFields.value
  sidebarExtractedImages.value = fields.filter(f =>
    f.key.toLowerCase().includes('image') && isValidImageUrl(f.value)
  ).map(f => ({ variable: f.key, url: f.value }))
}
const updateSidebarImageInJson = (variable: string, url: string) => {
  const field = sidebarFields.value.find(f => f.key === variable)
  if (field) {
    field.value = url
    updateSidebarJsonFromFields()
  }
}

// --- Вспомогательные ---
const isTextVariable = (key: string) => {
  // Можно доработать по аналогии с wjson.vue, если нужно
  return !key.toLowerCase().includes('image')
}

// --- Сохранение из сайдбара ---
const saveSidebarItem = async () => {
  try {
    loading.value = true
    await updateItem({
      collection: 'wjson',
      id: editingItem.value.id,
      item: { ...editingItem.value }
    })
    toast.add({ severity: 'success', summary: 'Успешно', detail: 'Запись сохранена' })
    sidebarVisible.value = false
    await loadData()
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось сохранить' })
  } finally {
    loading.value = false
  }
}

// Для отслеживания ошибок парсинга по каждому полю
const fieldParseErrors = ref<Record<string, boolean>>({})

// Вычисляемые свойства
const filteredItems = computed(() => {
  let result = [...items.value]
  
  // Фильтр по тегам
  if (selectedTagsFilter.value.length > 0) {
    result = result.filter(item =>
      selectedTagsFilter.value.some(tag => item.tags?.includes(tag))
    )
  }
  
  // Фильтр по коллекции
  if (selectedCollectionFilter.value.length > 0) {
    result = result.filter(item =>
      selectedCollectionFilter.value.some(collection => item.collection?.includes(collection))
    )
  }
  
  // Показать только отмеченные
  if (showOnlySelected.value) {
    result = result.filter(item => selectedItems.value.includes(item))
  }
  
  return result
})

// Методы
const loadData = async () => {
  loading.value = true
  try {
    const jsonItems = await getItems({
      collection: 'wjson',
      params: { sort: ['art'] }
    })
    items.value = jsonItems as WJson[]
    
    // Загружаем опции для фильтров
    await loadFilterOptions()
  } catch (error) {
    console.error('Error loading data:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить данные'
    })
  } finally {
    loading.value = false
  }
}

const loadFilterOptions = async () => {
  try {
    // Загружаем уникальные теги и коллекции из базы
    const allItems = await getItems({
      collection: 'wjson',
      params: { fields: ['tags', 'collection'] }
    }) as WJson[]
    
    const tags = new Set<string>()
    const collections = new Set<string>()
    
    allItems.forEach(item => {
      item.tags?.forEach(tag => tags.add(tag))
      item.collection?.forEach(collection => collections.add(collection))
    })
    
    tagOptions.value = Array.from(tags)
    collectionOptions.value = Array.from(collections)
  } catch (error) {
    console.error('Error loading filter options:', error)
  }
}

// Методы для массовых форм
const openBulkCreate = () => {
  console.log('openBulkCreate called')
  bulkFormMode.value = 'create'
  bulkForms.value = [createEmptyForm()]
  bulkFormVisible.value = true
  console.log('bulkFormVisible set to:', bulkFormVisible.value)
}

const openBulkEdit = () => {
  bulkFormMode.value = 'edit'
  bulkForms.value = selectedItems.value.map(item => ({
    id: item.id || '',
    art: item.art,
    title: item.title,
    description: item.description || '',
    tags: item.tags || [],
    collection: item.collection || [],
    json: item.json || '{}',
    fields: parseJsonToFields(item.json || '{}')
  }))
  bulkFormVisible.value = true
}

const createEmptyForm = (): BulkForm => ({
  id: `new_${Date.now()}_${Math.random()}`,
  art: '',
  title: '',
  description: '',
  tags: [],
  collection: [],
  json: '{}',
  fields: []
})

const closeBulkForms = () => {
  bulkFormVisible.value = false
  bulkForms.value = []
}

// Утилиты
const isValidImageUrl = (url: string) => {
  return typeof url === 'string' && (url.startsWith('http') || url.startsWith('/'))
}

const parseJsonToFields = (json: string) => {
  try {
    const parsed = JSON.parse(json)
    return Object.entries(parsed).map(([key, value]) => ({
      key,
      value: String(value)
    }))
  } catch {
    return []
  }
}

const getImageUrls = (item: WJson) => {
  try {
    const json = JSON.parse(item.json || '{}')
    const urls: string[] = []
    
    Object.entries(json).forEach(([key, value]) => {
      if (key.toLowerCase().includes('image') && typeof value === 'string' && isValidImageUrl(value)) {
        urls.push(value)
      }
    })
    
    return urls
  } catch {
    return []
  }
}

const getImageFields = (form: BulkForm) => {
  return form.fields.filter(field => 
    field.key.toLowerCase().includes('image') && isValidImageUrl(field.value)
  )
}

// Методы для работы с формами
const addBulkForm = () => {
  bulkForms.value.push(createEmptyForm())
}

const saveBulkForms = async () => {
  try {
    loading.value = true

    for (const form of bulkForms.value) {
      if (bulkFormMode.value === 'create') {
        await createItems({
          collection: 'wjson',
          items: [{
            art: form.art,
            title: form.title,
            description: form.description,
            tags: form.tags,
            collection: form.collection,
            json: form.json
          }]
        })
      } else {
        await updateItem({
          collection: 'wjson',
          id: form.id,
          item: {
            art: form.art,
            title: form.title,
            description: form.description,
            tags: form.tags,
            collection: form.collection,
            json: form.json
          }
        })
      }
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `${bulkForms.value.length} записей сохранено`
    })

    await loadData()
    closeBulkForms()
  } catch (error) {
    console.error('Error saving bulk forms:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить данные'
    })
  } finally {
    loading.value = false
  }
}

const saveSingleForm = async (form: BulkForm) => {
  try {
    if (bulkFormMode.value === 'create') {
      await createItems({
        collection: 'wjson',
        items: [{
          art: form.art,
          title: form.title,
          description: form.description,
          tags: form.tags,
          collection: form.collection,
          json: form.json
        }]
      })
    } else {
      await updateItem({
        collection: 'wjson',
        id: form.id,
        item: {
          art: form.art,
          title: form.title,
          description: form.description,
          tags: form.tags,
          collection: form.collection,
          json: form.json
        }
      })
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Форма сохранена'
    })

    await loadData()
  } catch (error) {
    console.error('Error saving single form:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить форму'
    })
  }
}

const addQuickVariable = (form: BulkForm, varType: string) => {
  // Находим следующий уникальный номер для переменной
  const existingKeys = form.fields.map(f => f.key)
  let newKey = varType
  let counter = 2

  while (existingKeys.includes(newKey)) {
    newKey = `${varType}${counter}`
    counter++
  }

  form.fields.push({ key: newKey, value: '' })
  updateFormJson(form)
}

const updateFormJson = (form: BulkForm) => {
  try {
    const jsonObj: Record<string, any> = {}
    form.fields.forEach(field => {
      if (field.key && field.value) {
        // Пытаемся распарсить как JSON, если не получается - оставляем как строку
        try {
          jsonObj[field.key] = JSON.parse(field.value)
        } catch {
          jsonObj[field.key] = field.value
        }
      }
    })
    form.json = JSON.stringify(jsonObj, null, 2)
  } catch (error) {
    console.error('Error updating JSON:', error)
  }
}

const updateFormFields = (form: BulkForm) => {
  try {
    const jsonObj = JSON.parse(form.json || '{}')

    // Очищаем существующие поля
    form.fields = []

    // Добавляем поля из JSON
    Object.entries(jsonObj).forEach(([key, value]) => {
      form.fields.push({
        key,
        value: typeof value === 'object' ? JSON.stringify(value) : String(value)
      })
    })
  } catch (error) {
    console.error('Error parsing JSON:', error)
  }
}

const addField = (form: BulkForm) => {
  form.fields.push({ key: '', value: '' })
}

const duplicateField = (form: BulkForm, index: number) => {
  const field = form.fields[index]
  form.fields.splice(index + 1, 0, { ...field })
}

const removeField = (form: BulkForm, index: number) => {
  form.fields.splice(index, 1)
  updateFormJson(form)
}

const moveFieldUp = (form: BulkForm, index: number) => {
  if (index > 0) {
    const field = form.fields.splice(index, 1)[0]
    form.fields.splice(index - 1, 0, field)
    updateFormJson(form)
  }
}

const moveFieldDown = (form: BulkForm, index: number) => {
  if (index < form.fields.length - 1) {
    const field = form.fields.splice(index, 1)[0]
    form.fields.splice(index + 1, 0, field)
    updateFormJson(form)
  }
}

const removeBulkForm = (index: number) => {
  bulkForms.value.splice(index, 1)
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.add({
      severity: 'success',
      summary: 'Скопировано',
      detail: 'Текст скопирован в буфер обмена'
    })
  } catch (error) {
    console.error('Error copying to clipboard:', error)
  }
}

const applyQuickAdd = async () => {
  if (!selectedItems.value.length) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите записи для применения'
    })
    return
  }

  const hasImages = quickAddImages.value.some(v => v)
  const hasBgs = quickAddImageBackgrounds.value.some(v => v)
  if (!hasImages && !hasBgs) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните хотя бы одно поле'
    })
    return
  }

  try {
    loading.value = true

    for (const item of selectedItems.value) {
      // Парсим существующий JSON
      let existingJson: Record<string, any> = {}
      try {
        existingJson = JSON.parse(item.json || '{}')
      } catch {
        existingJson = {}
      }

      // Анализируем существующие переменные
      const existingKeys = Object.keys(existingJson)

      // Добавляем все image
      let imageCounter = 1
      for (const img of quickAddImages.value) {
        if (img) {
          let imageKey = imageCounter === 1 ? 'image' : `image${imageCounter}`
          while (existingKeys.includes(imageKey)) {
            imageCounter++
            imageKey = `image${imageCounter}`
          }
          existingJson[imageKey] = img
          existingKeys.push(imageKey)
          imageCounter++
        }
      }
      // Добавляем все imageBackground
      let bgCounter = 1
      for (const bg of quickAddImageBackgrounds.value) {
        if (bg) {
          let bgKey = bgCounter === 1 ? 'imageBackground' : `imageBackground${bgCounter}`
          while (existingKeys.includes(bgKey)) {
            bgCounter++
            bgKey = `imageBackground${bgCounter}`
          }
          existingJson[bgKey] = bg
          existingKeys.push(bgKey)
          bgCounter++
        }
      }

      // Обновляем запись
      await updateItem({
        collection: 'wjson',
        id: item.id!,
        item: {
          json: JSON.stringify(existingJson, null, 2)
        }
      })
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Добавлено в ${selectedItems.value.length} записей`
    })

    // Очищаем поля быстрого добавления
    quickAddImages.value = ['']
    quickAddImageBackgrounds.value = ['']

    await loadData()
  } catch (error) {
    console.error('Error applying quick add:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось применить изменения'
    })
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  selectedTagsFilter.value = []
  selectedCollectionFilter.value = []
}

const clearSelection = () => {
  selectedItems.value = []
}

const applyToSelected = async () => {
  if (!selectedItems.value.length) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите записи для применения'
    })
    return
  }

  if (!applyTags.value.length && !applyCollection.value.length) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите теги или коллекции для применения'
    })
    return
  }

  try {
    loading.value = true

    for (const item of selectedItems.value) {
      const updateData: Partial<WJson> = {}

      if (applyTags.value.length) {
        // Объединяем существующие теги с новыми (без дубликатов)
        const existingTags = item.tags || []
        const newTags = [...new Set([...existingTags, ...applyTags.value])]
        updateData.tags = newTags
      }

      if (applyCollection.value.length) {
        // Объединяем существующие коллекции с новыми (без дубликатов)
        const existingCollections = item.collection || []
        const newCollections = [...new Set([...existingCollections, ...applyCollection.value])]
        updateData.collection = newCollections
      }

      await updateItem({
        collection: 'wjson',
        id: item.id!,
        item: updateData
      })
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Применено к ${selectedItems.value.length} записям`
    })

    // Очищаем поля применения
    applyTags.value = []
    applyCollection.value = []

    await loadData()
  } catch (error) {
    console.error('Error applying to selected:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось применить изменения'
    })
  } finally {
    loading.value = false
  }
}

const editItem = (item: WJson) => {
  editingItem.value = { ...item }
  sidebarVisible.value = true
}

const handleSidebarSave = async () => {
  try {
    await loadData()
    sidebarVisible.value = false
    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Запись сохранена'
    })
  } catch (error) {
    console.error('Error after sidebar save:', error)
  }
}

const isSelected = (item: WJson) => {
  return selectedItems.value.some(selected => selected.id === item.id)
}

const toggleSelection = (item: WJson) => {
  const index = selectedItems.value.findIndex(selected => selected.id === item.id)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(item)
  }
}

const applyFilters = () => {
  // Фильтрация происходит автоматически через computed filteredItems
}

// Функция подсветки для JSON
const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

// Функция для получения значения для input в массовом редакторе (bulkForms)
const getBulkFieldInputValue = (field: { key: string; value: any }) => {
  return typeof field.value === 'string' ? field.value : JSON.stringify(field.value)
}

// Функция для обработки изменения значения input
const handleFieldInput = (form: BulkForm, field: { key: string; value: any }, value: string, fieldIndex: number) => {
  // Пытаемся распарсить как JSON
  try {
    const parsed = JSON.parse(value)
    field.value = parsed
    fieldParseErrors.value[`${form.id}_${fieldIndex}`] = false
  } catch {
    field.value = value
    // Если value выглядит как JSON, но не парсится — ошибка
    if (value.trim().startsWith('{') || value.trim().startsWith('[')) {
      fieldParseErrors.value[`${form.id}_${fieldIndex}`] = true
    } else {
      fieldParseErrors.value[`${form.id}_${fieldIndex}`] = false
    }
  }
  updateFormJson(form)
}

// Методы для быстрого добавления
const addQuickAddImage = () => {
  quickAddImages.value.push('')
}
const removeQuickAddImage = (idx: number) => {
  if (quickAddImages.value.length > 1) quickAddImages.value.splice(idx, 1)
}
const addQuickAddImageBackground = () => {
  quickAddImageBackgrounds.value.push('')
}
const removeQuickAddImageBackground = (idx: number) => {
  if (quickAddImageBackgrounds.value.length > 1) quickAddImageBackgrounds.value.splice(idx, 1)
}

// В <script setup lang="ts"> добавляю массив типов переменных и функцию для подсчёта количества
const quickVariableTypes = ['title', 'image', 'imageBackground', 'url', 'linkText', 'icon', 'text', 'excerpt']
const getVariableCountSidebar = (type: string) => {
  return sidebarFields.value.filter(f => f.key && f.key.startsWith(type)).length
}

// Инициализация
onMounted(() => {
  loadData()

})
</script>

<style scoped>
.graphics-page {
  height: 100vh;
  overflow: hidden;
}

.forms-scroll {
  max-height: 400px;
}

.bulk-form-container {
  min-height: 350px;
}

.cards-grid {
  height: calc(100vh - 400px);
}

.card {
  transition: all 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.my-editor {
  padding: 0;
}
</style>
