<template>
    <div>
      <textarea 
        ref="codeEditor" 
        v-model="code"
        @input="update"
      />
  
      <pre 
        class="hljs" 
        v-html="highlightedCode"
      />
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, onUpdated, watch } from 'vue';
  import hljs from 'highlight.js/lib/core';
  import pug from 'highlightjs-pug/lib/pug'; 
  
  hljs.registerLanguage('pug', pug);
  
  const codeEditor = ref(null);
  const code = ref('');
  const highlightedCode = ref('');
  
  onMounted(() => {
    highlight();
  });
  
  const update = () => {
    highlight();
  };
  
  const highlight = () => {
    const result = hljs.highlightAuto(code.value);
    highlightedCode.value = result.value; 
  };
  
  </script>
  
  <style>
  /* Стили для редактора (необязательно) */
  /* textarea {
    width: 100%;
    height: 300px;
    font-family: monospace;
  } */
  </style>
  
  