<template>
  <div class="content-constructor">
    <!-- Компактный заголовок как в VariationConstructor -->
    <div class="header-section mb-2">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <h4 class="text-sm font-semibold">Конструктор контента для элементов</h4>
          <ToggleButton
            v-model="isEditMode"
            on-label="Создание"
            off-label="Выбор"
            class="text-xs"
          />
          <Badge 
            v-if="uniqueVariables.length > 0" 
            :value="`${uniqueVariables.length} переменных`" 
            severity="info"
            class="text-xs"
          />
        </div>
        
      </div>
    </div>

    <!-- Режим выбора (компактный) -->
    <div v-if="!isEditMode" class="selection-mode">
      <!-- Компактная панель управления -->
      <div class="grid grid-cols-4 gap-4 mb-4">
        <div class="col-span-1">
          <div class="p-3 bg-blue-50 border border-blue-200 rounded max-h-[620px] overflow-y-auto">
            <div class="flex items-center justify-between mb-2">
              <h6 class="text-sm font-medium text-blue-800">⚡ Быстрый конструктор</h6>
              <div class="text-xs text-blue-700 mb-2">
                Найдено {{ uniqueVariables.length }} переменных
              </div>
              <Button
                v-tooltip="'Применить быстрые переменные'"
                icon="pi pi-check"
                
                class="text-xs p-button-success"
                :disabled="Object.keys(quickVariables).filter(k => quickVariables[k]).length === 0"
                @click="applyQuickVariables"
              />
              <Button
                  icon="pi pi-times"
                  class="text-xs"
                  severity="secondary"
                  outlined
                  @click="clearQuickVariables"
                />
            </div>

            <div v-if="uniqueVariables.length > 0" class="space-y-2">
              

              <div
                v-for="variable in uniqueVariables.slice(0, 20)"
                :key="variable"
                class="flex items-center gap-2"
              >
                <span class="font-mono text-blue-600 text-xs w-12 flex-shrink-0">{{ variable }}:</span>
                <InputText
                  v-model="quickVariables[variable]"
                  :placeholder="getVariablePlaceholder(variable)"
                  class="text-xs flex-1"
                  size="small"
                />
                <span class="text-xs text-gray-500 w-6 text-right">{{ getCharCount(quickVariables[variable]) }}</span>
              </div>

              <div v-if="uniqueVariables.length > 20" class="text-xs text-gray-500 text-center">
                +{{ uniqueVariables.length - 20 }} переменных
              </div>

              
            </div>

            <div v-else class="text-center text-gray-500 py-4 text-xs">
              <i class="pi pi-search text-lg mb-2 block"/>
              <div>Выберите элементы</div>
              <div>с HBS шаблонами</div>
            </div>
          </div>
        </div>
        <!-- Библиотека контента (2/3 ширины слева) -->
        <div class="col-span-3 bg-white border rounded-lg p-3 max-h-[620px] overflow-y-auto">
          <div class="flex items-center gap-2 mb-2 gap-2">
            <h4 class="text-sm font-semibold">📚 Библиотека контента</h4>
            <InputText
              v-model="contentSearchQuery"
              placeholder="Поиск"
              class="text-xs flex-1"
              size="small"
              @input="filterContentLibrary"
            />
            <MultiSelect
              v-model="contentTypeFilter"
              :options="contentTypes"
              option-label="label"
              option-value="value"
              placeholder="Тип контента"
              class="text-xs w-60"
              @change="filterContentLibrary"
            />
            <MultiSelect
              v-model="contentSourceFilter"
              :options="contentSources"
              option-label="label"
              option-value="value"
              placeholder="Источник"
              class="text-xs w-40"
              @change="filterContentLibrary"
            />
            <div class="flex items-center gap-2">
            <Button
            v-tooltip="'Обновить библиотеку'"
            icon="pi pi-refresh"
            class="text-xs p-button-outlined p-1"
            :loading="analyzing"
            @click="loadContentLibrary"
          />
            <Button
            label="Применить"  
            icon="pi pi-check"
              class="text-xs p-button-success"              
              :disabled="selectedContentSets.length === 0"
              @click="applySelectedContent"              
            />
          </div>
          </div>
          <div class="grid grid-cols-4 gap-3 mb-4">
        <div
          v-for="contentSet in filteredContentSets"
          :key="contentSet.id"
          class="content-card border rounded p-3 bg-white cursor-pointer transition-all text-xs hover:shadow-md"
          :class="{ 'ring-2 ring-blue-500 bg-blue-50': selectedContentSets.some(s => s.id === contentSet.id) }"
          @click="toggleContentSet(contentSet)"
        >
          <!-- Заголовок карточки -->
          <div class="flex items-center gap-2 mb-2">
            <span class="font-medium text-sm truncate flex-1">{{ contentSet.title }}</span>
            <Checkbox
              :model-value="selectedContentSets.some(s => s.id === contentSet.id)"
              binary
              @click.stop
            />
          </div>

          <!-- Описание -->
          <div class="text-xs text-gray-600 mb-2 line-clamp-2">{{ contentSet.description }}</div>

          <!-- Метки -->
          <div class="flex items-center justify-between mb-2">
            <Badge :value="contentSet.type" :severity="getContentTypeSeverity(contentSet.type)" class="text-xs" />
            <Badge :value="contentSet.source" severity="secondary" class="text-xs" />
            <Badge
              :value="`${contentSet.compatibility}%`"
              :severity="contentSet.compatibility > 70 ? 'success' : contentSet.compatibility > 40 ? 'warning' : 'secondary'"
              class="text-xs"
            />
          </div>

          <!-- Превью переменных -->
          <div class="border-t pt-2">
            <div class="flex items-center justify-between mb-1">
              <span class="text-xs font-medium text-gray-700">Переменные ({{ Object.keys(contentSet.data).length }}):</span>
              <Button
                :icon="expandedCards.has(contentSet.id) ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
                class="text-xs p-button-text p-0"
                @click.stop="toggleCardExpansion(contentSet.id)"
              />
            </div>

            <!-- Компактное превью -->
            <div v-if="!expandedCards.has(contentSet.id)" class="space-y-1">
              <div
                v-for="(value, key) in Object.entries(contentSet.data).slice(0, 2)"
                :key="key"
                class="flex items-center gap-1"
              >
                <span class="text-blue-600 text-xs w-16 truncate">{{ value[0] }}:</span>
                <span class="text-gray-700 text-xs truncate flex-1">{{ String(value[1]).substring(0, 20) }}{{ String(value[1]).length > 20 ? '...' : '' }}</span>
                <span class="text-gray-400 text-xs">({{ String(value[1]).length }})</span>
              </div>
              <div v-if="Object.keys(contentSet.data).length > 2" class="text-xs text-gray-500 text-center">
                +{{ Object.keys(contentSet.data).length - 2 }} переменных
              </div>
            </div>

            <!-- Развернутое превью -->
            <div v-else class="space-y-1 max-h-32 overflow-y-auto">
              <div
                v-for="(value, key) in contentSet.data"
                :key="key"
                class="flex items-start gap-1 p-1 bg-gray-50 rounded"
              >
                <span class="font-mono text-blue-600 text-xs w-12 flex-shrink-0">{{ key }}:</span>
                <span class="text-gray-700 text-xs flex-1 break-words">{{ String(value) }}</span>
                <span class="text-gray-400 text-xs">({{ String(value).length }})</span>
              </div>
            </div>
          </div>
        </div>
      </div>
        </div>

        
        
      </div>

    </div>

    <!-- Режим создания (подробный) - горизонтальная прокрутка -->
    <div v-else class="edit-mode">
      <div class="overflow-x-auto">
        <div class="flex gap-4 pb-2" style="min-width: max-content;">
          <div
            v-for="(contentSet, index) in editableContentSets"
            :key="contentSet.id"
            class="content-set-editor border rounded p-3 bg-white flex-shrink-0 w-96"
          >
            <!-- Заголовок набора -->
            <div class="flex items-center justify-between mb-3">
              <InputText
                v-model="contentSet.title"
                placeholder="Название набора"
                class="font-semibold text-sm flex-1"
              />
              <div class="flex items-center gap-2 ml-2">
                <Button
                  v-tooltip="'Добавить переменную'"
                  icon="pi pi-plus"
                  class="text-xs p-button-outlined"
                  @click="addVariable(index)"
                />
                <Button
                  v-tooltip="'Удалить набор'"
                  icon="pi pi-trash"
                  class="text-xs p-button-danger p-button-outlined"
                  @click="removeContentSet(index)"
                />
              </div>
            </div>

            <!-- Тип и описание -->
            <div class="grid grid-cols-2 gap-2 mb-3">
              <Dropdown
                v-model="contentSet.type"
                :options="blockTypes"
                option-label="label"
                option-value="value"
                placeholder="Тип блока"
                class="text-xs"
              />
              <InputText
                v-model="contentSet.description"
                placeholder="Описание"
                class="text-xs"
              />
            </div>

            <!-- Быстрое добавление из найденных переменных -->
            <div v-if="uniqueVariables.length > 0" class="mb-3">
              <label class="block text-xs font-medium mb-1">
                Быстрое добавление из элементов ({{ uniqueVariables.length }}):
              </label>
              <div class="flex flex-wrap gap-1">
                <Button
                  v-for="variable in uniqueVariables"
                  :key="variable"
                  size="small"
                  text
                  severity="info"
                  class="text-xs"
                  @click="addVariableFromElement(index, variable)"
                >
                  <i class="pi pi-plus mr-1"/>
                  {{ variable }}
                </Button>
              </div>
            </div>

            <!-- Отладочная информация -->
            <div v-else class="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
              <div class="text-yellow-800">
                <strong>Отладка:</strong> Переменные не найдены
              </div>
              <div class="text-yellow-700 mt-1">
                Элементов выбрано: {{ props.selectedElements.length }}
              </div>
              <div class="text-yellow-700">
                Элементы с HBS: {{ props.selectedElements.filter(el => el.hbs).length }}
              </div>
            </div>

            <!-- Переменные -->
            <div class="space-y-2">
              <div
                v-for="(variable, varIndex) in contentSet.variables"
                :key="varIndex"
                class="flex items-center gap-2"
              >
                <Dropdown
                  v-model="variable.name"
                  :options="predefinedVariables"
                  option-label="label"
                  option-value="value"
                  placeholder="Переменная"
                  class="text-xs w-32"
                  editable
                  :filter="true"
                  filter-placeholder="Поиск или ввод своей..."
                />
                <InputText
                  v-model="variable.value"
                  :placeholder="getVariablePlaceholder(variable.name)"
                  class="text-xs flex-1"
                />
                <span class="text-xs text-gray-500 w-8 text-right">{{ getCharCount(variable.value) }}</span>
                <Button
                  icon="pi pi-trash"
                  class="text-xs p-button-danger p-button-text"
                  @click="removeVariable(index, varIndex)"
                />
              </div>
            </div>

            <!-- Превью набора -->
            <div v-if="contentSet.variables.some(v => v.name && v.value)" class="mt-3 pt-3 border-t">
              <div class="text-xs font-medium text-gray-700 mb-2">Превью набора:</div>
              <div class="bg-gray-50 p-2 rounded text-xs space-y-1">
                <div
                  v-for="variable in contentSet.variables.filter(v => v.name && v.value)"
                  :key="variable.name"
                  class="flex items-center gap-2"
                >
                  <span class="font-mono text-blue-600 w-16">{{ variable.name }}:</span>
                  <span class="text-gray-700 flex-1 truncate">{{ variable.value }}</span>
                  <span class="text-gray-400">({{ getCharCount(variable.value) }})</span>
                </div>
              </div>

              <!-- Тест на элементе -->
              <div v-if="props.selectedElements.length > 0" class="mt-2">
                <div class="text-xs font-medium text-gray-700 mb-1">Тест на элементе:</div>
                <iframe
                  :srcdoc="getTestPreviewHtml(index)"
                  class="w-full h-20 border rounded border-blue-200"
                  frameborder="0"
                  sandbox="allow-same-origin allow-scripts"
                />
              </div>
            </div>

            <!-- Кнопки действий -->
            <div class="flex gap-2 mt-3">
              <Button
                label="Сохранить"
                icon="pi pi-save"
                class="text-xs flex-1"
                severity="success"
                :disabled="!contentSet.title || contentSet.variables.length === 0"
                @click="saveContentSet(index)"
              />
            </div>
          </div>

          <!-- Кнопка добавления нового набора -->
          <div class="flex-shrink-0 w-96 border-2 border-dashed border-gray-300 rounded p-3 flex items-center justify-center">
            <Button
              label="Добавить набор"
              icon="pi pi-plus"
              class="text-sm"
              severity="secondary"
              outlined
              @click="addContentSet"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Превью применения - полноширинное как в VariationConstructor -->
    <div class="preview-section mt-6 p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <h5 class="text-sm font-semibold mb-3">Превью применения контента</h5>
      <div class="preview-content">
        <div v-if="props.selectedElements.length > 0 && (selectedContentSets.length > 0 || Object.keys(quickVariables).filter(k => quickVariables[k]).length > 0)" class="space-y-4">
          <!-- Показываем превью для каждого набора контента -->
          <div
            v-for="contentSet in (selectedContentSets.length > 0 ? selectedContentSets : [{ id: 'quick', title: 'Быстрые переменные', data: quickVariables }])"
            :key="contentSet.id"
            class="content-set-preview border rounded-lg p-3 bg-white"
          >
            <div class="flex items-center justify-between mb-3">
              <h6 class="text-sm font-medium text-gray-700">{{ contentSet.title }}</h6>
              <Badge :value="`${Object.keys(contentSet.data).length} переменных`" severity="info" class="text-xs" />
            </div>

            <!-- Превью для каждого элемента -->
            <div class="space-y-3">
              <div
                v-for="(element, index) in props.selectedElements.slice(0, 3)"
                :key="element.id || index"
                class="element-preview border rounded p-2"
              >
                <div class="text-xs font-medium text-gray-600 mb-2">
                  {{ element.title || `Элемент ${index + 1}` }}
                </div>

                <div class="grid grid-cols-2 gap-2">
                  <div class="original">
                    
                    <iframe
                      :srcdoc="getElementPreviewHtml(element, false)"
                      class="w-full h-36 border rounded"
                      frameborder="0"
                      sandbox="allow-same-origin allow-scripts"
                    />
                  </div>
                  <div class="modified">
                    
                    <iframe
                      :srcdoc="getElementPreviewWithSpecificContent(element, contentSet.data)"
                      class="w-full h-36 border rounded border-blue-200"
                      frameborder="0"
                      sandbox="allow-same-origin allow-scripts"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Статистика для этого набора -->
            <div class="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
              <div class="font-medium text-blue-800">
                Результат: {{ props.selectedElements.length }} элементов с набором "{{ contentSet.title }}"
              </div>
              <div class="text-blue-700 mt-1">
                Переменные: {{ Object.keys(contentSet.data).filter(k => contentSet.data[k]).join(', ') }}
              </div>
            </div>
          </div>
        </div>

        <div v-else class="text-center text-gray-500 py-8">
          <i class="pi pi-eye text-3xl mb-2 block"/>
          <div>Выберите элементы и наборы контента для превью</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useDirectusItems } from '#imports'

// Props
interface Props {
  selectedElements: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'content-applied': [data: any]
}>()

// Reactive data
const analyzing = ref(false)
const isEditMode = ref(false)
const uniqueVariables = ref<string[]>([])
const contentLibrary = ref<any[]>([])
const selectedContentSets = ref<any[]>([])
const selectedContentTypes = ref<string[]>([])
const contentSearchQuery = ref('')
const quickVariables = ref<{ [key: string]: string }>({})
const editableContentSets = ref<any[]>([])
const blockTypes = ref<any[]>([])
const expandedCards = ref<Set<string>>(new Set())
const selectedContentSources = ref<string[]>([])

// Composables
const { getItems } = useDirectusItems()

// Предопределенные переменные СТРОГО по системе htmlToTemplate
const predefinedVariables = [
  // Основные переменные из htmlToTemplate.js
  { label: 'title', value: 'title' },
  { label: 'title2', value: 'title2' },
  { label: 'title3', value: 'title3' },
  { label: 'text', value: 'text' },
  { label: 'text2', value: 'text2' },
  { label: 'text3', value: 'text3' },
  { label: 'image', value: 'image' },
  { label: 'image2', value: 'image2' },
  { label: 'image3', value: 'image3' },
  { label: 'imageAlt', value: 'imageAlt' },
  { label: 'imageAlt2', value: 'imageAlt2' },
  { label: 'imageBackground', value: 'imageBackground' },
  { label: 'imageBackground2', value: 'imageBackground2' },
  { label: 'url', value: 'url' },
  { label: 'url2', value: 'url2' },
  { label: 'url3', value: 'url3' },
  { label: 'linkText', value: 'linkText' },
  { label: 'linkText2', value: 'linkText2' },
  { label: 'linkText3', value: 'linkText3' },
  { label: 'excerpt', value: 'excerpt' },
  { label: 'excerpt2', value: 'excerpt2' },
  { label: 'excerpt3', value: 'excerpt3' },
  { label: 'cssUrl', value: 'cssUrl' },
  { label: 'cssUrl2', value: 'cssUrl2' },
  { label: 'icon', value: 'icon' },
  { label: 'icon2', value: 'icon2' },
  { label: 'icon3', value: 'icon3' }
]

const contentTypes = computed(() => [
  { label: 'Все типы', value: '' },
  { label: 'Первый экран', value: 'первый-экран' },
  { label: 'О компании', value: 'о-компании' },
  { label: 'Услуги', value: 'услуги' },
  { label: 'Преимущества', value: 'преимущества' },
  { label: 'Команда', value: 'команда' },
  { label: 'Отзывы', value: 'отзывы' },
  { label: 'Контакты', value: 'контакты' },
  { label: 'CTA', value: 'cta' }
])

const contentSources = computed(() => [
  { label: 'Все источники', value: '' },
  { label: 'Встроенные', value: 'builtin' },
  { label: 'wjson', value: 'wjson' },
  { label: 'wblock_proto', value: 'wblock_proto' },
  { label: 'Файлы', value: 'files' },
  { label: 'Быстрые', value: 'quick' },
  { label: 'Пользовательские', value: 'custom' }
])

// Типы контента
const contentTypeFilter = ref('')
const contentSourceFilter = ref('')
const searchQuery = ref('')

// Computed
const filteredContentSets = computed(() => {
  let filtered = contentLibrary.value

  // Фильтр по типам
  if (contentTypeFilter.value) {
    filtered = filtered.filter(set => set.type === contentTypeFilter.value)
  }

  if (contentSourceFilter.value) {
    filtered = filtered.filter(set => set.source === contentSourceFilter.value)
  }

  // Поиск
  if (contentSearchQuery.value) {
    const query = contentSearchQuery.value.toLowerCase()
    filtered = filtered.filter(set =>
      set.title.toLowerCase().includes(query) ||
      set.description.toLowerCase().includes(query)
    )
  }

  return filtered.sort((a, b) => b.compatibility - a.compatibility)
})

// Methods
const analyzeElements = async () => {
  if (props.selectedElements.length === 0) {
    uniqueVariables.value = []
    return
  }

  analyzing.value = true
  try {
    const allVariables = new Set<string>()

    console.log('🔍 Анализируем элементы:', props.selectedElements.length)

    for (const element of props.selectedElements) {
      console.log('📝 Элемент:', element.title || element.id, {
        hasHbs: !!element.hbs,
        hbsLength: element.hbs?.length || 0,
        hbsPreview: element.hbs?.substring(0, 100) + '...'
      })

      if (element.hbs && typeof element.hbs === 'string') {
        // Ищем все переменные Handlebars {{variable}}
        const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
        let match: RegExpExecArray | null

        while ((match = variableRegex.exec(element.hbs)) !== null) {
          const variable = match[1].trim()

          // Исключаем Handlebars хелперы и блоки
          if (!variable.startsWith('#') &&
              !variable.startsWith('/') &&
              !variable.startsWith('^') &&
              !variable.includes(' ') &&
              !variable.includes('.') &&
              variable.length > 0) {
            allVariables.add(variable)
            console.log('✅ Найдена переменная:', variable)
          }
        }
      }
    }

    const variablesArray = Array.from(allVariables).sort()
    uniqueVariables.value = variablesArray

    console.log('🎯 Найдено уникальных переменных:', variablesArray.length, variablesArray)

    // Инициализируем быстрые переменные пустыми значениями
    const newQuickVariables: { [key: string]: string } = {}
    variablesArray.forEach(variable => {
      newQuickVariables[variable] = ''
    })
    quickVariables.value = newQuickVariables

    // Загружаем библиотеку контента
    await loadContentLibrary()

    // Анализируем совместимость
    analyzeCompatibility()

  } catch (error) {
    console.error('❌ Ошибка анализа элементов:', error)
  } finally {
    analyzing.value = false
  }
}

const loadContentLibrary = async () => {
  try {
    const library = []

    // Всегда добавляем встроенные наборы
    library.push(...getBuiltInContentSets())

    try {
      // Пытаемся загрузить из wjson
      const wjsonData = await getItems({
        collection: 'wjson',
        params: {
          limit: 100,
          fields: ['id', 'title', 'description', 'tags', 'json']
        }
      })

      wjsonData.forEach((item: any) => {
        if (item.json) {
          const jsonData = typeof item.json === 'string' ? JSON.parse(item.json) : item.json
          library.push({
            id: `wjson-${item.id}`,
            title: item.title || 'Без названия',
            description: item.description || 'Набор из коллекции wjson',
            type: determineContentType(item.tags || ''),
            source: 'wjson',
            data: jsonData,
            preview: getPreviewData(jsonData),
            compatibility: calculateCompatibility(jsonData)
          })
        }
      })
    } catch (error) {
      console.warn('Не удалось загрузить wjson:', error)
    }

    // Загружаем из wblock_proto коллекции
    try {
      const wblockItems = await getItems({
        collection: 'wblock_proto',
        params: {
          limit: -1,
          fields: ['id', 'title', 'json', 'block_type'],
          filter: {
            json: {
              _nnull: true
            }
          }
        }
      })

      if (wblockItems && Array.isArray(wblockItems)) {
        const wblockSets = wblockItems
          .filter(item => item.json) // Фильтруем только элементы с JSON
          .map((item: any) => {
            let parsedData = {}
            try {
              if (typeof item.json === 'string') {
                parsedData = JSON.parse(item.json)
              } else if (typeof item.json === 'object') {
                parsedData = item.json
              }
            } catch (error) {
              console.warn(`Ошибка парсинга JSON для блока ${item.id}:`, error)
              return null
            }

            return {
              id: `wblock-${item.id}`,
              title: item.title || `Блок ${item.id}`,
              description: `Контент из блока ${item.title}`,
              type: item.block_type?.[0] || 'блок',
              source: 'wblock_proto',
              data: parsedData
            }
          })
          .filter(Boolean) // Удаляем null элементы

        contentLibrary.value.push(...wblockSets)
        console.log('✅ Загружено из wblock_proto:', wblockSets.length)
      }
    } catch (error) {
      console.log('⚠️ Не удалось загрузить из wblock_proto:', error)
    }

    // Загружаем из файлов
    try {
      const fileResponse = await $fetch('/api/load-user-settings', {
        method: 'POST',
        body: { type: 'content-sets' }
      })

      if (fileResponse.success && fileResponse.data) {
        const fileSets = fileResponse.data.map((set: any) => ({
          ...set,
          source: 'files'
        }))
        contentLibrary.value.push(...fileSets)
        console.log('✅ Загружено из файлов:', fileSets.length)
      }
    } catch (error) {
      console.log('⚠️ Файлы контента не найдены')
    }

    contentLibrary.value = library.sort((a, b) => b.compatibility - a.compatibility)

  } catch (error) {
    console.error('Ошибка загрузки библиотеки контента:', error)
    contentLibrary.value = getBuiltInContentSets()
  }
}

const loadBlockTypes = async () => {
  try {
    const blocks = await getItems({
      collection: 'wblock_proto',
      params: {
        fields: ['block_type'],
        limit: -1
      }
    })

    const types = new Set<string>()
    blocks.forEach((block: any) => {
      if (block.block_type && Array.isArray(block.block_type)) {
        block.block_type.forEach((type: string) => types.add(type))
      }
    })

    blockTypes.value = Array.from(types).sort().map(type => ({
      label: type,
      value: type
    }))

    // Добавляем стандартные типы если их нет
    const standardTypes = ['первый-экран', 'текст', 'преимущества', 'услуги-товары', 'о-компании', 'отзывы', 'cta']
    standardTypes.forEach(type => {
      if (!blockTypes.value.some(bt => bt.value === type)) {
        blockTypes.value.push({ label: type, value: type })
      }
    })

  } catch (error) {
    console.error('Ошибка загрузки типов блоков:', error)
    blockTypes.value = contentTypes.value.map(ct => ({ label: ct.label, value: ct.value }))
  }
}

const getBuiltInContentSets = () => {
  return [
    {
      id: 'builtin-hero-it',
      title: 'IT компания - Первый экран',
      description: 'Набор для главного экрана IT компании',
      type: 'первый-экран',
      source: 'builtin',
      data: {
        title: 'Развиваем ваш бизнес',
        title2: 'Профессиональные IT решения',
        text: 'Помогаем компаниям достигать новых высот с помощью современных технологий',
        text2: 'Начать сотрудничество',
        image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800',
        image2: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=1200',
        url: '#contact',
        linkText: 'Получить консультацию',
        excerpt: 'Краткое описание услуг'
      },
      preview: {
        title: 'Развиваем ваш бизнес',
        text: 'Помогаем компаниям достигать...',
        image: 'https://images.unsplash.com/...'
      },
      compatibility: 85
    },
    {
      id: 'builtin-services-web',
      title: 'Веб-разработка - Услуги',
      description: 'Набор для блока услуг веб-разработки',
      type: 'услуги-товары',
      source: 'builtin',
      data: {
        title: 'Веб-разработка',
        title2: 'Создание сайтов',
        text: 'Разрабатываем современные веб-сайты и приложения',
        text2: 'Подробнее',
        image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=600',
        url: '/services/web-development',
        linkText: 'Заказать разработку',
        excerpt: 'От лендингов до сложных порталов'
      },
      preview: {
        title: 'Веб-разработка',
        text: 'Разрабатываем современные...',
        image: 'https://images.unsplash.com/...'
      },
      compatibility: 90
    },
    {
      id: 'builtin-about-company',
      title: 'О компании - Стандартный',
      description: 'Универсальный набор для блока "О компании"',
      type: 'о-компании',
      source: 'builtin',
      data: {
        title: 'О нашей компании',
        text: 'Мы работаем на рынке уже более 10 лет и помогли сотням клиентов',
        text2: 'Узнать больше',
        image: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800',
        url: '/about',
        linkText: 'Наша история',
        excerpt: 'Опыт, качество, результат'
      },
      preview: {
        title: 'О нашей компании',
        text: 'Мы работаем на рынке...',
        image: 'https://images.unsplash.com/...'
      },
      compatibility: 75
    }
  ]
}

const determineContentType = (tags: string | null | undefined): string => {
  if (!tags || typeof tags !== 'string') return 'дополнительно'

  const tagsLower = tags.toLowerCase()
  if (tagsLower.includes('первый') || tagsLower.includes('hero')) return 'первый-экран'
  if (tagsLower.includes('услуг') || tagsLower.includes('товар')) return 'услуги-товары'
  if (tagsLower.includes('преимущ') || tagsLower.includes('benefit')) return 'преимущества'
  if (tagsLower.includes('компани') || tagsLower.includes('about')) return 'о-компании'
  if (tagsLower.includes('отзыв') || tagsLower.includes('review')) return 'отзывы'
  if (tagsLower.includes('cta') || tagsLower.includes('призыв')) return 'cta'
  if (tagsLower.includes('текст') || tagsLower.includes('text')) return 'текст'

  return 'дополнительно'
}

const getPreviewData = (jsonData: any) => {
  const entries = Object.entries(jsonData)
  return Object.fromEntries(entries.slice(0, 3))
}

const calculateCompatibility = (jsonData: any): number => {
  if (uniqueVariables.value.length === 0) {
    return Object.keys(jsonData).length * 10
  }

  const contentVars = Object.keys(jsonData)
  const matches = uniqueVariables.value.filter(v => contentVars.includes(v))
  return Math.round((matches.length / uniqueVariables.value.length) * 100)
}

const analyzeCompatibility = () => {
  contentLibrary.value.forEach(set => {
    set.compatibility = calculateCompatibility(set.data)
  })
  contentLibrary.value.sort((a, b) => b.compatibility - a.compatibility)
}

const toggleContentSet = (contentSet: any) => {
  const index = selectedContentSets.value.findIndex(s => s.id === contentSet.id)
  if (index > -1) {
    selectedContentSets.value.splice(index, 1)
  } else {
    selectedContentSets.value.push(contentSet)
  }
}

const getContentTypeSeverity = (type: string) => {
  switch (type) {
    case 'первый-экран': return 'danger'
    case 'текст': return 'info'
    case 'преимущества': return 'success'
    case 'услуги-товары': return 'warning'
    case 'о-компании': return 'info'
    case 'отзывы': return 'warning'
    case 'cta': return 'danger'
    default: return 'secondary'
  }
}

const getVariablePlaceholder = (variable: string): string => {
  if (!variable) return 'Значение'

  const varLower = variable.toLowerCase()
  if (varLower.includes('title')) return 'Заголовок'
  if (varLower.includes('text')) return 'Текст описания'
  if (varLower.includes('linktext')) return 'Текст кнопки'
  if (varLower.includes('image')) return 'URL изображения'
  if (varLower.includes('url')) return 'Ссылка'
  if (varLower.includes('excerpt')) return 'Краткое описание'

  return 'Значение'
}

const getCharCount = (text: string | undefined): string => {
  if (!text) return '0'
  return String(text.length)
}

const applyQuickVariables = () => {
  const quickVars = Object.keys(quickVariables.value).filter(k => quickVariables.value[k])
  if (quickVars.length === 0) return

  const quickContentSet = {
    id: `quick-${Date.now()}`,
    title: 'Быстрый набор',
    description: 'Созданный через конструктор переменных',
    type: 'дополнительно',
    source: 'quick',
    data: { ...quickVariables.value },
    preview: Object.fromEntries(Object.entries(quickVariables.value).slice(0, 3)),
    compatibility: 100
  }

  selectedContentSets.value = [quickContentSet]
}

const clearQuickVariables = () => {
  quickVariables.value = {}
}

const toggleCardExpansion = (cardId: string) => {
  if (expandedCards.value.has(cardId)) {
    expandedCards.value.delete(cardId)
  } else {
    expandedCards.value.add(cardId)
  }
}

const applySelectedContent = () => {
  if (selectedContentSets.value.length === 0) return

  const modifiedElements: any[] = []

  props.selectedElements.forEach(element => {
    selectedContentSets.value.forEach(contentSet => {
      let html = element.hbs || element.html
      Object.entries(contentSet.data).forEach(([variable, value]) => {
        const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
        html = html.replace(regex, String(value))
      })

      modifiedElements.push({
        ...element,
        html,
        appliedContentSet: contentSet,
        contentData: contentSet.data
      })
    })
  })

  emit('content-applied', {
    modifiedElements,
    contentSets: selectedContentSets.value,
    totalVariants: modifiedElements.length
  })
}

// Методы для режима создания
const addContentSet = () => {
  editableContentSets.value.push({
    id: `new-${Date.now()}`,
    title: '',
    description: '',
    type: '',
    variables: [{ name: '', value: '' }]
  })
}

const removeContentSet = (index: number) => {
  editableContentSets.value.splice(index, 1)
}

const addVariable = (setIndex: number) => {
  editableContentSets.value[setIndex].variables.push({ name: '', value: '' })
}

const removeVariable = (setIndex: number, varIndex: number) => {
  editableContentSets.value[setIndex].variables.splice(varIndex, 1)
}

const addVariableFromElement = (setIndex: number, variable: string) => {
  const contentSet = editableContentSets.value[setIndex]
  const exists = contentSet.variables.some((v: any) => v.name === variable)
  if (!exists) {
    contentSet.variables.push({
      name: variable,
      value: getVariablePlaceholder(variable)
    })
  }
}

const getTestPreviewHtml = (setIndex: number): string => {
  if (props.selectedElements.length === 0) {
    return '<div style="padding: 8px; text-align: center; color: #666;">Нет элементов для тестирования</div>'
  }

  const element = props.selectedElements[0]
  const contentSet = editableContentSets.value[setIndex]
  const testData: { [key: string]: string } = {}

  contentSet.variables.forEach((variable: any) => {
    if (variable.name && variable.value) {
      testData[variable.name] = variable.value
    }
  })

  let html = element.hbs || element.html
  Object.entries(testData).forEach(([variable, value]) => {
    const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
    html = html.replace(regex, String(value))
  })

  // Формируем полный HTML с CSS
  let fullHtml = '<!DOCTYPE html><html><head><meta charset="utf-8">'

  if (element.css) {
    fullHtml += element.css
  }

  fullHtml += '</head><body>' + html

  if (element.js) {
    fullHtml += '' + element.js + ''
  }

  fullHtml += '</body></html>'

  return fullHtml
}

const saveContentSet = async (index: number) => {
  const contentSet = editableContentSets.value[index]

  const data: { [key: string]: string } = {}
  contentSet.variables.forEach((variable: any) => {
    if (variable.name && variable.value) {
      data[variable.name] = variable.value
    }
  })

  const newSet = {
    id: `custom-${Date.now()}`,
    title: contentSet.title,
    description: contentSet.description,
    type: contentSet.type,
    source: 'custom',
    data,
    preview: Object.fromEntries(Object.entries(data).slice(0, 3)),
    compatibility: calculateCompatibility(data)
  }

  contentLibrary.value.unshift(newSet)
  selectedContentSets.value = [newSet]

  // Сохраняем в файл
  try {
    await $fetch('/api/save-user-settings', {
      method: 'POST',
      body: {
        type: 'content-sets',
        filename: `content-set-${Date.now()}.json`,
        data: newSet
      }
    })
  } catch (error) {
    console.error('Ошибка сохранения набора:', error)
  }
}

const getElementPreviewHtml = (element: any, withContent: boolean): string => {
  let html = element.html || ''
  const css = element.css || ''
  const js = element.js || ''

  if (withContent) {
    let contentData: any = {}

    const quickVars = Object.keys(quickVariables.value).filter(k => quickVariables.value[k])
    if (quickVars.length > 0) {
      contentData = { ...quickVariables.value }
    } else if (selectedContentSets.value.length > 0) {
      contentData = selectedContentSets.value[0].data
    }

    if (element.hbs && Object.keys(contentData).length > 0) {
      html = element.hbs
      Object.entries(contentData).forEach(([variable, value]) => {
        const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
        html = html.replace(regex, String(value))
      })
    }
  }

  let fullHtml = '<!DOCTYPE html><html><head><meta charset="utf-8">'

  if (css) {
    fullHtml += css
  }

  fullHtml += '</head><body>' + html

  if (js) {
    fullHtml += '' + js + ''
  }

  fullHtml += '</body></html>'

  return fullHtml
}

const getElementPreviewWithSpecificContent = (element: any, contentData: any): string => {
  let html = element.html || ''
  const css = element.css || ''
  const js = element.js || ''

  if (element.hbs && Object.keys(contentData).length > 0) {
    html = element.hbs
    Object.entries(contentData).forEach(([variable, value]) => {
      if (value) { // Только если значение не пустое
        const regex = new RegExp(`\\{\\{\\s*${variable}\\s*\\}\\}`, 'g')
        html = html.replace(regex, String(value))
      }
    })
  }

  let fullHtml = '<!DOCTYPE html><html><head><meta charset="utf-8">'

  if (css) {
    fullHtml += css
  }

  fullHtml += '</head><body>' + html

  if (js) {
    fullHtml += '' + js + ''
  }

  fullHtml += '</body></html>'

  return fullHtml
}

// Watchers
watch(() => props.selectedElements, async () => {
  console.log('🔄 Изменились выбранные элементы:', props.selectedElements.length)
  if (props.selectedElements.length > 0) {
    await analyzeElements()
  } else {
    uniqueVariables.value = []
    contentLibrary.value = []
    quickVariables.value = {}
  }
}, { immediate: true, deep: true })

watch(() => uniqueVariables.value, () => {
  if (uniqueVariables.value.length > 0) {
    analyzeCompatibility()
  }
}, { deep: true })

// Lifecycle
onMounted(async () => {
  console.log('🚀 ContentConstructor mounted, элементов:', props.selectedElements.length)

  await loadBlockTypes()

  // Принудительно анализируем элементы при монтировании
  if (props.selectedElements.length > 0) {
    console.log('🔍 Принудительный анализ при монтировании')
    await analyzeElements()
  }

  // Инициализируем режим создания с одним пустым набором
  editableContentSets.value = [{
    id: 'new-1',
    title: '',
    description: '',
    type: '',
    variables: [{ name: '', value: '' }]
  }]
})
</script>

<style scoped>


.content-card {
  transition: all 0.2s ease;
}

.content-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.content-set-editor {
  transition: all 0.2s ease;
}

.content-set-editor:hover {
  background-color: #f8fafc;
}
</style>
