<template>
  <div class="content-library-constructor h-screen flex flex-col">
    <!-- Специальный конструктор (50% высоты экрана) -->
    <div class="constructor-area bg-surface-0 dark:bg-surface-900 border rounded mb-2 p-2" style="height: 50vh;">
      <div class="flex h-full">
        <!-- Библиотека контента (1/2 ширины) -->
        <div class="w-1/2 pr-2">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-sm font-semibold">📚 Библиотека примитивов</h4>
            <div class="flex items-center gap-1">
              <Button
                :class="['text-xs', libraryMode === 'view' ? 'p-button-info' : 'p-button-outlined']"
                label="Просмотр"
                style="font-size: 10px; padding: 2px 6px"
                @click="libraryMode = 'view'"
              />
              <Button
                :class="['text-xs', libraryMode === 'edit' ? 'p-button-info' : 'p-button-outlined']"
                label="Редактор"
                style="font-size: 10px; padding: 2px 6px"
                @click="libraryMode = 'edit'"
              />
              <Button
                icon="pi pi-refresh"
                class="text-xs p-button-outlined"
                style="font-size: 8px; padding: 2px 4px"
                @click="loadPrimitives"
              />
            </div>
          </div>

          <!-- Фильтры -->
          <div class="flex gap-2 mb-2">
            <InputText
              v-model="searchQuery"
              placeholder="Поиск..."
              class="text-xs flex-1"
              style="font-size: 11px; height:30px;"
            />
            <MultiSelect
              v-model="selectedTypes"
              :options="availableTypes"
              placeholder="Типы"
              class="text-xs w-1/3 p-0"
              display="chip"
              :maxSelectedLabels="4"
              style="height:30px;font-size: 11px;"
              :pt="{
                item: { class: 'text-xs', style: 'margin-top:-8px' },
                header: { class: 'text-xs', style: 'margin-top:-8px' },
                placeholder: { class: 'text-xs', style: 'margin-top:-8px' }
              }"
            />
            <MultiSelect
              v-model="selectedSubtypes"
              :options="availableSubtypes"
              placeholder="Подтипы"
              class="text-xs w-1/4"
              display="chip"
              :maxSelectedLabels="4"
              style="height:30px;font-size: 11px;"
              :pt="{
                item: { class: 'text-xs', style: 'margin-top:-8px' },
                header: { class: 'text-xs', style: 'margin-top:-8px' },
                placeholder: { class: 'text-xs', style: 'margin-top:-8px' }
              }"
            />
            <MultiSelect
              v-model="selectedLengths"
              :options="lengthTypes"
              placeholder="Длина"
              class="text-xs w-1/5"
              display="chip"
              :maxSelectedLabels="4"
              style="height:30px;font-size: 11px;"
              :pt="{
                item: { class: 'text-xs', style: 'margin-top:-8px' },
                header: { class: 'text-xs', style: 'margin-top:-8px' },
                placeholder: { class: 'text-xs', style: 'margin-top:-8px' }
              }"
            />
          </div>

          <!-- Режим просмотра -->
          <div v-if="libraryMode === 'view'" class="h-86 overflow-auto">
            <DataTable
              v-model:selection="selectedPrimitives"
              :value="filteredPrimitives"
              selection-mode="multiple"
              data-key="id"
              class="p-datatable-sm text-xs"
              style="font-size: 9px"
              scrollable
              scroll-height="350px"
              sortMode="multiple"
              removableSort
            >
              <Column selection-mode="multiple" style="width: 20px" />
              <Column field="number" header="№" sortable style="width: 80px; font-size: 8px" />
              <Column field="type" header="Тип" sortable style="width: 80px; font-size: 9px" />
              <Column field="subtype" header="Подтип" sortable style="width: 80px; font-size: 9px" />
              <Column field="value" header="Значение" style="font-size: 9px">
                <template #body="{ data }">
                  <div class="flex items-center gap-1">
                    <Image
                      v-if="isImageUrl(data.value)"
                      :src="data.value"
                      alt="Preview"
                      width="30"
                      height="20"
                      preview
                      style="object-fit: cover"
                    />
                    <span class="truncate">{{ truncateText(data.value, 60) }}</span>
                  </div>
                </template>
              </Column>
              <Column field="group" header="Группа" sortable style="width: 60px; font-size: 9px" />
              <Column field="length" header="Длина" sortable style="width: 50px; font-size: 9px" />
              <Column header="Символы" style="width: 50px; font-size: 9px">
                <template #body="{ data }">
                  <span class="text-xs text-gray-500">{{ data.value ? data.value.length : 0 }}</span>
                </template>
              </Column>
              <Column header="Действия" style="width: 60px">
                <template #body="{ data }">
                  <Button
                    icon="pi pi-plus"
                    class="p-button-text p-button-sm"
                    style="width: 20px; height: 20px; padding: 0"
                    @click="addToConstructor(data)"
                  />
                </template>
              </Column>
            </DataTable>
          </div>

          <!-- Режим редактирования -->
          <div v-else class="h-80">
            <div class="overflow-auto" style="height: 320px;">
              <DataTable
                v-model:editingRows="editingRows"
                :value="editablePrimitives"
                edit-mode="row"
                data-key="id"
                class="p-datatable-sm text-xs compact-table"
                style="font-size: 9px"
                @row-edit-save="onRowEditSave"
              >
                <Column field="type" header="Тип" style="width: 80px">
                  <template #editor="{ data, field }">
                    <Dropdown
                      v-model="data[field]"
                      :options="primitiveTypes"
                      editable
                      class="text-xs compact-dropdown"
                      style="font-size: 8px; height: 20px"
                    />
                  </template>
                </Column>
                <Column field="subtype" header="Подтип" style="width: 80px">
                  <template #editor="{ data, field }">
                    <InputText
                      v-model="data[field]"
                      class="text-xs compact-input"
                      style="font-size: 8px; padding: 1px 2px; height: 20px"
                    />
                  </template>
                </Column>
                <Column field="value" header="Значение">
                  <template #editor="{ data, field }">
                    <Textarea
                      v-model="data[field]"
                      class="text-xs compact-textarea"
                      style="font-size: 8px; padding: 1px 2px; min-height: 20px; resize: vertical"
                      rows="1"
                      autoResize
                    />
                  </template>
                </Column>
                <Column field="group" header="Группа" style="width: 60px">
                  <template #editor="{ data, field }">
                    <InputText
                      v-model="data[field]"
                      class="text-xs compact-input"
                      style="font-size: 8px; padding: 1px 2px; height: 20px"
                    />
                  </template>
                </Column>
                <Column field="length" header="Длина" style="width: 50px">
                  <template #body="{ data }">
                    <span class="text-xs">{{ data.length }}</span>
                  </template>
                </Column>
                <Column :row-editor="true" style="width: 40px">
                  <template #roweditoriniticon>
                    <i class="pi pi-pencil" style="font-size: 10px"></i>
                  </template>
                  <template #roweditorsaveicon>
                    <i class="pi pi-check" style="font-size: 10px"></i>
                  </template>
                  <template #roweditorcancelicon>
                    <i class="pi pi-times" style="font-size: 10px"></i>
                  </template>
                </Column>
              </DataTable>
            </div>

            <div class="mt-2 flex gap-2">
              <Button
                label="Добавить строку"
                icon="pi pi-plus"
                class="text-xs p-button-outlined"
                style="font-size: 9px; padding: 4px 8px"
                @click="addNewPrimitive"
              />
              <Button
                label="Сохранить все"
                icon="pi pi-save"
                class="text-xs p-button-success"
                style="font-size: 9px; padding: 4px 8px"
                @click="saveAllPrimitives"
              />
            </div>
          </div>
        </div>

        <!-- Конструктор контента (1/2 ширины) -->
        <div class="w-1/2 pl-2 border-l">
          <!-- Верхняя строка с управлением -->
          <div class="flex items-center justify-between mb-2">

            <h4 class="text-sm font-semibold">⚙️ Конструктор</h4>
            <div class="flex items-center gap-1">
              <div class="text-xs text-gray-500 text-center">
            {{ constructorItems.length }} переменных, {{ selectedConstructorItems.length }} выбрано
          </div>
              <MultiSelect
              v-model="selectedFormIndexes"
              :options="actualFormOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="Формы"
              class="text-xs"
              
              display="chip"
              :maxSelectedLabels="4"
              style="height:30px;font-size: 11px;width: 160px;"
              :pt="{
                item: { class: 'text-xs', style: 'margin-top:-8px' },
                header: { class: 'text-xs', style: 'margin-top:-8px' },
                placeholder: { class: 'text-xs', style: 'margin-top:-8px' }
              }"
            />

            <Button
              label="Применить"
              icon="pi pi-check"
              class="text-xs p-button-success"
              style="font-size: 10px; padding: 2px 6px"
              :disabled="selectedConstructorItems.length === 0 || selectedFormIndexes.length === 0"
              @click="applyToSelectedForms"
            />

            <Button
              icon="pi pi-check-square"
              class="text-xs p-button-outlined"
              style="font-size: 8px; padding: 2px 4px"
              @click="selectAllConstructorItems"
              v-tooltip="'Выбрать все'"
            />

            <Button
              icon="pi pi-trash"
              class="text-xs p-button-outlined p-button-danger"
              style="font-size: 8px; padding: 2px 4px"
              @click="clearConstructor"
              v-tooltip="'Очистить'"
            />
          </div>
        </div>

          <!-- Сетка 3 в ряд -->
          <div class="h-96 overflow-auto">
            <div class="grid grid-cols-3 gap-1">
              <div
                v-for="(item, index) in constructorItems"
                :key="index"
                class="border rounded bg-gray-50 relative"
                style="font-size: 8px; padding: 2px"
              >
                <!-- Компактная верхняя строка -->
                <div class="flex items-center gap-1 mb-1">
                  <input
                    v-model="item.selected"
                    type="checkbox"
                    class="w-2 h-2"
                  />
                  <span class="text-xs font-medium flex-1 truncate" style="font-size: 9px">{{ item.type }}-{{ item.subtype }}</span>
                  <div class="text-center">
                  <span class="text-xs text-gray-500" style="font-size: 8px">({{ item.value ? item.value.length : 0 }} симв.)</span>
                </div>
                  <Button
                    icon="pi pi-times"
                    class="p-button-text p-button-sm"
                    style="width: 10px; height: 10px; padding: 0; font-size: 6px"
                    @click="removeFromConstructor(index)"
                  />
                </div>
                <div class="flex items-center gap-1 mb-1">
                <!-- Переменная -->
                <Dropdown
                  v-model="item.variable"
                  :options="getVariableOptions(item.type, item.subtype)"
                  editable
                  placeholder="Переменная"
                  class="text-xs w-1/4 mb-1"
                  style="font-size: 9px; padding: 1px; height:20px;"
                  :pt="{
                label: { style: 'padding-left:0' },
                dropdown: { style: 'padding-left:0;width:15px' },
                input: { style: 'padding-left:0' },
                option: { style: 'font-size: 9px;padding: 1px;' }
              }"
                />

                <!-- Значение -->
                <Dropdown
                  v-model="item.value"
                  :options="getValueOptions(item.type, item.subtype)"
                  editable
                  placeholder="Значение"
                  class="text-xs w-3/4 mb-1"
                  style="font-size: 9px; padding: 1px; height:20px;"
                  :pt="{
                label: { style: 'padding-left:0' },
                dropdown: { style: 'padding-left:0;width:15px' },
                input: { style: 'padding-left:0' },
                option: { style: 'font-size: 9px;padding: 1px;' }
              }"
                />
              </div>
                
                
              </div>
            </div>

            <div v-if="constructorItems.length === 0" class="text-center text-gray-500 py-4 col-span-3">
              <i class="pi pi-plus-circle text-lg mb-1 block"></i>
              <div class="text-xs">Добавьте примитивы</div>
            </div>
          </div>

          
        </div>
      </div>
    </div>

    <!-- Интеллектуальные формы (50% высоты экрана) -->
    <div class="forms-area" style="height: 50vh;">
      <IntelligentForms
        ref="intelligentFormsRef"
        :constructor-data="constructorItems"
        :selected-items="selectedItems"
        @forms-updated="handleFormsUpdated"
        @forms-count-changed="handleFormsCountChanged"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import MultiSelect from 'primevue/multiselect'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Image from 'primevue/image'
import Dropdown from 'primevue/dropdown'
import IntelligentForms from '~/components/IntelligentForms.vue'

// Props
interface Props {
  selectedItems: any[]
  formsCount?: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'content-applied': [data: any]
  'primitives-updated': [data: any]
  'apply-to-forms': [data: any]
  'forms-count-changed': [count: number]
}>()

// Composables
const toast = useToast()

// Reactive data
const libraryMode = ref<'view' | 'edit'>('view')
const searchQuery = ref('')
const selectedTypes = ref<string[]>([])
const selectedSubtypes = ref<string[]>([])
const selectedLengths = ref<string[]>([])
const selectedPrimitives = ref<any[]>([])
const editingRows = ref<any[]>([])
const selectedFormIndexes = ref<number[]>([])

// Данные библиотеки
const primitives = ref<any[]>([])
const editablePrimitives = ref<any[]>([])
const constructorItems = ref<any[]>([])

// Ref для IntelligentForms
const intelligentFormsRef = ref()

// Типы и опции
const primitiveTypes = [
  'Краткий-текст', 'Текст', 'Картинки', 'Преимущества', 'Продукт', 'О компании',
  'Галерея-работ', 'Прайс', 'Отзывы', 'Клиенты', 'Схема-работы', 'СТА',
  'Цифры', 'Виды', 'Характеристики', 'Особенности', 'Портфолио', 'Статьи',
  'Контент', 'Результаты', 'Возможности', 'Гарантии', 'Регалии', 'Команда',
  'Контакты', 'Дополнительно', 'FAQ'
]

const lengthTypes = [
  'Короткий', 'Небольшой', 'Средний', 'Длинный', 'Очень длинный', 'Текст'
]

// Computed
const availableTypes = computed(() => {
  const types = new Set(primitives.value.map(p => p.type))
  return Array.from(types)
})

const availableSubtypes = computed(() => {
  const subtypes = new Set(primitives.value.map(p => p.subtype))
  return Array.from(subtypes)
})

const filteredPrimitives = computed(() => {
  return primitives.value.filter(primitive => {
    const matchesSearch = !searchQuery.value ||
      primitive.value.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesType = !selectedTypes.value.length ||
      selectedTypes.value.includes(primitive.type)
    const matchesSubtype = !selectedSubtypes.value.length ||
      selectedSubtypes.value.includes(primitive.subtype)
    const matchesLength = !selectedLengths.value.length ||
      selectedLengths.value.includes(primitive.length)

    return matchesSearch && matchesType && matchesSubtype && matchesLength
  })
})

const selectedConstructorItems = computed(() => {
  return constructorItems.value.filter(item => item.selected)
})

const formOptions = computed(() => {
  // Здесь будут опции форм из IntelligentForms
  return Array.from({ length: 5 }, (_, i) => ({
    label: `Форма ${i + 1}`,
    value: i
  }))
})

const actualFormOptions = computed(() => {
  // Показываем только номера форм, которые реально добавлены
  const count = props.formsCount || 0
  if (count === 0) return []

  return Array.from({ length: count }, (_, i) => ({
    label: `${i + 1}`,
    value: i
  }))
})

// Methods
const loadPrimitives = async () => {
  try {
    // Сначала пытаемся загрузить из localStorage
    const savedPrimitives = localStorage.getItem('content-primitives')
    if (savedPrimitives) {
      primitives.value = JSON.parse(savedPrimitives)
    } else {
      // Если в localStorage нет данных, загружаем из JSON файла
      const response = await fetch('/data/content-primitives.json')
      if (response.ok) {
        primitives.value = await response.json()
      } else {
        primitives.value = []
      }
    }
    editablePrimitives.value = [...primitives.value]
  } catch (error) {
    console.error('Error loading primitives:', error)
    primitives.value = []
    editablePrimitives.value = []
  }
}

const isImageUrl = (url: string) => {
  return typeof url === 'string' && (url.startsWith('http') || url.startsWith('/'))
}

const truncateText = (text: string, maxLength: number) => {
  if (!text || text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const addToConstructor = (primitive: any) => {
  const item = {
    ...primitive,
    variable: getVariableName(primitive.type, primitive.subtype),
    selected: false,
    id: Date.now() + Math.random()
  }
  constructorItems.value.push(item)
}

const removeFromConstructor = (index: number) => {
  constructorItems.value.splice(index, 1)
}

const clearConstructor = () => {
  constructorItems.value = []
}

const getVariableName = (type: string, subtype: string) => {
  // Логика определения имени переменной на основе типа и подтипа
  const typeMap: Record<string, string> = {
    'Краткий-текст': 'title',
    'Текст': 'text',
    'Картинки': 'image',
    'Преимущества': 'title',
    'Продукт': 'title',
    'О компании': 'text',
    'Отзывы': 'text',
    'Контакты': 'text'
  }
  
  return typeMap[type] || 'text'
}

const selectAllConstructorItems = () => {
  constructorItems.value.forEach(item => {
    item.selected = true
  })
}

const getVariableOptions = (type: string, subtype: string) => {
  // Возвращаем опции переменных на основе типа и подтипа
  const baseOptions = ['title', 'text', 'image', 'imageBackground', 'url', 'linkText', 'icon', 'excerpt']

  // Добавляем специфичные для типа опции
  const typeSpecific = primitives.value
    .filter(p => p.type === type && p.subtype === subtype)
    .map(p => getVariableName(p.type, p.subtype))

  return [...new Set([...baseOptions, ...typeSpecific])]
}

const getValueOptions = (type: string, subtype: string) => {
  // Возвращаем опции значений из базы примитивов по типу и подтипу
  return primitives.value
    .filter(p => p.type === type && p.subtype === subtype)
    .map(p => p.value)
}

const applyToForms = () => {
  emit('content-applied', {
    constructorData: constructorItems.value,
    selectedItems: props.selectedItems
  })
}

const applyToSelectedForms = () => {
  const selectedItems = constructorItems.value.filter(item => item.selected)

  if (selectedItems.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите переменные для применения',
      life: 3000
    })
    return
  }

  if (selectedFormIndexes.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите формы для применения',
      life: 3000
    })
    return
  }

  // Напрямую вызываем метод в IntelligentForms
  if (intelligentFormsRef.value && intelligentFormsRef.value.applyConstructorDataToForms) {
    intelligentFormsRef.value.applyConstructorDataToForms(selectedItems, selectedFormIndexes.value)

    toast.add({
      severity: 'success',
      summary: 'Применено',
      detail: `Применено ${selectedItems.length} переменных к ${selectedFormIndexes.value.length} формам`,
      life: 3000
    })
  } else {
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось применить данные к формам',
      life: 3000
    })
  }
}

const handleFormsUpdated = (data: any) => {
  console.log('Forms updated:', data)
}

const handleFormsCountChanged = (count: number) => {
  emit('forms-count-changed', count)
}

const addNewPrimitive = () => {
  const newId = editablePrimitives.value.length + 1
  const newPrimitive = {
    id: newId,
    number: `${String(newId).padStart(2, '0')}-01-01-${String(newId).padStart(4, '0')}`,
    type: 'Краткий-текст',
    subtype: 'Заголовок',
    value: '',
    group: 'Основное',
    length: 'Короткий'
  }
  editablePrimitives.value.push(newPrimitive)
}

const onRowEditSave = (event: any) => {
  const { newData, index } = event
  // Автоматический подсчет длины
  const length = newData.value ? newData.value.length : 0
  if (length <= 20) newData.length = 'Короткий'
  else if (length <= 50) newData.length = 'Небольшой'
  else if (length <= 100) newData.length = 'Средний'
  else if (length <= 200) newData.length = 'Длинный'
  else if (length <= 500) newData.length = 'Очень длинный'
  else newData.length = 'Текст'
  
  editablePrimitives.value[index] = newData
}

const saveAllPrimitives = async () => {
  try {
    // Сохраняем в localStorage для сохранения между сессиями
    localStorage.setItem('content-primitives', JSON.stringify(editablePrimitives.value))
    primitives.value = [...editablePrimitives.value]

    // Уведомляем родительский компонент об обновлении
    emit('primitives-updated', editablePrimitives.value)

    toast.add({
      severity: 'success',
      summary: 'Сохранено',
      detail: 'Примитивы сохранены в библиотеку',
      life: 3000
    })
  } catch (error) {
    console.error('Error saving primitives:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить примитивы',
      life: 3000
    })
  }
}

onMounted(() => {
  loadPrimitives()
})
</script>

<style scoped>
.content-library-constructor {
  height: 100vh;
  overflow: hidden;
}

.constructor-area {
  height: 50vh;
}

:deep(.p-datatable-sm) {
  font-size: 10px;
}

:deep(.p-datatable-sm .p-datatable-tbody > tr > td) {
  padding: 2px 4px;
}

:deep(.p-datatable-sm .p-datatable-thead > tr > th) {
  padding: 2px 4px;
  font-size: 9px;
}

:deep(.compact-table .p-datatable-tbody > tr > td) {
  padding: 1px 2px;
  height: 24px;
}

:deep(.compact-table .p-datatable-thead > tr > th) {
  padding: 1px 2px;
  height: 20px;
  font-size: 8px;
}

:deep(.compact-dropdown .p-dropdown) {
  height: 20px;
  min-height: 20px;
}

:deep(.compact-dropdown .p-dropdown .p-inputtext) {
  padding: 1px 2px;
  font-size: 8px;
}

:deep(.compact-input) {
  height: 20px;
  min-height: 20px;
}

:deep(.compact-textarea) {
  min-height: 20px;
}

:deep(.compact-textarea .p-inputtextarea) {
  padding: 1px 2px;
  font-size: 8px;
  line-height: 1.2;
}
</style>
