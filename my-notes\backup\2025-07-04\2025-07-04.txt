---
✅проверить массовый скриншотинг
--- 
## проблемы на wpages
функция добавления страниц по url 
✅- необходимо добавить multiselect-поле wpage_type (с loadOptions из поля wpage_type коллекции wpage в directus) в строчки при добавление страниц по url (у каждой страницы добавлемой по url может быть свои типы - выбраанные типы мы сохраняем в поля wpage_type созданных записей коллекции wpage)
✅- при добавлении страниц по url код html во время сохранения wpage в поле html должен модифицироваться (необходимо в атрибуты src тега img и и другие атрибуты вроде background-image в случае относительных ссылок модифицировать их в абсолютные, чтобы изображения отображались даже если html код в локальном файле/на другом домене или в iframe)
✅- в поле description должна записываться строчка body с классами и атрибутами (без вложенненых тегов)

функция разделения на блоки 
✅- не проставляются связи разделенных блоков со страницами (действие "разделить на блоки + скрины", также можно проверить действие "разделить на блоки". m2m-связи wpage через поле wblock_proto (узловая коллекция wpage_wblock_proto - wpage_id <-> wblock_proto_id)
-процесс разделения ("разделение на блоки" и "разделение на блоки + скрины")  происходит неинформативно (необходимо видеть прогресс результата и понимать текущий шаг) сейчас я вижу процесс в терминале но не в приложении (можно оформить как в функции добавления страниц по url)

сайдбар редактирования страницы
- в сайдбар редактирования страниц в datatable "связанные блоки" надо между number и title добавить колонку sketch (картинка из поля sketch коллекции wblock_proto - image с preview) малого размера (до 40px в ширину с авто-высотой без искажений) и последню колонку с полем block_type (теги), также datatable должен быть отсортирован по полю number по умолчанию

## проблемы на wblock-proto2
сайдбар редактирования блока
- добавить поле multiselect с отображение связанных страниц (поле wpage) с loadOptions (метоположение - между layout и welem_proto) - аналогичное поле есть в app\components\WBlockProtoSidebarV2.vue (app\components\WBlockProtoSidebarV2.vue)
-вместо multiselect поля editingItem.welem_proto лучше отображать связанные элементы из поля welem_proto в виде datatable (аналогично как это сделано на странице wpage в сайдбаре редактирования страницы relatedWblockProto) колонки - sketch (image+preview), number (сортировка по умолчанию по этому полю), title, elem_type (теги)
- добавить таб "Редактор" в котором реализовать интеллектуальное отображение и редактирование значений из json (полностью аналогично тому как сделан Таб "Редактор" в сайдбаре редактирования на странице app\pages\wjson.vue)
- добавить таб "Картинки" в котором реализовать интеллектуальное отображение и редактирование значений картинок из json (полностью аналогично тому как сделан Таб "Картинки" в сайдбаре редактирования на странице app\pages\wjson.vue)

--- 01-1
оставшиеся проблемы на wpages
-процесс разделения ("разделить на блоки" и "разделить на блоки + скрины")  происходит неинформативно (необходимо видеть прогресс результата и понимать текущий шаг) сейчас я вижу процесс в терминале но не в приложении (можно оформить как в функции добавления страниц по url)
(сейчас при нажатии на кнопку "разделить на блоки + скрины" я вижу затемныенный datatable и крутящийся спиннер, а хотел бы видеть окно с пошаговым прогрессом в виде таблички (страница - общее количество блоков, обработано блоков, статус). Я вижу окно "результаты обработки" только в конце процесса и это, окнечно, тоже удобно, но не удобно что я не вижу сам процесс (только в терминале). можно модифицировать окно "результаты обработки" чтобы оно появлялось не в конце процесса а в начале и показывало прогресс процесса.
- не проставляются связи разделенных блоков со страницами (действие "разделить на блоки + скрины", также можно проверить действие "разделить на блоки". m2m-связи wpage через поле wblock_proto (узловая коллекция wpage_wblock_proto - wpage_id <-> wblock_proto_id) 
(посмотри пример похожей функции savePageAndBlocks в wblock-html-gen.vue - там связи wpage и wblock_proto устанавливаются корректно)

исправь эти проблемы быстро и эффективно и переходи к задачам, поставленным мною ранее на странице wblock-proto2.vue

--- 01-2

##оставшиеся проблемы на wpages
1. процесс "разделить на блоки" и "разделить на блоки + скрины" - я все еще вижу окно об успешном разделении (теперь в начале а не в конце), а потом жду пока сделаются скриншоты и не вижу самого процесса.
т.е. эта задача еще не решена в том виде что мне хотелось бы - видеть процесс в виде каких-то процентов, прогресс-бара общего или по каждому
подумай пожалуйста над этим пунктом

2. сайдбар редактирования страницы
- в сайдбар редактирования страниц в datatable "связанные блоки" надо между number и title добавить колонку sketch (картинка из поля sketch коллекции wblock_proto - image с preview) малого размера (до 40px в ширину с авто-высотой без искажений) и последню колонку с полем block_type (теги), также datatable должен быть отсортирован по полю number по умолчанию

##оставшиеся проблемы на wblock-proto2
сайдбар редактирования блока
- поле multiselect с отображение связанных страниц (поле wpage) показывает пустые chips, пожалуйста внимательно проанализируй причины и исправь это (возможно стоит посомтреть как это сделано в компоненте app\components\WBlockProtoSidebarV2.vue)
- добавить таб "Редактор" в котором реализовать интеллектуальное отображение и редактирование значений из json (полностью аналогично тому как сделан Таб "Редактор" в сайдбаре редактирования на странице app\pages\wjson.vue)
- добавить таб "Картинки" в котором реализовать интеллектуальное отображение и редактирование значений картинок из json (полностью аналогично тому как сделан Таб "Картинки" в сайдбаре редактирования на странице app\pages\wjson.vue)

также в app\components\WBlockProtoSidebarV2.vue добавь тоже табы редактор и картинки по тому же принципу + DataTable для связанных элементов как в wblock-proto2.vue

---01-5 
##оставшиеся задачи на wpages
1. Проблема в процессе "разделить на блоки" и "разделить на блоки + скрины" - при запуске процесса я вижу окно "Результаты обработки" и таблицу (страница - блоки - обработано - прогресс - статус).
Но данные в 3 колонках остаются неизменными - "обработано" - 0, "прогресс" - 0% и "статус" - Ожидание.
подумай пожалуйста над этим пунктом очень внимательно - изучи код который осуществляет этот процесс и подумай о том как передавать в эти колонки актуальные данные в течение процесса (я вижу процесс в терминале) изучи маршрут который отвечает за этот процесс и если будешь модифицировать код имей ввиду что на этот маршрут могут полагаться и другие страницы и компоненты в дргуих частях приложения (т.е. если будешь менять код позаботься об обратной совместимости)
ТЫ УЖЕ 5 РАЗ СКАЗАЛ ЧТО ВЫПОЛНИЛ ЭТУ ЗАДАЧУ НО ТАК ЕЕ И НЕ ВЫПОЛНИЛ!

##оставшиеся задачи на wblock-proto2.vue и WBlockProtoSidebarV2.vue
сайдбар редактирования блока
- таб "Редактор" и таб "Картинки" должны полностью соответсвовать табам "Редактор" и "Картинки" из сайдбара редактирования на странице wjson.vue 
(сейчас я вижу в табе "редактор" что некорректно работают счетчики переменных (обрати внимание на логику подсчета в wjson - это очень важно, причем кнопки добавления переменных не работают - они тоже имеют определенную логику!)
пожалуйста изучи весь код wjson и очень внимательно проанализируй КАЖДУЮ ДЕТАЛЬ, потом приведи в соответсвеие с этим примером табы в wblock-proto2.vue и WBlockProtoSidebarV2.vue 

выполни эти задачи быстро, эффективно и аккуратно


---------------
-----02
-----------------
