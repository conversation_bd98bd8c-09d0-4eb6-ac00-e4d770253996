import { load } from 'cheerio';

/**
 * Интерфейс для узла структуры HTML
 */
export interface StructureNode {
  id: string;
  tagName: string;
  name?: string;
  className?: string;
  isElement: boolean;
  level: number;
  children?: StructureNode[];
}

/**
 * Генерирует уникальный ID для узла
 */
export function generateId(): string {
  return 'node_' + Math.random().toString(36).substring(2, 9);
}

/**
 * Преобразует HTML в структуру узлов для отображения в StructurePanel
 * @param html HTML-код для парсинга
 * @returns Массив узлов структуры
 */
export function parseHtmlToStructure(html: string): StructureNode[] {
  if (!html.trim()) return [];
  
  const $ = load(html);
  const rootNodes: StructureNode[] = [];
  
  // Обрабатываем корневые элементы
  $('body > *').each((index, element) => {
    const node = processNode($, element, 0);
    if (node) rootNodes.push(node);
  });
  
  return rootNodes;
}

/**
 * Обрабатывает отдельный узел DOM и его дочерние элементы
 */
function processNode($: any, element: any, level: number): StructureNode | null {
  const tagName = element.tagName?.toLowerCase();
  if (!tagName) return null;
  
  // Создаем узел структуры
  const node: StructureNode = {
    id: $(element).attr('id') || generateId(),
    tagName,
    className: $(element).attr('class'),
    isElement: true,
    level,
    children: []
  };
  
  // Обрабатываем дочерние элементы
  $(element).children().each((index: number, child: any) => {
    const childNode = processNode($, child, level + 1);
    if (childNode) node.children.push(childNode);
  });
  
  return node;
}

/**
 * Преобразует структуру узлов обратно в HTML
 * @param nodes Массив узлов структуры
 * @returns HTML-код
 */
export function generateHtmlFromStructure(nodes: StructureNode[]): string {
  if (!nodes || nodes.length === 0) return '';
  
  const $ = load('<body></body>');
  
  // Добавляем узлы в DOM
  nodes.forEach(node => {
    const element = createElementFromNode($, node);
    $('body').append(element);
  });
  
  // Возвращаем HTML без обертки body
  return $('body').html() || '';
}

/**
 * Создает HTML-элемент из узла структуры
 */
function createElementFromNode($: any, node: StructureNode): any {
  const element = $(`<${node.tagName}></${node.tagName}>`);
  
  // Добавляем атрибуты
  if (node.id && !node.id.startsWith('node_')) {
    element.attr('id', node.id);
  }
  
  if (node.className) {
    element.attr('class', node.className);
  }
  
  // Добавляем дочерние элементы
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      element.append(createElementFromNode($, child));
    });
  }
  
  return element;
}