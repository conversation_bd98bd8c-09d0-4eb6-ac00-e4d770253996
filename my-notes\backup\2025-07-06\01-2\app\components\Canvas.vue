<template>
  <div
    class="canvas-container"
    :style="{ width: canvasWidth }"
  >
    <div
      class="canvas"
      @drop="drop"
      @dragover.prevent="dragOver"
      @dragleave="dragLeave"
    >
      <div
        v-for="(group, index) in groups"
        :key="index"
        class="group"
        :class="{ 'drag-over': dragOverIndex === index }"
        draggable="true"
        @dragstart="dragStartGroup($event, index)"
        @dragover.prevent="dragOverGroup($event, index)"
        @drop.stop="dropGroup($event, index)"
      >
        <span class="group-text flex-1">
          <span class="template-title">{{ group.whbs?.title || 'Выберите шаблон' }}</span>
          <span class="separator"> + </span>
          <span class="json-title">{{ group.wjson?.title || 'Выберите данные' }}</span>
        </span>

        <div class="actions">
          <button v-if="index > 0" @click="moveUp(index)">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-up" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5"/>
            </svg>
          </button>
          <button v-if="index < groups.length - 1" @click="moveDown(index)">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-down" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1"/>
            </svg>
          </button>
          <button @click="removeGroup(index)">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-trash3" viewBox="0 0 16 16">
              <path d="M6.5 1h3a.5.5 0 0 1 .5.5v1H6v-1a.5.5 0 0 1 .5-.5M11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3A1.5 1.5 0 0 0 5 1.5v1H1.5a.5.5 0 0 0 0 1h.538l.853 10.66A2 2 0 0 0 4.885 16h6.23a2 2 0 0 0 1.994-1.84l.853-10.66h.538a.5.5 0 0 0 0-1zm1.958 1-.846 10.58a1 1 0 0 1-.997.92h-6.23a1 1 0 0 1-.997-.92L3.042 3.5zm-7.487 1a.5.5 0 0 1 .528.47l.5 8.5a.5.5 0 0 1-.998.06L5 5.03a.5.5 0 0 1 .47-.53Zm5.058 0a.5.5 0 0 1 .47.53l-.5 8.5a.5.5 0 1 1-.998-.06l.5-8.5a.5.5 0 0 1 .528-.47M8 4.5a.5.5 0 0 1 .5.5v8.5a.5.5 0 0 1-1 0V5a.5.5 0 0 1 .5-.5"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineExpose, computed, watch } from 'vue';

// Переименовываем компонент для исправления linter ошибки
defineOptions({
  name: 'CanvasEditor'
});

const groups = ref([]);
const dragOverIndex = ref(null);
const draggedGroupIndex = ref(null);

// Принимаем ширину canvas как prop
const props = defineProps({
  canvasWidth: {
    type: String,
    default: '60%'
  }
});

// Следим за изменением ширины canvas
watch(() => props.canvasWidth, (newWidth) => {
  console.log('Canvas width changed:', newWidth);
});

// Метод для добавления секций
const addGroup = (whbs, wjson) => {
  groups.value.push({ whbs, wjson });
  console.log('Добавлена группа:', groups.value);
};

// Обработка добавления элементов
const handleAddToCanvas = ({ type, data }) => {
  console.log('Добавление на холст:', type, data);

  if (type === 'whbs') {
    // Добавляем шаблон
    groups.value.push({ whbs: data });
  } else if (type === 'wjson') {
    // Находим последнюю группу без данных
    const lastGroup = groups.value[groups.value.length - 1];
    if (lastGroup && !lastGroup.wjson) {
      lastGroup.wjson = data;
    } else {
      // Если нет группы без данных, создаем новую группу только с данными
      groups.value.push({ wjson: data });
    }
  }
};

// Экспортируем методы
defineExpose({
  groups: computed(() => groups.value),
  addGroup,
  handleAddToCanvas
});

const dragOver = (event) => {
  event.preventDefault();
  const target = event.target.closest('.group');
  if (target) {
    const index = Array.from(target.parentNode.children).indexOf(target);
    dragOverIndex.value = index;
  }
};

const dragLeave = (event) => {
  if (!event.target.closest('.group')) {
    dragOverIndex.value = null;
  }
};

const drop = (event) => {
  event.preventDefault();
  console.log('Drop на холст');

  try {
    const whbsData = event.dataTransfer.getData('whbs');
    const wjsonData = event.dataTransfer.getData('wjson');

    console.log('Drop данные:', { whbsData, wjsonData });

    if (whbsData) {
      const whbs = JSON.parse(whbsData);
      groups.value.push({ whbs, wjson: null });
      console.log('Добавлен шаблон:', whbs);
    } else if (wjsonData) {
      const wjson = JSON.parse(wjsonData);
      const lastGroup = groups.value[groups.value.length - 1];
      if (lastGroup && !lastGroup.wjson) {
        lastGroup.wjson = wjson;
        console.log('Добавлены данные к последней группе:', wjson);
      } else {
        // Если нет группы без данных, создаем новую группу только с данными
        groups.value.push({ wjson });
        console.log('Создана новая группа с данными:', wjson);
      }
    }
  } catch (error) {
    console.error('Ошибка при обработке drop:', error);
  }

  dragOverIndex.value = null;
};

const removeGroup = (index) => groups.value.splice(index, 1);
const moveUp = (index) => [groups.value[index - 1], groups.value[index]] = [groups.value[index], groups.value[index - 1]];
const moveDown = (index) => [groups.value[index], groups.value[index + 1]] = [groups.value[index + 1], groups.value[index]];

// Обработчики для перетаскивания групп
const dragStartGroup = (event, index) => {
  draggedGroupIndex.value = index;
  event.dataTransfer.effectAllowed = 'move';
  console.log('Начало перетаскивания группы:', index);
};

const dragOverGroup = (event, index) => {
  event.preventDefault();
  if (draggedGroupIndex.value !== null && draggedGroupIndex.value !== index) {
    dragOverIndex.value = index;
  }
};

const dropGroup = (event, index) => {
  event.preventDefault();
  event.stopPropagation();
  console.log('Drop группы:', { from: draggedGroupIndex.value, to: index });

  if (draggedGroupIndex.value !== null && draggedGroupIndex.value !== index) {
    // Перемещаем группу
    const item = groups.value[draggedGroupIndex.value];
    groups.value.splice(draggedGroupIndex.value, 1);
    groups.value.splice(index, 0, item);
  }

  draggedGroupIndex.value = null;
  dragOverIndex.value = null;
};

const emit = defineEmits(['update:groups']);

// Следим за изменениями в группах и эмитим событие
watch(groups, (newGroups) => {
  console.log('Группы изменились, эмитим событие:', newGroups);
  emit('update:groups', newGroups);
}, { deep: true });
</script>

<style scoped>
.canvas-container {
  transition: width 0.3s ease;
  height: 100%;
  overflow: hidden;
  position: relative;
  /* Убираем возможность resize */
  resize: none;
}

.canvas {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 2px;
  border: 2px dashed transparent;
  overflow-y: auto;
  /* Убираем возможность resize */
  resize: none;
  position: relative;
}

.canvas.drag-over {
  border-color: #666;
}

.group {
  background: #f5f5f5;
  margin: 1px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px 6px;
  border: 1px solid #ddd;
  border-radius: 3px;
  transition: all 0.2s ease;
  cursor: grab;
  width: 100%;
  /* Убираем возможность resize */
  resize: none;
  position: relative;
}

.group:active {
  cursor: grabbing;
}

.group.drag-over {
  border-color: #666;
  background: #eee;
}

.group-text {
  font-size: 9px;
  line-height: 1.1;
  display: flex;
  align-items: center;
  gap: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-title {
  color: #333;
}

.json-title {
  color: #1e40af;
  font-weight: 500;
}

.separator {
  color: #666;
  font-size: 8px;
}

.actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.actions button {
  padding: 4px;
  border-radius: 4px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.actions button:hover {
  background: #ddd;
}

.actions button:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Убираем все возможные resize handles */
* {
  resize: none !important;
}

/* Предотвращаем появление resize курсоров */
.canvas-container *,
.canvas *,
.group * {
  resize: none !important;
  cursor: inherit;
}

.canvas-container:hover,
.canvas:hover {
  cursor: default;
}
</style>


