<template>
  <div v-if="visible" class="bulk-form-container mb-1 border border-gray-200 rounded bg-gray-50 relative">
    <!-- Плавающий заголовок и кнопки -->
    <div class="floating-header">
      <span class="header-text">
        {{ mode === 'create' ? 'Массовое добавление' : 'Массовое редактирование' }}
        ({{ forms.length }})
      </span>
      <div class="header-buttons">
        <Button
          v-if="mode === 'create'"
          icon="pi pi-plus"
          class="micro-btn"
          v-tooltip.bottom="'Добавить форму'"
          @click="addForm"
        />
        <Button
          icon="pi pi-check-circle"
          class="micro-btn save-all-btn"
          v-tooltip.bottom="'Сохранить все'"
          @click="saveAllForms"
        />
        <Button
          icon="pi pi-times"
          class="micro-btn"
          v-tooltip.bottom="'Закрыть'"
          @click="$emit('close')"
        />
      </div>
    </div>

    <div class="bulk-forms-scroll">
      <div class="bulk-forms-container">
        <div
          v-for="(form, index) in forms"
          :key="form.id || index"
          class="bulk-form-card"
        >
          <!-- Плавающие кнопки формы -->
          <div class="form-floating-controls">
            <span class="form-number">{{ index + 1 }}</span>
            <div class="form-buttons">
              <Button
                icon="pi pi-check"
                class="micro-btn save-btn"
                
                :loading="form._saving"
                @click="saveForm(form, index)"
              />
              <Button
                v-if="mode === 'create' && forms.length > 1"
                icon="pi pi-trash"
                class="micro-btn delete-btn"
                v-tooltip.bottom="'Удалить'"
                @click="removeForm(index)"
              />
            </div>
          </div>

          <div class="form-content">
            <!-- Базовые поля (кроме directus_files и wjson - там они в конфигурации) -->
            <template v-if="collection !== 'directus_files' && collection !== 'wjson'">
              <div class="field-row">
                <div class="field-quarter">
                  <InputText
                    v-model="form.number"
                    placeholder="№*"
                    class="micro-input"
                  />
                </div>
                <div class="field-three-quarters">
                  <InputText
                    v-model="form.title"
                    placeholder="Название*"
                    class="micro-input"
                  />
                </div>
              </div>

              <div class="field-full">
                <Textarea
                  v-model="form.description"
                  placeholder="Описание"
                  rows="1"
                  class="micro-textarea"
                />
              </div>
            </template>

            <!-- Базовые поля для wjson (только title обязательное) -->
            <template v-if="collection === 'wjson'">
              <div class="field-full">
                <InputText
                  v-model="form.title"
                  placeholder="Название*"
                  class="micro-input"
                />
              </div>
            </template>

            <!-- Динамические поля на основе конфигурации -->
            <template v-for="field in fieldConfig" :key="field.name">
              <!-- Специальная обработка для поля file (sketch) -->
              <div v-if="field.type === 'file'" class="field-full">
                <div class="flex gap-2 items-start">
                  <Image
                    v-if="form[field.name]"
                    :src="`http://localhost:8055/assets/${form[field.name]}`"
                    alt="Эскиз"
                    width="80"
                    class="border border-gray-200 rounded"
                    preview
                  />
                  <FileUpload
                    mode="basic"
                    :auto="false"
                    accept="*/*"
                    :max-file-size="10000000"
                    :choose-label="field.placeholder"
                    class="p-button-sm text-xs compact-file-upload"
                    @select="(event) => onFileSelectForField(event, form, field.name)"
                  />
                </div>
              </div>
              <!-- Специальная обработка для поля chips -->
              <Chips
                v-else-if="field.type === 'chips'"
                v-model="form[field.name]"
                :placeholder="field.placeholder"
                :class="getFieldClass(field)"
                separator=","
              />
              <!-- Обычные поля -->
              <component
                v-else
                :is="getFieldComponent(field)"
                v-model="form[field.name]"
                v-bind="getFieldProps(field)"
                :class="getFieldClass(field)"
              />
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import 'vue-prism-editor/dist/prismeditor.min.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/themes/prism-tomorrow.css'
import { ref, computed, watch } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useDirectusItems } from '#imports'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import MultiSelect from 'primevue/multiselect'
import Chips from 'primevue/chips'
import FileUpload from 'primevue/fileupload'
import Image from 'primevue/image'
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'

interface BulkFormField {
  name: string
  type: 'text' | 'textarea' | 'multiselect' | 'file' | 'prism' | 'chips'
  placeholder?: string
  options?: any[]
  loadOptions?: () => Promise<any[]>
  props?: Record<string, any>
  class?: string
}

interface BulkFormData {
  id?: string
  number: string
  title: string
  description: string
  _saving?: boolean
  [key: string]: any
}

interface Props {
  visible: boolean
  mode: 'create' | 'edit'
  collection: string
  fieldConfig: BulkFormField[]
  selectedItems?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  selectedItems: () => []
})

const emit = defineEmits<{
  close: []
  saved: [items: any[]]
}>()

const toast = useToast()
const { createItems, updateItem } = useDirectusItems()

const forms = ref<BulkFormData[]>([])

// Функции подсветки синтаксиса (как в wpages.vue)
const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

// Универсальная функция подсветки для разных типов кода
const getHighlightFunction = (fieldName: string) => {
  if (fieldName === 'html' || fieldName === 'hbs') {
    return highlightHtml
  } else if (fieldName === 'css') {
    return highlightCss
  } else if (fieldName === 'js') {
    return highlightJs
  } else if (fieldName === 'json') {
    return highlightJs // JSON использует JavaScript подсветку
  }
  return highlightHtml // По умолчанию HTML
}

// Инициализация форм
const initializeForms = () => {
  if (props.mode === 'create') {
    forms.value = [createEmptyForm()]
  } else {
    forms.value = props.selectedItems.map(item => ({
      ...item,
      _saving: false
    }))
  }
}

const createEmptyForm = (): BulkFormData => {
  const form: BulkFormData = {
    number: '',
    title: '',
    description: '',
    _saving: false
  }

  // Для wjson добавляем поле art вместо number
  if (props.collection === 'wjson') {
    form.art = ''
    delete form.number
  }

  // Инициализируем поля из конфигурации
  props.fieldConfig.forEach(field => {
    if (field.type === 'multiselect' || field.type === 'chips') {
      form[field.name] = []
    } else {
      form[field.name] = ''
    }
  })

  return form
}

const addForm = () => {
  forms.value.push(createEmptyForm())
}

const removeForm = (index: number) => {
  forms.value.splice(index, 1)
}

const onFileSelect = (event: any, form: BulkFormData) => {
  const file = event.files[0]
  if (file) {
    form._uploadedFile = file
    // Автоматически заполняем название файла если оно пустое
    if (!form.title) {
      form.title = file.name.replace(/\.[^/.]+$/, '') // Убираем расширение
    }
  }
}

// Функция для обработки загрузки файлов для sketch поля (как в wpages.vue)
const onFileSelectForField = async (event: any, form: BulkFormData, fieldName: string) => {
  const file = event.files[0]
  if (file) {
    try {
      // Используем прямой fetch к Directus API как в wpages.vue
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('http://localhost:8055/files', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Ошибка загрузки файла')
      }

      const data = await response.json()
      // Сохраняем ID файла в поле
      form[fieldName] = data.data.id

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Файл загружен`,
        life: 3000
      })
    } catch (error) {
      console.error('Ошибка загрузки файла:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: `Не удалось загрузить файл: ${error.message}`,
        life: 5000
      })
    }
  }
}

// Функция массового сохранения всех форм
const saveAllForms = async () => {
  let validForms

  if (props.collection === 'wjson') {
    validForms = forms.value.filter(form => form.title && !form._saving)
  } else {
    validForms = forms.value.filter(form => form.number && form.title && !form._saving)
  }

  if (validForms.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Нет валидных форм для сохранения',
      life: 3000
    })
    return
  }

  let successCount = 0
  let errorCount = 0

  for (let i = 0; i < forms.value.length; i++) {
    const form = forms.value[i]
    const isValid = props.collection === 'wjson'
      ? (form.title && !form._saving)
      : (form.number && form.title && !form._saving)

    if (isValid) {
      try {
        await saveForm(form, i)
        successCount++
      } catch (error) {
        errorCount++
      }
    }
  }

  toast.add({
    severity: successCount > 0 ? 'success' : 'error',
    summary: 'Массовое сохранение',
    detail: `Успешно: ${successCount}, Ошибок: ${errorCount}`,
    life: 5000
  })
}

const getFieldComponent = (field: BulkFormField) => {
  switch (field.type) {
    case 'textarea':
      return Textarea
    case 'multiselect':
      return MultiSelect
    case 'chips':
      return Chips
    case 'file':
      return FileUpload
    case 'prism':
      return PrismEditorWithCopy
    default:
      return InputText
  }
}

const getFieldProps = (field: BulkFormField) => {
  const baseProps = {
    placeholder: field.placeholder || field.name,
    ...field.props
  }
  
  if (field.type === 'multiselect') {
    return {
      ...baseProps,
      options: field.options || [],
      display: 'chip',
      class: 'text-xs w-full',
      panelClass: 'text-xs'
    }
  }

  if (field.type === 'chips') {
    return {
      ...baseProps,
      separator: ',',
      class: 'text-xs w-full'
    }
  }
  
  if (field.type === 'file') {
    return {
      ...baseProps,
      mode: 'basic',
      auto: false,
      accept: '*/*',
      maxFileSize: 10000000,
      chooseLabel: field.placeholder || 'Выбрать файл'
    }
  }
  
  if (field.type === 'prism') {
    return {
      ...baseProps,
      editorClass: 'my-editor text-xs',
      maxHeight: '80px',
      highlight: getHighlightFunction(field.name) // Правильная функция подсветки
    }
  }
  
  return baseProps
}

const getFieldClass = (field: BulkFormField) => {
  if (field.class) return field.class

  switch (field.type) {
    case 'textarea':
      return 'compact-textarea'
    case 'multiselect':
      return 'compact-multiselect'
    case 'chips':
      return 'compact-chips'
    case 'prism':
      return 'compact-prism'
    default:
      return 'compact-input'
  }
}

// Функция очистки данных для разных коллекций (как в wpages.vue)
const cleanDataForCollection = (data: any, collection: string) => {
  if (collection === 'wpage') {
    return {
      ...data,
      // Убираем пустые массивы и null значения
      wpage_type: data.wpage_type?.length ? data.wpage_type : null,
      tags: data.tags?.length ? data.tags : null,
      sketch: data.sketch || null,
      html: data.html || '',
      css: data.css || '',
      js: data.js || '',
      description: data.description || null
    }
  } else if (collection === 'welem_proto') {
    return {
      ...data,
      elem_type: data.elem_type?.length ? data.elem_type : null,
      collection: data.collection?.length ? data.collection : null,
      sketch: data.sketch || null,
      html: data.html || '',
      css: data.css || '',
      js: data.js || '',
      hbs: data.hbs || '',
      json: data.json || '',
      description: data.description || null,
      composition: data.composition || null
    }
  } else if (collection === 'wblock_proto') {
    return {
      ...data,
      block_type: data.block_type?.length ? data.block_type : null,
      layout: data.layout?.length ? data.layout : null,
      style: data.style?.length ? data.style : null,
      elements: data.elements?.length ? data.elements : null,
      graphics: data.graphics?.length ? data.graphics : null,
      collection: data.collection?.length ? data.collection : null,
      concept: data.concept?.length ? data.concept : null,
      features: data.features?.length ? data.features : null,
      sketch: data.sketch || null,
      html: data.html || '',
      css: data.css || '',
      js: data.js || '',
      hbs: data.hbs || '',
      json: data.json || '',
      description: data.description || null,
      composition: data.composition || null,
      notes: data.notes || null,
      status: data.status || 'idea'
    }
  } else if (collection === 'directus_files') {
    return {
      ...data,
      // Для directus_files специальная обработка
      tags: data.tags?.length ? data.tags : null,
      description: data.description || null,
      folder: '974200ea-bd51-4320-875f-b49089200404' // Папка по умолчанию files2
    }
  } else if (collection === 'wjson') {
    return {
      ...data,
      // Для wjson специальная обработка
      art: data.art || '',
      title: data.title || '',
      description: data.description || null,
      tags: data.tags?.length ? data.tags : null,
      json: data.json || ''
    }
  }

  // Для других коллекций возвращаем как есть
  return data
}

const saveForm = async (form: BulkFormData, index: number) => {
  // Разная валидация для разных коллекций
  if (props.collection === 'wjson') {
    if (!form.title) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Заполните обязательное поле: Название',
        life: 3000
      })
      return
    }
  } else {
    if (!form.number || !form.title) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Заполните обязательные поля: Номер и Название',
        life: 3000
      })
      return
    }
  }

  form._saving = true

  try {
    const { _saving, _uploadedFile, id, ...saveData } = form

    // Специальная обработка для directus_files
    if (props.collection === 'directus_files') {
      // Для directus_files только редактирование метаданных
      await handleFileMetadataUpdate(form, saveData, index)
    } else {
      // Очищаем данные как в wpages.vue
      const cleanedData = cleanDataForCollection(saveData, props.collection)

      // Обычная обработка для других коллекций
      if (props.mode === 'create') {
        const result = await createItems({
          collection: props.collection,
          items: [cleanedData]
        })

        toast.add({
          severity: 'success',
          summary: 'Успешно',
          detail: `Запись создана`,
          life: 3000
        })

        // Заменяем форму на созданную запись для возможности дальнейшего редактирования
        if (Array.isArray(result) && result.length > 0) {
          forms.value[index] = { ...result[0], _saving: false }
        }
      } else {
        await updateItem({
          collection: props.collection,
          id: form.id!,
          item: cleanedData
        })

        toast.add({
          severity: 'success',
          summary: 'Успешно',
          detail: `Запись обновлена`,
          life: 3000
        })
      }
    }

    emit('saved', forms.value.filter(f => !f._saving))
  } catch (error) {
    console.error('Ошибка сохранения:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: `Не удалось сохранить запись: ${error.message}`,
      life: 5000
    })
  } finally {
    form._saving = false
  }
}

// Функция для обновления метаданных файла (точно как в files2.vue)
const handleFileMetadataUpdate = async (form: BulkFormData, saveData: any, index: number) => {
  try {
    // Используем прямой fetch к Directus API как в files2.vue saveFile()
    const response = await fetch(`http://localhost:8055/files/${form.id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        number: saveData.number,
        title: saveData.title,
        description: saveData.description,
        tags: saveData.tags
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.errors?.[0]?.message || `HTTP error! status: ${response.status}`)
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Файл успешно обновлен`,
      life: 3000
    })

    // Обновляем форму с новыми данными (как в files2.vue)
    forms.value[index] = {
      ...forms.value[index],
      number: saveData.number,
      title: saveData.title,
      description: saveData.description,
      tags: saveData.tags,
      _saving: false
    }
  } catch (error) {
    throw new Error(`Не удалось обновить файл: ${error.message}`)
  }
}

// Специальная функция для обработки загрузки файлов
const handleFileUpload = async (form: BulkFormData, saveData: any, index: number) => {
  if (form._uploadedFile) {
    // Загружаем файл через прямой fetch как в wpages.vue
    const formData = new FormData()
    formData.append('file', form._uploadedFile)

    const response = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error('Ошибка загрузки файла')
    }

    const data = await response.json()
    const uploadedFile = data.data

    // Обновляем загруженный файл с дополнительными данными и папкой
    await updateItem({
      collection: 'directus_files',
      id: uploadedFile.id,
      item: {
        title: saveData.title,
        description: saveData.description,
        tags: saveData.tags,
        folder: '974200ea-bd51-4320-875f-b49089200404' // Папка files2
      }
    })

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Файл загружен и обновлен`,
      life: 3000
    })

    // Заменяем форму на созданную запись
    forms.value[index] = { ...uploadedFile, ...saveData, _saving: false }
  } else {
    throw new Error('Необходимо загрузить файл')
  }
}

// Инициализация при изменении видимости или режима
watch([() => props.visible, () => props.mode, () => props.selectedItems], () => {
  if (props.visible) {
    initializeForms()
  }
}, { immediate: true })
</script>

<style scoped>
.bulk-form-container {
  max-height: 400px;
  padding: 0;
  position: relative;
  z-index: 1000;
}

/* Плавающий заголовок */
.floating-header {
  position: absolute;
  top: -2px;
  left: 8px;
  right: 8px;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(249, 250, 251, 0.95);
  backdrop-filter: blur(4px);
  border-radius: 4px;
  padding: 2px 6px;
  border: 1px solid #e5e7eb;
}

.header-text {
  font-size: 9px;
  font-weight: 600;
  color: #374151;
}

.header-buttons {
  display: flex;
  gap: 2px;
}

.bulk-forms-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 20px 4px 4px 4px;
}

.bulk-forms-container {
  display: flex;
  gap: 8px;
  min-height: 250px;
  align-items: flex-start;
}

.bulk-form-card {
  flex: 0 0 380px; /* Уменьшено на 35% */
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  max-height: 350px;
  overflow-y: auto;
}

/* Скроллбар для каждой формы */
.bulk-form-card::-webkit-scrollbar {
  width: 6px;
}

.bulk-form-card::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
}

.bulk-form-card::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.bulk-form-card::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Плавающие кнопки формы */
.form-floating-controls {
  position: absolute;
  top: 2px;
  right: 2px;
  z-index: 5;
  display: flex;
  align-items: center;
  gap: 2px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  border-radius: 3px;
  padding: 1px 3px;
  border: 1px solid #f3f4f6;
}

.form-number {
  font-weight: 600;
  font-size: 9px;
  color: #6b7280;
  margin-right: 2px;
}

.form-buttons {
  display: flex;
  gap: 1px;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-top: 16px;
}

.field-row {
  display: flex;
  gap: 4px;
}

.field-quarter {
  flex: 0 0 25%;
}

.field-three-quarters {
  flex: 1;
}

.field-full {
  width: 100%;
}

/* Микро стили для полей */
.micro-input {
  padding: 2px 4px !important;
  font-size: 9px !important;
  height: 20px !important;
  line-height: 1.2 !important;
}

.micro-textarea {
  padding: 2px 4px !important;
  font-size: 9px !important;
  line-height: 1.2 !important;
  min-height: 20px !important;
}

.compact-multiselect {
  font-size: 9px !important;
}

.compact-multiselect :deep(.p-multiselect-label) {
  padding: 2px 4px !important;
  font-size: 9px !important;
  min-height: 18px !important;
}

.compact-multiselect :deep(.p-multiselect-token) {
  padding: 0px 2px !important;
  font-size: 8px !important;
  margin: 1px !important;
}

.compact-chips {
  font-size: 9px !important;
}

.compact-chips :deep(.p-chips-input-token) {
  padding: 2px 4px !important;
  font-size: 9px !important;
  min-height: 18px !important;
}

.compact-chips :deep(.p-chips-token) {
  padding: 0px 2px !important;
  font-size: 8px !important;
  margin: 1px !important;
}

.compact-prism {
  font-size: 8px !important;
}

/* Стили для PrismEditor как в wpages.vue */
.my-editor :deep(.prism-editor__textarea),
.my-editor :deep(.prism-editor__editor) {
  font-size: 9px !important;
  line-height: 1.2 !important;
  padding: 4px !important;
  min-height: 60px !important;
  max-height: 120px !important;
  overflow-y: auto !important;
  font-family: 'Courier New', Courier, monospace !important;
}

.my-editor :deep(.prism-editor__container) {
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background: #f9fafb !important;
}

.compact-prism :deep(.prism-editor__textarea) {
  font-size: 8px !important;
  padding: 2px !important;
  min-height: 60px !important;
  max-height: 120px !important;
  overflow-y: auto !important;
}

.compact-file-upload {
  font-size: 9px !important;
}

.compact-file-upload :deep(.p-fileupload-choose) {
  padding: 2px 4px !important;
  font-size: 9px !important;
  height: 20px !important;
}

/* Микро кнопки */
.micro-btn {
  padding: 1px 3px !important;
  font-size: 8px !important;
  height: 16px !important;
  width: 16px !important;
  min-width: 16px !important;
}

.micro-btn :deep(.p-button-icon) {
  font-size: 8px !important;
}

.save-btn {
  background: #10b981 !important;
  border-color: #10b981 !important;
}

.delete-btn {
  background: #ef4444 !important;
  border-color: #ef4444 !important;
}

.save-all-btn {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

/* Скроллбар */
.bulk-forms-scroll::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.bulk-forms-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.bulk-forms-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.bulk-forms-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.bulk-forms-scroll::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Принудительно скрываем застрявшие tooltips */
.p-tooltip {
  z-index: -1 !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Альтернативный способ - скрываем все tooltips в bulk формах */
.bulk-form-container .p-tooltip,
.bulk-form-container [data-pc-name="tooltip"] {
  display: none !important;
}
</style>
