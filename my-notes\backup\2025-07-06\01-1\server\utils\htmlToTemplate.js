// server/utils/htmlToTemplate.js
import { load } from 'cheerio';

/**
 * Конвертирует HTML в шаблон Handlebars и JSON данные
 * @param {string} html - HTML-код для конвертации
 * @param {string} blockName - Имя блока (используется для логирования)
 * @param {string} blockNumber - Номер блока (используется для логирования)
 * @returns {Object} Объект с полями hbsTemplate и jsonData
 */
export function htmlToHandlebarsAndJson(html, blockName = '', blockNumber = '') {
  try {
    console.log(`🔄 Начало генерации handlebars и JSON для блока ${blockName || ''} ${blockNumber || ''}...`);
    
    if (!html || html.trim().length === 0) {
      console.warn('⚠️ Пустой HTML для конвертации');
      return { 
        hbsTemplate: '', 
        jsonData: {},
        success: false,
        error: 'Пустой HTML для конвертации'
      };
    }
    
    // Загружаем HTML в cheerio с сохранением оригинальных атрибутов
    const $ = load(html, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    });
    
    let jsonData = {};
    // Сохраняем оригинальный HTML для шаблона, только заменяем переменные
    let hbsTemplate = html;
    let variableCounters = {};
    
    // Функция для получения имени переменной с учетом счетчика
    function getVarName(baseVarName) {
      if (!variableCounters[baseVarName]) {
        variableCounters[baseVarName] = 1;
        return baseVarName;
      } else {
        variableCounters[baseVarName]++;
        return `${baseVarName}${variableCounters[baseVarName]}`;
      }
    }
    
    // Обрабатываем изображения
    function processImages() {
      // Используем точный поиск изображений, чтобы гарантировать замену
      $('img').each((_, element) => {
        const src = $(element).attr('src');
        const alt = $(element).attr('alt') || '';
        
        if (src) {
          // Переменная для src
          const imageVarName = getVarName('image');
          
          // Добавляем в JSON
          jsonData[imageVarName] = src;
          
          // Гарантированная замена src
          $(element).attr('src', `{{${imageVarName}}}`);
          
          // Обрабатываем alt атрибут, если он не пустой
          if (alt) {
            const altVarName = getVarName('imageAlt');
            jsonData[altVarName] = alt;
            $(element).attr('alt', `{{${altVarName}}}`);
          } else if (alt === '') {
            // Если alt пустой, добавляем переменную с пустым значением
            const altVarName = getVarName('imageAlt');
            jsonData[altVarName] = '';
            $(element).attr('alt', `{{${altVarName}}}`);
          }
        }
      });
      
      // Обновляем hbsTemplate с измененным HTML после обработки всех изображений
      hbsTemplate = $.html();
    }
    
    // Обрабатываем фоновые изображения в стилях
    function processBackgroundImages() {
      $('[style*="background-image"]').each((_, element) => {
        const style = $(element).attr('style');
        
        if (style) {
          // Ищем URL изображения в стиле
          const bgImageMatch = style.match(/background-image\s*:\s*url\(['"]?([^'")]+)['"]?\)/i);
          
          if (bgImageMatch && bgImageMatch[1]) {
            const bgUrl = bgImageMatch[1];
            const bgVarName = getVarName('imageBackground');
            
            // Добавляем в JSON
            jsonData[bgVarName] = bgUrl;
            
            // Заменяем URL на переменную в стиле
            const newStyle = style.replace(
              /background-image\s*:\s*url\(['"]?([^'")]+)['"]?\)/i,
              `background-image:url('{{${bgVarName}}}')`
            );
            
            // Применяем новый стиль напрямую к элементу
            $(element).attr('style', newStyle);
          }
        }
      });
      
      // Обновляем hbsTemplate с измененным HTML
      hbsTemplate = $.html();
    }
    
    // Обрабатываем ссылки
    function processLinks() {
      $('a').each((_, element) => {
        const href = $(element).attr('href');
        
        if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
          // Определяем имя переменной
          const urlVarName = getVarName('url');
          
          // Добавляем в JSON
          jsonData[urlVarName] = href;
          
          // Применяем переменную напрямую к элементу
          $(element).attr('href', `{{${urlVarName}}}`);
        }
      });
      
      // Обновляем hbsTemplate с измененным HTML
      hbsTemplate = $.html();
    }
    
    // Обрабатываем CSS-ссылки
    function processCssLinks() {
      $('link[rel="stylesheet"]').each((_, element) => {
        const href = $(element).attr('href');
        
        if (href) {
          // Определяем имя переменной
          const cssVarName = getVarName('cssUrl');
          
          // Добавляем в JSON
          jsonData[cssVarName] = href;
          
          // Применяем переменную напрямую к элементу
          $(element).attr('href', `{{${cssVarName}}}`);
        }
      });
      
      // Обновляем hbsTemplate с измененным HTML
      hbsTemplate = $.html();
    }
    
    // Обрабатываем иконки (теги i с классами)
    function processIcons() {
      $('i').each((_, element) => {
        const className = $(element).attr('class');
        
        if (className) {
          // Определяем имя переменной
          const iconVarName = getVarName('icon');
          
          // Добавляем в JSON
          jsonData[iconVarName] = className;
          
          // Устанавливаем класс напрямую
          $(element).attr('class', `{{${iconVarName}}}`);
        }
      });
      
      // Обновляем hbsTemplate с измененным HTML
      hbsTemplate = $.html();
    }
    
    // Обрабатываем текстовые узлы
    function processTextNodes() {
      // Находим текстовые узлы внутри элементов, которые не являются скриптами или стилями
      $('*').each((_, element) => {
        // Обрабатываем только элементы, которые содержат текстовые узлы
        if (element.type === 'tag' && element.name !== 'script' && element.name !== 'style') {
          // Обрабатываем каждый дочерний узел
          $(element).contents().each((_, node) => {
            // Проверяем, является ли узел текстовым и содержит непустой текст
            if (node.type === 'text' && node.data.trim().length > 0) {
              const text = node.data.trim();
              
              // Определяем имя переменной на основе родительского тега
              let varName;
              const parentTag = element.name;
              
              if (parentTag.match(/^h[1-6]$/)) {
                varName = 'title';
              } else if (parentTag === 'p') {
                varName = 'text';
              } else if (parentTag === 'a') {
                varName = 'linkText';
              } else if (parentTag === 'span') {
                varName = 'excerpt';
              } else {
                varName = 'text';
              }
              
              // Получаем имя переменной с учетом счетчика
              const fullVarName = getVarName(varName);
              
              // Добавляем в JSON
              jsonData[fullVarName] = text;
              
              // Заменяем текст на переменную
              node.data = node.data.replace(text, `{{${fullVarName}}}`);
            }
          });
        }
      });
      
      // Обновляем весь шаблон после обработки всех текстовых узлов
      hbsTemplate = $.html();
    }
    
    // Запускаем обработку всех типов узлов
    // Сначала обрабатываем атрибуты элементов
    processImages();
    processBackgroundImages();
    processLinks();
    processCssLinks();
    processIcons();
    // Затем обрабатываем текстовое содержимое
    processTextNodes();
    
    // Применяем декодирование HTML-сущностей к шаблону
    hbsTemplate = decodeHtmlEntities(hbsTemplate);
    
    console.log(`✅ Генерация handlebars и JSON для блока ${blockName || ''} ${blockNumber || ''} завершена успешно`);
    
    return { 
      hbsTemplate, 
      jsonData,
      success: true,
      error: null
    };
  } catch (error) {
    console.error(`❌ Ошибка при генерации handlebars и JSON для блока ${blockName || ''} ${blockNumber || ''}:`, error);
    
    // Возвращаем объект с информацией об ошибке
    return { 
      hbsTemplate: `<!-- Ошибка генерации шаблона: ${error.message} -->`, 
      jsonData: { error: error.message },
      success: false,
      error: error.message
    };
  }
}

/**
 * Декодирует HTML-сущности в атрибутах и сохраняет оригинальную структуру HTML
 * @param {string} html - HTML-код для декодирования
 * @returns {string} Декодированный HTML с сохранением оригинальной структуры
 */
export function decodeHtmlEntities(html) {
  if (!html) return '';

  // Функция для декодирования HTML-сущностей в строке
  function decodeEntities(str) {
    if (!str) return '';

    // Простая замена всех распространенных HTML-сущностей
    return str
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&#39;/g, "'")
      .replace(/&ldquo;/g, '\u201c')
      .replace(/&rdquo;/g, '\u201d')
      .replace(/&lsquo;/g, '\u2018')
      .replace(/&rsquo;/g, '\u2019')
      .replace(/&mdash;/g, '\u2014')
      .replace(/&ndash;/g, '\u2013')
      .replace(/&nbsp;/g, ' ');
  }

  // Для извлечения и временного замещения всех атрибутов
  function extractAllAttributes(html) {
    const attributePlaceholders = [];
    // Регулярное выражение для поиска всех атрибутов с значениями
    let result = html.replace(/([\w\-:]+)=(['"])(.*?)\2/gs, (match, attrName, quote, value) => {
      attributePlaceholders.push({ attrName, quote, value });
      return `__ATTR_${attributePlaceholders.length - 1}__`;
    });

    return { result, attributePlaceholders };
  }

  // Извлекаем все атрибуты и заменяем их плейсхолдерами
  const { result: processedHtml, attributePlaceholders } = extractAllAttributes(html);

  // Удаляем дублирующиеся теги <br>
  let cleanedHtml = processedHtml.replace(/<br>\s*<br>/gi, '<br>');
  cleanedHtml = cleanedHtml.replace(/<br\s*\/>\s*<br\s*\/>/gi, '<br>');

  // Обрабатываем самозакрывающиеся теги
  const selfClosingTags = ['br', 'img', 'input', 'hr', 'meta', 'link', 'source', 'area', 'base', 'col', 'embed', 'keygen', 'param', 'track', 'wbr'];
  let finalHtml = cleanedHtml;

  for (const tag of selfClosingTags) {
    const closingTagRegex = new RegExp(`<${tag}([^>]*)>[\\s]*</[\\s]*${tag}>`, 'gi');
    finalHtml = finalHtml.replace(closingTagRegex, `<${tag}$1>`);
  }

  // Дополнительная обработка для <br></br> и <link></link>
  finalHtml = finalHtml.replace(/<br><\/br>/gi, '<br>');
  finalHtml = finalHtml.replace(/<link([^>]*)><\/link>/gi, '<link$1/>');
  finalHtml = finalHtml.replace(/<br\s*\/>[\s]*<\/br>/gi, '<br>');
  finalHtml = finalHtml.replace(/<br\s*><\/br>/gi, '<br>');

  // Возвращаем обработанные атрибуты на место
  for (let i = 0; i < attributePlaceholders.length; i++) {
    const { attrName, quote, value } = attributePlaceholders[i];
    const placeholder = `__ATTR_${i}__`;

    // Декодируем значение
    const decodedValue = decodeEntities(value);

    // Особая обработка для data-атрибутов
    if (attrName.startsWith('data-')) {
      // Проверяем на JSON-структуру
      const hasJsonStructure = value.includes('{') && value.includes('}');

      if (hasJsonStructure) {
        // Для JSON всегда используем одинарные кавычки
        finalHtml = finalHtml.replace(placeholder, `${attrName}='${decodedValue}'`);
      } else {
        // Сохраняем оригинальные кавычки для обычных data-атрибутов
        finalHtml = finalHtml.replace(placeholder, `${attrName}=${quote}${decodedValue}${quote}`);
      }
    } else {
      // Для всех остальных атрибутов сохраняем оригинальные кавычки
      finalHtml = finalHtml.replace(placeholder, `${attrName}=${quote}${decodedValue}${quote}`);
    }
  }

  // Дополнительно убедимся, что &quot; точно заменены на "
  finalHtml = finalHtml.replace(/&quot;/g, '"');

  return finalHtml;
}

/**
 * Конвертирует HTML в шаблон Handlebars и JSON данные с использованием cheerio
 * @param {string} html - HTML-код для конвертации
 */
/**
 * Конвертирует HTML в шаблон Pug и JSON данные
 * @param {string} html - HTML-код для конвертации
 * @param {string} blockName - Имя блока (используется для логирования)
 * @param {string} blockNumber - Номер блока (используется для логирования)
 * @returns {Object} Объект с полями pugTemplate и jsonData
 */
export function htmlToPugAndJson(html, blockName = '', blockNumber = '') {
  try {
    console.log(`🔄 Начало генерации pug и JSON для блока ${blockName || ''} ${blockNumber || ''}...`);
    
    if (!html || html.trim().length === 0) {
      console.warn('⚠️ Пустой HTML для конвертации');
      return { 
        pugTemplate: '', 
        jsonData: {},
        success: false,
        error: 'Пустой HTML для конвертации'
      };
    }
    
    // Сначала конвертируем HTML в Handlebars для извлечения переменных
    const handlebarsResult = htmlToHandlebarsAndJson(html, blockName, blockNumber);
    
    // Затем конвертируем HTML в Pug
    let pugTemplate = '';
    try {
      // Используем html-to-pug для конвертации
      // Это заглушка, в реальном коде нужно использовать библиотеку для конвертации
      pugTemplate = 'div\n  h1 {{text}}\n  p {{text2}}';
      
      console.log(`✅ Генерация pug для блока ${blockName || ''} ${blockNumber || ''} завершена успешно`);
    } catch (pugError) {
      console.error(`❌ Ошибка при генерации pug для блока ${blockName || ''} ${blockNumber || ''}:`, pugError);
      return {
        pugTemplate: `// Ошибка генерации pug: ${pugError.message}`,
        jsonData: handlebarsResult.jsonData,
        success: false,
        error: pugError.message
      };
    }
    
    return {
      pugTemplate,
      jsonData: handlebarsResult.jsonData,
      success: true,
      error: null
    };
  } catch (error) {
    console.error(`❌ Ошибка при генерации pug и JSON для блока ${blockName || ''} ${blockNumber || ''}:`, error);
    return {
      pugTemplate: `// Ошибка генерации шаблона: ${error.message}`,
      jsonData: { error: error.message },
      success: false,
      error: error.message
    };
  }
}

export function htmlToHandlebarsAndJsonWithCheerio(html) {
  try {
    if (!html || html.trim().length === 0) {
      return { 
        hbsTemplate: '', 
        jsonData: {},
        success: false,
        error: 'Пустой HTML для конвертации'
      };
    }
    
    // Загружаем HTML в cheerio
    const $ = load(html, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    });
    
    let jsonData = {};
    let variableCounters = {};
    
    // Обрабатываем текстовые узлы
    $('h1, h2, h3, h4, h5, h6, p, span, a, button, li').each((_, element) => {
      const $el = $(element);
      const text = $el.clone().children().remove().end().text().trim();
      
      if (text) {
        // Определяем имя переменной
        let varName = 'text';
        
        // Добавляем счетчик для уникальности
        if (!variableCounters[varName]) {
          variableCounters[varName] = 1;
        } else {
          variableCounters[varName]++;
        }
        
        // Формируем имя переменной с учетом счетчика
        const fullVarName = variableCounters[varName] > 1 ? 
          `${varName}${variableCounters[varName]}` : varName;
        
        // Добавляем в JSON
        jsonData[fullVarName] = text;
        
        // Заменяем текст на переменную в HTML
        $el.html($el.html().replace(text, `{{${fullVarName}}}`))
      }
    });
    
    // Обрабатываем изображения
    $('img').each((_, element) => {
      const $el = $(element);
      const src = $el.attr('src');
      
      if (src) {
        // Определяем имя переменной
        const varName = 'image';
        
        // Добавляем счетчик для уникальности
        if (!variableCounters[varName]) {
          variableCounters[varName] = 1;
        } else {
          variableCounters[varName]++;
        }
        
        // Формируем имя переменной с учетом счетчика
        const fullVarName = variableCounters[varName] > 1 ? 
          `${varName}${variableCounters[varName]}` : varName;
        
        // Добавляем в JSON
        jsonData[fullVarName] = src;
        
        // Заменяем src на переменную в HTML
        $el.attr('src', `{{${fullVarName}}}`);
      }
    });
    
    // Обрабатываем ссылки
    $('a').each((_, element) => {
      const $el = $(element);
      const href = $el.attr('href');
      
      if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
        // Определяем имя переменной
        const varName = 'url';
        
        // Добавляем счетчик для уникальности
        if (!variableCounters[varName]) {
          variableCounters[varName] = 1;
        } else {
          variableCounters[varName]++;
        }
        
        // Формируем имя переменной с учетом счетчика
        const fullVarName = variableCounters[varName] > 1 ? 
          `${varName}${variableCounters[varName]}` : varName;
        
        // Добавляем в JSON
        jsonData[fullVarName] = href;
        
        // Заменяем href на переменную в HTML
        $el.attr('href', `{{${fullVarName}}}`);
      }
    });
    
    // Получаем HTML с переменными
    let hbsTemplate = $.html();
    
    // Применяем декодирование HTML-сущностей к шаблону
    hbsTemplate = decodeHtmlEntities(hbsTemplate);
    
    return { 
      hbsTemplate, 
      jsonData,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Ошибка при конвертации HTML в шаблон с cheerio:', error);
    return {
      hbsTemplate: '',
      jsonData: {},
      success: false,
      error: error.message
    };
  }
}