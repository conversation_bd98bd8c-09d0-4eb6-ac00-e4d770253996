import { htmlToHandlebarsAndJson } from '../utils/htmlToTemplate.js'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { html } = body

    if (!html) {
      throw createError({
        statusCode: 400,
        statusMessage: 'HTML content is required'
      })
    }

    // Конвертируем HTML в HBS шаблон и JSON переменные
    const result = htmlToHandlebarsAndJson(html)

    return {
      success: result.success,
      hbs: result.hbsTemplate,
      variables: result.jsonData,
      error: result.error
    }
  } catch (error) {
    console.error('Ошибка в html-to-template API:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Internal server error'
    })
  }
})
