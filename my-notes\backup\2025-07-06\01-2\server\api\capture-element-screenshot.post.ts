import { defineEvent<PERSON><PERSON><PERSON>, readBody, createError, setHeader } from 'h3'
import puppeteer from 'puppeteer'

interface ElementScreenshotRequest {
  html: string
  selector: string
  width?: number
  height?: number
}

export default defineEventHandler(async (event) => {
  try {
    const { html, selector, width = 1400, height = 800 } = await readBody<ElementScreenshotRequest>(event)

    if (!html) {
      throw createError({
        statusCode: 400,
        statusMessage: 'HTML content is required'
      })
    }

    if (!selector) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Element selector is required'
      })
    }

    console.log(`🔄 Создание скриншота элемента по селектору: ${selector}`)

    // Запускаем браузер
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    })

    const page = await browser.newPage()

    try {
      // Устанавливаем размер viewport
      await page.setViewport({ width, height })
      console.log(`📐 Установлен viewport: ${width}x${height}`)

      // Загружаем HTML контент
      await page.setContent(html, {
        waitUntil: 'networkidle0',
        timeout: 30000
      })
      console.log('📄 HTML контент загружен')

      // Ждем дополнительное время для анимаций
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Ищем элемент по селектору
      const targetElement = await page.$(selector)

      if (!targetElement) {
        throw new Error(`Element not found by selector: ${selector}`)
      }

      // Получаем размеры элемента
      const boundingBox = await targetElement.boundingBox()

      if (!boundingBox) {
        throw new Error(`Could not get element dimensions for selector: ${selector}`)
      }

      console.log(`📏 Размеры элемента: ${boundingBox.width}x${boundingBox.height}px (x:${boundingBox.x}, y:${boundingBox.y})`)

      // Создаем скриншот конкретного элемента
      const screenshotBuffer = await targetElement.screenshot({
        type: 'png',
        omitBackground: false
      })

      console.log(`📸 Скриншот элемента создан (${screenshotBuffer.length} bytes)`)

      // Возвращаем скриншот как blob
      setHeader(event, 'Content-Type', 'image/png')
      setHeader(event, 'Content-Length', screenshotBuffer.length.toString())

      return screenshotBuffer

    } finally {
      // Закрываем браузер
      await browser.close()
      console.log('🔒 Браузер закрыт')
    }

  } catch (error) {
    console.error('❌ Ошибка при создании скриншота элемента:', error)
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to capture element screenshot'
    })
  }
})
