<template>
  <div class="flex flex-col gap-4 p-1">
    <!-- Заголовок и режимы работы -->
    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border rounded-lg">
      <div>
        <h1 class="text-xl font-bold text-gray-800 dark:text-gray-200">Генератор блоков 2.0</h1>
        <p class="text-sm text-gray-600 dark:text-gray-400">Расширенные возможности генерации и комбинирования блоков</p>
      </div>
      <div class="flex gap-2">
        <Button
          v-for="mode in workModes"
          :key="mode.value"
          :label="mode.label"
          :class="[
            'text-xs px-3 py-2',
            currentMode === mode.value ? 'p-button-info' : 'p-button-outlined'
          ]"
          @click="setWorkMode(mode.value)"
        />
      </div>
    </div>

    <!-- Too<PERSON>bar с полями ввода и кнопками -->
    <div v-if="currentMode === 'prototyping'" class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <!-- Поля для режима прототипирования -->
      <InputText
        v-model="formData.number"
        placeholder="Номер страницы"
        class="w-28 text-xs"
        style="font-size: 11px"
      />
      <InputText
        v-model="formData.title"
        placeholder="Название страницы"
        class="flex-1 text-xs"
        style="font-size: 12px"
      />
      <MultiSelect
        v-model="formData.tags"
        :options="pageTagsOptions"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Теги страницы"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="formData.wpage_type"
        :options="pageTypeOptions"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Тип страницы"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <Button
        v-tooltip.top="'Сохранить страницу и блоки'"
        label="Страницу и блоки"
        icon="pi pi-save"
        class="p-button-success text-xs"
        :loading="prototypingLoading"
        :disabled="prototypingGroups.length === 0"
        @click="savePageAndPrototypes"
      />
    </div>

    <!-- Стандартный toolbar для других режимов -->
    <div v-else class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <InputText
        v-model="formData.number"
        placeholder="Номер блока"
        class="w-28 text-xs"
        style="font-size: 11px"
      />
      <InputText
        v-model="formData.title"
        placeholder="Название блока"
        class="flex-1 text-xs"
        style="font-size: 12px"
      />
      <MultiSelect
        v-model="formData.block_type"
        :options="blockTypeOptions"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Выберите типы блока"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="formData.collection"
        :options="collectionOptions"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Выберите коллекции"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <Button
        v-tooltip.top="'Генерировать скриншоты'"
        icon="pi pi-camera"
        class="p-button-warning text-xs"
        :loading="generating"
        :disabled="!selectedBlocks.length"
        @click="generateScreenshots"
      />
      <Button
        v-tooltip.top="'Сохранить'"
        icon="pi pi-save"
        class="text-xs"
        :loading="saving"
        :disabled="!selectedBlocks.length"
        @click="saveSelectedBlocks"
      />
    </div>

    <!-- Панель режимов работы -->
    <div v-if="currentMode === 'combination'" class="flex flex-col gap-4">
      <!-- Область редактирования (конструктор/холст) -->
      <div class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center gap-2">
            <label class="text-sm font-medium">Доски-страницы:</label>
            <Badge :value="combinationBoards.length" severity="info" />
          </div>
          <Button
            label="Создать доску"
            icon="pi pi-plus"
            class="text-xs"
            @click="createNewBoard"
          />
        </div>

        <!-- Доски -->
        <div v-if="combinationBoards.length > 0" class="flex gap-4 overflow-x-auto pb-4">
          <div
            v-for="(board, boardIndex) in combinationBoards"
            :key="board.id"
            class="flex-shrink-0 w-[400px] bg-white dark:bg-surface-800 border rounded-lg p-3"
          >
            <!-- Панель управления доской -->
            <div class="flex flex-col gap-2 mb-3 p-2 bg-surface-50 dark:bg-surface-700 rounded">
              <div class="flex gap-2">
                <InputText
                  v-model="board.number"
                  placeholder="Номер"
                  class="w-20 text-xs"
                  style="font-size: 11px"
                />
                <InputText
                  v-model="board.title"
                  placeholder="Название"
                  class="flex-1 text-xs"
                  style="font-size: 11px"
                />
                <Button
                  icon="pi pi-save"
                  class="text-xs"
                  :disabled="!board.number || !board.title || board.blocks.length === 0"
                  @click="savePageAndBlocks(boardIndex)"
                />
                <Button
                  icon="pi pi-trash"
                  class="text-xs p-button-danger"
                  @click="removeBoard(boardIndex)"
                />
              </div>
              <div class="flex gap-2">
                <MultiSelect
                  v-model="board.wpage_type"
                  :options="pageTypeOptions"
                  placeholder="Тип страницы"
                  display="chip"
                  class="text-xs flex-1"
                  panel-class="text-xs"
                />
                <MultiSelect
                  v-model="board.tags"
                  :options="pageTagsOptions"
                  placeholder="Теги"
                  display="chip"
                  class="text-xs flex-1"
                  panel-class="text-xs"
                />
              </div>
            </div>

            <!-- Блоки на доске -->
            <div class="min-h-[200px] relative board-blocks">
              <div
                v-for="(boardBlock, blockIndex) in board.blocks"
                :key="boardBlock.id"
                class="relative group board-block"
                draggable="true"
                @dragstart="onDragStart($event, boardBlock.id, boardIndex)"
                @dragover.prevent
                @drop="onDrop($event, blockIndex, boardIndex)"
              >
                <!-- Скриншот блока (бесшовное расположение) -->
                <div class="w-full">
                  <Image
                    v-if="boardBlock.tempScreenshot || boardBlock.sketch"
                    :src="boardBlock.tempScreenshot || `http://localhost:8055/assets/${boardBlock.sketch}`"
                    alt="Блок"
                    class="w-full h-auto object-contain block"
                    preview
                  />
                  <!-- Белая плашка для блоков без sketch -->
                  <div v-else class="w-full h-[30px] bg-white"/>
                </div>

                <!-- Панель действий (абсолютное позиционирование справа) -->
                <div class="block-controls bg-white dark:bg-surface-800 border rounded shadow-lg p-1 flex flex-col gap-1">
                  <Button
                    icon="pi pi-arrow-up"
                    class="text-xs p-button-text"
                    style="width: 20px; height: 20px; padding: 2px;"
                    :disabled="blockIndex === 0"
                    @click="moveBlockUp(boardIndex, blockIndex)"
                  />
                  <Button
                    icon="pi pi-arrow-down"
                    class="text-xs p-button-text"
                    style="width: 20px; height: 20px; padding: 2px;"
                    :disabled="blockIndex === board.blocks.length - 1"
                    @click="moveBlockDown(boardIndex, blockIndex)"
                  />
                  <Button
                    icon="pi pi-pencil"
                    class="text-xs p-button-text"
                    style="width: 20px; height: 20px; padding: 2px;"
                    @click="openBlockEditor(boardBlock.id, boardIndex)"
                  />
                  <Button
                    icon="pi pi-copy"
                    class="text-xs p-button-text"
                    style="width: 20px; height: 20px; padding: 2px;"
                    @click="duplicateBlockOnBoard(boardBlock.id, boardIndex)"
                  />
                  <Button
                    icon="pi pi-camera"
                    class="text-xs p-button-text"
                    style="width: 20px; height: 20px; padding: 2px;"
                    @click="generateBlockScreenshot(boardBlock.id, boardIndex)"
                  />
                  <Button
                    icon="pi pi-arrows-h"
                    class="text-xs p-button-text"
                    style="width: 20px; height: 20px; padding: 2px;"
                    @click="startBlockMove(boardBlock.id, boardIndex)"
                  />
                  <Button
                    icon="pi pi-trash"
                    class="text-xs p-button-text p-button-danger"
                    style="width: 20px; height: 20px; padding: 2px;"
                    @click="removeBlockFromBoard(boardBlock.id, boardIndex)"
                  />
                </div>
              </div>

              <!-- Зона для добавления блоков -->
              <div class="mt-4 p-2 border-2 border-dashed border-surface-300 dark:border-surface-600 rounded">
                <MultiSelect
                  :model-value="[]"
                  :options="selectedForCombination.map(block => ({
                    label: `${block.number} - ${block.title}`,
                    value: block.id,
                    image: block.sketch ? `http://localhost:8055/assets/${block.sketch}` : null
                  }))"
                  option-label="label"
                  option-value="value"
                  placeholder="Добавить блоки на доску"
                  class="text-xs w-full"
                  display="chip"
                  @change="(event) => addMultipleBlocksToBoard(event.value, boardIndex)"
                >
                  <template #option="{ option }">
                    <div class="flex items-center gap-2">
                      <img
                        v-if="option.image"
                        :src="option.image"
                        :alt="option.label"
                        class="w-8 h-8 object-cover rounded"
                      >
                      <div v-else class="w-8 h-8 bg-surface-200 rounded flex items-center justify-center">
                        <i class="pi pi-image text-xs text-surface-400"/>
                      </div>
                      <span class="text-xs">{{ option.label }}</span>
                    </div>
                  </template>
                </MultiSelect>
              </div>

              <!-- Кнопка генерации скриншота доски -->
              <div class="mt-2 text-center">
                <Button
                  label="Обновить скриншоты"
                  icon="pi pi-camera"
                  class="text-xs p-button-outlined"
                  @click="generateBoardScreenshots(boardIndex)"
                />
              </div>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8 text-surface-500">
          <i class="pi pi-plus text-4xl mb-4 block"/>
          <p>Создайте доску для начала работы</p>
        </div>
      </div>

      <!-- Область предпросмотра -->
      <div class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center gap-2">
            <label class="text-sm font-medium">Предпросмотр:</label>
            <Dropdown
              v-if="combinationBoards.length > 0"
              :model-value="previewState.currentBoardIndex"
              :options="combinationBoards.map((board, index) => ({
                label: `Доска ${index}: ${board.title || 'Без названия'}`,
                value: index
              }))"
              option-label="label"
              option-value="value"
              placeholder="Выберите доску"
              class="text-xs"
              @change="(event) => setPreviewBoard(event.value)"
            />
          </div>
          <div class="flex gap-2">
            <Button
              label="1440px"
              :class="['text-xs', previewState.viewport === '1440' ? 'p-button-primary' : 'p-button-outlined']"
              @click="setPreviewViewport('1440')"
            />
            <Button
              label="768px"
              :class="['text-xs', previewState.viewport === '768' ? 'p-button-primary' : 'p-button-outlined']"
              @click="setPreviewViewport('768')"
            />
            <Button
              label="375px"
              :class="['text-xs', previewState.viewport === '375' ? 'p-button-primary' : 'p-button-outlined']"
              @click="setPreviewViewport('375')"
            />
          </div>
        </div>

        <div class="h-[800px] bg-surface-100 dark:bg-surface-800 rounded overflow-auto">
          <div class="flex justify-center p-4">
            <iframe
              v-if="previewState.previewHtml"
              :srcdoc="previewState.previewHtml"
              :style="{ width: previewWidth, height: '750px' }"
              class="border rounded border-surface-300 bg-white"
              frameborder="0"
              sandbox="allow-scripts allow-same-origin allow-forms"
            />
            <div v-else class="flex items-center justify-center h-full">
              <p class="text-surface-500">Создайте доску с блоками для предпросмотра</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Информация о выбранных блоках -->
      <div class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
        <div class="flex items-center gap-2 mb-3">
          <label class="text-sm font-medium">Блоки для комбинирования:</label>
          <Badge :value="selectedForCombination.length" severity="info" />
        </div>

        <div class="text-center py-2 text-surface-500">
          <p class="text-sm">Используйте MultiSelect в карточках блоков ниже для добавления на доски</p>
        </div>
      </div>
    </div>

    <div v-else-if="currentMode === 'content'" class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <!-- Интеллектуальный конструктор контента -->
      <BlockContentConstructor
        :selected-blocks="selectedBlocks"
        @content-applied="handleContentApplied"
      />

      <!-- Показ блоков с контентом -->
      <div v-if="contentBlocks.length > 0" class="mt-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-md font-semibold">Блоки с контентом ({{ contentBlocks.length }})</h4>
          <div class="flex gap-2">
            <Button
              label="Генерировать скриншоты"
              icon="pi pi-camera"
              class="text-xs"
              :loading="generating"
              @click="generateContentScreenshots"
            />
            <Button
              label="Сохранить выбранные"
              icon="pi pi-save"
              class="text-xs"
              :loading="saving"
              :disabled="!selectedContentBlocks.length"
              @click="saveSelectedContentBlocks"
            />
          </div>
        </div>

        <!-- Сетка блоков с контентом -->
        <div class="masonry-grid gap-4">
          <Card
            v-for="contentBlock in contentBlocks"
            :key="contentBlock.id"
            class="overflow-hidden cursor-pointer transition-all duration-200"
            :class="{
              'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedContentBlocks.includes(contentBlock.id)
            }"
            @click="toggleContentBlockSelection(contentBlock.id)"
          >
            <template #header>
              <div class="relative">
                <Image
                  v-if="contentBlock.screenshot"
                  :src="contentBlock.screenshot"
                  alt="Блок с контентом"
                  class="w-full h-auto object-contain"
                  preview
                />
                <div v-else class="w-full h-32 bg-surface-100 dark:bg-surface-800 flex items-center justify-center">
                  <i class="pi pi-image text-2xl text-surface-400"/>
                </div>
                <div class="absolute top-2 right-2 flex gap-1">
                <!-- Основной чекбокс (синий) -->
                <Checkbox
                  :model-value="selectedContentBlocks.includes(contentBlock.id)"
                  binary
                  class="bg-white rounded shadow"
                  @click.stop
                  @change="toggleContentBlockSelection(contentBlock.id)"
                />                             
              </div>
            </div></template>
            <template #content>
              <div class="px-2 py-1">
                <div class="text-sm font-medium">{{ contentBlock.title }}</div>
                <div class="text-xs text-surface-500">{{ contentBlock.contentType }}</div>
                <div v-if="contentBlock.contentSource?.title" class="text-xs text-blue-600">
                  Источник: {{ contentBlock.contentSource.title }}
                </div>
                <div v-if="contentBlock.contentMapping" class="text-xs text-gray-500">
                  Маппинг: {{ Object.keys(contentBlock.contentMapping).length }} переменных
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>
    </div>

    <!-- Стандартные группы JSON/HBS (как в базовой версии) -->
    <div v-if="currentMode === 'standard'" class="flex flex-col gap-2">
      <div
        v-for="group in dataGroups"
        :key="group.id"
        class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg"
      >
        <!-- Номер группы -->
        <div class="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-sm font-medium">
          {{ group.id }}
        </div>

        <!-- JSON редактор -->
        <div class="w-[30%]">
          <ClientOnly>
            <PrismEditor
              v-model="group.jsonContent"
              class="w-full text-xs p-2 border rounded my-editor h-[180px] overflow-auto max-h-[180px]"
              :highlight="highlightJson"
              placeholder="Введите JSON данные"
              line-numbers
            />
          </ClientOnly>
        </div>

        <!-- HBS редактор -->
        <div class="flex-1">
          <ClientOnly>
            <PrismEditor
              v-model="group.hbsContent"
              class="w-full text-xs p-2 border rounded my-editor h-[180px] overflow-auto max-h-[180px]"
              :highlight="highlightHtml"
              placeholder="Введите HBS шаблон"
              line-numbers
            />
          </ClientOnly>
        </div>

        <!-- Дополнительные поля для групп 2+ -->
        <div v-if="group.id > 1" class="flex flex-col gap-2 w-48">
          <MultiSelect
            v-model="group.block_type"
            :options="blockTypeOptions"
            display="chip"
            class="text-xs"
            filter
            placeholder="Тип блока"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
          <MultiSelect
            v-model="group.collection"
            :options="collectionOptions"
            display="chip"
            class="text-xs"
            filter
            placeholder="Коллекция"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
          <!-- Кнопка добавления новой группы (только для последней группы) -->
          <Button
            v-if="group.id === dataGroups[dataGroups.length - 1].id"
            v-tooltip.top="'Добавить новую группу'"
            icon="pi pi-plus"
            class="p-button-outlined text-xs"
            @click="addNewGroup"
          />
        </div>

        <!-- Кнопка добавления новой группы для первой группы -->
        <div v-else class="flex-shrink-0">
          <Button
            v-if="group.id === dataGroups[dataGroups.length - 1].id"
            v-tooltip.top="'Добавить новую группу'"
            icon="pi pi-plus"
            class="p-button-outlined text-xs"
            @click="addNewGroup"
          />
        </div>
      </div>
    </div>

    <!-- Режим прототипирования (вертикальные группы) -->
    <div v-if="currentMode === 'prototyping'" class="mt-1">
        <!-- Панель управления прототипированием -->
        <div class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg mb-4">
          <!-- Первая строка: заголовок и глобальные поля -->
          <div class="flex items-center gap-4">
            <h3 class="text-lg font-semibold">🔬 Прототип</h3>

            <!-- Глобальные поля -->
            <div class="flex items-center gap-2 ml-5">
              <InputText
                v-model="globalPrototypingSettings.number"
                placeholder="Номер"
                class="text-xs"
                style="width: 100px; font-size: 10px; padding: 2px 4px; height: 24px"
              />
              <InputText
                v-model="globalPrototypingSettings.title"
                placeholder="Название"
                class="text-xs"
                style="width: 300px; font-size: 10px; padding: 2px 4px; height: 24px"
              />
              <MultiSelect
                v-model="globalPrototypingSettings.block_type"
                :options="blockTypeOptions"
                placeholder="Тип блока"
                display="chip"
                class="text-xs"
                style="width: 180px; font-size: 9px"
                :pt="{
                  root: { style: 'height: 24px; font-size: 9px' },
                  input: { style: 'padding: 1px 2px; font-size: 9px' }
                }"
              />
              <MultiSelect
                v-model="globalPrototypingSettings.collection"
                :options="collectionOptions"
                placeholder="Коллекция"
                display="chip"
                class="text-xs"
                style="width: 180px; font-size: 9px"
                :pt="{
                  root: { style: 'height: 24px; font-size: 9px' },
                  input: { style: 'padding: 1px 2px; font-size: 9px' }
                }"
              />
              
            <div class="flex gap-2 ml-auto">
              <div class="text-sm text-surface-600">
              Выбрано блоков: {{ selectedForPrototyping.length }} | Групп: {{ prototypingGroups.length }}
            </div>
              <Button
                label="Создать группы"
                icon="pi pi-plus"
                class="p-button-primary p-button-sm"
                :disabled="selectedForPrototyping.length === 0"
                @click="createPrototypingGroups"
              />
              <Button
                label="Блоки"
                icon="pi pi-save"
                class="p-button-success p-button-sm"
                :disabled="prototypingGroups.length === 0"
                :loading="prototypingLoading"
                @click="saveAllPrototypes"
              />
              <Button
                label="Очистить"
                icon="pi pi-trash"
                class="p-button-outlined p-button-sm"
                @click="prototypingGroups = []"
              />
            </div>
            </div>
          </div>

        </div>

        

        <!-- Вертикальные группы прототипирования -->
        <div v-if="prototypingGroups.length > 0" class="space-y-4">
          <div
            v-for="(group, groupIndex) in prototypingGroups"
            :key="group.id"
            class="border rounded-lg overflow-hidden bg-surface-0 dark:bg-surface-900"
          >
            <!-- Заголовок группы с консолидированными полями -->
            <div class="bg-surface-300 dark:bg-surface-800 p-1 border-b">
              <div class="flex items-center gap-3 flex-wrap">
                <span class="text-sm font-semibold">Блок {{ groupIndex + 1 }}:</span>

                <!-- Dropdown выбора блока -->
                <Dropdown
                  v-model="group.sourceBlockId"
                  :options="selectedForPrototyping.map(b => ({ label: `${b.number} - ${b.title}`, value: b.id }))"
                  option-label="label"
                  option-value="value"
                  placeholder="Выбрать блок"
                  class="text-xs"
                  style="width: 350px; font-size: 10px"
                  @change="updateSourceBlock(group)"
                />

                <!-- Поля из настроек -->
                <MultiSelect
                  v-model="group.block_type"
                  :options="blockTypeOptions"
                  placeholder="Тип блока"
                  display="chip"
                  class="text-xs"
                  style="width: 180px; font-size: 9px"
                />

                <MultiSelect
                  v-model="group.collection"
                  :options="collectionOptions"
                  placeholder="Коллекция"
                  display="chip"
                  class="text-xs"
                  style="width: 180px; font-size: 9px"
                />

                <!-- Кнопки управления -->
                <div class="flex gap-1 ml-auto">
                  <Button
                    v-tooltip="'Переместить вверх'"
                    icon="pi pi-arrow-up"
                    class="p-button-sm p-button-outlined"
                    style="font-size: 8px; height: 24px; width: 24px"
                    :disabled="groupIndex === 0"
                    @click="moveGroupUp(groupIndex)"
                  />
                  <Button
                    v-tooltip="'Переместить вниз'"
                    icon="pi pi-arrow-down"
                    class="p-button-sm p-button-outlined"
                    style="font-size: 8px; height: 24px; width: 24px"
                    :disabled="groupIndex === prototypingGroups.length - 1"
                    @click="moveGroupDown(groupIndex)"
                  />
                  <Button
                    v-tooltip="'Дублировать'"
                    icon="pi pi-copy"
                    class="p-button-sm p-button-outlined"
                    style="font-size: 8px; height: 24px; width: 24px"
                    @click="duplicateGroup(group, groupIndex)"
                  />
                  <Button
                    v-tooltip="'Сохранить'"
                    icon="pi pi-save"
                    class="p-button-sm p-button-success"
                    style="font-size: 8px; height: 24px; width: 24px"
                    :loading="group.saving"
                    @click="savePrototype(group)"
                  />
                  <Button
                    v-tooltip="'Удалить'"
                    icon="pi pi-trash"
                    class="p-button-sm p-button-danger"
                    style="font-size: 8px; height: 24px; width: 24px"
                    @click="removeGroup(groupIndex)"
                  />
                </div>
              </div>
            </div>

            <!-- Основной контент группы (5 колонок: 15% + 20% + 30% + 20% + 15%) -->
            <div class="p-3">
              <div class="flex gap-3">
                <!-- Колонка 1: Изображение исходного блока (15%) -->
                <div style="width: 18%">
                  
                  <div v-if="group.sourceBlock">
                    <div class="bg-gray-100 rounded overflow-hidden" style="width: 250px; height: auto;">
                      <Image
                        v-if="group.sourceBlock.sketch"
                        :src="`http://localhost:8055/assets/${group.sourceBlock.sketch}`"
                        alt="Sketch"
                        width="250"
                        preview
                        class="object-contain"
                        style="width: 250px; height: auto;"
                      />
                      <div v-else class="flex items-center justify-center text-gray-400 h-10" style="width: 250px;">
                        <span class="text-xs">Нет изображения</span>
                      </div>
                    </div>
                    <div class="text-xs my-2">
                      
                      <div class="text-gray-600"><span class="font-gray-400">{{ group.sourceBlock.number }}</span> {{ group.sourceBlock.title }}</div>
                    </div>
                  </div>
                  <div v-else class="text-center text-gray-400 py-4">
                    <i class="pi pi-box text-xl mb-1 block"/>
                    <span class="text-xs">Блок не выбран</span>
                  </div>
                  <Button
                      label="Генерировать скрин"
                      icon="pi pi-image"
                      class="p-button-sm my-2 p-button-outlined"
                      style="font-size: 8px; height: 20px"
                      :loading="group.generating"
                      @click="generatePreview(group)"
                    />
                  <div class="bg-gray-100 rounded overflow-hidden border" style="width: 250px; height: auto;">
                      <Image
                        v-if="group.previewImage"
                        :src="group.previewImage"
                        alt="Preview"
                        width="250"
                        preview
                        class="object-contain"
                        style="width: 250px; height: auto;"
                      />
                      <div v-else class="w-full h-10 flex items-center justify-center text-gray-400">
                        <div class="text-center">
                          <i class="pi pi-image text-xl m-1 block"/>
                          <span class="text-xs">Нет превью</span>
                        </div>
                      </div>
                    </div>

                    
                </div>

                <!-- Колонка 2: Исходный JSON (20%) -->
                <div style="width: 20%">
                  
                  <ClientOnly>
                    <PrismEditorWithCopy
                      :model-value="group.sourceJson"
                      field-name="Source JSON"
                      max-height="400px"
                      height="400px"
                      min-height="400px"
                      class="my-editor"
                      :highlight="highlightJson"
                      readonly
                    />
                  </ClientOnly>
                </div>

                <!-- Колонка 3: Интеллектуальный редактор полей (30%) -->
                <div style="width: 40%">
                  

                  <!-- Быстрые кнопки переменных -->
                  <div class="flex flex-wrap gap-1 mb-1">
                    <div
                      v-for="varType in quickVariableTypes"
                      :key="varType"
                      class="relative"
                    >
                      <Button
                        :label="varType"
                        class="p-button-sm p-button-outlined"
                        style="font-size: 8px; padding: 1px 2px; height: 16px; margin-top:-5px;"
                        @click="addQuickVariable(group, varType)"
                      />
                      <!-- Счетчик использованных переменных -->
                      <span
                        v-if="getVariableCount(group, varType) > 0"
                        class="absolute -top-1 -right-1 bg-blue-500 text-white rounded-full text-xs w-3 h-3 flex items-center justify-center"
                        style="font-size: 6px; min-width: 12px; min-height: 12px"
                      >
                        {{ getVariableCount(group, varType) }}
                      </span>
                    </div>
                  </div>

                  <!-- Поля переменных в одну строку -->
                  <div class="space-y-0 max-h-[350px] overflow-y-auto">
                    <div
                      v-for="(field, fieldIndex) in group.editableFields"
                      :key="fieldIndex"
                      class="flex gap-1 items-start"
                    >
                      <!-- Название переменной -->
                      <div class="w-[100px]">
                        <InputText
                          v-model="field.key"
                          class="w-full text-xs"
                          style="font-size: 8px; padding: 1px 2px; height: 18px; border:none;"
                          placeholder="Переменная"
                          @input="updateGroupJson(group)"
                        />
                      </div>

                      <!-- Значение переменной с превью изображения -->
                      <div class="w-[220px]">
                        <Textarea
                          v-model="field.value"
                          class="w-full text-xs"
                          
                          rows="1"
                          style="font-size: 9px; padding: 1px 2px; height: 22px"
                          placeholder="Значение"
                          @input="updateGroupJson(group)"
                        />
                      </div>
                      <Button
                        icon="pi pi-copy"
                        class="group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                        style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); right: 4px; top: 4px;"
                        @click="copyToClipboard(field.value)"
                      />
                        <!-- Превью изображения для image полей -->
                        <div v-if="field.key && field.key.toLowerCase().includes('image') && field.value && isImageUrl(field.value)"  >
                          <Image
                          
                          :src="field.value"
                            preview
                            width="50"
                            class="my"
                          />
                        </div>
                        <!-- Счетчик символов -->
                        <div class="text-xs text-gray-400 w-[60px]" style="font-size: 8px">
                          {{ field.value ? field.value.length : 0 }}
                          <span v-if="field.originalValue" class="ml-1">
                            ({{ getCharacterDifference(field) }})
                          </span>
                        </div>
                      

                      <!-- Dropdown типа -->
                      <div class="w-[120px]">
                        <Dropdown
                          v-model="field.selectedType"
                          :options="getContentTypeOptions()"
                          option-label="label"
                          option-value="value"
                          placeholder="Тип"
                          class="w-full"
                          style="font-size: 8px; height: 22px"
                          :pt="{
              root: { class: 'text-xs' },
              input: { class: 'text-xs p-1' },
              panel: { class: 'text-xs' },
              item: { class: 'text-xs p-1' }
            }"
                          @change="updateFieldValues(group, fieldIndex)"
                        />
                      </div>

                      <!-- Dropdown автоподстановки -->
                      <div v-if="field.selectedType" class="w-[120px]">
                        <Dropdown
                          
                          v-model="field.selectedValue"
                          :options="getContentValueOptions(field.selectedType)"
                          option-label="label"
                          option-value="value"
                          placeholder="Авто"
                          class="w-full"
                          style="font-size: 8px; height: 22px"
                          :pt="{
              root: { class: 'text-xs' },
              input: { class: 'text-xs p-1' },
              panel: { class: 'text-xs' },
              item: { class: 'text-xs p-1' }
            }"
                          @change="updateFieldValue(group, fieldIndex)"
                        />
                      </div>

                      <!-- Кнопки управления -->
                      <div class="w-[90px] flex gap-1">
                        <Button
                          v-tooltip="'Вверх'"
                          icon="pi pi-arrow-up"
                          class="p-button-text p-button-sm"
                          style="width: 16px; height: 16px; padding: 0; font-size: 6px"
                          :disabled="fieldIndex === 0"
                          @click="moveFieldUp(group, fieldIndex)"
                        />
                        <Button
                          v-tooltip="'Вниз'"
                          icon="pi pi-arrow-down"
                          class="p-button-text p-button-sm"
                          style="width: 16px; height: 16px; padding: 0; font-size: 6px"
                          :disabled="fieldIndex === group.editableFields.length - 1"
                          @click="moveFieldDown(group, fieldIndex)"
                        />
                        <Button
                          v-tooltip="'Дублировать'"
                          icon="pi pi-clone"
                          class="p-button-text p-button-sm"
                          style="width: 16px; height: 16px; padding: 0; font-size: 6px"
                          @click="duplicateField(group, fieldIndex)"
                        />
                        <Button
                          v-tooltip="'Удалить'"
                          icon="pi pi-trash"
                          class="p-button-text p-button-sm p-button-danger"
                          style="width: 16px; height: 16px; padding: 0; font-size: 6px"
                          @click="removeField(group, fieldIndex)"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Кнопка добавления и статистика -->
                  <div class="flex gap-2 mt-2">
                    <Button
                      label="+ Поле"
                      class="text-xs p-button-text w-2/3"
                      style="font-size: 9px; padding: 2px; height: 18px; margin-top: 2px"
                      @click="addFieldToGroup(group)"
                    />
                    <div class="text-xs text-gray-500 text-center" style="font-size: 9px; margin-top: 2px">
                      Переменных: {{ group.editableFields.length }} ({{ getFilledFieldsCount(group) }} заполнено)
                    </div>
                  </div>
                </div>

                <!-- Колонка 4: Финальный JSON с табами (20%) -->
                <div style="width: 22%">
                  
                  <TabView
class="text-xs"
                  :pt="{
                panelcontainer: { style: 'padding:0' },
              }">
                    <TabPanel
header="JSON" class="text-xs"
                    :pt="{
                    header: { class: 'p-0' },
                    headerAction: { class: 'text-xs p-0' },
                    content: { class: 'p-0' }
                  }">
                      <ClientOnly>
                        <PrismEditorWithCopy
                          v-model="group.finalJson"
                          field-name="Final JSON"
                          max-height="350px"
                      height="350px"
                      min-height="350px"
                          
                          class="my-editor"
                          :highlight="highlightJson"
                          @update:model-value="updateFieldsFromJson(group)"
                        />
                      </ClientOnly>
                    </TabPanel>
                    <TabPanel
header="HBS" class="text-xs"
                    :pt="{
                    header: { class: 'p-0' },
                    headerAction: { class: 'text-xs p-0' },
                    content: { class: 'p-0' }
                  }">
                      <ClientOnly>
                        <PrismEditorWithCopy
                          v-model="group.finalHbs"
                          field-name="Final HBS"
                          max-height="350px"
                      height="350px"
                      min-height="350px"
                          class="my-editor"
                          :highlight="highlightHtml"
                        />
                      </ClientOnly>
                    </TabPanel>
                  </TabView>
                </div>

                
                
              </div>
            </div>
          </div>
        </div>

        <!-- Пустое состояние для прототипирования -->
        <div v-else class="text-center py-8">
          <i class="pi pi-cog text-4xl text-gray-400 mb-4 block"/>
          <div class="text-lg text-gray-500 mb-2">Режим прототипирования</div>
          <div class="text-gray-400 mb-4">Выберите блоки выше и создайте группы для прототипирования</div>
          <Button
            label="Создать первый блок"
            icon="pi pi-plus"
            class="p-button-outlined"
            :disabled="selectedForPrototyping.length === 0"
            @click="createPrototypingGroups"
          />
        </div>
        <div class="flex justify-center my-4">
          <Button
            label="Добавить блок"
            icon="pi pi-plus"
            class="p-button-outlined p-button-sm"
            @click="addNewPrototypingGroup"
          />
        </div>
        <!-- Область превью страницы -->
        <div class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg mb-4 ">
          <div class="flex items-center justify-between mb-1">
            <h4 class="text-sm font-semibold">📄 Превью страницы</h4>
            <div class="flex gap-2">
              <Button
                label="Обновить"
                icon="pi pi-refresh"
                class="p-button-sm p-button-outlined"
                @click="generatePageHtml"
              />
            </div>
          </div>

          <!-- Поля CSS и JS -->
          

          <!-- Iframe превью -->
          <div class="flex justify-center">
            <iframe
              v-if="pageHtml"
              :srcdoc="getFullPageHtml()"
              class="border rounded border-surface-300 bg-white w-[1400px] h-[800px] oveflow-y"
              title="Превью страницы"
            />
            <div v-else class="flex items-center justify-center h-full text-gray-500">
              <div class="text-center">
                <i class="pi pi-eye text-2xl mb-2 block"/>
                <div>Создайте блоки для просмотра превью</div>
              </div>
            </div>
            
          </div>
          <div class="flex justify-center">
          <div class="grid grid-cols-2 gap-4 my-3 w-[1400px]">
            <div>
              
              <ClientOnly>
                <PrismEditorWithCopy
                  v-model="pageCss"
                  editor-class="w-full text-xs p-2 border rounded my-editor"
                  :highlight="highlightCss"
                  placeholder="CSS код будет собран автоматически"
                  field-name="CSS"
                  max-height="100px"
                  line-numbers
                />
              </ClientOnly>
            </div>
            <div>
              
              <ClientOnly>
                <PrismEditorWithCopy
                  v-model="pageJs"
                  editor-class="w-full text-xs p-2 border rounded my-editor"
                  :highlight="highlightJs"
                  placeholder="JS код будет собран автоматически"
                  field-name="JavaScript"
                  max-height="100px"
                  line-numbers
                />
              </ClientOnly>
            </div>
          </div>
        </div>
        </div>

        
        

      </div>

    <!-- Фильтры -->
    <div class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <MultiSelect
        v-model="filters.block_type"
        :options="filterOptions.block_type"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Тип блока"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.collection"
        :options="filterOptions.collection"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Коллекция"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.concept"
        :options="filterOptions.concept"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Концепт"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.elements"
        :options="filterOptions.elements"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Элементы"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.layout"
        :options="filterOptions.layout"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Макет"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.style"
        :options="filterOptions.style"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Стиль"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.features"
        :options="filterOptions.features"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Функции"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <Button
        v-tooltip.top="'Сбросить все фильтры'"
        icon="pi pi-times"
        class="p-button-outlined text-xs"
        @click="clearAllFilters"
      />
      <Button
        v-tooltip.top="'Показать только выбранные'"
        icon="pi pi-filter"
        class="p-button-outlined text-xs"
        :class="{ 'p-button-info': showOnlySelected }"
        @click="toggleShowOnlySelected"
      />
      <Button
        v-tooltip.top="'Снять все отметки'"
        icon="pi pi-check-square"
        class="p-button-outlined text-xs"
        @click="clearAllSelections"
      />
    </div>

    <!-- Блоки с пагинацией -->
    <div v-if="filteredBlocks.length > 0" class="flex flex-col gap-4">
      <!-- Информация о количестве блоков -->
      <div class="text-sm text-surface-600 dark:text-surface-400">
        Найдено блоков: {{ filteredBlocks.length }}
        <span v-if="selectedBlocks.length > 0">
          | Выбрано: {{ selectedBlocks.length }}
        </span>
        <span v-if="currentMode === 'combination' && selectedForCombination.length > 0">
          | Для комбинирования: {{ selectedForCombination.length }}
        </span>
        <span v-if="currentMode === 'prototyping' && selectedForPrototyping.length > 0">
          | Для прототипирования: {{ selectedForPrototyping.length }}
        </span>
      </div>
      
      <!-- Сетка блоков -->
      <div class="masonry-grid gap-4">
        <Card
          v-for="block in paginatedBlocks"
          :key="block.id"
          class="overflow-hidden cursor-pointer transition-all duration-50 group"
          :class="{
            'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 border-blue-500': isBlockSelected(block),
            'ring-2 ring-green-500 bg-green-50 dark:bg-green-900/20 border-green-500': isBlockSelectedForCombination(block),
            'hover:shadow-lg': !isBlockSelected(block) && !isBlockSelectedForCombination(block)
          }"
          :style="getBlockCardStyle(block)"
          style="background-color: #f9f9f9;"
          @click="handleBlockClick(block)"
        >
          <template #header>
            <div class="relative bg-white border-b border-gray-200">
              <!-- Показываем временный скриншот если есть -->
              <Image
                v-if="tempScreenshots.has(block.id!)"
                :src="tempScreenshots.get(block.id!)"
                alt="Сгенерированный скриншот"
                class="w-full h-auto object-contain cursor-pointer"
                preview
                @click.stop
              />
              <!-- Показываем оригинальный эскиз если нет временного скриншота -->
              <Image
                v-else-if="block.sketch"
                :src="`http://localhost:8055/assets/${block.sketch}`"
                alt="Предпросмотр блока"
                class="w-full h-auto object-contain cursor-pointer"
                preview
                @click.stop
              />
              <!-- Показываем заглушку если нет изображений -->
              <div
                v-else
                class="w-full h-12 bg-surface-100 dark:bg-surface-800 flex items-center justify-center"
              >
                <i class="pi pi-image text-4xl text-surface-400"/>
              </div>
              
              <!-- Кнопка редактирования (появляется при hover, 50% на карточке, 50% вне) -->
              

              <!-- Чекбоксы для разных режимов -->
              <div class="absolute top-2 right-2 flex gap-1">
                <!-- Основной чекбокс (синий) -->
                <Checkbox
                  :model-value="isBlockSelected(block)"
                  binary
                  class="bg-white rounded shadow"
                  @click.stop
                  @change="toggleBlockSelection(block)"
                />
                <!-- Чекбокс для комбинирования (зеленый) -->
                <Checkbox
                  v-if="currentMode === 'combination'"
                  :model-value="isBlockSelectedForCombination(block)"
                  binary
                  class="bg-white rounded shadow"
                  style="--p-checkbox-checked-background: #10b981; --p-checkbox-checked-border-color: #10b981;"
                  @click.stop
                  @change="toggleCombinationSelection(block)"
                />
                <!-- Чекбокс для прототипирования (фиолетовый) -->
                <Checkbox
                  v-if="currentMode === 'prototyping'"
                  :model-value="isBlockSelectedForPrototyping(block)"
                  binary
                  class="bg-white rounded shadow"
                  style="--p-checkbox-checked-background: #8b5cf6; --p-checkbox-checked-border-color: #8b5cf6;"
                  @click.stop
                  @change="togglePrototypingSelection(block)"
                />
                <Button
                icon="pi pi-pencil"
                class="absolute bottom-1 opacity-0 group-hover:opacity-100 transition-opacity p-button-sm p-button-rounded p-button-secondary"
                style="width: 24px; height: 24px;"
                @click.stop="openFullBlockEditor(block)"
              />
              </div>
            </div>
          </template>
          <template #content>
            <div class="p-0">
              
              <div class="space-y-1">
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-1">
                    <span class="text-gray-500 text-sm" style="font-size: 9px;">{{ block.number }}</span>
                    <span class="text-base" style="font-size: 13px;">{{ block.title }}</span>
                  </div>

                  <!-- MultiSelect для досок (режим комбинаций) -->
                  <div v-if="currentMode === 'combination'" class="flex-shrink-0">
                    <MultiSelect
                      :model-value="getBlockBoardSelections(block.id!)"
                      :options="combinationBoards.map((board, index) => ({ label: `${index}`, value: index }))"
                      option-label="label"
                      option-value="value"
                      placeholder="Доски"
                      class="text-xs w-20"
                      display="chip"
                      :pt="{
                        root: { class: 'text-xs h-6' },
                        input: { class: 'text-xs p-1 h-6' },
                        trigger: { class: 'w-4' },
                        panel: { class: 'text-xs' },
                        item: { class: 'text-xs p-1' }
                      }"
                      @change="(event) => updateBlockBoardSelections(block.id!, event.value)"
                      @click.stop
                    />
                  </div>

                  <!-- Dropdown для выбора группы (показывается только если групп больше 1 и не режим комбинаций) -->
                  <div v-else-if="dataGroups.length > 1" class="flex-shrink-0">
                    <Dropdown
                      :model-value="getSelectedGroupForBlock(block.id!)"
                      :options="groupOptions"
                      option-label="label"
                      option-value="value"
                      class="text-xs w-12"
                      :pt="{
                        root: { class: 'text-xs h-6' },
                        input: { class: 'text-xs p-1 h-6' },
                        trigger: { class: 'w-4' },
                        panel: { class: 'text-xs' },
                        item: { class: 'text-xs p-1' }
                      }"
                      @change="(event) => setSelectedGroupForBlock(block.id!, event.value)"
                      @click.stop
                    />
                  </div>
                </div>
              </div>

              <!-- Типы блока как микро-бейджи -->
              <div v-if="block.block_type && block.block_type.length > 0" class="flex flex-wrap gap-1 mt-1">
                <span
                  v-for="type in block.block_type.slice(0, 6)"
                  :key="type"
                  class="text-xs text-gray-600 dark:text-gray-400"
                  style="font-size: 8px; padding: 0 2px; background-color: #f0f0f0; color: #888; font-weight: 400; height: 14px; border-radius: 10px;"
                >
                  {{ type }}
                </span>
                <span
                  v-if="block.block_type.length > 6"
                  class="inline-block px-1 py-0 bg-surface-100 dark:bg-surface-700 text-surface-600 dark:text-surface-400 rounded"
                  style="font-size: 9px; line-height: 1.2;"
                >
                  +{{ block.block_type.length - 6 }}
                </span>
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Пагинация -->
      <Paginator
        v-model:first="first"
        :rows="rowsPerPage"
        :total-records="totalRecords"
        :rows-per-page-options="[100, 200, 300]"
        template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
        @page="onPageChange"
      />

      
    </div>

    <!-- Сообщение если блоков нет -->
    <div v-else-if="!loading" class="text-center py-8 text-surface-500">
      <i class="pi pi-inbox text-4xl mb-4 block"/>
      <p>Блоки не найдены</p>
      <p class="text-sm">Попробуйте изменить фильтры</p>
    </div>

    <Toast />

    <!-- Сайдбар для редактирования блоков (режим комбинаций) -->
    <div
      v-if="editingSidebar.visible"
      class="fixed top-0 right-0 h-full w-96 bg-white dark:bg-surface-900 border-l shadow-lg z-50 overflow-y-auto"
    >
      <div class="p-4">
        <!-- Заголовок -->
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">Редактирование блока</h3>
          <Button
            icon="pi pi-times"
            class="p-button-text"
            @click="closeBlockEditor"
          />
        </div>

        <!-- Информация о блоке -->
        <div v-if="editingSidebar.originalBlock" class="mb-4 p-3 bg-surface-50 dark:bg-surface-800 rounded">
          <div class="text-sm font-medium mb-2">Информация о блоке:</div>
          <div class="space-y-1 text-xs">
            <div><strong>Номер:</strong> {{ editingSidebar.originalBlock.number }}</div>
            <div><strong>Название:</strong> {{ editingSidebar.originalBlock.title }}</div>
            <div v-if="editingSidebar.originalBlock.block_type">
              <strong>Тип:</strong> {{ editingSidebar.originalBlock.block_type.join(', ') }}
            </div>
            <div v-if="editingSidebar.originalBlock.collection">
              <strong>Коллекция:</strong> {{ editingSidebar.originalBlock.collection.join(', ') }}
            </div>
          </div>

          <!-- Превью блока -->
          <div v-if="editingSidebar.originalBlock.sketch" class="mt-2">
            <Image
              :src="`http://localhost:8055/assets/${editingSidebar.originalBlock.sketch}`"
              alt="Превью блока"
              class="w-full h-auto object-contain"
              preview
            />
          </div>
        </div>

        <!-- Редакторы -->
        <div class="space-y-4">
          <!-- JSON редактор -->
          <div>
            <label class="text-sm font-medium mb-2 block">JSON данные:</label>
            <ClientOnly>
              <PrismEditorWithCopy
                v-model="editingSidebar.editedJson"
                editor-class="w-full text-xs p-2 border rounded my-editor"
                :highlight="highlightJson"
                placeholder="Введите JSON данные"
                field-name="JSON"
                max-height="200px"
                line-numbers
              />
            </ClientOnly>
          </div>

          <!-- HBS редактор -->
          <div>
            <label class="text-sm font-medium mb-2 block">HBS шаблон:</label>
            <ClientOnly>
              <PrismEditorWithCopy
                v-model="editingSidebar.editedHbs"
                editor-class="w-full text-xs p-2 border rounded my-editor"
                :highlight="highlightHtml"
                placeholder="Введите HBS шаблон"
                field-name="HBS"
                max-height="200px"
                line-numbers
              />
            </ClientOnly>
          </div>
        </div>

        <!-- Кнопки действий -->
        <div class="flex justify-end gap-2 mt-6">
          <Button
            label="Отмена"
            icon="pi pi-times"
            class="p-button-outlined"
            @click="closeBlockEditor"
          />
          <Button
            label="Сохранить"
            icon="pi pi-check"
            @click="saveBlockEdits"
          />
        </div>
      </div>
    </div>

    

    <!-- Полный сайдбар редактирования (точная копия из wblock-proto2) -->
    <WcontSidebar
    v-model:visible="fullSidebarVisible"
    :collapsed="false"
    title="Редактирование блока"
    @close="closeFullSidebar"
    @toggle-collapse="() => {}"
  >
    <div class="p-fluid">
      <!-- Базовая информация -->
      <div class="space-y-2">
        <div class="flex gap-2">
          <div class="field w-1/4">
            <InputText
              v-model="editingFullBlock.number"
              required
              class="w-full"
              placeholder="Номер блока*"
              style="padding: 6px; font-size: 10px"
            />
          </div>

          <div class="field w-3/4">
            <InputText
              v-model="editingFullBlock.title"
              required
              class="w-full"
              placeholder="Название*"
              style="padding: 6px; font-size: 11px"
            />
          </div>
        </div>

        <div class="field">
          <Textarea
            v-model="editingFullBlock.description"
            rows="2"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Описание"
            style="padding: 4px; font-size: 10px"
          />
        </div>

        <div class="field mb-0" style="margin-top: 0">
          <Textarea
            v-model="editingFullBlock.composition"
            rows="4"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Композиция"
            style="padding: 4px; font-size: 10px"
          />
        </div>

        <div class="field mb-2" style="margin-top: 0">
          <MultiSelect
            v-model="editingFullBlock.block_type"
            :options="blockTypeOptions"
            placeholder="Выберите типы блока"
            display="chip"
            class="text-xs w-full p-0"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="flex gap-2">
          <div class="field w-1/3">
            <Dropdown
              v-model="editingFullBlock.status"
              :options="statusOptions"
              option-label="label"
              option-value="value"
              placeholder="Выберите статус"
              class="w-full text-xs"
              style="font-size: 11px"
            />
          </div>

          <div class="field w-2/3">
            <MultiSelect
              v-model="editingFullBlock.concept"
              :options="conceptOptions"
              placeholder="Выберите концепции"
              display="chip"
              class="w-full text-xs"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="editingFullBlock.collection"
            :options="collectionOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите коллекции"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="editingFullBlock.layout"
            :options="layoutOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите макеты"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="editingFullBlock.welem_proto"
            :options="welemOptions"
            option-label="label"
            option-value="value"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите элементы"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="editingFullBlock.elements"
            :options="elementOptions"
            placeholder="Выберите типы элементов"
            display="chip"
            class="w-full text-xs"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="editingFullBlock.features"
            :options="featuresOptions"
            placeholder="Выберите особенности"
            display="chip"
            class="w-full text-xs"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="flex gap-2">
          <div class="field w-1/2">
            <MultiSelect
              v-model="editingFullBlock.style"
              :options="styleOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите стили"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field w-1/2">
            <MultiSelect
              v-model="editingFullBlock.graphics"
              :options="graphicsOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите графику"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>

        <div class="field mb-2">
          <div class="flex gap-2">
            <Image
              v-if="editingFullBlock.sketch"
              :src="`http://localhost:8055/assets/${editingFullBlock.sketch}`"
              alt="Эскиз"
              width="200"
              class="my"
              preview
            />
            <FileUpload
              mode="basic"
              :auto="true"
              accept="image/*"
              :max-file-size="1000000"
              choose-label="Эскиз"
              class="p-button-sm"
              @select="onSketchSelect"
            />
          </div>
        </div>

        <div class="field mb-0">
          <TabView
            class="text-xs"
            :pt="{
            panelcontainer: { style: 'padding:0' },
          }"
          >
            <TabPanel
              header="HTML/CSS/JS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <div class="space-y-1">
                <PrismEditorWithCopy
                  v-model="editingFullBlock.html"
                  editor-class="my-editor text-xs"
                  :highlight="highlightHtml"
                  placeholder="Введите HTML код"
                  field-name="HTML"
                  max-height="120px"
                />
                <div class="p-0 grid grid-cols-2 gap-4">
                  <div class="flex flex-col h-full">
                  <PrismEditorWithCopy
                    v-model="editingFullBlock.css"
                    editor-class="my-editor text-xs w-full"
                    :highlight="highlightCss"
                    placeholder="CSS код"
                    field-name="CSS"
                    max-height="60px !important"
                  />
                </div>
                <div class="flex flex-col h-full">
                  <PrismEditorWithCopy
                    v-model="editingFullBlock.js"
                    editor-class="my-editor text-xs w-full"
                    :highlight="highlightJs"
                    placeholder="JS код"
                    field-name="JavaScript"
                    max-height="60px !important"
                  />
                </div>
                </div>
              </div>
            </TabPanel>
            <TabPanel
              header="HBS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="editingFullBlock.hbs"
                editor-class="my-editor text-xs"
                :highlight="highlightHtml"
                placeholder="Введите HBS код"
                field-name="HBS"
                max-height="200px"
              />
            </TabPanel>
            <TabPanel
              header="JSON"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="editingFullBlock.json"
                editor-class="my-editor text-xs"
                :highlight="highlightJson"
                placeholder="Введите JSON код"
                field-name="JSON"
                max-height="200px"
              />
            </TabPanel>
          </TabView>
        </div>

        <div class="field mb-2">
          <Textarea
            v-model="editingFullBlock.notes"
            rows="1"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Заметки"
            style="padding: 4px; font-size: 10px"
          />
        </div>
      </div>

      <div class="flex justify-end gap-2 mt-0">
        <Button
          label="Отмена"
          icon="pi pi-times"
          class="p-button-sm"
          @click="closeFullSidebar"
        />
        <Button
          label="Сохранить"
          icon="pi pi-check"
          class="p-button-sm"
          :loading="saving"
          @click="saveFullBlock"
        />
      </div>
    </div>
    </WcontSidebar>
  
</div></template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { PrismEditor } from 'vue-prism-editor'
import 'vue-prism-editor/dist/prismeditor.min.css'
// @ts-ignore
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
// @ts-ignore
import handlebars from 'handlebars/dist/handlebars.min.js'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'

// Импорт компонентов для блоков
import BlockContentConstructor from '~/components/wblock-gen/BlockContentConstructor.vue'
import BlockCombinationPreview from '~/components/wblock-gen/BlockCombinationPreview.vue'

// PrimeVue компоненты
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import MultiSelect from 'primevue/multiselect'
import Card from 'primevue/card'
import Checkbox from 'primevue/checkbox'
import Toast from 'primevue/toast'
import Image from 'primevue/image'
import Paginator from 'primevue/paginator'
import Badge from 'primevue/badge'
import Dropdown from 'primevue/dropdown'
import Textarea from 'primevue/textarea'
import FileUpload from 'primevue/fileupload'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'

// Компоненты
import WcontSidebar from '~/components/WcontSidebar.vue'
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'

// Определение метаданных страницы
definePageMeta({
  title: 'Генератор блоков 2.0',
  description: 'Расширенные возможности генерации и комбинирования блоков',
  navOrder: 16,
  type: 'primary',
  icon: 'i-mdi-view-grid-plus-outline',
})

// Режимы работы
const workModes = [
  { label: 'Стандарт', value: 'standard' },
  { label: 'Комбинации', value: 'combination' },
  { label: 'Контент', value: 'content' },
  { label: 'Прототип', value: 'prototyping' }
]

const currentMode = ref('standard')

const setWorkMode = (mode: string) => {
  currentMode.value = mode
  // Сбрасываем выборки при смене режима
  if (mode !== 'combination') {
    selectedForCombination.value = []
  }
}

// Интерфейсы
interface WBlock {
  id?: string
  number: string
  title: string
  block_type?: string[]
  collection?: string[]
  json?: string
  hbs?: string
  html?: string
  sketch?: string
  css?: string
  js?: string
  concept?: string[]
  elements?: string[]
  layout?: string[]
  style?: string[]
  features?: string[]
}

interface DataGroup {
  id: number
  jsonContent: string
  hbsContent: string
  block_type?: string[]
  collection?: string[]
}

// Composables
const { getItems, createItems, updateItem } = useDirectusItems()
const toast = useToast()

// Реактивные данные
const formData = ref({
  number: '',
  title: '',
  block_type: [] as string[],
  collection: [] as string[],
  tags: [] as string[],
  wpage_type: [] as string[]
})

// Группы данных JSON/HBS
const dataGroups = ref<DataGroup[]>([
  { id: 1, jsonContent: '', hbsContent: '', block_type: [], collection: [] }
])

// Выбранная группа для каждого блока (по умолчанию группа 1)
const blockGroupSelection = ref<Map<string, number>>(new Map())

// Данные для блоков
const allBlocks = ref<WBlock[]>([])
const selectedBlocks = ref<WBlock[]>([])
const selectedForCombination = ref<WBlock[]>([])

// Данные для режима контента
const contentBlocks = ref<any[]>([])
const selectedContentBlocks = ref<string[]>([])

// Данные для режима комбинаций
const combinationBoards = ref<any[]>([])
const selectedBoardBlocks = ref<Map<number, string[]>>(new Map())

// Данные для режима прототипирования
const prototypingGroups = ref<any[]>([])
const selectedForPrototyping = ref<WBlock[]>([])
const contentPrimitives = ref<any[]>([])
const prototypingLoading = ref(false)

// Глобальные настройки прототипирования
const globalPrototypingSettings = ref({
  number: '',
  title: '',
  block_type: [] as string[],
  collection: [] as string[]
})

// Отслеживание хешей данных для проверки актуальности скриншотов
const groupDataHashes = ref<Map<string, string>>(new Map())
const tempGroupScreenshots = ref<Map<string, string>>(new Map())

// Общие поля для страницы (по паттерну wblock-page-gen)
const pageHtml = ref('')
const pageCss = ref('')
const pageJs = ref('')

// Быстрые переменные как в IntelligentForms.vue
const quickVariableTypes = ['title', 'image', 'imageBackground', 'url', 'linkText', 'icon', 'text', 'excerpt']


const editingSidebar = ref({
  visible: false,
  blockId: null as string | null,
  boardIndex: null as number | null,
  originalBlock: null as WBlock | null,
  editedHbs: '',
  editedJson: ''
})

// Состояние для полного сайдбара редактирования (как в wblock-proto2)
const fullSidebarVisible = ref(false)
const editingFullBlock = ref<any>({
  id: '',
  number: '',
  title: '',
  description: '',
  composition: '',
  notes: '',
  block_type: [],
  elements: [],
  layout: [],
  style: [],
  graphics: [],
  collection: [],
  concept: [],
  sketch: '',
  welem_proto: [],
  html: '',
  css: '',
  js: '',
  hbs: '',
  json: ''
})

// Состояние предпросмотра
const previewState = ref({
  currentBoardIndex: 0,
  viewport: '1440' as '1440' | '768' | '375',
  previewHtml: ''
})

// Состояния загрузки
const loading = ref(false)
const generating = ref(false)
const saving = ref(false)

// Опции для мультиселектов
const blockTypeOptions = ref<string[]>([])
const collectionOptions = ref<string[]>([])

// Опции для страниц (режим комбинаций)
const pageTypeOptions = ref<string[]>([])
const pageTagsOptions = ref<string[]>([])

// Опции из Directus (как в wblock-proto2)
const conceptOptions = ref<string[]>([])
const elementOptions = ref<string[]>([])
const layoutOptions = ref<string[]>([])
const styleOptions = ref<string[]>([])
const graphicsOptions = ref<string[]>([])
const featuresOptions = ref<string[]>([])
const welemOptions = ref<any[]>([])

// Опции статуса
const statusOptions = [
  { label: 'Идея', value: 'idea' },
  { label: 'В разработке', value: 'in_progress' },
  { label: 'Готово', value: 'done' },
  { label: 'Архив', value: 'archived' },
]

// Пагинация
const first = ref(0)
const rowsPerPage = ref(100)

// Фильтры
const filters = ref({
  block_type: [] as string[],
  collection: [] as string[],
  concept: [] as string[],
  elements: [] as string[],
  layout: [] as string[],
  style: [] as string[],
  features: [] as string[]
})

// Опции для фильтров
const filterOptions = ref({
  block_type: [] as string[],
  collection: [] as string[],
  concept: [] as string[],
  elements: [] as string[],
  layout: [] as string[],
  style: [] as string[],
  features: [] as string[]
})

const showOnlySelected = ref(false)

// Кэширование для оптимизации генерации скриншотов
const tempScreenshots = ref<Map<string, string>>(new Map())
const blockDataHashes = ref<Map<string, string>>(new Map())

// Вспомогательные функции для работы с группами и хэшами
const getSelectedGroupForBlock = (blockId: string): number => {
  return blockGroupSelection.value.get(blockId) || 1
}

const setSelectedGroupForBlock = (blockId: string, groupId: number) => {
  blockGroupSelection.value.set(blockId, groupId)
  console.log(`Блок ${blockId} теперь использует группу ${groupId}`)
}

const getGroupData = (groupId: number) => {
  return dataGroups.value.find(g => g.id === groupId)
}

const generateDataHash = (block: WBlock, groupId: number): string => {
  const group = getGroupData(groupId)
  if (!group) return ''

  // Создаем хэш на основе всех данных, влияющих на генерацию
  const dataToHash = {
    blockId: block.id,
    blockNumber: block.number,
    blockHbs: block.hbs || '',
    blockJson: block.json || '',
    groupId: groupId,
    groupJson: group.jsonContent,
    groupHbs: group.hbsContent
  }

  // Безопасный хэш с поддержкой Unicode
  const jsonString = JSON.stringify(dataToHash)
  // Используем простой хэш вместо btoa для поддержки Unicode
  let hash = 0
  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Конвертируем в 32-битное число
  }
  return hash.toString()
}

const isScreenshotActual = (blockId: string, currentHash: string): boolean => {
  const storedHash = blockDataHashes.value.get(blockId)
  return storedHash === currentHash && tempScreenshots.value.has(blockId)
}

const updateBlockHash = (blockId: string, hash: string) => {
  blockDataHashes.value.set(blockId, hash)
}

// Функция для сохранения blob в Directus (точная копия из оригинала)
const saveBlobToDirectus = async (blobUrl: string, filename: string): Promise<string | null> => {
  try {
    console.log(`💾 Сохранение временного скриншота в Directus: ${filename}...`)

    // Получаем blob из URL
    const response = await fetch(blobUrl)
    const blob = await response.blob()

    // Создаем FormData для загрузки в Directus
    const formData = new FormData()
    formData.append('title', filename)
    formData.append('file', blob, filename)

    // Загружаем в Directus
    const uploadResponse = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!uploadResponse.ok) {
      throw new Error(`Failed to upload to Directus: ${uploadResponse.statusText}`)
    }

    const data = await uploadResponse.json()
    console.log(`✅ Временный скриншот сохранен в Directus: ${data.data.id}`)

    return data.data.id

  } catch (error) {
    console.error(`❌ Ошибка сохранения временного скриншота в Directus:`, error)
    return null
  }
}

const generateScreenshotForCard = async (card: any) => {
  if (card.screenshot) {
    return // Скриншот уже создан
  }

  try {
    console.log(`Создание скриншота для ${card.number}...`)

    // Получаем CSS и JS из оригинального блока
    const originalBlock = card.originalBlock
    const css = originalBlock.css || ''
    const js = originalBlock.js || ''

    // Создаем полный HTML для скриншота с CSS/JS из блока
    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${card.title}</title>

  ${css}
</head>
<body>
  ${card.html}
  ${js}
</body>
</html>`

    const filename = `wblock_gen_${card.number}_${card.title.replace(/[^a-zA-Z0-9]/g, '_')}`

    const response = await $fetch('/api/capture-html-screenshot', {
      method: 'POST',
      body: {
        html: fullHtml,
        filename: filename,
        width: 1400,
        height: 800
      }
    })

    card.screenshot = response.fileId
    console.log(`Скриншот создан для ${card.number}: ${response.fileId}`)

  } catch (error) {
    console.error(`Ошибка создания скриншота для ${card.number}:`, error)
  }
}

// Computed свойства
const filteredBlocks = computed(() => {
  let blocks = allBlocks.value

  // Фильтр "Только выбранные"
  if (showOnlySelected.value) {
    blocks = blocks.filter(block => selectedBlocks.value.some(s => s.id === block.id))
  }

  // Фильтр по типу блока
  if (filters.value.block_type.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.block_type) return false
      return filters.value.block_type.some(selectedType =>
        block.block_type!.includes(selectedType)
      )
    })
  }

  // Фильтр по коллекции
  if (filters.value.collection.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.collection) return false
      return filters.value.collection.some(selectedCollection =>
        block.collection!.includes(selectedCollection)
      )
    })
  }

  // Фильтр по концепту
  if (filters.value.concept.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.concept) return false
      return filters.value.concept.some(selectedConcept =>
        block.concept!.includes(selectedConcept)
      )
    })
  }

  // Фильтр по элементам
  if (filters.value.elements.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.elements) return false
      return filters.value.elements.some(selectedElement =>
        block.elements!.includes(selectedElement)
      )
    })
  }

  // Фильтр по макету
  if (filters.value.layout.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.layout) return false
      return filters.value.layout.some(selectedLayout =>
        block.layout!.includes(selectedLayout)
      )
    })
  }

  // Фильтр по стилю
  if (filters.value.style.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.style) return false
      return filters.value.style.some(selectedStyle =>
        block.style!.includes(selectedStyle)
      )
    })
  }

  // Фильтр по функциям
  if (filters.value.features.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.features) return false
      return filters.value.features.some(selectedFeature =>
        block.features!.includes(selectedFeature)
      )
    })
  }

  return blocks
})

const paginatedBlocks = computed(() => {
  const start = first.value
  const end = start + rowsPerPage.value
  return filteredBlocks.value.slice(start, end)
})

const totalRecords = computed(() => filteredBlocks.value.length)

// Вычисляемые свойства для групп
const groupOptions = computed(() => {
  return dataGroups.value.map(group => ({
    label: group.id.toString(),
    value: group.id
  }))
})

// Методы для работы с группами
const addNewGroup = () => {
  const newId = Math.max(...dataGroups.value.map(g => g.id)) + 1
  dataGroups.value.push({
    id: newId,
    jsonContent: '',
    hbsContent: '',
    block_type: [],
    collection: []
  })
}

// Подсветка синтаксиса
const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

// Загрузка данных
const loadBlocks = async () => {
  loading.value = true
  try {
    const blocks = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: ['*']
      }
    }) as WBlock[]
    allBlocks.value = blocks
  } catch (error) {
    console.error('Ошибка загрузки блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить блоки',
      life: 3000
    })
  } finally {
    loading.value = false
  }
}

const loadOptions = async () => {
  try {
    // Загружаем уникальные значения для опций
    const blocks = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: ['block_type', 'collection', 'concept', 'elements', 'layout', 'style', 'features']
      }
    })

    const blockTypes = new Set<string>()
    const collections = new Set<string>()
    const concepts = new Set<string>()
    const elements = new Set<string>()
    const layouts = new Set<string>()
    const styles = new Set<string>()
    const features = new Set<string>()

    blocks.forEach((block: any) => {
      if (block.block_type && Array.isArray(block.block_type)) {
        block.block_type.forEach((type: string) => blockTypes.add(type))
      }
      if (block.collection && Array.isArray(block.collection)) {
        block.collection.forEach((coll: string) => collections.add(coll))
      }
      if (block.concept && Array.isArray(block.concept)) {
        block.concept.forEach((concept: string) => concepts.add(concept))
      }
      if (block.elements && Array.isArray(block.elements)) {
        block.elements.forEach((element: string) => elements.add(element))
      }
      if (block.layout && Array.isArray(block.layout)) {
        block.layout.forEach((layout: string) => layouts.add(layout))
      }
      if (block.style && Array.isArray(block.style)) {
        block.style.forEach((style: string) => styles.add(style))
      }
      if (block.features && Array.isArray(block.features)) {
        block.features.forEach((feature: string) => features.add(feature))
      }
    })

    blockTypeOptions.value = Array.from(blockTypes).sort()
    collectionOptions.value = Array.from(collections).sort()

    filterOptions.value = {
      block_type: Array.from(blockTypes).sort(),
      collection: Array.from(collections).sort(),
      concept: Array.from(concepts).sort(),
      elements: Array.from(elements).sort(),
      layout: Array.from(layouts).sort(),
      style: Array.from(styles).sort(),
      features: Array.from(features).sort()
    }

    // Загружаем данные для страниц (режим комбинаций)
    const wpageItems = await getItems({
      collection: 'wpage',
      params: {
        limit: -1,
        fields: ['wpage_type', 'tags']
      }
    })

    // Извлекаем уникальные типы страниц
    const allPageTypes = new Set<string>()
    wpageItems?.forEach((item: any) => {
      if (item.wpage_type && Array.isArray(item.wpage_type)) {
        item.wpage_type.forEach((type: string) => allPageTypes.add(type))
      }
    })
    pageTypeOptions.value = Array.from(allPageTypes).sort()

    // Извлекаем уникальные теги страниц
    const allPageTags = new Set<string>()
    wpageItems?.forEach((item: any) => {
      if (item.tags && Array.isArray(item.tags)) {
        item.tags.forEach((tag: string) => allPageTags.add(tag))
      }
    })
    pageTagsOptions.value = Array.from(allPageTags).sort()

  } catch (error) {
    console.error('Ошибка загрузки опций:', error)
  }
}

// Методы для работы с выбором блоков
const toggleBlockSelection = (block: WBlock) => {
  const index = selectedBlocks.value.findIndex(s => s.id === block.id)
  if (index > -1) {
    selectedBlocks.value.splice(index, 1)
  } else {
    selectedBlocks.value.push(block)
  }
}

const toggleCombinationSelection = (block: WBlock) => {
  const index = selectedForCombination.value.findIndex(s => s.id === block.id)
  if (index > -1) {
    selectedForCombination.value.splice(index, 1)
    // Удаляем блок из всех досок
    selectedBoardBlocks.value.forEach((blocks) => {
      const blockIndex = blocks.indexOf(block.id!)
      if (blockIndex > -1) {
        blocks.splice(blockIndex, 1)
      }
    })
    // Удаляем блок из досок
    combinationBoards.value.forEach(board => {
      const boardBlockIndex = board.blocks.findIndex((b: any) => b.id === block.id)
      if (boardBlockIndex > -1) {
        board.blocks.splice(boardBlockIndex, 1)
      }
    })
  } else {
    selectedForCombination.value.push(block)
  }
}

// Методы для прототипирования
const isBlockSelectedForPrototyping = (block: WBlock) => {
  return selectedForPrototyping.value.some(b => b.id === block.id)
}

const togglePrototypingSelection = (block: WBlock) => {
  const index = selectedForPrototyping.value.findIndex(b => b.id === block.id)
  if (index > -1) {
    selectedForPrototyping.value.splice(index, 1)
  } else {
    selectedForPrototyping.value.push(block)
  }
}

// Функции для работы с MultiSelect в карточках
const getBlockBoardSelections = (blockId: string): number[] => {
  const selections: number[] = []
  combinationBoards.value.forEach((board, index) => {
    if (board.blocks.some((b: any) => b.id === blockId)) {
      selections.push(index)
    }
  })
  return selections
}

const updateBlockBoardSelections = (blockId: string, selectedBoards: number[]) => {
  const block = allBlocks.value.find(b => b.id === blockId)
  if (!block) return

  // Добавляем блок в selectedForCombination если его там нет
  if (!selectedForCombination.value.some(b => b.id === blockId)) {
    selectedForCombination.value.push(block)
  }

  // Удаляем блок из всех досок
  combinationBoards.value.forEach((board, boardIndex) => {
    const blockIndex = board.blocks.findIndex((b: any) => b.id === blockId)
    if (blockIndex > -1) {
      board.blocks.splice(blockIndex, 1)
    }

    // Обновляем selectedBoardBlocks
    const boardBlocks = selectedBoardBlocks.value.get(boardIndex) || []
    const selectedBlockIndex = boardBlocks.indexOf(blockId)
    if (selectedBlockIndex > -1) {
      boardBlocks.splice(selectedBlockIndex, 1)
      selectedBoardBlocks.value.set(boardIndex, boardBlocks)
    }
  })

  // Добавляем блок на выбранные доски
  selectedBoards.forEach(boardIndex => {
    if (boardIndex < combinationBoards.value.length) {
      const board = combinationBoards.value[boardIndex]

      // Добавляем блок на доску
      board.blocks.push({
        ...block,
        tempScreenshot: null,
        editedHbs: block.hbs || '',
        editedJson: block.json || ''
      })

      // Обновляем selectedBoardBlocks
      const boardBlocks = selectedBoardBlocks.value.get(boardIndex) || []
      if (!boardBlocks.includes(blockId)) {
        boardBlocks.push(blockId)
        selectedBoardBlocks.value.set(boardIndex, boardBlocks)
      }
    }
  })

  // Если блок не выбран ни на одной доске, удаляем его из selectedForCombination
  if (selectedBoards.length === 0) {
    const index = selectedForCombination.value.findIndex(b => b.id === blockId)
    if (index > -1) {
      selectedForCombination.value.splice(index, 1)
    }
  }
}

const toggleContentBlockSelection = (blockId: string) => {
  const index = selectedContentBlocks.value.indexOf(blockId)
  if (index > -1) {
    selectedContentBlocks.value.splice(index, 1)
  } else {
    selectedContentBlocks.value.push(blockId)
  }
}

// Функции для работы с досками (режим комбинаций)
const createNewBoard = () => {
  const newBoardIndex = combinationBoards.value.length
  combinationBoards.value.push({
    id: newBoardIndex,
    number: '',
    title: '',
    wpage_type: [],
    tags: [],
    blocks: []
  })
  selectedBoardBlocks.value.set(newBoardIndex, [])
}

const removeBoard = (boardIndex: number) => {
  combinationBoards.value.splice(boardIndex, 1)
  selectedBoardBlocks.value.delete(boardIndex)

  // Перенумеровываем доски
  const newBoards = []
  const newSelectedBlocks = new Map()

  combinationBoards.value.forEach((board, index) => {
    board.id = index
    newBoards.push(board)
    newSelectedBlocks.set(index, selectedBoardBlocks.value.get(boardIndex + (index >= boardIndex ? 1 : 0)) || [])
  })

  combinationBoards.value = newBoards
  selectedBoardBlocks.value = newSelectedBlocks
}

const addBlockToBoard = (blockId: string, boardIndex: number) => {
  const boardBlocks = selectedBoardBlocks.value.get(boardIndex) || []
  if (!boardBlocks.includes(blockId)) {
    boardBlocks.push(blockId)
    selectedBoardBlocks.value.set(boardIndex, boardBlocks)

    // Обновляем блоки в доске
    const board = combinationBoards.value[boardIndex]
    if (board) {
      const block = allBlocks.value.find(b => b.id === blockId)
      if (block) {
        board.blocks.push({
          ...block,
          tempScreenshot: null,
          editedHbs: block.hbs || '',
          editedJson: block.json || ''
        })
      }
    }
  }
}

const removeBlockFromBoard = (blockId: string, boardIndex: number) => {
  const boardBlocks = selectedBoardBlocks.value.get(boardIndex) || []
  const blockIndex = boardBlocks.indexOf(blockId)
  if (blockIndex > -1) {
    boardBlocks.splice(blockIndex, 1)
    selectedBoardBlocks.value.set(boardIndex, boardBlocks)

    // Удаляем из доски
    const board = combinationBoards.value[boardIndex]
    if (board) {
      const boardBlockIndex = board.blocks.findIndex(b => b.id === blockId)
      if (boardBlockIndex > -1) {
        board.blocks.splice(boardBlockIndex, 1)
      }
    }

    // Снимаем отметку в основном списке
    const mainIndex = selectedForCombination.value.findIndex(b => b.id === blockId)
    if (mainIndex > -1) {
      selectedForCombination.value.splice(mainIndex, 1)
    }
  }
}

// Функция для открытия полного сайдбара редактирования
const openFullBlockEditor = (block: any) => {
  editingFullBlock.value = { ...block }
  fullSidebarVisible.value = true
}

const closeFullSidebar = () => {
  fullSidebarVisible.value = false
  editingFullBlock.value = {
    id: '',
    number: '',
    title: '',
    description: '',
    composition: '',
    notes: '',
    block_type: [],
    elements: [],
    layout: [],
    style: [],
    graphics: [],
    collection: [],
    concept: [],
    sketch: '',
    welem_proto: [],
    html: '',
    css: '',
    js: '',
    hbs: '',
    json: ''
  }
}

const saveFullBlock = async () => {
  saving.value = true
  try {
    if (editingFullBlock.value.id) {
      await updateItem({
        collection: 'wblock_proto',
        id: editingFullBlock.value.id,
        item: editingFullBlock.value
      })
    } else {
      await createItems({
        collection: 'wblock_proto',
        items: [editingFullBlock.value]
      })
    }

    // Обновляем данные
    await loadBlocks()

    closeFullSidebar()
    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Блок сохранен'
    })
  } catch (error) {
    console.error('Error saving block:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блок'
    })
  } finally {
    saving.value = false
  }
}

// Функция для загрузки эскиза (точная копия из wblock-proto2)
const onSketchSelect = async (event: any) => {
  const file = event.files[0]
  if (file) {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('http://localhost:8055/files', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Ошибка загрузки файла')
      }

      const data = await response.json()
      editingFullBlock.value.sketch = data.data.id

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Эскиз загружен'
      })
    } catch (error) {
      console.error('Ошибка при загрузке файла:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить файл',
      })
    }
  }
}

// Функции для сайдбара редактирования
const openBlockEditor = (blockId: string, boardIndex: number) => {
  const board = combinationBoards.value[boardIndex]
  const block = board?.blocks.find(b => b.id === blockId)

  if (block) {
    editingSidebar.value = {
      visible: true,
      blockId,
      boardIndex,
      originalBlock: block,
      editedHbs: block.editedHbs || block.hbs || '',
      editedJson: block.editedJson || block.json || ''
    }
  }
}

const closeBlockEditor = () => {
  editingSidebar.value = {
    visible: false,
    blockId: null,
    boardIndex: null,
    originalBlock: null,
    editedHbs: '',
    editedJson: ''
  }
}

const saveBlockEdits = () => {
  if (editingSidebar.value.blockId && editingSidebar.value.boardIndex !== null) {
    const board = combinationBoards.value[editingSidebar.value.boardIndex]
    const block = board?.blocks.find(b => b.id === editingSidebar.value.blockId)

    if (block) {
      block.editedHbs = editingSidebar.value.editedHbs
      block.editedJson = editingSidebar.value.editedJson

      // Сбрасываем временный скриншот, чтобы он перегенерировался
      block.tempScreenshot = null

      // Обновляем предпросмотр
      updatePreview()
    }
  }

  closeBlockEditor()
}

// Функции для управления блоками на досках
const duplicateBlockOnBoard = (blockId: string, boardIndex: number) => {
  const board = combinationBoards.value[boardIndex]
  const block = board?.blocks.find(b => b.id === blockId)

  if (block) {
    const duplicatedBlock = {
      ...block,
      id: `${block.id}_copy_${Date.now()}`, // Уникальный ID для копии
      tempScreenshot: null // Сбрасываем временный скриншот
    }

    // Находим индекс оригинального блока и вставляем копию после него
    const originalIndex = board.blocks.findIndex(b => b.id === blockId)
    board.blocks.splice(originalIndex + 1, 0, duplicatedBlock)

    // Обновляем selectedBoardBlocks
    const boardBlocks = selectedBoardBlocks.value.get(boardIndex) || []
    boardBlocks.push(duplicatedBlock.id)
    selectedBoardBlocks.value.set(boardIndex, boardBlocks)
  }
}

const startBlockMove = (blockId: string, fromBoardIndex: number) => {
  // Простая реализация - показываем dropdown для выбора целевой доски
  const availableBoards = combinationBoards.value
    .map((board, index) => `${index}: ${board.title || 'Без названия'}`)
    .filter((_, index) => index !== fromBoardIndex)
    .join('\n')

  const targetBoardIndex = prompt(`Переместить блок на доску:\n${availableBoards}\n\nВведите номер доски:`)

  if (targetBoardIndex !== null) {
    const targetIndex = parseInt(targetBoardIndex)
    if (targetIndex >= 0 && targetIndex < combinationBoards.value.length && targetIndex !== fromBoardIndex) {
      moveBlockBetweenBoards(blockId, fromBoardIndex, targetIndex)
    }
  }
}

const moveBlockBetweenBoards = (blockId: string, fromBoardIndex: number, toBoardIndex: number) => {
  const fromBoard = combinationBoards.value[fromBoardIndex]
  const toBoard = combinationBoards.value[toBoardIndex]

  if (fromBoard && toBoard) {
    // Находим и удаляем блок с исходной доски
    const blockIndex = fromBoard.blocks.findIndex(b => b.id === blockId)
    if (blockIndex > -1) {
      const [block] = fromBoard.blocks.splice(blockIndex, 1)

      // Добавляем блок на целевую доску
      toBoard.blocks.push(block)

      // Обновляем selectedBoardBlocks
      const fromBoardBlocks = selectedBoardBlocks.value.get(fromBoardIndex) || []
      const fromBlockIndex = fromBoardBlocks.indexOf(blockId)
      if (fromBlockIndex > -1) {
        fromBoardBlocks.splice(fromBlockIndex, 1)
        selectedBoardBlocks.value.set(fromBoardIndex, fromBoardBlocks)
      }

      const toBoardBlocks = selectedBoardBlocks.value.get(toBoardIndex) || []
      toBoardBlocks.push(blockId)
      selectedBoardBlocks.value.set(toBoardIndex, toBoardBlocks)
    }
  }
}

// Функции для управления порядком блоков
const moveBlockUp = (boardIndex: number, blockIndex: number) => {
  if (blockIndex > 0) {
    const board = combinationBoards.value[boardIndex]
    const blocks = board.blocks
    const temp = blocks[blockIndex]
    blocks[blockIndex] = blocks[blockIndex - 1]
    blocks[blockIndex - 1] = temp
  }
}

const moveBlockDown = (boardIndex: number, blockIndex: number) => {
  const board = combinationBoards.value[boardIndex]
  if (blockIndex < board.blocks.length - 1) {
    const blocks = board.blocks
    const temp = blocks[blockIndex]
    blocks[blockIndex] = blocks[blockIndex + 1]
    blocks[blockIndex + 1] = temp
  }
}

// Drag & Drop функции
let draggedBlock: { blockId: string, fromBoardIndex: number } | null = null

const onDragStart = (event: DragEvent, blockId: string, boardIndex: number) => {
  draggedBlock = { blockId, fromBoardIndex: boardIndex }
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
  }
}

const onDrop = (event: DragEvent, targetIndex: number, boardIndex: number) => {
  event.preventDefault()

  if (!draggedBlock) return

  const { blockId, fromBoardIndex } = draggedBlock

  if (fromBoardIndex === boardIndex) {
    // Перемещение внутри одной доски
    const board = combinationBoards.value[boardIndex]
    const blocks = board.blocks
    const currentIndex = blocks.findIndex(b => b.id === blockId)

    if (currentIndex !== -1 && currentIndex !== targetIndex) {
      const [movedBlock] = blocks.splice(currentIndex, 1)
      blocks.splice(targetIndex, 0, movedBlock)
    }
  } else {
    // Перемещение между досками
    moveBlockBetweenBoards(blockId, fromBoardIndex, boardIndex)
  }

  draggedBlock = null
}

// Функция для добавления нескольких блоков на доску
const addMultipleBlocksToBoard = (blockIds: string[], boardIndex: number) => {
  blockIds.forEach(blockId => {
    const block = allBlocks.value.find(b => b.id === blockId)
    if (block) {
      const board = combinationBoards.value[boardIndex]

      // Проверяем, нет ли уже этого блока на доске
      if (!board.blocks.some((b: any) => b.id === blockId)) {
        board.blocks.push({
          ...block,
          tempScreenshot: null,
          editedHbs: block.hbs || '',
          editedJson: block.json || ''
        })

        // Обновляем selectedBoardBlocks
        const boardBlocks = selectedBoardBlocks.value.get(boardIndex) || []
        if (!boardBlocks.includes(blockId)) {
          boardBlocks.push(blockId)
          selectedBoardBlocks.value.set(boardIndex, boardBlocks)
        }
      }
    }
  })
}

// Функция для генерации хэша блока в режиме комбинаций
const generateCombinationBlockHash = (block: any): string => {
  const dataToHash = {
    blockId: block.id,
    blockNumber: block.number,
    blockHbs: block.hbs || '',
    blockJson: block.json || '',
    blockCss: block.css || '',
    blockJs: block.js || '',
    editedHbs: block.editedHbs || '',
    editedJson: block.editedJson || ''
  }

  const jsonString = JSON.stringify(dataToHash)
  let hash = 0
  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash
  }
  return hash.toString()
}

// Функции для генерации скриншотов блоков
const generateBlockScreenshot = async (blockId: string, boardIndex: number) => {
  const board = combinationBoards.value[boardIndex]
  const block = board?.blocks.find((b: any) => b.id === blockId)

  if (!block) return

  // Проверяем актуальность скриншота
  const currentHash = generateCombinationBlockHash(block)
  const storedHash = blockDataHashes.value.get(blockId)

  if (storedHash === currentHash && block.tempScreenshot) {
    console.log(`⚡ Скриншот блока ${block.number} актуален, пропускаем генерацию`)
    return
  }

  try {
    console.log(`📸 Создание временного скриншота для блока ${block.number}...`)

    // Компилируем HTML блока
    let html = ''
    try {
      if (block.editedHbs && block.editedJson) {
        const template = handlebars.compile(block.editedHbs)
        const jsonData = JSON.parse(block.editedJson)
        html = template(jsonData)
      } else if (block.hbs && block.json) {
        const template = handlebars.compile(block.hbs)
        const jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
        html = template(jsonData)
      } else {
        html = block.html || ''
      }
    } catch (error) {
      console.error(`Ошибка компиляции блока ${block.number}:`, error)
      html = block.html || ''
    }

    // Создаем полный HTML для скриншота
    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${block.title}</title>
  ${block.css || ''}
</head>
<body>
  ${html}
  ${block.js || ''}
</body>
</html>`

    const filename = `block_${block.number}_${block.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`

    // Создаем временный скриншот (blob URL, не сохраняется в Directus)
    const response = await $fetch('/api/capture-html-screenshot-temp', {
      method: 'POST',
      body: {
        html: fullHtml,
        width: 1400,
        height: 800
      },
      responseType: 'blob'
    })

    // Создаем временный URL из blob
    const tempUrl = URL.createObjectURL(response as Blob)

    // Обновляем временный скриншот
    block.tempScreenshot = tempUrl

    // Обновляем хэш для оптимизации
    blockDataHashes.value.set(blockId, currentHash)

    console.log(`✅ Временный скриншот блока создан (не сохранен в Directus)`)

  } catch (error) {
    console.error(`❌ Ошибка создания скриншота блока:`, error)
  }
}

const generateBoardScreenshots = async (boardIndex: number) => {
  const board = combinationBoards.value[boardIndex]
  if (!board || board.blocks.length === 0) return

  console.log(`🔍 Проверка актуальности скриншотов для доски ${boardIndex}...`)

  let updatedCount = 0
  let skippedCount = 0

  for (const block of board.blocks) {
    // Проверяем актуальность скриншота
    const currentHash = generateCombinationBlockHash(block)
    const storedHash = blockDataHashes.value.get(block.id)

    if (storedHash === currentHash && block.tempScreenshot) {
      console.log(`⚡ Скриншот блока ${block.number} актуален, пропускаем`)
      skippedCount++
    } else {
      await generateBlockScreenshot(block.id, boardIndex)
      updatedCount++
    }
  }

  console.log(`✅ Скриншоты доски ${boardIndex}: обновлено ${updatedCount}, пропущено ${skippedCount}`)
}

// Функции для предпросмотра
const setPreviewViewport = (viewport: '1440' | '768' | '375') => {
  previewState.value.viewport = viewport
}

const setPreviewBoard = (boardIndex: number) => {
  previewState.value.currentBoardIndex = boardIndex
  updatePreview()
}

const updatePreview = () => {
  const board = combinationBoards.value[previewState.value.currentBoardIndex]
  if (!board || board.blocks.length === 0) {
    previewState.value.previewHtml = '<div style="padding: 20px; text-align: center; color: #666;">Выберите доску с блоками для предпросмотра</div>'
    return
  }

  try {
    // Компилируем HTML блоков
    const compiledBlocks = []
    for (const block of board.blocks) {
      let html = ''

      try {
        if (block.editedHbs && block.editedJson) {
          const template = handlebars.compile(block.editedHbs)
          const jsonData = JSON.parse(block.editedJson)
          html = template(jsonData)
        } else if (block.hbs && block.json) {
          const template = handlebars.compile(block.hbs)
          const jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
          html = template(jsonData)
        } else {
          html = block.html || ''
        }
      } catch (error) {
        console.error(`Ошибка компиляции блока ${block.number}:`, error)
        html = block.html || ''
      }

      compiledBlocks.push({
        ...block,
        compiledHtml: html
      })
    }

    // HTML страницы
    const pageHtml = compiledBlocks.map(block => block.compiledHtml).join('\n')

    // CSS и JS (удаляем дубликаты)
    const allCss = new Set<string>()
    const allJs = new Set<string>()

    compiledBlocks.forEach(block => {
      if (block.css) {
        block.css.split('\n').forEach((line: string) => {
          if (line.trim()) allCss.add(line.trim())
        })
      }
      if (block.js) {
        block.js.split('\n').forEach((line: string) => {
          if (line.trim()) allJs.add(line.trim())
        })
      }
    })

    const pageCss = Array.from(allCss).join('\n')
    const pageJs = Array.from(allJs).join('\n')

    // Создаем полный HTML для предпросмотра
    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${board.title || 'Предпросмотр'}</title>
  
    ${pageCss}
  
</head>
<body>
  ${pageHtml}
  
    ${pageJs}
  
</body>
</html>`

    previewState.value.previewHtml = fullHtml

  } catch (error) {
    console.error('Ошибка создания предпросмотра:', error)
    previewState.value.previewHtml = '<div style="padding: 20px; text-align: center; color: #f00;">Ошибка создания предпросмотра</div>'
  }
}

// Computed для получения ширины iframe в зависимости от viewport
const previewWidth = computed(() => {
  switch (previewState.value.viewport) {
    case '768': return '768px'
    case '375': return '375px'
    default: return '1440px'
  }
})

// Функция сохранения страницы и блоков (режим комбинаций)
const savePageAndBlocks = async (boardIndex: number) => {
  const board = combinationBoards.value[boardIndex]
  if (!board || !board.number || !board.title || board.blocks.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните номер, название страницы и добавьте блоки',
      life: 3000
    })
    return
  }

  saving.value = true

  try {
    console.log('🚀 Начинаем сохранение страницы и блоков...')

    // Подготовка скриншотов
    const screenshotsToCreate = []

    // Компилируем HTML блоков
    const compiledBlocks = []
    for (const block of board.blocks) {
      let html = ''

      try {
        if (block.editedHbs && block.editedJson) {
          const template = handlebars.compile(block.editedHbs)
          const jsonData = JSON.parse(block.editedJson)
          html = template(jsonData)
        } else if (block.hbs && block.json) {
          const template = handlebars.compile(block.hbs)
          const jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
          html = template(jsonData)
        } else {
          html = block.html || ''
        }
      } catch (error) {
        console.error(`Ошибка компиляции блока ${block.number}:`, error)
        html = block.html || ''
      }

      compiledBlocks.push({
        ...block,
        compiledHtml: html
      })
    }

    // HTML страницы
    const pageHtml = compiledBlocks.map(block => block.compiledHtml).join('\n')

    // CSS и JS (удаляем дубликаты)
    const allCss = new Set<string>()
    const allJs = new Set<string>()

    compiledBlocks.forEach(block => {
      if (block.css) {
        block.css.split('\n').forEach(line => {
          if (line.trim()) allCss.add(line.trim())
        })
      }
      if (block.js) {
        block.js.split('\n').forEach(line => {
          if (line.trim()) allJs.add(line.trim())
        })
      }
    })

    const pageCss = Array.from(allCss).join('\n')
    const pageJs = Array.from(allJs).join('\n')

    // Скриншот страницы
    const pageFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${board.title}</title>
  ${pageCss}
</head>
<body>
  ${pageHtml}
  ${pageJs}
</body>
</html>`

    const pageFilename = `page_${board.title.replace(/[^a-zA-Z0-9]/g, '_')}`
    screenshotsToCreate.push({
      html: pageFullHtml,
      filename: pageFilename
    })

    // Скриншоты блоков
    compiledBlocks.forEach((block, index) => {
      const blockFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${block.title}</title>
  ${block.css || ''}
</head>
<body>
  ${block.compiledHtml}
  ${block.js || ''}
</body>
</html>`

      const blockNumber = String(index + 1).padStart(2, '0')
      const blockFilename = `block_${board.number}-${blockNumber}_${block.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`
      screenshotsToCreate.push({
        html: blockFullHtml,
        filename: blockFilename
      })
    })

    // Создание скриншотов
    console.log(`📸 Создание ${screenshotsToCreate.length} скриншотов...`)
    let screenshotFileIds: (string | null)[] = []

    try {
      screenshotFileIds = await generateScreenshotsOptimal(screenshotsToCreate)
      console.log('✅ Все скриншоты созданы:', screenshotFileIds)
    } catch (screenshotError) {
      console.error('⚠️ Ошибка создания скриншотов:', screenshotError)
      screenshotFileIds = new Array(screenshotsToCreate.length).fill(null)
    }

    // Сохранение страницы
    console.log('📄 Сохраняем страницу в wpage...')
    const pageData = {
      title: board.title,
      number: board.number,
      html: pageHtml,
      css: pageCss,
      js: pageJs,
      wpage_type: board.wpage_type,
      tags: board.tags,
      sketch: screenshotFileIds[0]
    }

    const savedPage = await createItems({
      collection: 'wpage',
      items: [pageData]
    })

    const pageId = Array.isArray(savedPage) ? savedPage[0].id : savedPage.id
    console.log('✅ Страница сохранена с ID:', pageId)

    // Сохранение блоков
    console.log('📦 Сохраняем блоки в wblock_proto...')
    const blocksToSave = []

    for (let index = 0; index < compiledBlocks.length; index++) {
      const block = compiledBlocks[index]

      // Анализируем HTML
      const analysisResult = await analyzeHtmlContent(block.compiledHtml)

      const blockNumber = String(index + 1).padStart(2, '0')
      const finalBlockNumber = `${board.number}-${blockNumber}`
      const finalBlockTitle = `${board.title} - ${blockNumber}`

      const blockData = {
        number: finalBlockNumber,
        title: finalBlockTitle,
        hbs: block.editedHbs || block.hbs || '',
        json: block.editedJson || block.json || '',
        html: block.compiledHtml,
        css: block.css || '',
        js: block.js || '',
        collection: board.tags || [],
        composition: analysisResult.composition,
        layout: analysisResult.layout,
        elements: analysisResult.elements,
        graphics: analysisResult.graphics,
        features: analysisResult.features,
        sketch: screenshotFileIds[index + 1]
      }

      blocksToSave.push(blockData)
    }

    const savedBlocks = await createItems({
      collection: 'wblock_proto',
      items: blocksToSave
    })

    const blockIds = Array.isArray(savedBlocks) ? savedBlocks.map(block => block.id) : [savedBlocks.id]
    console.log('✅ Блоки сохранены с ID:', blockIds)

    // Связывание страницы и блоков
    console.log('🔗 Создаем связи в wpage_wblock_proto...')
    const linkData = blockIds.map(blockId => ({
      wpage_id: pageId,
      wblock_proto_id: blockId
    }))

    await createItems({
      collection: 'wpage_wblock_proto',
      items: linkData
    })

    console.log('✅ Связи успешно созданы!')

    toast.add({
      severity: 'success',
      summary: 'Успех',
      detail: `Страница и ${blockIds.length} блоков сохранены и связаны!`,
      life: 3000
    })

    // Очищаем доску после сохранения
    removeBoard(boardIndex)

  } catch (error) {
    console.error('❌ Ошибка сохранения страницы и блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить страницу и блоки',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}



// Функция для оптимального создания скриншотов
const generateScreenshotsOptimal = async (screenshots: Array<{html: string, filename: string}>) => {
  console.log(`📸 Создание ${screenshots.length} скриншотов по одному...`)

  const fileIds: string[] = []

  for (let i = 0; i < screenshots.length; i++) {
    const screenshot = screenshots[i]
    try {
      console.log(`📸 Создание скриншота ${i + 1}/${screenshots.length}: ${screenshot.filename}`)

      const response = await $fetch('/api/capture-html-screenshot', {
        method: 'POST',
        body: {
          html: screenshot.html,
          filename: screenshot.filename,
          width: 1400,
          height: 800
        }
      }) as { fileId: string }

      fileIds.push(response.fileId)
      console.log(`✅ Скриншот ${i + 1} создан: ${response.fileId}`)

    } catch (error) {
      console.error(`❌ Ошибка создания скриншота ${i + 1}:`, error)
      fileIds.push('')
    }
  }

  return fileIds
}

// Инициализация компонента
onMounted(async () => {
  await loadOptions()
  await loadBlocks()
})

// Автоматическое обновление предпросмотра при изменениях
watch([combinationBoards, () => previewState.value.currentBoardIndex], () => {
  if (currentMode.value === 'combination') {
    updatePreview()
  }
}, { deep: true })

// Методы для генерации скриншотов (полная копия из wblock-gen.vue)
const generateScreenshots = async () => {
  if (selectedBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите блоки для генерации скриншотов',
      life: 3000
    })
    return
  }

  // Проверяем, что хотя бы в одной группе есть данные
  const hasAnyData = dataGroups.value.some(group => group.jsonContent || group.hbsContent)
  if (!hasAnyData) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните JSON или HBS поле хотя бы в одной группе',
      life: 3000
    })
    return
  }

  generating.value = true

  try {
    console.log(`Генерация скриншотов для ${selectedBlocks.value.length} выбранных блоков...`)

    let generatedCount = 0
    let skippedCount = 0

    for (const block of selectedBlocks.value) {
      try {
        // Получаем выбранную группу для этого блока
        const selectedGroupId = getSelectedGroupForBlock(block.id!)
        const selectedGroup = getGroupData(selectedGroupId)

        if (!selectedGroup) {
          console.warn(`Группа ${selectedGroupId} не найдена для блока ${block.number}`)
          continue
        }

        // Генерируем хэш текущих данных
        const currentHash = generateDataHash(block, selectedGroupId)

        // Проверяем актуальность существующего скриншота
        if (isScreenshotActual(block.id!, currentHash)) {
          console.log(`⏭️ Пропускаем блок ${block.number} - скриншот актуален`)
          skippedCount++
          continue
        }

        let html = ''

        // Функционал 1: JSON заполнен в группе - используем его с HBS из блока
        if (selectedGroup.jsonContent && block.hbs) {
          const template = handlebars.compile(block.hbs)
          const jsonData = JSON.parse(selectedGroup.jsonContent)
          html = template(jsonData)
        }
        // Функционал 2: HBS заполнен в группе - используем его с JSON из блока
        else if (selectedGroup.hbsContent && block.json) {
          const template = handlebars.compile(selectedGroup.hbsContent)
          const jsonData = typeof block.json === 'string'
            ? JSON.parse(block.json)
            : block.json
          html = template(jsonData)
        }

        if (html) {
          // Освобождаем старый blob URL если есть
          const oldUrl = tempScreenshots.value.get(block.id!)
          if (oldUrl && oldUrl.startsWith('blob:')) {
            URL.revokeObjectURL(oldUrl)
          }

          // Создаем новый временный скриншот
          const tempUrl = await generateTempScreenshot(block, html)
          if (tempUrl) {
            tempScreenshots.value.set(block.id!, tempUrl)
            updateBlockHash(block.id!, currentHash)
            generatedCount++
            console.log(`✅ Временный скриншот создан для блока ${block.number} с группой ${selectedGroupId}`)
          }
        }

      } catch (error) {
        console.error(`Ошибка генерации для блока ${block.number}:`, error)
      }
    }

    let message = ''

    if (generatedCount > 0 && skippedCount > 0) {
      message = `Создано: ${generatedCount}, пропущено: ${skippedCount} (актуальные)`
    } else if (generatedCount > 0) {
      message = `Создано ${generatedCount} скриншотов`
    } else if (skippedCount > 0) {
      message = `Все ${skippedCount} скриншотов актуальны`
    } else {
      message = 'Нет данных для генерации'
    }

    toast.add({
      severity: 'success',
      summary: 'Генерация завершена',
      detail: message,
      life: 3000
    })

  } catch (error) {
    console.error('Error generating screenshots:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сгенерировать скриншоты',
      life: 3000
    })
  } finally {
    generating.value = false
  }
}

// Функция для создания временного скриншота (БЕЗ сохранения в Directus)
const generateTempScreenshot = async (block: WBlock, html: string): Promise<string | null> => {
  try {
    console.log(`Создание временного скриншота для ${block.number}...`)

    // Получаем CSS и JS из оригинального блока
    const css = block.css || ''
    const js = block.js || ''

    // Создаем полный HTML для скриншота с CSS/JS из блока
    const fullHtml = '<!DOCTYPE html>' +
      '<html lang="en">' +
      '<head>' +
      '<meta charset="UTF-8">' +
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
      '<title>' + (block.title || '') + '</title>' +
      css +
      '</head>' +
      '<body>' +
      html +
      js +
      '</body>' +
      '</html>'

    // Используем новый API для временных скриншотов (НЕ сохраняет в Directus)
    const response = await $fetch('/api/capture-html-screenshot-temp', {
      method: 'POST',
      body: {
        html: fullHtml,
        width: 1400,
        height: 800
      },
      responseType: 'blob'
    })

    // Создаем временный URL из blob
    const tempUrl = URL.createObjectURL(response as Blob)

    console.log(`Временный скриншот создан (БЕЗ сохранения в Directus)`)
    return tempUrl

  } catch (error) {
    console.error(`Ошибка создания временного скриншота для ${block.number}:`, error)
    return null
  }
}

// Методы для сохранения (полная копия из оригинала)
const saveSelectedBlocks = async () => {
  if (selectedBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите блоки для сохранения',
      life: 3000
    })
    return
  }

  // Проверяем, что хотя бы в одной группе есть данные
  const hasAnyData = dataGroups.value.some(group => group.jsonContent || group.hbsContent)
  if (!hasAnyData) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните JSON или HBS поле хотя бы в одной группе',
      life: 3000
    })
    return
  }

  saving.value = true

  try {

    const blocksToSave = []
    const analysisData = []

    console.log(`Обработка ${selectedBlocks.value.length} выбранных блоков для сохранения...`)

    // Подготавливаем данные для сохранения
    for (let i = 0; i < selectedBlocks.value.length; i++) {
      const block = selectedBlocks.value[i]
      const counter = String(i + 1).padStart(2, '0')

      // Генерируем HTML для блока
      let html = ''
      let sourceData = ''
      let sourceTemplate = ''

      // Получаем выбранную группу для этого блока
      const selectedGroupId = getSelectedGroupForBlock(block.id!)
      const selectedGroup = getGroupData(selectedGroupId)

      if (!selectedGroup) {
        console.warn(`Группа ${selectedGroupId} не найдена для блока ${block.number}`)
        continue
      }

      // Функционал 1: JSON заполнен в группе - используем его с HBS из блока
      if (selectedGroup.jsonContent && block.hbs) {
        const template = handlebars.compile(block.hbs)
        const jsonData = JSON.parse(selectedGroup.jsonContent)
        html = template(jsonData)
        sourceData = selectedGroup.jsonContent
        sourceTemplate = block.hbs
      }
      // Функционал 2: HBS заполнен в группе - используем его с JSON из блока
      else if (selectedGroup.hbsContent && block.json) {
        const template = handlebars.compile(selectedGroup.hbsContent)
        const jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
        html = template(jsonData)
        sourceData = typeof block.json === 'string' ? block.json : JSON.stringify(block.json, null, 2)
        sourceTemplate = selectedGroup.hbsContent
      }

      // Используем уже созданный скриншот или создаем новый
      let screenshotId = null
      if (html) {
        // Генерируем хэш для проверки актуальности
        const currentHash = generateDataHash(block, selectedGroupId)

        // Проверяем, есть ли актуальный временный скриншот
        if (isScreenshotActual(block.id!, currentHash)) {
          // Если есть актуальный временный скриншот, сохраняем его в Directus
          try {
            const tempBlobUrl = tempScreenshots.value.get(block.id!)
            if (tempBlobUrl) {
              const filename = `wblock_gen_${block.number}_${block.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}.png`
              screenshotId = await saveBlobToDirectus(tempBlobUrl, filename)
              console.log(`✅ Временный скриншот сохранен в Directus для блока ${block.number}: ${screenshotId}`)
            } else {
              console.warn(`⚠️ Временный скриншот не найден для блока ${block.number}`)
            }
          } catch (error) {
            console.error(`Ошибка сохранения временного скриншота для блока ${block.number}:`, error)
          }
        } else {
          // Если нет временного скриншота, создаем новый
          console.log(`🔄 Создание нового скриншота для блока ${block.number} (нет временного)...`)
          try {
            const tempCard = {
              id: block.id,
              number: block.number,
              title: block.title,
              html,
              hbs: sourceTemplate,
              json: sourceData,
              originalBlock: block,
              screenshot: undefined
            }

            await generateScreenshotForCard(tempCard)
            screenshotId = tempCard.screenshot
            console.log(`✅ Новый скриншот создан для блока ${block.number}: ${screenshotId}`)
          } catch (error) {
            console.error(`Ошибка создания нового скриншота для блока ${block.number}:`, error)
          }
        }
      }

      // Определяем block_type и collection для этого блока
      const blockType = (selectedGroup.block_type && selectedGroup.block_type.length > 0)
        ? selectedGroup.block_type
        : formData.value.block_type

      const collection = (selectedGroup.collection && selectedGroup.collection.length > 0)
        ? selectedGroup.collection
        : formData.value.collection

      const blockData = {
        number: `${formData.value.number}-${counter}`,
        title: `${formData.value.title}-${counter}`,
        block_type: blockType,
        collection: collection,
        json: sourceData,
        hbs: sourceTemplate,
        html: html,
        css: block.css || '',
        js: block.js || '',
        sketch: screenshotId,
        status: 'draft'
      }

      blocksToSave.push(blockData)
      analysisData.push({
        html: html,
        tempIndex: i
      })
    }

    // Сохраняем блоки в базу
    const savedBlocks = await createItems({
      collection: 'wblock_proto',
      items: blocksToSave
    })

    console.log(`Сохранено ${savedBlocks.length} блоков`)

    // Анализируем HTML и обновляем поля
    if (analysisData.length > 0) {
      try {
        const analysisRecords = savedBlocks.map((block: any, index: number) => ({
          id: block.id,
          html: analysisData[index].html
        }))

        const { results } = await $fetch('/api/batch-analyze-html', {
          method: 'POST',
          body: { records: analysisRecords }
        })

        // Обновляем блоки с результатами анализа
        for (const result of results) {
          await updateItem({
            collection: 'wblock_proto',
            id: result.id,
            item: {
              layout: result.layout,
              elements: result.elements,
              graphics: result.graphics,
              composition: result.treeStructure
            }
          })
        }

        console.log('Анализ HTML завершен')
      } catch (analysisError) {
        console.error('Ошибка анализа HTML:', analysisError)
      }
    }

    // Очищаем временные blob URL из памяти
    tempScreenshots.value.forEach(url => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url)
      }
    })

    // Очищаем выбранные блоки и временные скриншоты
    selectedBlocks.value = []
    tempScreenshots.value.clear()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранено ${savedBlocks.length} блоков`,
      life: 3000
    })

  } catch (error) {
    console.error('Error saving blocks:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блоки',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}

// Обработчики событий от компонентов
const onCombinationSaved = (data: any) => {
  toast.add({
    severity: 'success',
    summary: 'Комбинация сохранена',
    detail: `Создано ${data.savedCount} новых блоков`,
    life: 3000
  })
  loadBlocks() // Перезагружаем данные
}

const handleContentApplied = (data: any) => {
  contentBlocks.value = data.modifiedBlocks
  selectedContentBlocks.value = []

  toast.add({
    severity: 'info',
    summary: 'Контент применен',
    detail: `Создано ${data.totalVariants} вариантов блоков`,
    life: 3000
  })
}

const generateContentScreenshots = async () => {
  generating.value = true
  try {
    for (const block of contentBlocks.value) {
      if (!block.screenshot) {
        const screenshotUrl = await generateContentBlockScreenshot(block)
        if (screenshotUrl) {
          block.screenshot = screenshotUrl
        }
      }
    }
    toast.add({
      severity: 'success',
      summary: 'Скриншоты сгенерированы',
      detail: `Обработано ${contentBlocks.value.length} блоков`,
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка генерации скриншотов контента:', error)
  } finally {
    generating.value = false
  }
}

const generateContentBlockScreenshot = async (block: any): Promise<string | null> => {
  try {
    const css = block.css || ''
    const js = block.js || ''
    const html = block.html || ''

    const fullHtml = '<!DOCTYPE html>' +
      '<html lang="en">' +
      '<head>' +
      '<meta charset="UTF-8">' +
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
      '<title>' + (block.title || '') + '</title>' +
      css +
      '</head>' +
      '<body>' +
      html +
      js +
      '</body>' +
      '</html>'

    const response = await $fetch('/api/capture-html-screenshot-temp', {
      method: 'POST',
      body: {
        html: fullHtml,
        width: 1400,
        height: 800
      },
      responseType: 'blob'
    })

    const tempUrl = URL.createObjectURL(response as Blob)
    return tempUrl

  } catch (error) {
    console.error(`Ошибка создания скриншота для ${block.id}:`, error)
    return null
  }
}

const saveSelectedContentBlocks = async () => {
  if (selectedContentBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите блоки для сохранения',
      life: 3000
    })
    return
  }

  saving.value = true

  try {
    const blocksToSave = []
    const selectedBlocks = contentBlocks.value.filter(block => selectedContentBlocks.value.includes(block.id))

    for (let i = 0; i < selectedBlocks.length; i++) {
      const block = selectedBlocks[i]
      const counter = String(i + 1).padStart(2, '0')

      // Сохраняем скриншот если есть
      let screenshotId = null
      if (block.screenshot && block.screenshot.startsWith('blob:')) {
        const filename = `wblock_gen2_content_${block.number || 'untitled'}_${counter}.png`
        screenshotId = await saveBlobToDirectus(block.screenshot, filename)
      }

      // Используем данные из оригинального блока
      const originalBlock = allBlocks.value.find(b => block.id.includes(b.id!)) || block

      const blockData = {
        number: block.number, // Используем уже сформированный number с префиксом
        title: block.title, // Используем уже сформированный title с префиксом
        block_type: originalBlock.block_type || formData.value.block_type || ['content'],
        collection: originalBlock.collection || formData.value.collection || [],
        html: block.html,
        hbs: block.hbs || originalBlock.hbs || '',
        css: originalBlock.css || '',
        js: originalBlock.js || '',
        json: JSON.stringify(block.contentData || {}),
        sketch: screenshotId,
        status: 'draft'
      }

      blocksToSave.push(blockData)
    }

    // Сохраняем блоки
    const savedBlocks = await createItems({
      collection: 'wblock_proto',
      items: blocksToSave
    })

    // Анализируем HTML для заполнения дополнительных полей
    const analysisRecords = savedBlocks.map((block: any) => ({
      id: block.id,
      html: block.html
    }))

    try {
      const response = await $fetch('/api/batch-analyze-html', {
        method: 'POST',
        body: { records: analysisRecords }
      }) as { results: any[] }

      // Обновляем с результатами анализа
      for (const result of response.results) {
        await updateItem({
          collection: 'wblock_proto',
          id: result.id,
          item: {
            composition: result.treeStructure || result.composition || [],
            layout: result.layoutTypes || result.layout || [],
            elements: result.elementTypes || result.elements || [],
            style: result.styleTypes || result.style || [],
            features: result.featureTypes || result.features || [],
            graphics: result.graphicsInfo || result.graphics || []
          }
        })
      }
    } catch (analysisError) {
      console.warn('Анализ HTML не удался, блоки сохранены без дополнительных полей:', analysisError)
    }

    // Очищаем временные данные
    selectedBlocks.forEach(block => {
      if (block.screenshot && block.screenshot.startsWith('blob:')) {
        URL.revokeObjectURL(block.screenshot)
      }
    })

    selectedContentBlocks.value = []

    toast.add({
      severity: 'success',
      summary: 'Блоки сохранены',
      detail: `Сохранено ${savedBlocks.length} блоков с контентом`,
      life: 3000
    })

    await loadBlocks()

  } catch (error) {
    console.error('Ошибка сохранения блоков с контентом:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блоки',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}

// Методы для фильтров
const clearAllFilters = () => {
  filters.value = {
    block_type: [],
    collection: [],
    concept: [],
    elements: [],
    layout: [],
    style: [],
    features: []
  }
}

const toggleShowOnlySelected = () => {
  showOnlySelected.value = !showOnlySelected.value
}

const clearAllSelections = () => {
  selectedBlocks.value = []
  selectedForCombination.value = []
  selectedContentBlocks.value = []
}

// Методы для проверки состояния
const isBlockSelected = (block: WBlock) => {
  return selectedBlocks.value.some(s => s.id === block.id)
}

const isBlockSelectedForCombination = (block: WBlock) => {
  return selectedForCombination.value.some(s => s.id === block.id)
}

const getBlockCardStyle = (block: WBlock) => {
  if (isBlockSelected(block)) {
    return 'border: 2px solid #3b82f6; background-color: rgba(59, 130, 246, 0.1);'
  }
  if (isBlockSelectedForCombination(block)) {
    return 'border: 2px solid #10b981; background-color: rgba(16, 185, 129, 0.1);'
  }
  return ''
}

const handleBlockClick = (block: WBlock) => {
  if (currentMode.value === 'combination') {
    toggleCombinationSelection(block)
  } else if (currentMode.value === 'prototyping') {
    togglePrototypingSelection(block)
  } else {
    toggleBlockSelection(block)
  }
}

// Пагинация
const onPageChange = (event: any) => {
  first.value = event.first
  rowsPerPage.value = event.rows
}

// Методы для режима прототипирования
const createPrototypingGroups = () => {
  selectedForPrototyping.value.forEach(block => {
    const group = createPrototypingGroup(block)
    prototypingGroups.value.push(group)
  })
}

const createPrototypingGroup = (sourceBlock: WBlock) => {
  const sourceJson = sourceBlock.json || '{}'
  let parsedJson = {}

  try {
    parsedJson = JSON.parse(sourceJson)
  } catch (error) {
    console.error('Error parsing source JSON:', error)
  }

  const editableFields = Object.entries(parsedJson).map(([key, value]) => ({
    key,
    value: '', // По умолчанию пустое значение для удобного заполнения
    originalValue: typeof value === 'object' ? JSON.stringify(value) : String(value),
    selectedType: '',
    selectedValue: ''
  }))

  return {
    id: Date.now() + Math.random(),
    sourceBlock,
    sourceBlockId: sourceBlock.id,
    sourceJson,
    editableFields,
    finalJson: JSON.stringify(Object.fromEntries(Object.keys(parsedJson).map(key => [key, ''])), null, 2),
    finalHbs: sourceBlock.hbs || '', // Инициализируем HBS для редактирования
    previewImage: null,
    generating: false,
    saving: false,
    blockType: sourceBlock.block_type || [],
    collection: sourceBlock.collection || [],
    number: '',
    title: `${sourceBlock.title} (прототип)`
  }
}

const addPrototypingGroup = () => {
  if (selectedForPrototyping.value.length > 0) {
    const group = createPrototypingGroup(selectedForPrototyping.value[0])
    prototypingGroups.value.push(group)
  }
}

const removePrototypingGroup = (index: number) => {
  prototypingGroups.value.splice(index, 1)
}

const moveGroupLeft = (index: number) => {
  if (index > 0) {
    const group = prototypingGroups.value.splice(index, 1)[0]
    prototypingGroups.value.splice(index - 1, 0, group)
  }
}

const moveGroupRight = (index: number) => {
  if (index < prototypingGroups.value.length - 1) {
    const group = prototypingGroups.value.splice(index, 1)[0]
    prototypingGroups.value.splice(index + 1, 0, group)
  }
}

const updateSourceBlock = (group: any) => {
  const block = allBlocks.value.find(b => b.id === group.sourceBlockId)
  if (block) {
    group.sourceBlock = block
    group.sourceJson = block.json || '{}'
    group.finalHbs = block.hbs || '' // Инициализируем HBS для редактирования
    updateEditableFields(group)
    console.log('✅ Исходный блок обновлен:', block.number, block.title)
  }
}

const updateEditableFields = (group: any) => {
  try {
    const parsedJson = JSON.parse(group.sourceJson)
    group.editableFields = Object.entries(parsedJson).map(([key, value]) => ({
      key,
      value: '', // По умолчанию пустое значение для удобного заполнения
      originalValue: typeof value === 'object' ? JSON.stringify(value) : String(value),
      selectedType: '',
      selectedValue: ''
    }))

    // Инициализируем finalJson с пустыми значениями
    const emptyJsonObj: any = {}
    group.editableFields.forEach((field: any) => {
      emptyJsonObj[field.key] = ''
    })
    group.finalJson = JSON.stringify(emptyJsonObj, null, 2)

    console.log('✅ Поля инициализированы с пустыми значениями')
  } catch (error) {
    console.error('Error parsing JSON:', error)
  }
}

const updateGroupJson = (group: any) => {
  try {
    const jsonObj: Record<string, any> = {}

    group.editableFields.forEach((field: any) => {
      if (field.key && field.value) {
        try {
          jsonObj[field.key] = JSON.parse(field.value)
        } catch {
          jsonObj[field.key] = field.value
        }
      }
    })

    group.finalJson = JSON.stringify(jsonObj, null, 2)

    // Обновляем data_structure отдельно (для сохранения в Directus)
    updateDataStructure(group)

    // Автоматически обновляем HTML страницы
    generatePageHtml()
  } catch (error) {
    console.error('Error updating group JSON:', error)
  }
}

const updateDataStructure = (group: any) => {
  const dataStructure: string[] = []

  group.editableFields.forEach((field: any) => {
    if (field.key && field.selectedType) {
      dataStructure.push(`${field.key}: ${field.selectedType} - ${field.selectedValue || 'default'}`)
    }
  })

  group.data_structure = dataStructure.join('\n')
}

const updateFieldsFromJson = (group: any) => {
  try {
    const parsedJson = JSON.parse(group.finalJson)
    group.editableFields.forEach((field: any) => {
      if (Object.prototype.hasOwnProperty.call(parsedJson, field.key)) {
        const value = parsedJson[field.key]
        field.value = typeof value === 'object' ? JSON.stringify(value) : String(value)
      }
    })
  } catch (error) {
    console.error('Error updating fields from JSON:', error)
  }
}

const getContentTypeOptions = () => {
  const types = new Set<string>()
  contentPrimitives.value.forEach((item: any) => {
    types.add(item.type)
  })
  return Array.from(types).map(type => ({ label: type, value: type }))
}

const getContentValueOptions = (type: string) => {
  return contentPrimitives.value
    .filter((item: any) => item.type === type)
    .map((item: any) => ({ label: item.value, value: item.value }))
}

const updateFieldValues = (group: any, fieldIndex: number) => {
  const field = group.editableFields[fieldIndex]
  // При выборе типа selectedValue остается пустым, пользователь должен выбрать значение вручную
  field.selectedValue = ''
  // field.value не изменяется автоматически
}

const updateFieldValue = (group: any, fieldIndex: number) => {
  const field = group.editableFields[fieldIndex]
  // Заполняем field.value только если selectedValue выбран пользователем
  if (field.selectedValue && field.selectedValue !== '') {
    field.value = field.selectedValue
    updateGroupJson(group)
  }
}

const isImageUrl = (url: string) => {
  return url && (url.includes('http') || url.includes('data:image')) &&
         (url.includes('.jpg') || url.includes('.png') || url.includes('.gif') ||
          url.includes('.webp') || url.includes('.svg') || url.includes('placeholder'))
}

// Функции для проверки актуальности скриншотов групп
const generateGroupHash = (group: any): string => {
  const data = {
    sourceBlockId: group.sourceBlockId,
    finalJson: group.finalJson || '{}',
    finalHbs: group.finalHbs || ''
  }
  // Используем простой хеш вместо btoa для избежания проблем с кодировкой
  const jsonString = JSON.stringify(data)
  let hash = 0
  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Конвертируем в 32-битное целое
  }
  return hash.toString()
}

const isGroupScreenshotActual = (groupId: string, currentHash: string): boolean => {
  const storedHash = groupDataHashes.value.get(groupId)
  return storedHash === currentHash && tempGroupScreenshots.value.has(groupId)
}

const updateGroupHash = (groupId: string, hash: string) => {
  groupDataHashes.value.set(groupId, hash)
}

// Функция для генерации общего HTML страницы (по паттерну wblock-page-gen)
const generatePageHtml = () => {
  const htmlParts: string[] = []
  const cssParts: string[] = []
  const jsParts: string[] = []

  // Собираем HTML, CSS и JS из всех групп
  prototypingGroups.value.forEach(group => {
    if (group.sourceBlock && group.finalJson) {
      try {
        // Генерируем HTML из HBS и JSON
        const hbsTemplate = group.finalHbs || group.sourceBlock.hbs || ''
        const template = handlebars.compile(hbsTemplate)
        const jsonData = JSON.parse(group.finalJson)
        const compiledHtml = template(jsonData)

        htmlParts.push(compiledHtml)

        // Собираем уникальные CSS строки
        if (group.sourceBlock.css) {
          const cssLines = group.sourceBlock.css.split('\n').filter(line => line.trim())
          cssLines.forEach(line => {
            if (!cssParts.includes(line)) {
              cssParts.push(line)
            }
          })
        }

        // Собираем уникальные JS строки
        if (group.sourceBlock.js) {
          const jsLines = group.sourceBlock.js.split('\n').filter(line => line.trim())
          jsLines.forEach(line => {
            if (!jsParts.includes(line)) {
              jsParts.push(line)
            }
          })
        }
      } catch (error) {
        console.error('Ошибка генерации HTML для группы:', group.id, error)
      }
    }
  })

  pageHtml.value = htmlParts.join('\n')
  pageCss.value = cssParts.join('\n')
  pageJs.value = jsParts.join('\n')

  console.log('✅ HTML страницы сгенерирован:', {
    htmlLength: pageHtml.value.length,
    cssLines: cssParts.length,
    jsLines: jsParts.length
  })
}

// Функция получения полного HTML для iframe (по паттерну wblock-page-gen)
const getFullPageHtml = () => {
  const title = formData.value.title || 'Прототип страницы'
  const css = pageCss.value || ''
  const html = pageHtml.value || ''
  const js = pageJs.value || ''

  let result = '<!DOCTYPE html>\n'
  result += '<html lang="en">\n'
  result += '<head>\n'
  result += '<meta charset="UTF-8">\n'
  result += '<meta name="viewport" content="width=device-width, initial-scale=1.0">\n'
  result += '<title>' + title + '</title>\n'
  
  result += css + '\n'
 
  result += '</head>\n'
  result += '<body>\n'
  result += html + '\n'
  
  result += js + '\n'
  
  result += '</body>\n'
  result += '</html>'

  return result
}

// Функция анализа HTML через API (по паттерну wblock-page-gen)
const analyzeHtmlContent = async (htmlContent: string) => {
  try {
    const { layout, elements, graphics, features, treeStructure } = await $fetch(
      '/api/analyze-html',
      {
        method: 'POST',
        body: { html: htmlContent },
      },
    )

    return {
      composition: treeStructure || '',
      layout: layout || [],
      elements: elements || [],
      graphics: graphics || [],
      features: features || []
    }
  } catch (error) {
    console.error('Error analyzing HTML:', error)
    return {
      composition: '',
      layout: [],
      elements: [],
      graphics: [],
      features: []
    }
  }
}

// Функция создания скриншота HTML контента (по паттерну wblock-page-gen)
const generateScreenshot = async (htmlContent: string, filename: string = 'screenshot') => {
  try {
    console.log(`📸 Создание скриншота: ${filename}...`)

    const response = await $fetch('/api/capture-html-screenshot', {
      method: 'POST',
      body: {
        html: htmlContent,
        filename: filename,
        width: 1400,
        height: 800
      }
    })

    console.log(`✅ Скриншот создан: ${response.filename}, ID: ${response.fileId}`)
    return response.fileId
  } catch (error) {
    console.error(`❌ Ошибка создания скриншота ${filename}:`, error)
    throw error
  }
}

const generatePreview = async (group: any) => {
  group.generating = true
  try {
    // Генерируем хеш текущих данных группы
    const currentHash = generateGroupHash(group)

    // Проверяем, есть ли актуальный скриншот
    if (isGroupScreenshotActual(group.id, currentHash)) {
      console.log('✅ Используем актуальный скриншот для группы:', group.id)
      const existingUrl = tempGroupScreenshots.value.get(group.id)
      if (existingUrl) {
        group.previewImage = existingUrl
        group.previewSize = '1400x800'
        group.previewDate = new Date().toLocaleString()
      }
      return
    }

    // Генерируем HTML из HBS шаблона и JSON данных
    const sourceBlock = group.sourceBlock
    if (!sourceBlock) {
      throw new Error('Нет исходного блока для генерации')
    }

    // Используем отредактированный HBS или исходный
    const hbsTemplate = group.finalHbs || sourceBlock.hbs
    if (!hbsTemplate) {
      throw new Error('Нет HBS шаблона для генерации')
    }

    // Компилируем HBS с новыми данными
    const template = handlebars.compile(hbsTemplate)
    const jsonData = JSON.parse(group.finalJson)
    const compiledHtml = template(jsonData)

    // Создаем полный HTML для скриншота
    const cssContent = sourceBlock.css || ''
    const jsContent = sourceBlock.js || ''
    const fullHtml = [
      '<!DOCTYPE html>',
      '<html>',
      '<head>',
      '<meta charset="UTF-8">',
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">',
      
      cssContent,
      
      '</head>',
      '<body>',
      compiledHtml,
      
      jsContent,
      
      '</body>',
      '</html>'
    ].join('')

    // Генерируем временный скриншот (по паттерну успешных функций)
    const response = await $fetch('/api/capture-html-screenshot-temp', {
      method: 'POST',
      body: {
        html: fullHtml,
        width: 1400,
        height: 800
      },
      responseType: 'blob' // Важно! Получаем blob
    })

    // Создаем временный URL из blob (по паттерну tempScreenshots)
    const tempUrl = URL.createObjectURL(response as Blob)
    console.log('📸 Временный URL создан:', tempUrl)

    // Сохраняем временный скриншот и обновляем хеш
    group.previewImage = tempUrl
    group.previewSize = '1400x800' // Фиксированный размер
    group.previewDate = new Date().toLocaleString()

    tempGroupScreenshots.value.set(group.id, tempUrl)
    updateGroupHash(group.id, currentHash)

    console.log('✅ Новый скриншот создан для группы:', group.id, 'URL:', tempUrl)
  } catch (error) {
    console.error('Error generating preview:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сгенерировать превью'
    })
  } finally {
    group.generating = false
  }
}

const savePrototype = async (group: any) => {
  group.saving = true
  try {
    const sourceBlock = group.sourceBlock
    if (!sourceBlock) {
      throw new Error('Нет исходного блока')
    }

    // Генерируем HTML из HBS и JSON
    const hbsTemplate = group.finalHbs || sourceBlock.hbs || ''
    const template = handlebars.compile(hbsTemplate)
    const jsonData = JSON.parse(group.finalJson)
    const compiledHtml = template(jsonData)

    // Анализируем HTML через API (по паттерну wblock-page-gen)
    console.log('🔍 Анализируем HTML для прототипа...')
    const analysisResult = await analyzeHtmlContent(compiledHtml)
    console.log('✅ Результат анализа:', analysisResult)

    // Генерируем номер и название с учетом глобальных настроек
    const groupIndex = prototypingGroups.value.findIndex(g => g.id === group.id) + 1
    const groupNumber = groupIndex.toString().padStart(2, '0')

    const finalNumber = globalPrototypingSettings.value.number
      ? `${globalPrototypingSettings.value.number}-${groupNumber}`
      : group.number || `${sourceBlock.number}_proto_${groupNumber}`

    const finalTitle = globalPrototypingSettings.value.title
      ? `${globalPrototypingSettings.value.title} - ${groupNumber}`
      : group.title || `${sourceBlock.title} (прототип ${groupNumber})`

    // Создаем скриншот блока (по паттерну wblock-page-gen)
    console.log('📸 Создание скриншота для прототипа...')
    let blockSketchFileId = null

    try {
      // Генерируем полный HTML для скриншота блока
      const blockFullHtml = [
        '<!DOCTYPE html>',
        '<html lang="en">',
        '<head>',
        '<meta charset="UTF-8">',
        '<meta name="viewport" content="width=device-width, initial-scale=1.0">',
        `<title>${finalTitle}</title>`,
        sourceBlock.css || '',
        '</head>',
        '<body>',
        compiledHtml,
        sourceBlock.js || '',
        '</body>',
        '</html>'
      ].join('')

      const blockFilename = `block_${finalNumber}_${finalTitle.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`
      blockSketchFileId = await generateScreenshot(blockFullHtml, blockFilename)
      console.log('✅ Скриншот прототипа создан:', blockSketchFileId)
    } catch (screenshotError) {
      console.error('⚠️ Ошибка создания скриншота прототипа:', screenshotError)
      // Продолжаем сохранение без скриншота
    }

    // Подготавливаем данные для сохранения в wblock_proto (используем pageCss и pageJs)
    const blockProtoData = {
      number: finalNumber,
      title: finalTitle,
      description: sourceBlock.description,
      html: compiledHtml,
      css: pageCss.value, // Используем общий CSS страницы
      js: pageJs.value, // Используем общий JS страницы
      hbs: hbsTemplate, // Используем отредактированный HBS
      json: group.finalJson,
      data_structure: group.data_structure, // Добавляем data_structure
      block_type: group.block_type?.length > 0 ? group.block_type : globalPrototypingSettings.value.block_type,
      collection: group.collection?.length > 0 ? group.collection : globalPrototypingSettings.value.collection,
      composition: analysisResult.composition,
      layout: analysisResult.layout,
      elements: analysisResult.elements,
      graphics: analysisResult.graphics,
      features: analysisResult.features,
      status: group.status || 'draft',
      sketch: blockSketchFileId // Добавляем скриншот блока
    }

    // Сохраняем в wblock_proto
    const { createItems } = useDirectusItems()
    const savedBlock = await createItems({
      collection: 'wblock_proto',
      items: [blockProtoData]
    })

    const blockId = Array.isArray(savedBlock) ? savedBlock[0].id : savedBlock.id
    console.log('✅ Прототип сохранен с ID:', blockId)

    // Создаем запись в wjson с аналогичным title
    const existingWjson = await $fetch(`http://localhost:8055/items/wjson?filter[title][_eq]=${encodeURIComponent(group.title)}`)

    if (!existingWjson.data || existingWjson.data.length === 0) {
      const wjsonData = {
        art: blockProtoData.number,
        title: group.title,
        description: sourceBlock.description,
        json: group.finalJson,
        tags: group.blockType,
        collection: group.collection
      }

      const savedWjson = await createItems({
        collection: 'wjson',
        items: [wjsonData]
      })

      const wjsonId = Array.isArray(savedWjson) ? savedWjson[0].id : savedWjson.id

      // Создаем M2M связь через wblock_proto_wjson
      await createItems({
        collection: 'wblock_proto_wjson',
        items: [{
          wblock_proto_id: blockId,
          wjson_id: wjsonId
        }]
      })
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Прототип "${group.title}" сохранен`
    })

    console.log('Prototype saved successfully:', blockId)

    // Возвращаем ID блока для связывания
    return blockId
  } catch (error) {
    console.error('Error saving prototype:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить прототип'
    })
  } finally {
    group.saving = false
  }
}

const saveAllPrototypes = async () => {
  prototypingLoading.value = true
  try {
    for (const group of prototypingGroups.value) {
      await savePrototype(group)
    }
  } catch (error) {
    console.error('Error saving all prototypes:', error)
  } finally {
    prototypingLoading.value = false
  }
}

const loadContentPrimitives = async () => {
  try {
    const response = await fetch('/data/content-primitives.json')
    contentPrimitives.value = await response.json()
  } catch (error) {
    console.error('Error loading content primitives:', error)
  }
}

// Дополнительные методы для прототипирования
const addFieldToGroup = (group: any) => {
  group.editableFields.push({
    key: `field_${group.editableFields.length + 1}`,
    value: '',
    originalValue: '',
    selectedType: '',
    selectedValue: ''
  })
  updateGroupJson(group)
}

const duplicateGroup = (group: any, groupIndex: number) => {
  const newGroup = {
    ...group,
    id: Date.now() + Math.random(),
    title: `${group.title} (копия)`,
    previewImage: null,
    generating: false,
    saving: false
  }
  prototypingGroups.value.splice(groupIndex + 1, 0, newGroup)
}

// Функция добавления новой группы прототипирования вручную
const addNewPrototypingGroup = () => {
  if (allBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Нет доступных блоков для создания группы'
    })
    return
  }

  // Берем первый доступный блок как основу
  const firstBlock = allBlocks.value[0]
  const newGroup = createPrototypingGroup(firstBlock)

  // Добавляем группу в конец списка
  prototypingGroups.value.push(newGroup)

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Новая группа добавлена'
  })

  console.log('✅ Новая группа добавлена:', newGroup.id)
}

const removeGroup = (groupIndex: number) => {
  prototypingGroups.value.splice(groupIndex, 1)
}

const moveGroupUp = (groupIndex: number) => {
  if (groupIndex > 0) {
    const group = prototypingGroups.value.splice(groupIndex, 1)[0]
    prototypingGroups.value.splice(groupIndex - 1, 0, group)
  }
}

const moveGroupDown = (groupIndex: number) => {
  if (groupIndex < prototypingGroups.value.length - 1) {
    const group = prototypingGroups.value.splice(groupIndex, 1)[0]
    prototypingGroups.value.splice(groupIndex + 1, 0, group)
  }
}

const getChangedFieldsCount = (group: any) => {
  return group.editableFields.filter((field: any) =>
    field.value !== field.originalValue
  ).length
}

// Быстрые переменные для прототипирования
const quickVariables = ref([
  { name: 'title' },
  { name: 'image' },
  { name: 'imageBackground' },
  { name: 'url' },
  { name: 'linkText' },
  { name: 'icon' },
  { name: 'text' },
  { name: 'excerpt' }
])

// Методы для работы с полями
const getVariableCount = (group: any, varName: string) => {
  return group.editableFields.filter((field: any) => {
    // Точное совпадение или с числовым суффиксом (image, image2, image3, но не imageBackground)
    return field.key === varName || /^[a-zA-Z]+\d+$/.test(field.key) && field.key.startsWith(varName) && field.key.slice(varName.length).match(/^\d+$/)
  }).length
}

const addQuickVariable = (group: any, varName: string) => {
  const count = getVariableCount(group, varName)
  const newKey = count === 0 ? varName : `${varName}${count + 1}`

  group.editableFields.push({
    key: newKey,
    value: '',
    originalValue: '',
    selectedType: '',
    selectedValue: ''
  })

  updateGroupJson(group)
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.add({
      severity: 'success',
      summary: 'Скопировано',
      detail: 'Текст скопирован в буфер обмена'
    })
  } catch (error) {
    console.error('Ошибка копирования:', error)
  }
}

const getChangeIndicator = (field: any) => {
  if (!field.originalValue) return ''
  return field.value !== field.originalValue ? 'text-orange-500' : 'text-green-500'
}

const getChangeCount = (field: any) => {
  if (!field.originalValue) return ''
  const diff = field.value.length - field.originalValue.length
  return diff > 0 ? `+${diff}` : diff < 0 ? `${diff}` : ''
}

const getCharacterDifference = (field: any) => {
  if (!field.originalValue) return ''
  const diff = (field.value || '').length - field.originalValue.length
  if (diff > 0) return `+${diff}`
  if (diff < 0) return `${diff}`
  return '0'
}



const moveFieldUp = (group: any, index: number) => {
  if (index > 0) {
    const field = group.editableFields.splice(index, 1)[0]
    group.editableFields.splice(index - 1, 0, field)
    updateGroupJson(group)
  }
}

const moveFieldDown = (group: any, index: number) => {
  if (index < group.editableFields.length - 1) {
    const field = group.editableFields.splice(index, 1)[0]
    group.editableFields.splice(index + 1, 0, field)
    updateGroupJson(group)
  }
}

const duplicateField = (group: any, index: number) => {
  const field = group.editableFields[index]
  const newField = {
    ...field,
    key: `${field.key}_copy`
  }
  group.editableFields.splice(index + 1, 0, newField)
  updateGroupJson(group)
}

const removeField = (group: any, index: number) => {
  group.editableFields.splice(index, 1)
  updateGroupJson(group)
}

const getFilledFieldsCount = (group: any) => {
  return group.editableFields.filter((field: any) =>
    field.value && field.value.trim() !== ''
  ).length
}



// Метод для сохранения страницы и прототипов
const savePageAndPrototypes = async () => {
  if (!formData.value.number || !formData.value.title) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните номер и название страницы'
    })
    return
  }

  prototypingLoading.value = true

  try {
    // Генерируем HTML страницы
    generatePageHtml()

    // Создаем скриншот страницы (по паттерну wblock-page-gen)
    console.log('📸 Создание скриншота страницы...')
    let pageSketchFileId = null

    try {
      const pageFullHtml = getFullPageHtml()
      const pageFilename = `page_${formData.value.number}_${formData.value.title.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`
      pageSketchFileId = await generateScreenshot(pageFullHtml, pageFilename)
      console.log('✅ Скриншот страницы создан:', pageSketchFileId)
    } catch (screenshotError) {
      console.error('⚠️ Ошибка создания скриншота страницы:', screenshotError)
      // Продолжаем сохранение без скриншота
    }

    // Сохраняем страницу БЕЗ анализа HTML (только для блоков)
    const pageData = {
      number: formData.value.number,
      title: formData.value.title,
      html: pageHtml.value, // Собранный HTML из всех блоков
      css: pageCss.value, // Уникальные CSS строки
      js: pageJs.value, // Уникальные JS строки
      wpage_type: formData.value.wpage_type || [],
      tags: formData.value.tags || [],
      status: 'draft',
      sketch: pageSketchFileId // Добавляем скриншот страницы
    }

    const { createItems } = useDirectusItems()
    const savedPage = await createItems({
      collection: 'wpage',
      items: [pageData]
    })

    const pageId = Array.isArray(savedPage) ? savedPage[0].id : savedPage.id
    console.log('✅ Страница сохранена с ID:', pageId)

    // Сохраняем все прототипы и собираем их ID
    let savedCount = 0
    const blockIds: string[] = []

    for (const group of prototypingGroups.value) {
      if (group.title && group.finalJson) {
        const blockId = await savePrototype(group)
        if (blockId) {
          blockIds.push(blockId)
        }
        savedCount++
      }
    }

    // Создаем связи между страницей и блоками (по паттерну wblock-page-gen)
    if (blockIds.length > 0) {
      console.log('🔗 Создаем связи в wpage_wblock_proto...')

      const linkData = blockIds.map(blockId => ({
        wpage_id: pageId,
        wblock_proto_id: blockId
      }))

      await createItems({
        collection: 'wpage_wblock_proto',
        items: linkData
      })

      console.log('✅ Связи успешно созданы!')
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Страница и ${savedCount} прототипов сохранены и связаны`
    })

  } catch (error) {
    console.error('Ошибка сохранения:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить страницу и прототипы'
    })
  } finally {
    prototypingLoading.value = false
  }
}





// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadBlocks(),
    loadOptions(),
    loadSidebarOptions(),
    loadContentPrimitives()
  ])
})

// Загрузка опций для сайдбара (точная копия из wblock-proto2)
const loadSidebarOptions = async () => {
  try {
    // Загружаем опции из коллекции wblock_proto (как в wblock-proto2)
    const items = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: [
          'concept',
          'block_type',
          'elements',
          'layout',
          'style',
          'graphics',
          'collection',
          'features',
        ],
      },
    })

    if (Array.isArray(items)) {
      const concepts = new Set()
      const blockTypes = new Set()
      const elements = new Set()
      const layouts = new Set()
      const styles = new Set()
      const graphics = new Set()
      const collections = new Set()
      const features = new Set()

      items.forEach((item: any) => {
        item.concept?.forEach((c: any) => concepts.add(c))
        item.block_type?.forEach((t: any) => blockTypes.add(t))
        item.elements?.forEach((e: any) => elements.add(e))
        item.layout?.forEach((l: any) => layouts.add(l))
        item.style?.forEach((s: any) => styles.add(s))
        item.graphics?.forEach((g: any) => graphics.add(g))
        item.collection?.forEach((c: any) => collections.add(c))
        item.features?.forEach((f: any) => features.add(f))
      })

      conceptOptions.value = Array.from(concepts) as string[]
      blockTypeOptions.value = Array.from(blockTypes) as string[]
      elementOptions.value = Array.from(elements) as string[]
      layoutOptions.value = Array.from(layouts) as string[]
      styleOptions.value = Array.from(styles) as string[]
      graphicsOptions.value = Array.from(graphics) as string[]
      collectionOptions.value = Array.from(collections) as string[]
      featuresOptions.value = Array.from(features) as string[]
    }

    // Загружаем welem_proto отдельно (как в wblock-proto2)
    const elements = await getItems({
      collection: 'welem_proto',
      params: {
        limit: -1,
        fields: ['id', 'title', 'number'],
      },
    })

    if (Array.isArray(elements)) {
      welemOptions.value = elements.map((elem: any) => ({
        value: elem.id,
        label: elem.title ? `${elem.number} - ${elem.title}` : elem.id,
      }))
    }
  } catch (error) {
    console.error('Error loading sidebar options:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить справочники',
    })
  }
}
</script>

<style scoped>
/* Убираем все отступы между блоками на досках */
.board-blocks {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin: 0;
  padding: 0;
}

.board-block {
  margin: 0;
  padding: 0;
  border: none;
  display: block;
  line-height: 0;
}

.board-block img {
  display: block;
  margin: 0;
  padding: 0;
  border: none;
  line-height: 0;
}

/* Убираем отступы у Image компонента PrimeVue */
:deep(.p-image) {
  margin: 0;
  padding: 0;
  border: none;
  display: block;
  line-height: 0;
}

:deep(.p-image img) {
  display: block;
  margin: 0;
  padding: 0;
  border: none;
  line-height: 0;
}

/* Панель управления блоком */
.block-controls {
  position: absolute;
  top: 0;
  right: -30px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.group:hover .block-controls {
  opacity: 1;
}

.masonry-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 1rem;
  grid-auto-rows: min-content;
  align-items: start;
}

.my-editor {
  /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
  background: #f3f3f3;
  color: #666;

  /* you must provide font-family font-size line-height. Example: */
  font-family:
    Fira code,
    Fira Mono,
    Consolas,
    Menlo,
    Courier,
    monospace;
  font-size: 10px;
  line-height: 1.4;
  padding: 2px;
}

.my-editor .prism-editor__textarea:focus {
  outline: none;
}

.my-editor .prism-editor__container {
  border-radius: 4px;
}

.my-editor .prism-editor__editor {
  border-radius: 4px;
}

/* Стили для режимов работы */
.work-mode-button {
  transition: all 0.2s ease;
}

.work-mode-button:hover {
  transform: translateY(-1px);
}

/* Стили для карточек блоков */
.block-card {
  transition: all 0.2s ease;
}

.block-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Анимации для чекбоксов */
.checkbox-animation {
  transition: all 0.2s ease;
}

.checkbox-animation:hover {
  transform: scale(1.1);
}

/* Стили для превью контента */
.content-preview {
  max-height: 400px;
  overflow-y: auto;
}

.content-preview::-webkit-scrollbar {
  width: 6px;
}

.content-preview::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-preview::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-preview::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Стили для режима прототипирования */
.prototyping-mode {
  height: calc(100vh - 200px);
}

.prototyping-groups {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.prototyping-groups::-webkit-scrollbar {
  height: 8px;
}

.prototyping-groups::-webkit-scrollbar-track {
  background: #f7fafc;
}

.prototyping-groups::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.prototyping-groups::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.prototyping-group {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.compact-tabs .p-tabview-nav {
  padding: 0;
}

.compact-tabs .p-tabview-nav li {
  margin: 0;
}

.compact-tabs .p-tabview-nav li .p-tabview-nav-link {
  padding: 4px 8px;
  font-size: 10px;
}

/* Стили для режима прототипирования */
.prototyping-mode {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.prototyping-groups {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.prototyping-group {
  border: 2px solid #e5e7eb;
  transition: border-color 0.2s;
}

.prototyping-group:hover {
  border-color: #3b82f6;
}

.prototyping-columns {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 8px;
  min-height: 400px;
}

.prototyping-column {
  flex-shrink: 0;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
  overflow: hidden;
}

.prototyping-column h4 {
  background: #f3f4f6;
  margin: 0;
  padding: 8px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 11px;
  font-weight: 600;
}

/* Компактные поля в редакторе */
.prototyping-field {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: #f9fafb;
  margin-bottom: 4px;
}

.prototyping-field input {
  font-size: 8px !important;
  padding: 2px 4px !important;
  height: 18px !important;
  border: none !important;
  background: transparent !important;
}

.prototyping-field .p-dropdown {
  font-size: 7px !important;
  min-height: 18px !important;
}

.prototyping-field .p-button {
  width: 16px !important;
  height: 16px !important;
  padding: 0 !important;
  font-size: 8px !important;
}

/* Быстрые кнопки переменных */
.quick-variables {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  margin-bottom: 8px;
}

.quick-variables .p-button {
  font-size: 7px !important;
  height: 18px !important;
  padding: 0 6px !important;
}

/* Превью изображений */
.prototyping-preview {
  max-height: 120px;
  overflow: hidden;
  border-radius: 4px;
}

.prototyping-preview img {
  width: 100%;
  height: auto;
  object-fit: contain;
}

/* Статистика и счетчики */
.prototyping-stats {
  font-size: 7px;
  color: #6b7280;
  padding: 4px;
  background: #f9fafb;
  border-radius: 4px;
  margin-top: 8px;
}

/* Горизонтальная прокрутка */
.horizontal-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  padding-bottom: 8px;
}

.horizontal-scroll::-webkit-scrollbar {
  height: 6px;
}

.horizontal-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.horizontal-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.horizontal-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
