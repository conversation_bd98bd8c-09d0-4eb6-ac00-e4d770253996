<template>
  <div class="flex flex-col gap-4 p-1">
    <!-- Заголовок и режимы работы -->
    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border rounded-lg">
      <div>
        <h1 class="text-xl font-bold text-gray-800 dark:text-gray-200">Генератор элементов 2.0</h1>
        <p class="text-sm text-gray-600 dark:text-gray-400">Расширенные возможности генерации и комбинирования элементов</p>
      </div>
      <div class="flex gap-2">
        <Button
          v-for="mode in workModes"
          :key="mode.value"
          :label="mode.label"
          :class="[
            'text-xs px-3 py-2',
            currentMode === mode.value ? 'p-button-info' : 'p-button-outlined'
          ]"
          @click="setWorkMode(mode.value)"
        />
      </div>
    </div>

    <!-- Too<PERSON>bar с полями ввода и кнопками -->
    <div class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <InputText
        v-model="formData.number"
        placeholder="Номер элемента"
        class="w-28 text-xs"
        style="font-size: 11px"
      />
      <InputText
        v-model="formData.title"
        placeholder="Название элемента"
        class="flex-1 text-xs"
        style="font-size: 12px"
      />
      <MultiSelect
        v-model="formData.collection"
        :options="collectionOptions"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Выберите коллекции"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <Button
        v-tooltip.top="'Генерировать скриншоты'"
        icon="pi pi-camera"
        class="p-button-warning text-xs"
        :loading="generating"
        :disabled="!selectedElements.length"
        @click="generateScreenshots"
      />
      <Button
        v-tooltip.top="'Сохранить'"
        icon="pi pi-save"
        class="text-xs"
        :loading="saving"
        :disabled="!selectedElements.length"
        @click="saveSelectedElements"
      />
    </div>

    <!-- Панель режимов работы -->
    <div v-if="currentMode === 'combination'" class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      
      <div class="flex items-center gap-2 mb-3">
        <label class="text-sm font-medium">Выберите элементы для комбинирования (2+):</label>
        <Badge :value="selectedForCombination.length" severity="info" />
      </div>

      <!-- Компонент превью комбинаций -->
      <CombinationPreview
        v-if="selectedForCombination.length >= 2"
        :selected-elements="selectedForCombination"
        :collection-options="collectionOptions"
        @saved="onCombinationSaved"
      />

      <div v-else class="text-center py-8 text-surface-500">
        <i class="pi pi-objects-column text-4xl mb-4 block"/>
        <p>Выберите минимум 2 элемента для создания комбинации</p>
        <p class="text-sm">Используйте зеленые чекбоксы на карточках элементов</p>
      </div>

      
    </div>

    <div v-else-if="currentMode === 'variation'" class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <h3 class="text-lg font-semibold mb-3">Element Variation Generator</h3>

      <!-- Визуальный конструктор вариаций -->
      <VariationConstructor
        :selected-elements="selectedElements"
        @variation-applied="handleVariationApplied"
      />

      <!-- Показ созданных вариаций -->
      <div v-if="generatedVariations.length > 0" class="mt-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-md font-semibold">Созданные вариации ({{ generatedVariations.length }})</h4>
          <div class="flex gap-2">
            <Button
              label="Генерировать скриншоты"
              icon="pi pi-camera"
              class="text-xs"
              :loading="generating"
              @click="generateVariationScreenshots"
            />
            <Button
              label="Сохранить выбранные"
              icon="pi pi-save"
              class="text-xs"
              :loading="saving"
              :disabled="!selectedVariations.length"
              @click="saveSelectedVariations"
            />
          </div>
        </div>

        <!-- Сетка вариаций -->
        <div class="grid grid-cols-3 gap-4">
          <Card
            v-for="variation in generatedVariations"
            :key="variation.id"
            class="overflow-hidden cursor-pointer transition-all duration-200"
            :class="{
              'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedVariations.includes(variation.id)
            }"
            @click="toggleVariationSelection(variation.id)"
          >
            <template #header>
              <div class="relative">
                <Image
                  v-if="variation.screenshot"
                  :src="variation.screenshot"
                  alt="Вариация"
                  class="w-full h-auto object-contain"
                  preview
                />
                <div v-else class="w-full h-32 bg-surface-100 dark:bg-surface-800 flex items-center justify-center">
                  <i class="pi pi-image text-2xl text-surface-400"/>
                </div>
                <Checkbox
                  :model-value="selectedVariations.includes(variation.id)"
                  binary
                  class="absolute top-2 right-2 bg-white rounded shadow"
                  @click.stop
                  @change="toggleVariationSelection(variation.id)"
                />
              </div>
            </template>
            <template #content>
              <div class="px-2 py-1">
                <div class="text-sm font-medium">{{ variation.title }}</div>
                <div class="text-xs text-surface-500">{{ variation.description }}</div>
              </div>
            </template>
          </Card>
        </div>
      </div>
    </div>

    <div v-else-if="currentMode === 'theming'" class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <h3 class="text-lg font-semibold mb-3">Bulk Element Theming</h3>

      <!-- Визуальный конструктор тем -->
      <ThemeConstructor
        :selected-elements="selectedElements"
        @theme-applied="handleThemeApplied"
      />

      <!-- Показ элементов с примененными темами -->
      <div v-if="themedElements.length > 0" class="mt-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-md font-semibold">Элементы с темами ({{ themedElements.length }})</h4>
          <div class="flex gap-2">
            <Button
              label="Генерировать скриншоты"
              icon="pi pi-camera"
              class="text-xs"
              :loading="generating"
              @click="generateThemedScreenshots"
            />
            <Button
              label="Сохранить выбранные"
              icon="pi pi-save"
              class="text-xs"
              :loading="saving"
              :disabled="!selectedThemedElements.length"
              @click="saveSelectedThemedElements"
            />
          </div>
        </div>

        <!-- Сетка элементов с темами -->
        <div class="grid grid-cols-3 gap-4">
          <Card
            v-for="themedElement in themedElements"
            :key="themedElement.id"
            class="overflow-hidden cursor-pointer transition-all duration-200"
            :class="{
              'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedThemedElements.includes(themedElement.id)
            }"
            @click="toggleThemedElementSelection(themedElement.id)"
          >
            <template #header>
              <div class="relative">
                <Image
                  v-if="themedElement.screenshot"
                  :src="themedElement.screenshot"
                  alt="Элемент с темой"
                  class="w-full h-auto object-contain"
                  preview
                />
                <div v-else class="w-full h-32 bg-surface-100 dark:bg-surface-800 flex items-center justify-center">
                  <i class="pi pi-image text-2xl text-surface-400"/>
                </div>
                <Checkbox
                  :model-value="selectedThemedElements.includes(themedElement.id)"
                  binary
                  class="absolute top-2 right-2 bg-white rounded shadow"
                  @click.stop
                  @change="toggleThemedElementSelection(themedElement.id)"
                />
              </div>
            </template>
            <template #content>
              <div class="px-2 py-1">
                <div class="text-sm font-medium">{{ themedElement.title }}</div>
                <div class="text-xs text-surface-500">{{ themedElement.themeName }}</div>
              </div>
            </template>
          </Card>
        </div>
      </div>
    </div>

    <div v-else-if="currentMode === 'content'" class="p-3 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <!-- Интеллектуальный конструктор контента -->
      <ContentConstructor
        :selected-elements="selectedElements"
        @content-applied="handleContentApplied"
      />

      <!-- Показ элементов с контентом -->
      <div v-if="contentElements.length > 0" class="mt-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-md font-semibold">Элементы с контентом ({{ contentElements.length }})</h4>
          <div class="flex gap-2">
            <Button
              label="Генерировать скриншоты"
              icon="pi pi-camera"
              class="text-xs"
              :loading="generating"
              @click="generateContentScreenshots"
            />
            <Button
              label="Сохранить выбранные"
              icon="pi pi-save"
              class="text-xs"
              :loading="saving"
              :disabled="!selectedContentElements.length"
              @click="saveSelectedContentElements"
            />
          </div>
        </div>

        <!-- Сетка элементов с контентом -->
        <div class="masonry-grid gap-4">
          <Card
            v-for="contentElement in contentElements"
            :key="contentElement.id"
            class="overflow-hidden cursor-pointer transition-all duration-200"
            :class="{
              'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedContentElements.includes(contentElement.id)
            }"
            @click="toggleContentElementSelection(contentElement.id)"
          >
            <template #header>
              <div class="relative">
                <Image
                  v-if="contentElement.screenshot"
                  :src="contentElement.screenshot"
                  alt="Элемент с контентом"
                  class="w-full h-auto object-contain"
                  preview
                />
                <div v-else class="w-full h-32 bg-surface-100 dark:bg-surface-800 flex items-center justify-center">
                  <i class="pi pi-image text-2xl text-surface-400"/>
                </div>
                <div class="absolute top-2 right-2 flex gap-1">
                <Checkbox
                  :model-value="selectedContentElements.includes(contentElement.id)"
                  binary
                  class="absolute top-2 right-2 bg-white rounded shadow"
                  @click.stop
                  @change="toggleContentElementSelection(contentElement.id)"
                />
              </div>
              </div>
            </template>
            <template #content>
              <div class="px-2 py-1">
                <div class="text-sm font-medium">{{ contentElement.title }}</div>
                <!-- <div class="text-xs text-surface-500">{{ contentElement.contentType }}</div> -->
                <!-- <div v-if="contentElement.contentSource?.title" class="text-xs text-blue-600">
                  Источник: {{ contentElement.contentSource.title }}
                </div> -->
                <div v-if="contentElement.contentMapping" class="text-xs text-gray-500">
                  Маппинг: {{ Object.keys(contentElement.contentMapping).length }} переменных
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>
    </div>

    <!-- Стандартные группы JSON/HBS (как в базовой версии) -->
    <div v-else class="flex flex-col gap-2">
      <div
        v-for="group in dataGroups"
        :key="group.id"
        class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg"
      >
        <!-- Номер группы -->
        <div class="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-sm font-medium">
          {{ group.id }}
        </div>

        <!-- JSON редактор -->
        <div class="w-[30%]">
          <ClientOnly>
            <PrismEditor
              v-model="group.jsonContent"
              class="w-full text-xs p-2 border rounded my-editor h-[180px] overflow-auto max-h-[180px]"
              :highlight="highlightJson"
              placeholder="Введите JSON данные"
              line-numbers
            />
          </ClientOnly>
        </div>

        <!-- HBS редактор -->
        <div class="flex-1">
          <ClientOnly>
            <PrismEditor
              v-model="group.hbsContent"
              class="w-full text-xs p-2 border rounded my-editor h-[180px] overflow-auto max-h-[180px]"
              :highlight="highlightHtml"
              placeholder="Введите HBS шаблон"
              line-numbers
            />
          </ClientOnly>
        </div>

        <!-- Bootstrap Wrapper селекторы для всех групп -->
        <div class="flex flex-col gap-2 w-48">
          <Dropdown
            v-model="group.wrapperRowType"
            :options="rowTypeOptions"
            option-label="label"
            option-value="value"
            class="text-xs"
            placeholder="Тип контейнера"
            :pt="{
              root: { class: 'text-xs' },
              input: { class: 'text-xs p-1' },
              panel: { class: 'text-xs' },
              item: { class: 'text-xs p-1' }
            }"
          />
          <Dropdown
            v-model="group.wrapperColWidth"
            :options="colWidthOptions"
            option-label="label"
            option-value="value"
            class="text-xs"
            placeholder="Ширина колонки"
            :pt="{
              root: { class: 'text-xs' },
              input: { class: 'text-xs p-1' },
              panel: { class: 'text-xs' },
              item: { class: 'text-xs p-1' }
            }"
          />
          <MultiSelect
            v-if="group.id > 1"
            v-model="group.collection"
            :options="collectionOptions"
            display="chip"
            class="text-xs"
            filter
            placeholder="Коллекция"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
          <!-- Кнопка добавления новой группы (только для последней группы) -->
          <Button
            v-if="group.id === dataGroups[dataGroups.length - 1].id"
            v-tooltip.top="'Добавить новую группу'"
            icon="pi pi-plus"
            class="p-button-outlined text-xs"
            @click="addNewGroup"
          />
        </div>
      </div>
    </div>

    <!-- Фильтры -->
    <div class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <MultiSelect
        v-model="filters.elem_type"
        :options="filterOptions.elem_type"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Тип элемента"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.collection"
        :options="filterOptions.collection"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Коллекция"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <Button
        v-tooltip.top="'Сбросить все фильтры'"
        icon="pi pi-times"
        class="p-button-outlined text-xs"
        @click="clearAllFilters"
      />
      <Button
        v-tooltip.top="'Показать только выбранные'"
        icon="pi pi-filter"
        class="p-button-outlined text-xs"
        :class="{ 'p-button-info': showOnlySelected }"
        @click="toggleShowOnlySelected"
      />
      <Button
        v-tooltip.top="'Снять все отметки'"
        icon="pi pi-check-square"
        class="p-button-outlined text-xs"
        @click="clearAllSelections"
      />
    </div>

    <!-- Элементы с пагинацией -->
    <div v-if="filteredElements.length > 0" class="flex flex-col gap-4">
      <!-- Информация о количестве элементов -->
      <div class="text-sm text-surface-600 dark:text-surface-400">
        Найдено элементов: {{ filteredElements.length }}
        <span v-if="selectedElements.length > 0">
          | Выбрано: {{ selectedElements.length }}
        </span>
        <span v-if="currentMode === 'combination' && selectedForCombination.length > 0">
          | Для комбинирования: {{ selectedForCombination.length }}
        </span>
      </div>

      <!-- Сетка элементов -->
      <div class="masonry-grid gap-4">
        <Card
          v-for="element in paginatedElements"
          :key="element.id"
          class="group overflow-hidden cursor-pointer transition-all duration-50"
          :class="{
            'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 border-blue-500': isElementSelected(element),
            'ring-2 ring-green-500 bg-green-50 dark:bg-green-900/20 border-green-500': isElementSelectedForCombination(element),
            'hover:shadow-lg': !isElementSelected(element) && !isElementSelectedForCombination(element)
          }"
          :style="getElementCardStyle(element)"
          style="background-color: #f9f9f9;"
          @click="handleElementClick(element)"
        >
          <template #header>
            <div class="relative">
              <!-- ИСПРАВЛЕННОЕ: Отцентрированное изображение с тонкой полоской -->
              <div class="image-container bg-white border-b border-gray-200 flex items-center justify-center p-0">
                <!-- Показываем временный скриншот если есть -->
                <Image
                  v-if="tempScreenshots.has(element.id!)"
                  :src="tempScreenshots.get(element.id!)"
                  alt="Сгенерированный скриншот"
                  class="max-w-full h-auto object-contain cursor-pointer"
                  
                  preview
                  @click.stop
                />
                <!-- Показываем оригинальный эскиз если нет временного скриншота -->
                <Image
                  v-else-if="element.sketch"
                  :src="`http://localhost:8055/assets/${element.sketch}`"
                  alt="Предпросмотр элемента"
                  class="max-w-full h-auto object-contain cursor-pointer"
                  
                  preview
                  @click.stop
                />
                <!-- Показываем заглушку если нет изображений -->
                <div
                  v-else
                  class="w-full h-12 bg-surface-100 dark:bg-surface-800 flex items-center justify-center"
                >
                  <i class="pi pi-image text-4xl text-surface-400"/>
                </div>
              </div>
              <div class="absolute top-2 right-2 flex gap-1">
                <div
                  class="w-4 h-4 rounded border-2 border-blue-200 backdrop-blur flex items-center justify-center cursor-pointer transition-all hover:scale-110"
                  :class="{ 'bg-blue-500': isElementSelected(element) }"
                  @click.stop="toggleSelection(element)"
                >
                  <i v-if="isElementSelected(element)" class="pi pi-check text-white text-xs"/>
                </div>
                <div
                  v-if="currentMode === 'combination'"
                  class="w-4 h-4 rounded border-2 border-green-200 backdrop-blur flex items-center justify-center cursor-pointer transition-all hover:scale-110"
                  :class="{ 'bg-green-500': isElementSelectedForCombination(element) }"
                  @click.stop="toggleCombinationSelection(element)"
                >
                  <i v-if="isElementSelectedForCombination(element)" class="pi pi-check text-white text-xs"/>
                </div>
                <Button
                  icon="pi pi-pencil"
                  class="opacity-0 group-hover:opacity-100 transition-opacity p-button-sm p-button-rounded p-button-secondary"
                  style="width: 24px; height: 24px;"
                  @click.stop="openFullElemEditor(element)"
                />
              </div>
            </div>
          </template>
          <template #content>
            <div class="p-0">
              <!-- ИСПРАВЛЕННОЕ: Добавлены типы элементов тегами -->
              <div class="space-y-1">
                <div class="flex items-center justify-between">
                <div class="flex items-center gap-1">
                  <span class="text-sm text-gray-500" style="font-size: 9px;">{{ element.number }}</span>
                  <span class="text-base" style="font-size: 13px;">{{ element.title }}</span>
                </div>

                <div v-if="dataGroups.length > 1 && currentMode === 'standard'" class="flex-shrink-0">
                  <Dropdown
                    :model-value="getSelectedGroupForElement(element.id!)"
                    :options="groupOptions"
                    option-label="label"
                    option-value="value"
                    class="text-xs w-12"
                    :pt="{
                      root: { class: 'text-xs h-6' },
                      input: { class: 'text-xs p-1 h-6' },
                      trigger: { class: 'w-4' },
                      panel: { class: 'text-xs' },
                      item: { class: 'text-xs p-1' }
                    }"
                    @change="(event) => setSelectedGroupForElement(element.id!, event.value)"
                    @click.stop
                  />
                </div>
              </div>
            </div>
                <!-- Типы элементов как теги -->
                <div v-if="element.elem_type && element.elem_type.length > 0" class="flex flex-wrap gap-1">
                  <Badge
                    v-for="type in element.elem_type"
                    :key="type"
                    :value="type"
                    
                    class="text-xs text-gray-600 dark:text-gray-400"
                    style="font-size: 8px; padding: 2px; background-color: #f0f0f0; color: #888; font-weight: 400; height: 14px;"
                  />
                </div>
              </div>

              
            
          </template>
        </Card>
      </div>

      <!-- Пагинация -->
      <Paginator
        v-model:first="first"
        :rows="rowsPerPage"
        :total-records="filteredElements.length"
        :rows-per-page-options="[100, 200, 300]"
        template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
        @page="onPageChange"
      />
    </div>

    <!-- Сообщение если элементов нет -->
    <div v-else-if="!loading" class="text-center py-8 text-surface-500">
      <i class="pi pi-inbox text-4xl mb-4 block"/>
      <p>Элементы не найдены</p>
      <p class="text-sm">Попробуйте изменить фильтры</p>
    </div>

    <Toast />
  </div>

  <!-- Полный сайдбар редактирования элементов (точная копия из welem-proto) -->
  <div
    v-if="sidebarVisible"
    class="fixed inset-0 z-50 flex"
  >
    <!-- Overlay -->
    <div
      class="fixed inset-0 bg-black bg-opacity-50"
      @click="closeSidebar"
    />

    <!-- Сайдбар -->
    <div class="relative ml-auto w-96 h-full bg-white dark:bg-surface-800 shadow-xl overflow-y-auto">
      <div class="p-4">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold">
            {{ editingItem.id ? 'Редактирование' : 'Создание' }} элемента
          </h2>
          <Button
            icon="pi pi-times"
            class="p-button-text p-button-sm"
            @click="closeSidebar"
          />
        </div>

        <div class="p-fluid">
          <!-- Базовая информация -->
          <div class="space-y-2">
            <div class="flex gap-2">
              <div class="field w-1/4">
                <InputText
                  v-model="editingItem.number"
                  required
                  class="w-full"
                  placeholder="Номер элемента*"
                  style="padding: 6px; font-size: 10px"
                />
              </div>

              <div class="field w-3/4">
                <InputText
                  v-model="editingItem.title"
                  required
                  class="w-full"
                  placeholder="Название*"
                  style="padding: 6px; font-size: 11px"
                />
              </div>
            </div>

            <div class="field">
              <Textarea
                v-model="editingItem.description"
                rows="2"
                class="w-full text-xs [&>textarea]:text-xs"
                placeholder="Описание"
                style="padding: 4px; font-size: 10px"
              />
            </div>

            <div class="field mb-0" style="margin-top: 0">
              <Textarea
                v-model="editingItem.composition"
                rows="4"
                class="w-full text-xs [&>textarea]:text-xs"
                placeholder="Композиция"
                style="padding: 4px; font-size: 10px"
              />
            </div>

            <div class="field mb-2" style="margin-top: 0">
              <MultiSelect
                v-model="editingItem.elem_type"
                :options="elemTypeOptions"
                placeholder="Выберите типы элементов"
                display="chip"
                class="text-xs w-full p-0"
                panel-class="text-xs"
                style="font-size: 11px"
                :pt="{
                  item: { class: 'text-xs' },
                  header: { class: 'text-xs' },
                }"
              />
            </div>

            <div class="field mb-2">
              <MultiSelect
                v-model="editingItem.collection"
                :options="collectionOptions"
                display="chip"
                class="w-full text-xs"
                placeholder="Выберите коллекции"
                panel-class="text-xs"
                style="font-size: 11px"
                :pt="{
                  item: { class: 'text-xs' },
                  header: { class: 'text-xs' },
                }"
              />
            </div>

            <div class="field mb-2">
              <MultiSelect
                v-model="editingItem.wblock_proto"
                :options="wblockOptions"
                option-label="label"
                option-value="value"
                display="chip"
                class="w-full text-xs"
                placeholder="Выберите блоки"
                panel-class="text-xs"
                style="font-size: 11px"
                :pt="{
                  item: { class: 'text-xs' },
                  header: { class: 'text-xs' },
                }"
              />
            </div>

            <div class="field mb-2">
              <div class="flex gap-2">
                <Image
                  v-if="editingItem.sketch"
                  :src="`http://localhost:8055/assets/${editingItem.sketch}`"
                  alt="Эскиз"
                  width="200"
                  class="my"
                  preview
                />
                <FileUpload
                  mode="basic"
                  :auto="true"
                  accept="image/*"
                  :max-file-size="1000000"
                  choose-label="Эскиз"
                  class="p-button-sm"
                  @select="onSketchSelect"
                />
              </div>
            </div>

            <div class="field mb-0">
              <TabView
                class="text-xs"
                :pt="{
                panelcontainer: { style: 'padding:0' },
              }"
              >
                <TabPanel
                  header="HTML/CSS/JS"
                  :pt="{
                    header: { class: 'p-0' },
                    headerAction: { class: 'text-xs p-0' },
                    content: { class: 'p-0' }
                  }">
                  <div class="space-y-1">
                    <PrismEditorWithCopy
                      v-model="editingItem.html"
                      editor-class="my-editor text-xs"
                      :highlight="highlightHtml"
                      placeholder="Введите HTML код"
                      field-name="HTML"
                      max-height="120px"
                    />
                    <div class="p-0 grid grid-cols-2 gap-4">
                      <div class="flex flex-col h-full">
                      <PrismEditorWithCopy
                        v-model="editingItem.css"
                        editor-class="my-editor text-xs w-full"
                        :highlight="highlightCss"
                        placeholder="CSS код"
                        field-name="CSS"
                        max-height="60px !important"
                      />
                    </div>
                    <div class="flex flex-col h-full">
                      <PrismEditorWithCopy
                        v-model="editingItem.js"
                        editor-class="my-editor text-xs w-full"
                        :highlight="highlightJs"
                        placeholder="JS код"
                        field-name="JavaScript"
                        max-height="60px !important"
                      />
                    </div>
                    </div>
                  </div>
                </TabPanel>
                <TabPanel
                  header="HBS"
                  :pt="{
                    header: { class: 'p-0' },
                    headerAction: { class: 'text-xs p-0' },
                    content: { class: 'p-0' }
                  }">
                  <PrismEditorWithCopy
                    v-model="editingItem.hbs"
                    editor-class="my-editor text-xs"
                    :highlight="highlightHtml"
                    placeholder="Введите HBS код"
                    field-name="HBS"
                    max-height="200px"
                  />
                </TabPanel>
                <TabPanel
                  header="JSON"
                  :pt="{
                    header: { class: 'p-0' },
                    headerAction: { class: 'text-xs p-0' },
                    content: { class: 'p-0' }
                  }">
                  <PrismEditorWithCopy
                    v-model="editingItem.json"
                    editor-class="my-editor text-xs"
                    :highlight="highlightJson"
                    placeholder="Введите JSON код"
                    field-name="JSON"
                    max-height="200px"
                  />
                </TabPanel>
              </TabView>
            </div>
          </div>

          <div class="flex justify-end gap-2 mt-0">
            <Button
              label="Отмена"
              icon="pi pi-times"
              class="p-button-sm"
              @click="closeSidebar"
            />
            <Button
              label="Сохранить"
              icon="pi pi-check"
              class="p-button-sm"
              :loading="saving"
              @click="saveItem"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { PrismEditor } from 'vue-prism-editor'
import 'vue-prism-editor/dist/prismeditor.min.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import handlebars from 'handlebars/dist/handlebars.min.js'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'

// PrimeVue компоненты
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import MultiSelect from 'primevue/multiselect'
import Card from 'primevue/card'
import Checkbox from 'primevue/checkbox'
import Toast from 'primevue/toast'
import Image from 'primevue/image'
import Paginator from 'primevue/paginator'
import Dropdown from 'primevue/dropdown'
import Badge from 'primevue/badge'
import Textarea from 'primevue/textarea'
import FileUpload from 'primevue/fileupload'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'

// Компоненты
import CombinationPreview from '~/components/welem-gen/CombinationPreview.vue'
import VariationSelector from '~/components/welem-gen/VariationSelector.vue'
import ThemeSelector from '~/components/welem-gen/ThemeSelector.vue'
import ContentConstructor from '~/components/welem-gen/ContentConstructorNew.vue'
import VariationConstructor from '~/components/welem-gen/VariationConstructor.vue'
import ThemeConstructor from '~/components/welem-gen/ThemeConstructor.vue'
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'

// Утилиты маппинга
import { getVariationsByType, applyVariationToHtml } from '~/utils/variation-mappings'
import { applyVariationRules, getAllVariationGroups } from '~/utils/advanced-variation-mappings'
import { getAllThemes, applyThemeToHtml } from '~/utils/advanced-theme-mappings'
import { generateContentData, getContentPreview } from '~/utils/content-mappings'


// Определение метаданных страницы
definePageMeta({
  title: 'Генератор элементов 2.0',
  description: 'Расширенная генерация элементов с комбинированием и темизацией',
  navOrder: 17,
  type: 'primary',
  icon: 'i-mdi-view-module-outline',
})

// Интерфейсы
interface WElem {
  id?: string
  number: string
  title: string
  description?: string
  elem_type?: string[]
  collection?: string[]
  html?: string
  css?: string
  js?: string
  hbs?: string
  json?: string
  composition?: string
  sketch?: string
  wblock_proto?: string[]
}

interface DataGroup {
  id: number
  jsonContent: string
  hbsContent: string
  wrapperRowType?: string
  wrapperColWidth?: string
  collection?: string[]
}

// Composables
const { getItems, createItems, updateItem, deleteItems } = useDirectusItems()
const toast = useToast()

// Режимы работы
const workModes = ref([
  { label: 'Стандарт', value: 'standard' },
  { label: 'Комбинации', value: 'combination' },
  { label: 'Вариации', value: 'variation' },
  { label: 'Темы', value: 'theming' },
  { label: 'Контент', value: 'content' }
])

const currentMode = ref('standard')

// Bootstrap wrapper опции
const rowTypeOptions = ref([
  { label: 'div.row', value: 'row' },
  { label: 'div.row-fluid', value: 'row-fluid' }
])

const colWidthOptions = ref([
  { label: '.col-12', value: 'col-12' },
  { label: '.col-6', value: 'col-6' },
  { label: '.col-4', value: 'col-4' },
  { label: '.col-8', value: 'col-8' },
  { label: '.col-3', value: 'col-3' },
  { label: '.col-9', value: 'col-9' },
  { label: '.col-2', value: 'col-2' },
  { label: '.col-10', value: 'col-10' },
  { label: '.col-1', value: 'col-1' },
  { label: '.col-5', value: 'col-5' },
  { label: '.col-7', value: 'col-7' },
  { label: '.col-11', value: 'col-11' },
  { label: '.col-1-5', value: 'col-1-5' },
  { label: '.col-2-5', value: 'col-2-5' },
  { label: '.col-3-5', value: 'col-3-5' },
  { label: '.col-4-5', value: 'col-4-5' }
])

// Комбинации элементов
const combinationLayouts = ref([
  { label: 'Горизонтально', value: 'horizontal' },
  { label: 'Вертикально', value: 'vertical' },
  { label: 'Сетка 2x2', value: 'grid-2x2' },
  { label: 'Сетка 3x1', value: 'grid-3x1' },
  { label: 'Сетка 1x3', value: 'grid-1x3' }
])

const combinationLayout = ref('horizontal')
const selectedForCombination = ref<WElem[]>([])

// Вариации элементов
const variationTypes = ref([
  { label: 'Цветовые схемы', value: 'colors' },
  { label: 'Размеры', value: 'sizes' },
  { label: 'Стили границ', value: 'borders' },
  { label: 'Тени', value: 'shadows' },
  { label: 'Анимации', value: 'animations' }
])

const selectedVariationTypes = ref<string[]>([])

// Реактивные данные (базовые из welem-gen.vue)
const formData = ref({
  number: '',
  title: '',
  collection: [] as string[]
})

const dataGroups = ref<DataGroup[]>([
  {
    id: 1,
    jsonContent: '',
    hbsContent: '',
    wrapperRowType: 'row',
    wrapperColWidth: 'col-12',
    collection: []
  }
])

const elementGroupSelection = ref<Map<string, number>>(new Map())
const elementDataHashes = ref<Map<string, string>>(new Map())
const tempScreenshots = ref<Map<string, string>>(new Map())

const loading = ref(false)
const generating = ref(false)
const saving = ref(false)

const allElements = ref<WElem[]>([])
const selectedElements = ref<WElem[]>([])
const first = ref(0)
const rowsPerPage = ref(100)

const collectionOptions = ref<string[]>([])

const filters = ref({
  elem_type: [] as string[],
  collection: [] as string[]
})

const filterOptions = ref({
  elem_type: [] as string[],
  collection: [] as string[]
})

const showOnlySelected = ref(false)

// Состояния для режимов работы
const generatedVariations = ref<any[]>([])
const selectedVariations = ref<string[]>([])

const themedElements = ref<any[]>([])
const selectedThemedElements = ref<string[]>([])

const contentElements = ref<any[]>([])
const selectedContentElements = ref<string[]>([])

// Обновляем доступные темы из маппинга
const availableThemes = ref<any[]>([])

// Обновляем предпросмотр контента при изменении типа/варианта
const previewContent = ref<any[]>([])

// Состояние для полного сайдбара редактирования элементов (как в welem-proto)
const sidebarVisible = ref(false)
const editingItem = ref<WElem>({
  number: '',
  title: '',
  description: '',
  elem_type: [],
  html: '',
  css: '',
  js: '',
  hbs: '',
  json: '',
  composition: '',
  collection: [],
  sketch: '',
  wblock_proto: [],
})

// Опции для сайдбара (как в welem-proto)
const elemTypeOptions = ref<string[]>([])
const wblockOptions = ref<any[]>([])
const relatedBlocks = ref<any[]>([])

// Вычисляемые свойства для групп
const groupOptions = computed(() => {
  return dataGroups.value.map(group => ({
    label: group.id.toString(),
    value: group.id
  }))
})

// Функции режимов работы
const setWorkMode = (mode: string) => {
  currentMode.value = mode
  console.log('Переключен режим работы:', mode)
}

// Функции для работы с элементами
const isElementSelected = (element: WElem): boolean => {
  return selectedElements.value.some(e => e.id === element.id)
}

const isElementSelectedForCombination = (element: WElem): boolean => {
  return selectedForCombination.value.some(e => e.id === element.id)
}

const getElementCardStyle = (element: WElem): string => {
  const styles = []

  if (isElementSelected(element)) {
    styles.push('box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5)')
  }

  if (isElementSelectedForCombination(element)) {
    styles.push('box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.5)')
  }

  // Если выбраны оба, создаем двойную тень
  if (isElementSelected(element) && isElementSelectedForCombination(element)) {
    styles.length = 0 // Очищаем предыдущие стили
    styles.push('box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5), 0 0 0 4px rgba(16, 185, 129, 0.5)')
  }

  return styles.join('; ')
}

const handleElementClick = (element: WElem) => {
  if (currentMode.value === 'combination') {
    toggleCombinationSelection(element)
  } else {
    toggleSelection(element)
  }
}

const toggleSelection = (element: WElem) => {
  const index = selectedElements.value.findIndex(e => e.id === element.id)
  if (index > -1) {
    selectedElements.value.splice(index, 1)
  } else {
    selectedElements.value.push(element)
  }
  console.log('Выбранные элементы:', selectedElements.value.length)
}

const toggleCombinationSelection = (element: WElem) => {
  const index = selectedForCombination.value.findIndex(e => e.id === element.id)
  if (index > -1) {
    selectedForCombination.value.splice(index, 1)
  } else {
    selectedForCombination.value.push(element)
  }
  console.log('Элементы для комбинирования:', selectedForCombination.value.length)
}

// Обработчик сохранения комбинации
const onCombinationSaved = (savedBlock: any) => {
  console.log('Комбинация сохранена:', savedBlock)
  selectedForCombination.value = []
  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: `Комбинация сохранена как блок: ${savedBlock.title}`,
    life: 3000
  })
}

// Обработчики событий от новых компонентов
const onVariationsCreated = (data: any) => {
  console.log('Создание вариаций:', data)
  generatedVariations.value = []
  let variationId = 1

  for (const element of selectedElements.value) {
    for (const type of data.selectedTypes) {
      const selectedVariationNames = data.selectedVariations[type] || []
      const variations = getVariationsByType(type)

      for (const variationName of selectedVariationNames) {
        const variation = variations.find(v => v.name === variationName)
        if (variation) {
          const { html, hbs } = applyVariationToHtml(element.html || '', element.hbs || '', variation)

          generatedVariations.value.push({
            id: `var-${variationId++}`,
            originalId: element.id,
            title: `${element.title} - ${variation.name}`,
            description: variation.description,
            html,
            hbs,
            css: element.css || '',
            js: element.js || '',
            variationType: type,
            variationName: variation.name,
            screenshot: null
          })
        }
      }
    }
  }

  console.log(`Создано ${generatedVariations.value.length} вариаций`)
}

// ИСПРАВЛЕННАЯ ФУНКЦИЯ: Каждое правило создает отдельную вариацию
const handleVariationApplied = (data: any) => {
  console.log('Вариации применены:', data)

  // Очищаем предыдущие вариации
  generatedVariations.value = []

  // НОВАЯ ЛОГИКА: Создаем отдельную вариацию для каждого правила и каждого элемента
  const selectedRules = data.selectedRules || []
  const originalElements = data.originalElements || []

  console.log('Создаем вариации для правил:', selectedRules)
  console.log('Для элементов:', originalElements.length)

  let variationCounter = 1

  // Для каждого элемента
  for (const element of originalElements) {
    // Для каждого правила создаем отдельную вариацию
    for (const ruleId of selectedRules) {
      // Применяем только одно правило к элементу
      const modifiedHtml = applyVariationRules(element.html || '', [ruleId])

      // Находим информацию о правиле
      const allGroups = getAllVariationGroups()
      let ruleName = 'Неизвестное правило'
      let groupName = 'Неизвестная группа'

      for (const group of allGroups) {
        const rule = group.rules.find(r => r.id === ruleId)
        if (rule) {
          ruleName = rule.name
          groupName = group.name
          break
        }
      }

      generatedVariations.value.push({
        id: `variation-${Date.now()}-${variationCounter++}`,
        originalId: element.id,
        originalNumber: element.number,
        title: `${element.title} - ${ruleName}`,
        description: `${groupName}: ${ruleName}`,
        html: modifiedHtml,
        hbs: element.hbs || '',
        css: element.css || '',
        js: element.js || '',
        elem_type: element.elem_type || [],
        collection: element.collection || [],
        variationType: 'custom',
        variationName: ruleName,
        groupName: groupName,
        ruleId: ruleId,
        appliedRules: [ruleId],
        screenshot: null
      })
    }
  }

  console.log(`Создано ${generatedVariations.value.length} вариаций (${selectedRules.length} правил × ${originalElements.length} элементов)`)

  toast.add({
    severity: 'success',
    summary: 'Вариации созданы',
    detail: `Создано ${generatedVariations.value.length} вариаций (${selectedRules.length} правил × ${originalElements.length} элементов)`,
    life: 3000
  })
}

const onThemesCreated = (data: any) => {
  console.log('Создание тем:', data)
  themedElements.value = []
  let themedId = 1

  for (const element of selectedElements.value) {
    for (const themeName of data.selectedThemes) {
      const theme = data.availableThemes.find((t: any) => t.name === themeName)
      if (theme) {
        const { html, hbs, css } = applyThemeToHtml(element.html || '', element.hbs || '', theme)

        themedElements.value.push({
          id: `themed-${themedId++}`,
          originalId: element.id,
          title: `${element.title} - ${theme.label}`,
          html,
          hbs,
          css: (element.css || '') + '\n' + css,
          js: element.js || '',
          themeName: theme.label,
          screenshot: null
        })
      }
    }
  }

  console.log(`Создано ${themedElements.value.length} элементов с темами`)
}

const onContentCreated = (data: any) => {
  console.log('Создание контента:', data)
  contentElements.value = []
  let contentId = 1

  for (const element of selectedElements.value) {
    if (!element.hbs) continue // Пропускаем элементы без HBS шаблонов

    try {
      const template = handlebars.compile(element.hbs)
      const newHtml = template(data.contentData)

      contentElements.value.push({
        id: `content-${contentId++}`,
        originalId: element.id,
        title: `${element.title} - ${data.contentType}`,
        html: newHtml,
        hbs: element.hbs,
        css: element.css || '',
        js: element.js || '',
        contentType: data.contentType,
        contentVariant: data.contentVariant,
        contentData: data.contentData,
        screenshot: null
      })
    } catch (error) {
      console.error('Ошибка компиляции HBS для элемента:', element.title, error)
    }
  }

  console.log(`Создано ${contentElements.value.length} элементов с контентом`)
}

// ОБНОВЛЕННАЯ ФУНКЦИЯ: Обработчик применения интеллектуального контента
const handleContentApplied = (data: any) => {
  console.log('Интеллектуальный контент применен:', data)

  // Очищаем предыдущие элементы с контентом
  contentElements.value = []

  // Добавляем модифицированные элементы в список контентных элементов
  data.modifiedElements.forEach((element: any, index: number) => {
    const contentSet = element.appliedContentSet

    contentElements.value.push({
      id: `content-${Date.now()}-${index}`,
      originalId: element.id,
      originalNumber: element.number,
      title: `${element.title} - ${contentSet.title}`,
      description: `${contentSet.description} (${contentSet.type})`,
      html: element.html,
      hbs: element.hbs || '',
      css: element.css || '',
      js: element.js || '',
      elem_type: element.elem_type || [],
      collection: element.collection || [],
      contentType: 'intelligent',
      contentSource: contentSet,
      contentData: element.contentData,
      appliedContentSet: contentSet,
      screenshot: null
    })
  })

  console.log(`Создано ${contentElements.value.length} элементов с интеллектуальным контентом`)

  toast.add({
    severity: 'success',
    summary: 'Интеллектуальный контент применен',
    detail: `Создано ${contentElements.value.length} вариантов (${data.contentSets.length} наборов × ${selectedElements.value.length} элементов)`,
    life: 3000
  })
}

// Функции для полного сайдбара редактирования элементов (точная копия из welem-proto)
const openFullElemEditor = async (element: WElem) => {
  try {
    const [elementData] = await getItems({
      collection: 'welem_proto',
      params: {
        filter: { id: { _eq: element.id } },
        fields: ['*'],
        limit: 1,
      },
    })

    const relations = await loadElementRelations(element.id!)
    const relatedBlocksData = await loadRelatedBlocksDetails(relations)

    editingItem.value = {
      ...elementData,
      wblock_proto: relations,
    }
    relatedBlocks.value = relatedBlocksData
    sidebarVisible.value = true
  } catch (error) {
    console.error('Error loading element:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить данные элемента',
      life: 3000,
    })
  }
}

const closeSidebar = () => {
  sidebarVisible.value = false
  relatedBlocks.value = []
  editingItem.value = {
    number: '',
    title: '',
    description: '',
    elem_type: [],
    html: '',
    css: '',
    js: '',
    hbs: '',
    json: '',
    composition: '',
    collection: [],
    sketch: '',
    wblock_proto: [],
  }
}

const saveItem = async () => {
  saving.value = true
  try {
    const { id, wblock_proto, ...saveData } = editingItem.value

    if (id) {
      // Обновление существующего элемента
      await updateItem({
        collection: 'welem_proto',
        id,
        item: saveData,
      })
      await saveRelations(id, wblock_proto || [])
    } else {
      // Создание нового элемента
      const result = await createItems({
        collection: 'welem_proto',
        items: [saveData],
      })
      const newId = Array.isArray(result) ? result[0]?.id : result?.id
      if (newId && wblock_proto?.length) {
        await saveRelations(newId, wblock_proto)
      }
    }

    await loadAllElements()
    closeSidebar()
    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: id ? 'Элемент обновлен' : 'Элемент создан',
      life: 3000,
    })
  } catch (error) {
    console.error('Save error:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: error.message || 'Не удалось сохранить элемент',
      life: 5000,
    })
  } finally {
    saving.value = false
  }
}

const onSketchSelect = async (event: any) => {
  const file = event.files[0]
  if (file) {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('http://localhost:8055/files', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Ошибка загрузки файла')
      }

      const data = await response.json()
      editingItem.value.sketch = data.data.id
    } catch (error) {
      console.error('Ошибка при загрузке файла:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить файл',
      })
    }
  }
}

// Методы для работы с M2M связями (точная копия из welem-proto)
const loadElementRelations = async (elementId: string) => {
  try {
    const relations = await getItems({
      collection: 'wblock_proto_welem_proto',
      params: {
        filter: { welem_proto_id: { _eq: elementId } },
        fields: ['wblock_proto_id'],
      },
    })
    return relations.map((r: any) => r.wblock_proto_id)
  } catch (error) {
    console.error('Error loading relations:', error)
    return []
  }
}

const loadRelatedBlocksDetails = async (blockIds: string[]) => {
  if (!blockIds.length) return []

  try {
    const blocks = await getItems({
      collection: 'wblock_proto',
      params: {
        filter: { id: { _in: blockIds } },
        fields: ['id', 'number', 'title'],
      },
    })
    return blocks || []
  } catch (error) {
    console.error('Error loading related blocks:', error)
    return []
  }
}

const saveRelations = async (elementId: string, wblockIds: string[]) => {
  try {
    // Удаляем старые связи
    const currentRelations = await getItems({
      collection: 'wblock_proto_welem_proto',
      params: {
        filter: { welem_proto_id: { _eq: elementId } },
        fields: ['id'],
      },
    })

    if (currentRelations.length > 0) {
      await deleteItems({
        collection: 'wblock_proto_welem_proto',
        items: currentRelations.map((r: any) => r.id),
      })
    }

    // Добавляем новые
    if (wblockIds.length > 0) {
      await createItems({
        collection: 'wblock_proto_welem_proto',
        items: wblockIds.map((id) => ({
          welem_proto_id: elementId,
          wblock_proto_id: id,
        })),
      })
    }
  } catch (error) {
    console.error('Error saving relations:', error)
    throw error
  }
}

// Функции расширенных режимов
const generateCombinations = async () => {
  if (selectedForCombination.value.length < 2) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите минимум 2 элемента для комбинирования',
      life: 3000
    })
    return
  }

  generating.value = true

  try {
    console.log('Генерация комбинаций:', combinationLayout.value, selectedForCombination.value.length)

    // Создаем HTML комбинацию на основе выбранного layout
    const combinedHtml = await createCombinationHtml(selectedForCombination.value, combinationLayout.value)

    if (combinedHtml) {
      // Генерируем скриншот комбинации
      const screenshotUrl = await generateCombinationScreenshot(combinedHtml)

      if (screenshotUrl) {
        // Сохраняем комбинацию как новый блок
        await saveCombinationAsBlock(combinedHtml, screenshotUrl)

        toast.add({
          severity: 'success',
          summary: 'Успешно',
          detail: 'Комбинация элементов создана и сохранена',
          life: 3000
        })
      }
    }

  } catch (error) {
    console.error('Ошибка генерации комбинаций:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось создать комбинацию элементов',
      life: 3000
    })
  } finally {
    generating.value = false
  }
}

// Создание HTML комбинации
const createCombinationHtml = async (elements: WElem[], layout: string): Promise<string> => {
  let html = ''

  // Собираем CSS и JS от всех элементов
  const allCss = elements.map(el => el.css || '').join('\n')
  const allJs = elements.map(el => el.js || '').join('\n')

  // Создаем структуру в зависимости от layout
  switch (layout) {
    case 'horizontal':
      html = `
        <div class="container-fluid">
          <div class="row">
            ${elements.map(el => `
              <div class="col-${Math.floor(12 / elements.length)}">
                ${el.html || ''}
              </div>
            `).join('')}
          </div>
        </div>
      `
      break

    case 'vertical':
      html = `
        <div class="container-fluid">
          ${elements.map(el => `
            <div class="row mb-3">
              <div class="col-12">
                ${el.html || ''}
              </div>
            </div>
          `).join('')}
        </div>
      `
      break

    case 'grid-2x2':
      html = `
        <div class="container-fluid">
          <div class="row">
            ${elements.slice(0, 2).map(el => `
              <div class="col-6">
                ${el.html || ''}
              </div>
            `).join('')}
          </div>
          ${elements.length > 2 ? `
            <div class="row mt-3">
              ${elements.slice(2, 4).map(el => `
                <div class="col-6">
                  ${el.html || ''}
                </div>
              `).join('')}
            </div>
          ` : ''}
        </div>
      `
      break

    case 'grid-3x1':
      html = `
        <div class="container-fluid">
          <div class="row">
            ${elements.slice(0, 3).map(el => `
              <div class="col-4">
                ${el.html || ''}
              </div>
            `).join('')}
          </div>
          ${elements.length > 3 ? `
            <div class="row mt-3">
              <div class="col-12">
                ${elements[3].html || ''}
              </div>
            </div>
          ` : ''}
        </div>
      `
      break

    case 'grid-1x3':
      html = `
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              ${elements[0]?.html || ''}
            </div>
          </div>
          ${elements.length > 1 ? `
            <div class="row mt-3">
              ${elements.slice(1, 4).map(el => `
                <div class="col-4">
                  ${el.html || ''}
                </div>
              `).join('')}
            </div>
          ` : ''}
        </div>
      `
      break

    default:
      html = `
        <div class="container-fluid">
          <div class="row">
            ${elements.map(el => `
              <div class="col-${Math.floor(12 / elements.length)}">
                ${el.html || ''}
              </div>
            `).join('')}
          </div>
        </div>
      `
  }

  return html
}

// Генерация скриншота комбинации
const generateCombinationScreenshot = async (html: string): Promise<string | null> => {
  try {
    // Собираем CSS от всех выбранных элементов
    const allCss = selectedForCombination.value.map(el => el.css || '').join('\n')
    const allJs = selectedForCombination.value.map(el => el.js || '').join('\n')

    // Добавляем Bootstrap CSS
    const bootstrapCss = `
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
      <style>
        .container-fluid { padding: 20px; }
        .row { margin: 0; }
        [class*="col-"] { padding: 15px; }
        body { background: #f8f9fa; }
      </style>
    `

    // Создаем полный HTML
    const fullHtml = '<!DOCTYPE html>' +
      '<html lang="en">' +
      '<head>' +
      '<meta charset="UTF-8">' +
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
      '<title>Element Combination</title>' +
      bootstrapCss +
      allCss +
      '</head>' +
      '<body>' +
      html +
      allJs +
      '</body>' +
      '</html>'

    // Генерируем скриншот
    const response = await $fetch('/api/capture-html-screenshot-temp', {
      method: 'POST',
      body: {
        html: fullHtml,
        width: 1400,
        height: 800
      },
      responseType: 'blob'
    })

    // Создаем временный URL
    const tempUrl = URL.createObjectURL(response as Blob)
    console.log('Скриншот комбинации создан')
    return tempUrl

  } catch (error) {
    console.error('Ошибка создания скриншота комбинации:', error)
    return null
  }
}

// Сохранение комбинации как блока
const saveCombinationAsBlock = async (html: string, screenshotUrl: string) => {
  try {
    // Собираем CSS и JS от всех элементов
    const allCss = selectedForCombination.value.map(el => el.css || '').join('\n')
    const allJs = selectedForCombination.value.map(el => el.js || '').join('\n')

    // Сохраняем скриншот в Directus
    const screenshotId = await saveBlobToDirectus(screenshotUrl, `combination_${Date.now()}.png`)

    // Создаем данные блока
    const blockData = {
      number: `${formData.value.number}-combo-${Date.now()}`,
      title: `${formData.value.title} - Комбинация (${combinationLayout.value})`,
      collection: formData.value.collection,
      html: html,
      css: allCss,
      js: allJs,
      sketch: screenshotId,
      block_type: ['комбинация'],
      status: 'draft'
    }

    // Сохраняем в wblock_proto
    const savedBlock = await createItems({
      collection: 'wblock_proto',
      items: [blockData]
    })

    // Анализируем HTML
    const { results } = await $fetch('/api/batch-analyze-html', {
      method: 'POST',
      body: { records: [{ id: savedBlock[0].id, html: html }] }
    })

    // Обновляем с результатами анализа
    if (results.length > 0) {
      await updateItem({
        collection: 'wblock_proto',
        id: savedBlock[0].id,
        item: {
          block_type: results[0].blockTypes,
          composition: results[0].treeStructure
        }
      })
    }

    console.log('Комбинация сохранена как блок:', savedBlock[0].id)

    // Очищаем выбор
    selectedForCombination.value = []

    // Освобождаем blob URL
    URL.revokeObjectURL(screenshotUrl)

  } catch (error) {
    console.error('Ошибка сохранения комбинации:', error)
    throw error
  }
}

// Функция для сохранения blob в Directus (адаптированная из welem-gen.vue)
const saveBlobToDirectus = async (blobUrl: string, filename: string): Promise<string | null> => {
  try {
    console.log(`💾 Сохранение скриншота в Directus: ${filename}...`)

    const response = await fetch(blobUrl)
    const blob = await response.blob()

    const formData = new FormData()
    formData.append('title', filename)
    formData.append('file', blob, filename)

    const uploadResponse = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!uploadResponse.ok) {
      throw new Error(`Failed to upload to Directus: ${uploadResponse.statusText}`)
    }

    const data = await uploadResponse.json()
    console.log(`✅ Скриншот сохранен в Directus: ${data.data.id}`)

    return data.data.id

  } catch (error) {
    console.error(`❌ Ошибка сохранения скриншота в Directus:`, error)
    return null
  }
}



// Функции для управления выбором контентных элементов
const toggleContentSelection = (contentId: string) => {
  const index = selectedContentElements.value.indexOf(contentId)
  if (index > -1) {
    selectedContentElements.value.splice(index, 1)
  } else {
    selectedContentElements.value.push(contentId)
  }
}

// Альтернативное название для совместимости с шаблоном
const toggleContentElementSelection = toggleContentSelection

// Генерация скриншотов для контентных элементов
const generateContentScreenshots = async () => {
  generating.value = true

  try {
    for (const element of contentElements.value) {
      if (!element.screenshot) {
        const screenshotUrl = await generateElementScreenshot(element)
        if (screenshotUrl) {
          element.screenshot = screenshotUrl
        }
      }
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Скриншоты контентных элементов созданы',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка генерации скриншотов контентных элементов:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось создать скриншоты',
      life: 3000
    })
  } finally {
    generating.value = false
  }
}

// Сохранение выбранных контентных элементов
const saveSelectedContentElements = async () => {
  if (!selectedContentElements.value.length) return

  saving.value = true

  try {
    const elementsToSave = []
    const blocksToSave = []

    for (let i = 0; i < selectedContentElements.value.length; i++) {
      const contentId = selectedContentElements.value[i]
      const contentElement = contentElements.value.find(c => c.id === contentId)
      if (!contentElement) continue

      const counter = String(i + 1).padStart(2, '0')

      // Сохраняем скриншот если есть
      let screenshotId = null
      if (contentElement.screenshot) {
        const filename = `welem_gen2_content_${contentElement.originalNumber}_${contentElement.contentSource?.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'content'}.png`
        screenshotId = await saveBlobToDirectus(contentElement.screenshot, filename)
      }

      // Формирование номера: оригинальный-content-№
      const baseNumber = contentElement.originalNumber || formData.value.number

      // Определяем тип сохранения (элемент или блок)
      const shouldSaveAsBlock = contentElement.html && contentElement.html.includes('container') ||
                               contentElement.html && contentElement.html.includes('row')

      if (shouldSaveAsBlock) {
        // Сохраняем как блок
        const blockData = {
          number: `${baseNumber}-content-block-${counter}`,
          title: contentElement.title,
          collection: contentElement.collection || formData.value.collection,
          html: contentElement.html,
          css: contentElement.css || '',
          js: contentElement.js || '',
          hbs: contentElement.hbs || '',
          json: '', // Будет заполнено после анализа
          block_type: ['контент'],
          composition: '', // Будет заполнено после анализа
          sketch: screenshotId,
          status: 'draft'
        }
        blocksToSave.push(blockData)
      } else {
        // Сохраняем как элемент
        const elementData = {
          number: `${baseNumber}-content-${counter}`,
          title: contentElement.title,
          collection: contentElement.collection || formData.value.collection,
          html: contentElement.html,
          css: contentElement.css || '',
          js: contentElement.js || '',
          hbs: contentElement.hbs || '',
          json: '', // Будет заполнено после анализа
          elem_type: contentElement.elem_type || [],
          composition: '', // Будет заполнено после анализа
          sketch: screenshotId,
          status: 'draft'
        }
        elementsToSave.push(elementData)
      }
    }

    let savedCount = 0

    // Сохраняем элементы
    if (elementsToSave.length > 0) {
      const savedElements = await createItems({
        collection: 'welem_proto',
        items: elementsToSave
      })

      // Анализируем HTML элементов
      const analysisRecords = savedElements.map((element: any) => ({
        id: element.id,
        html: element.html
      }))

      const analysisResult: any = await $fetch('/api/batch-analyze-html-element', {
        method: 'POST',
        body: { records: analysisRecords }
      })

      // Обновляем с результатами анализа
      if (analysisResult.results) {
        for (const result of analysisResult.results) {
          await updateItem({
            collection: 'welem_proto',
            id: result.id,
            item: {
              elem_type: result.elementTypes,
              composition: result.treeStructure
            }
          })
        }
      }

      // Генерируем HBS и JSON
      for (const element of savedElements) {
        try {
          const templateResult: any = await $fetch('/api/html-to-template', {
            method: 'POST',
            body: { html: element.html }
          })

          if (templateResult.success) {
            await updateItem({
              collection: 'welem_proto',
              id: element.id,
              item: {
                hbs: templateResult.hbs,
                json: JSON.stringify(templateResult.variables, null, 2)
              }
            })
          }
        } catch (error) {
          console.error('Ошибка генерации HBS/JSON для контентного элемента:', element.id, error)
        }
      }

      savedCount += savedElements.length
    }

    // Сохраняем блоки
    if (blocksToSave.length > 0) {
      const savedBlocks = await createItems({
        collection: 'wblock_proto',
        items: blocksToSave
      })

      // Анализируем HTML блоков
      const blockAnalysisRecords = savedBlocks.map((block: any) => ({
        id: block.id,
        html: block.html
      }))

      const blockAnalysisResult: any = await $fetch('/api/batch-analyze-html', {
        method: 'POST',
        body: { records: blockAnalysisRecords }
      })

      // Обновляем с результатами анализа
      if (blockAnalysisResult.results) {
        for (const result of blockAnalysisResult.results) {
          await updateItem({
            collection: 'wblock_proto',
            id: result.id,
            item: {
              block_type: result.blockTypes,
              composition: result.treeStructure
            }
          })
        }
      }

      // Генерируем HBS и JSON для блоков
      for (const block of savedBlocks) {
        try {
          const templateResult: any = await $fetch('/api/html-to-template', {
            method: 'POST',
            body: { html: block.html }
          })

          if (templateResult.success) {
            await updateItem({
              collection: 'wblock_proto',
              id: block.id,
              item: {
                hbs: templateResult.hbs,
                json: JSON.stringify(templateResult.variables, null, 2)
              }
            })
          }
        } catch (error) {
          console.error('Ошибка генерации HBS/JSON для контентного блока:', block.id, error)
        }
      }

      savedCount += savedBlocks.length
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранено ${savedCount} элементов/блоков с контентом`,
      life: 3000
    })

    // Очищаем данные
    contentElements.value = []
    selectedContentElements.value = []

  } catch (error) {
    console.error('Ошибка сохранения контентных элементов:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить элементы с контентом',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}

// Функции для управления выбором вариаций
const toggleVariationSelection = (variationId: string) => {
  const index = selectedVariations.value.indexOf(variationId)
  if (index > -1) {
    selectedVariations.value.splice(index, 1)
  } else {
    selectedVariations.value.push(variationId)
  }
}

// Генерация скриншотов для вариаций
const generateVariationScreenshots = async () => {
  generating.value = true

  try {
    for (const variation of generatedVariations.value) {
      if (!variation.screenshot) {
        const screenshotUrl = await generateElementScreenshot(variation)
        if (screenshotUrl) {
          variation.screenshot = screenshotUrl
        }
      }
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Скриншоты вариаций созданы',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка генерации скриншотов вариаций:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось создать скриншоты',
      life: 3000
    })
  } finally {
    generating.value = false
  }
}

// ИСПРАВЛЕННОЕ сохранение вариаций с sketch и правильными полями
const saveSelectedVariations = async () => {
  if (!selectedVariations.value.length) return

  saving.value = true

  try {
    const elementsToSave = []

    for (let i = 0; i < selectedVariations.value.length; i++) {
      const variationId = selectedVariations.value[i]
      const variation = generatedVariations.value.find(v => v.id === variationId)
      if (!variation) continue

      const counter = String(i + 1).padStart(2, '0')

      // Сохраняем скриншот если есть
      let screenshotId = null
      if (variation.screenshot) {
        const filename = `welem_gen2_variation_${variation.originalNumber}_${variation.variationName?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}.png`
        screenshotId = await saveBlobToDirectus(variation.screenshot, filename)
      }

      // ИСПРАВЛЕННОЕ формирование номера: оригинальный-var-№
      const baseNumber = variation.originalNumber || formData.value.number

      const elementData = {
        number: `${baseNumber}-var-${counter}`,
        title: variation.title,
        collection: variation.collection || formData.value.collection,
        html: variation.html,
        css: variation.css || '',
        js: variation.js || '',
        hbs: variation.hbs || '',
        json: '', // Будет заполнено после анализа
        elem_type: variation.elem_type || [],
        composition: '', // Будет заполнено после анализа
        sketch: screenshotId,
        status: 'draft'
      }

      elementsToSave.push(elementData)
    }

    // Сохраняем элементы
    const savedElements = await createItems({
      collection: 'welem_proto',
      items: elementsToSave
    })

    // АНАЛИЗИРУЕМ HTML и заполняем поля как в стандартном режиме
    const analysisRecords = savedElements.map((element: any) => ({
      id: element.id,
      html: element.html
    }))

    const analysisResult: any = await $fetch('/api/batch-analyze-html-element', {
      method: 'POST',
      body: { records: analysisRecords }
    })

    // Обновляем с результатами анализа
    if (analysisResult.results) {
      for (const result of analysisResult.results) {
        await updateItem({
          collection: 'welem_proto',
          id: result.id,
          item: {
            elem_type: result.elementTypes,
            composition: result.treeStructure
          }
        })
      }
    }

    // ГЕНЕРИРУЕМ HBS и JSON как в стандартном режиме
    for (const element of savedElements) {
      try {
        const templateResult: any = await $fetch('/api/html-to-template', {
          method: 'POST',
          body: { html: element.html }
        })

        if (templateResult.success) {
          await updateItem({
            collection: 'welem_proto',
            id: element.id,
            item: {
              hbs: templateResult.hbs,
              json: JSON.stringify(templateResult.variables, null, 2)
            }
          })
        }
      } catch (error) {
        console.error('Ошибка генерации HBS/JSON для вариации:', element.id, error)
      }
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранено ${savedElements.length} вариаций с полным анализом`,
      life: 3000
    })

    // Очищаем данные
    generatedVariations.value = []
    selectedVariations.value = []

  } catch (error) {
    console.error('Ошибка сохранения вариаций:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить вариации',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}







// Функции для управления выбором элементов с темами
const toggleThemedElementSelection = (elementId: string) => {
  const index = selectedThemedElements.value.indexOf(elementId)
  if (index > -1) {
    selectedThemedElements.value.splice(index, 1)
  } else {
    selectedThemedElements.value.push(elementId)
  }
}

// Генерация скриншотов для элементов с темами
const generateThemedScreenshots = async () => {
  generating.value = true

  try {
    for (const themedElement of themedElements.value) {
      if (!themedElement.screenshot) {
        const screenshotUrl = await generateElementScreenshot(themedElement)
        if (screenshotUrl) {
          themedElement.screenshot = screenshotUrl
        }
      }
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Скриншоты элементов с темами созданы',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка генерации скриншотов элементов с темами:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось создать скриншоты',
      life: 3000
    })
  } finally {
    generating.value = false
  }
}

// Сохранение выбранных элементов с темами
const saveSelectedThemedElements = async () => {
  if (!selectedThemedElements.value.length) return

  saving.value = true

  try {
    const elementsToSave = themedElements.value
      .filter(e => selectedThemedElements.value.includes(e.id))
      .map((themedElement, index) => ({
        number: `${formData.value.number}-theme-${String(index + 1).padStart(2, '0')}`,
        title: themedElement.title,
        collection: formData.value.collection,
        html: themedElement.html,
        css: themedElement.css,
        js: themedElement.js,
        hbs: themedElement.hbs,
        status: 'draft'
      }))

    const savedElements = await createItems({
      collection: 'welem_proto',
      items: elementsToSave
    })

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранено ${savedElements.length} элементов с темами`,
      life: 3000
    })

    // Очищаем данные
    themedElements.value = []
    selectedThemedElements.value = []

  } catch (error) {
    console.error('Ошибка сохранения элементов с темами:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить элементы с темами',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}















// Базовые функции (адаптированные из welem-gen.vue)
const addNewGroup = () => {
  const newId = Math.max(...dataGroups.value.map(g => g.id)) + 1
  dataGroups.value.push({
    id: newId,
    jsonContent: '',
    hbsContent: '',
    wrapperRowType: 'row',
    wrapperColWidth: 'col-12',
    collection: []
  })
  console.log(`Добавлена новая группа ${newId}`)
}

const getSelectedGroupForElement = (elementId: string): number => {
  return elementGroupSelection.value.get(elementId) || 1
}

const setSelectedGroupForElement = (elementId: string, groupId: number) => {
  elementGroupSelection.value.set(elementId, groupId)
  console.log(`Элемент ${elementId} теперь использует группу ${groupId}`)
}

const getGroupData = (groupId: number): DataGroup | undefined => {
  return dataGroups.value.find(g => g.id === groupId)
}

// Вычисляемые свойства
const filteredElements = computed(() => {
  let elements = allElements.value

  // Применяем фильтры
  if (filters.value.elem_type.length > 0) {
    elements = elements.filter(element => {
      if (!element.elem_type) return false
      return filters.value.elem_type.some(selectedType =>
        element.elem_type.includes(selectedType)
      )
    })
  }

  if (filters.value.collection.length > 0) {
    elements = elements.filter(element => {
      if (!element.collection) return false
      return filters.value.collection.some(selectedCollection =>
        element.collection.includes(selectedCollection)
      )
    })
  }

  // Фильтр "показать только выбранные"
  if (showOnlySelected.value) {
    elements = elements.filter(element => selectedElements.value.some(e => e.id === element.id))
  }

  // Сортируем по полю number по возрастанию
  return elements.sort((a, b) => {
    const numA = a.number || ''
    const numB = b.number || ''
    return numA.localeCompare(numB, undefined, { numeric: true })
  })
})

// Пагинированные элементы
const paginatedElements = computed(() => {
  const start = first.value
  const end = start + rowsPerPage.value
  return filteredElements.value.slice(start, end)
})

// Функции подсветки синтаксиса
const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

// Методы загрузки данных
const loadOptions = async () => {
  try {
    const elements = await getItems({
      collection: 'welem_proto',
      params: {
        limit: -1,
        fields: ['elem_type', 'collection']
      }
    })

    const collections = new Set<string>()
    const filterCollections = new Set<string>()
    const elemTypes = new Set<string>()

    elements.forEach((element: any) => {
      if (element.elem_type) {
        element.elem_type.forEach((type: string) => {
          elemTypes.add(type)
        })
      }
      if (element.collection) {
        element.collection.forEach((coll: string) => {
          collections.add(coll)
          filterCollections.add(coll)
        })
      }
    })

    collectionOptions.value = Array.from(collections).sort()
    filterOptions.value.elem_type = Array.from(elemTypes).sort()
    filterOptions.value.collection = Array.from(filterCollections).sort()
  } catch (error) {
    console.error('Error loading options:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить опции',
      life: 3000
    })
  }
}

const loadAllElements = async () => {
  loading.value = true

  try {
    const elements = await getItems({
      collection: 'welem_proto',
      params: {
        limit: -1,
        fields: ['*']
      }
    })

    allElements.value = elements
    console.log(`Загружено ${elements.length} элементов из базы`)

  } catch (error) {
    console.error('Error loading elements:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить элементы',
      life: 3000
    })
  } finally {
    loading.value = false
  }
}

// Функции для управления фильтрами
const clearAllFilters = () => {
  filters.value.elem_type = []
  filters.value.collection = []
  console.log('Все фильтры сброшены')
}

const toggleShowOnlySelected = () => {
  showOnlySelected.value = !showOnlySelected.value
  console.log('Показать только выбранные:', showOnlySelected.value)
}

const clearAllSelections = () => {
  selectedElements.value = []
  selectedForCombination.value = []
  console.log('Все выборы сняты')
}

const onPageChange = (event: any) => {
  first.value = event.first
  rowsPerPage.value = event.rows
  console.log('Переход на страницу:', event.page, 'first:', event.first, 'rows:', event.rows)
}

// Основные функции генерации скриншотов и сохранения
const generateScreenshots = async () => {
  if (selectedElements.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите элементы для генерации скриншотов',
      life: 3000
    })
    return
  }

  generating.value = true

  try {
    console.log(`Генерация скриншотов для ${selectedElements.value.length} элементов в режиме: ${currentMode.value}`)

    let generatedCount = 0
    const skippedCount = 0

    for (const element of selectedElements.value) {
      try {
        // Применяем модификации в зависимости от режима
        let modifiedElement = { ...element }

        switch (currentMode.value) {
          case 'variation':
            if (selectedVariationTypes.value.length > 0) {
              modifiedElement = await applyVariationForScreenshot(element, selectedVariationTypes.value[0])
            }
            break
          case 'theming':
            // Используем первую доступную тему для демонстрации
            if (availableThemes.value.length > 0) {
              modifiedElement = await applyThemeForScreenshot(element, availableThemes.value[0])
            }
            break
          case 'content':
            if (element.hbs) {
              const contentData = { title: 'Заголовок', text: 'Текст', button: 'Кнопка' }
              modifiedElement = await applyContentForScreenshot(element, contentData)
            }
            break
        }

        // Генерируем скриншот
        const tempUrl = await generateElementScreenshot(modifiedElement)
        if (tempUrl) {
          tempScreenshots.value.set(element.id!, tempUrl)
          generatedCount++
          console.log(`✅ Скриншот создан для элемента ${element.number}`)
        }

      } catch (error) {
        console.error(`Ошибка генерации для элемента ${element.number}:`, error)
      }
    }

    const message = generatedCount > 0
      ? `Создано ${generatedCount} скриншотов`
      : 'Нет данных для генерации'

    toast.add({
      severity: 'success',
      summary: 'Генерация завершена',
      detail: message,
      life: 3000
    })

  } catch (error) {
    console.error('Error generating screenshots:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сгенерировать скриншоты',
      life: 3000
    })
  } finally {
    generating.value = false
  }
}

// Применение вариации для скриншота
const applyVariationForScreenshot = async (element: WElem, variationType: string) => {
  const variations = getVariationsByType(variationType)
  if (variations.length > 0) {
    const { html, hbs } = applyVariationToHtml(element.html || '', element.hbs || '', variations[0])
    return { ...element, html, hbs }
  }
  return element
}

// Применение темы для скриншота
const applyThemeForScreenshot = async (element: WElem, theme: any) => {
  const { html, hbs, css } = applyThemeToHtml(element.html || '', element.hbs || '', theme)
  return { ...element, html, hbs, css: (element.css || '') + '\n' + css }
}

// Применение контента для скриншота
const applyContentForScreenshot = async (element: WElem, contentData: any) => {
  try {
    const template = handlebars.compile(element.hbs || '')
    const newHtml = template(contentData)
    return { ...element, html: newHtml }
  } catch (error) {
    console.error('Ошибка применения контента для скриншота:', error)
    return element
  }
}

// Генерация скриншота элемента
const generateElementScreenshot = async (element: WElem): Promise<string | null> => {
  try {
    const css = element.css || ''
    const js = element.js || ''
    const html = element.html || ''

    // Добавляем Bootstrap CSS
    const bootstrapCss = `
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
      <style>
        body { padding: 20px; background: #f8f9fa; }
      </style>
    `

    const fullHtml = '<!DOCTYPE html>' +
      '<html lang="en">' +
      '<head>' +
      '<meta charset="UTF-8">' +
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
      '<title>' + (element.title || '') + '</title>' +
      bootstrapCss +
      css +
      '</head>' +
      '<body>' +
      html +
      js +
      '</body>' +
      '</html>'

    const response = await $fetch('/api/capture-html-screenshot-temp', {
      method: 'POST',
      body: {
        html: fullHtml,
        width: 1400,
        height: 800
      },
      responseType: 'blob'
    })

    const tempUrl = URL.createObjectURL(response as Blob)
    return tempUrl

  } catch (error) {
    console.error(`Ошибка создания скриншота для ${element.number}:`, error)
    return null
  }
}

const saveSelectedElements = async () => {
  if (selectedElements.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите элементы для сохранения',
      life: 3000
    })
    return
  }

  saving.value = true

  try {
    const elementsToSave = []

    for (let i = 0; i < selectedElements.value.length; i++) {
      const element = selectedElements.value[i]
      const counter = String(i + 1).padStart(2, '0')

      // Сохраняем скриншот если есть
      let screenshotId = null
      const tempBlobUrl = tempScreenshots.value.get(element.id!)
      if (tempBlobUrl) {
        const filename = `welem_gen2_${element.number}_${element.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}.png`
        screenshotId = await saveBlobToDirectus(tempBlobUrl, filename)
      }

      const elementData = {
        number: `${formData.value.number}-${counter}`,
        title: `${formData.value.title}-${counter}`,
        collection: formData.value.collection,
        html: element.html,
        css: element.css || '',
        js: element.js || '',
        hbs: element.hbs || '',
        json: element.json || '',
        sketch: screenshotId,
        status: 'draft'
      }

      elementsToSave.push(elementData)
    }

    // Сохраняем элементы
    const savedElements = await createItems({
      collection: 'welem_proto',
      items: elementsToSave
    })

    // Анализируем HTML
    const analysisRecords = savedElements.map((element: any, index: number) => ({
      id: element.id,
      html: element.html
    }))

    const { results } = await $fetch('/api/batch-analyze-html-element', {
      method: 'POST',
      body: { records: analysisRecords }
    })

    // Обновляем с результатами анализа
    for (const result of results) {
      await updateItem({
        collection: 'welem_proto',
        id: result.id,
        item: {
          elem_type: result.elementTypes,
          composition: result.treeStructure
        }
      })
    }

    // Очищаем временные данные
    tempScreenshots.value.forEach(url => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url)
      }
    })

    selectedElements.value = []
    tempScreenshots.value.clear()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранено ${savedElements.length} элементов`,
      life: 3000
    })

  } catch (error) {
    console.error('Error saving elements:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить элементы',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}

// Загрузка опций для сайдбара (точная копия из welem-proto)
const loadSidebarOptions = async () => {
  try {
    // Загружаем опции для elem_type и collection из существующих записей
    const elements = await getItems({
      collection: 'welem_proto',
      params: {
        limit: -1,
        fields: ['elem_type', 'collection'],
      },
    })

    if (Array.isArray(elements)) {
      const elemTypes = new Set()
      const collections = new Set()

      elements.forEach((item: any) => {
        item.elem_type?.forEach((type: any) => elemTypes.add(type))
        item.collection?.forEach((collection: any) => collections.add(collection))
      })

      elemTypeOptions.value = Array.from(elemTypes) as string[]
      collectionOptions.value = Array.from(collections) as string[]
    }
  } catch (error) {
    console.error('Error loading options:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить справочники',
    })
  }
}

const loadWblockOptions = async () => {
  try {
    const blocks = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: ['id', 'title', 'number'],
      },
    })

    if (Array.isArray(blocks)) {
      wblockOptions.value = blocks.map((block: any) => ({
        value: block.id,
        label: `${block.number} - ${block.title}` || block.id,
      }))
    }
  } catch (error) {
    console.error('Ошибка загрузки блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить блоки для выбора',
      life: 3000,
    })
  }
}

// Инициализация
onMounted(async () => {
  await Promise.all([
    loadOptions(),
    loadAllElements(),
    loadSidebarOptions(),
    loadWblockOptions()
  ])

  // Загружаем темы только на клиенте
  availableThemes.value = getAllThemes()
})
</script>

<style scoped>
.my-editor {
    background: #f3f3f3;
    color: #666;
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 10px;
    line-height: 1.4;
    padding: 2px;
  }

.my-editor .prism-editor__textarea:focus {
  outline: none;
}

.masonry-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 1rem;
  grid-auto-rows: min-content;
  align-items: start;
}

@media (max-width: 1024px) {
  .masonry-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .masonry-grid {
    grid-template-columns: 1fr;
  }
}

/* Улучшенное выделение выбранных карточек */
.selected-card {
  border: 1px solid #68a0f8 !important;
  background-color: rgba(59, 131, 246, 0.24) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* Стили для режимов работы */
.work-mode-panel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
}

.theme-preview {
  transition: all 0.3s ease;
}

.theme-preview:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Анимации для карточек элементов */
.element-card {
  transition: all 0.3s ease;
}

.element-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Градиентные фоны для разных режимов */
.mode-combination {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.mode-variation {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.mode-theming {
  background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
}

.mode-content {
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
}

/* Простые кастомные чекбоксы заменены на div элементы */

/* Чекбокс для вариаций (оранжевый) */
:deep(.variation-checkbox .p-checkbox-box) {
  border: 2px solid #f59e0b !important;
  background: #fffbeb !important;
}

:deep(.variation-checkbox .p-checkbox-box.p-highlight) {
  background: #f59e0b !important;
  border-color: #f59e0b !important;
}

/* Чекбокс для тем (фиолетовый) */
:deep(.theme-checkbox .p-checkbox-box) {
  border: 2px solid #8b5cf6 !important;
  background: #faf5ff !important;
}

:deep(.theme-checkbox .p-checkbox-box.p-highlight) {
  background: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
}

/* Чекбокс для контента (розовый) */
:deep(.content-checkbox .p-checkbox-box) {
  border: 2px solid #ec4899 !important;
  background: #fdf2f8 !important;
}

:deep(.content-checkbox .p-checkbox-box.p-highlight) {
  background: #ec4899 !important;
  border-color: #ec4899 !important;
}
</style>
