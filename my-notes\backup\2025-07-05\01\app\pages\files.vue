<template>
  <DataView
    :value="sortedFiles"
    :sort-order="sortOrder"
    :sort-field="sortField"
  >
    <template #header>
      <Select
        v-model="sortKey"
        :options="sortOptions"
        option-label="label"
        placeholder="Сортировка"
        @change="onSortChange($event)"
      />
    </template>
    <template #list="slotProps">
      <div class="flex flex-col">
        <div v-for="(file, index) in sortedFiles" :key="file.id">
          <div
            class="flex flex-col sm:flex-row sm:items-center p-6 gap-4"
            :class="{
              'border-t border-surface-200 dark:border-surface-700':
                index !== 0,
            }"
          >
            <div class="md:w-40 relative">
              <img
                class="block xl:block mx-auto rounded w-full"
                src="https://fm-demo.ru/wp-content/uploads/2025/02/spa-salon-image-052.jpg"
                :alt="file.title"
              />
              <div
                class="absolute bg-black/70 rounded-border"
                style="left: 4px; top: 4px; margin-top: 20px"
              >
                <Tag v-if="file.tags">{{ file.tags.join(', ') }}</Tag>
              </div>
            </div>
            <div
              class="flex flex-col md:flex-row justify-between md:items-center flex-1 gap-6"
            >
              <div
                class="flex flex-row md:flex-col justify-between items-start gap-2"
              >
                <div style="margin-top: 7px">
                  <span
                    class="category dark:text-surface-400 font-medium text-sm text-surface-500"
                    >{{ file.type }}</span
                  >
                  <div class="text-lg font-medium mt-2">
                    <NuxtLink :to="`/file/${encodeURIComponent(file.title)}`">
                      {{ file.title }}
                    </NuxtLink>
                  </div>
                </div>
                <div class="bg-surface-100 p-1" style="border-radius: 30px">
                  <div
                    class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2"
                    style="
                      border-radius: 30px;
                      box-shadow:
                        0px 1px 2px 0px rgba(0, 0, 0, 0.04),
                        0px 1px 2px 0px rgba(0, 0, 0, 0.06);
                    "
                  >
                    <span class="text-surface-900 font-medium text-sm"
                      >Рейтинг</span
                    >
                    <i class="pi pi-star-fill text-yellow-500" />
                  </div>
                </div>
              </div>
              <div class="flex flex-col md:items-end gap-8">
                <span class="text-xl font-semibold">цена</span>
                <div class="flex flex-row-reverse md:flex-row gap-2">
                  <Button icon="pi pi-heart" outlined />
                  <Button
                    icon="pi pi-shopping-cart"
                    label="Купить"
                    class="flex-auto md:flex-initial whitespace-nowrap"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DataView>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed, watch } from 'vue'
  import DataView from 'primevue/dataview'
  import Select from 'primevue/select'

  const files = ref([])
  const sortKey = ref('title')
  const sortOrder = ref(1)
  const sortField = ref('title')
  const sortOptions = ref([
    { label: 'По возрастанию', value: 'title' },
    { label: 'По убыванию', value: '!title' },
  ])

  // Используем computed для сортировки файлов
  const sortedFiles = computed(() => {
    return [...files.value].sort((a, b) => {
      let valueA = a[sortKey.value]
      let valueB = b[sortKey.value]

      if (sortOrder.value === -1) {
        ;[valueA, valueB] = [valueB, valueA]
      }

      if (typeof valueA === 'string') {
        return valueA.localeCompare(valueB)
      } else {
        return valueA - valueB
      }
    })
  }, [sortKey, sortOrder, sortField]) //  Добавили sortField

  onMounted(async () => {
    try {
      const response = await fetch('http://localhost:8055/files')
      const data = await response.json()
      files.value = data.data
    } catch (error) {
      console.error('Ошибка при получении файлов:', error)
    }
  })

  const onSortChange = (event: { value: { value: string } }) => {
    const value = event.value.value
    sortKey.value = value.replace('!', '')
    sortOrder.value = value.startsWith('!') ? -1 : 1
  }
</script>
