// server/api/html-to-dynamic-pug.js
import { defineEventHandler, readBody, createError } from 'h3';
import html2pug from 'html2pug';
import { load } from 'cheerio';

export default defineEventHandler(async (event) => {
  try {
    const { html } = await readBody(event);
    
    if (!html) {
      throw createError({
        statusCode: 400,
        statusMessage: 'HTML не предоставлен'
      });
    }
    
    // Конвертируем HTML в Pug с улучшенной обработкой атрибутов
    const pugCode = convertHtmlToPug(html);
    
    // Добавляем маркеры для dynamic_pug
    const dynamicPugCode = addDynamicPugMarkers(pugCode);
    
    return {
      success: true,
      pug: pugCode,
      dynamic_pug: dynamicPugCode
    };
    
  } catch (error) {
    console.error('❌ Ошибка при конвертации HTML в Pug:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message
    });
  }
});

// Функция для конвертации HTML в Pug с улучшенной обработкой атрибутов
function convertHtmlToPug(html) {
  if (!html) return '';
  
  console.log('🔄 Конвертация HTML в Pug...');
  
  try {
    // Очищаем HTML от внешних тегов и обрабатываем сложные атрибуты
    const cleanedHtml = cleanHtmlForPug(html);
    
    // Пробуем конвертировать с разными настройками
    try {
      // Первая попытка - стандартные настройки
      const pugCode = html2pug(cleanedHtml, {
        fragment: true, // Обрабатываем как фрагмент HTML, а не полный документ
        doctype: false,  // Не добавляем doctype
        commas: false,   // Не используем запятые
        doubleQuotes: true // Используем двойные кавычки
      });
      console.log('✅ HTML успешно конвертирован в Pug');
      return pugCode;
    } catch (firstError) {
      console.warn(`⚠️ Первая попытка конвертации HTML в Pug не удалась: ${firstError.message}`);
      
      try {
        // Вторая попытка - упрощенный HTML с сохранением структуры атрибутов
        // Создаем карту для временного хранения сложных атрибутов
        const attrMap = new Map();
        let attrIndex = 0;
        
        // Временно заменяем сложные атрибуты на простые
        const simplifiedHtml = cleanedHtml.replace(/([\w-]+=)(['"])(\{.*?\}|.*?['"].*?|.*?<.*?>.*?)\2/g, (match, attrName, quote, attrValue) => {
          const placeholder = `data-placeholder-${attrIndex++}`;
          attrMap.set(placeholder, match);
          return `${placeholder}=${quote}placeholder${quote}`;
        });
        
        const pugCode = html2pug(simplifiedHtml, {
          fragment: true,
          doctype: false,
          bodyless: true // Игнорируем теги html, head и body
        });
        
        // Восстанавливаем оригинальные атрибуты в Pug коде
        let restoredPugCode = pugCode;
        for (const [placeholder, original] of attrMap.entries()) {
          // Преобразуем HTML атрибут в формат Pug
          const pugAttr = original.replace(/([\w-]+=)(['"])(.*?)\2/g, (match, name, quote, value) => {
            return `${name.slice(0, -1)}="${value}"`;
          });
          restoredPugCode = restoredPugCode.replace(new RegExp(`${placeholder}="placeholder"`, 'g'), pugAttr);
        }
        
        console.log('✅ HTML успешно конвертирован в Pug (вторая попытка)');
        return restoredPugCode;
      } catch (secondError) {
        console.warn(`⚠️ Вторая попытка конвертации HTML в Pug не удалась: ${secondError.message}`);
        
        // Третья попытка - создаем базовый Pug вручную с сохранением атрибутов
        console.log('🔄 Создание базового Pug кода...');
        
        // Извлекаем основной тег и его атрибуты
        const tagMatch = cleanedHtml.match(/<([a-z][\w-]*)([^>]*)>/i);
        if (tagMatch) {
          const [, tagName, attributes] = tagMatch;
          // Преобразуем атрибуты в формат Pug
          const pugAttributes = attributes.trim()
            .replace(/([\w-]+=)(['"])(.*?)\2/g, (match, name, quote, value) => {
              return `${name.slice(0, -1)}="${value}"`;
            });
          
          // Создаем базовый Pug код
          const pugCode = `${tagName}${pugAttributes ? '(' + pugAttributes + ')' : ''}\n  | [Содержимое блока не может быть автоматически конвертировано в Pug]`;
          console.log('✅ Создан базовый Pug код');
          return pugCode;
        } else {
          // Если не удалось извлечь тег, создаем заглушку
          console.log('⚠️ Создана Pug-заглушка');
          return `div\n  | [Содержимое блока не может быть автоматически конвертировано в Pug]`;
        }
      }
    }
  } catch (error) {
    console.error('❌ Ошибка при конвертации HTML в Pug:', error.message);
    return `div\n  | [Ошибка конвертации HTML в Pug: ${error.message}]`;
  }
}

// Функция для очистки HTML от внешних тегов html, head и body перед конвертацией в Pug
function cleanHtmlForPug(html) {
  if (!html) return '';
  
  console.log('🧹 Очистка HTML от внешних тегов для конвертации в Pug...');
  
  try {
    // Создаем карту для хранения всех сложных атрибутов
    const attributePlaceholders = new Map();
    let placeholderIndex = 0;
    
    // Функция для создания уникального токена
    const createPlaceholder = (content, prefix = 'JSON') => {
      const token = `__${prefix}_PLACEHOLDER_${placeholderIndex++}__`;
      attributePlaceholders.set(token, content);
      return token;
    };
    
    // Предварительная обработка проблемных атрибутов с JSON-данными
    let processedHtml = html;
    
    // 1. Обработка data-атрибутов с JSON
    processedHtml = processedHtml.replace(/data-[\w-]+=(['"])(\{.*?\})\1/g, (match, quote, jsonContent) => {
      const token = createPlaceholder(jsonContent, 'JSON');
      return match.replace(jsonContent, token);
    });
    
    // 2. Обработка атрибутов с вложенными JSON-объектами
    processedHtml = processedHtml.replace(/([\w-]+=)(['"])(.*?\{.*?\}.*?)\2/g, (match, attrName, quote, attrValue) => {
      // Пропускаем, если атрибут уже был обработан или это data-атрибут
      if (match.startsWith('data-') || attrValue.includes('__JSON_PLACEHOLDER_')) {
        return match;
      }
      const token = createPlaceholder(attrValue, 'ATTR');
      return `${attrName}${quote}${token}${quote}`;
    });
    
    // 3. Обработка атрибутов с кавычками внутри значений
    processedHtml = processedHtml.replace(/([\w-]+=)(['"])(.*?['"].*?)\2/g, (match, attrName, quote, attrValue) => {
      // Пропускаем, если атрибут уже был обработан
      if (attrValue.includes('__JSON_PLACEHOLDER_') || attrValue.includes('__ATTR_PLACEHOLDER_')) {
        return match;
      }
      
      const token = createPlaceholder(attrValue, 'QUOTE');
      return `${attrName}${quote}${token}${quote}`;
    });
    
    // 4. Обработка атрибутов с угловыми скобками
    processedHtml = processedHtml.replace(/([\w-]+=)(['"])(.*?<.*?>.*?)\2/g, (match, attrName, quote, attrValue) => {
      // Пропускаем, если атрибут уже был обработан
      if (attrValue.includes('__') && attrValue.includes('_PLACEHOLDER_')) {
        return match;
      }
      
      const token = createPlaceholder(attrValue, 'HTML');
      return `${attrName}${quote}${token}${quote}`;
    });
    
    // Используем cheerio для извлечения содержимого body
    const $ = load(processedHtml, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    });
    
    // Если есть тег body, извлекаем его содержимое
    let cleanedHtml;
    if ($('body').length) {
      console.log('✅ Найден тег body, извлекаем его содержимое');
      cleanedHtml = $('body').html() || processedHtml;
    } else {
      // Если нет тега body, используем исходный HTML
      console.log('ℹ️ Тег body не найден, используем исходный HTML');
      cleanedHtml = processedHtml;
    }
    
    // Восстанавливаем оригинальные значения атрибутов после конвертации
    for (const [token, originalValue] of attributePlaceholders.entries()) {
      cleanedHtml = cleanedHtml.replace(new RegExp(token, 'g'), originalValue);
    }
    
    // Дополнительная обработка для html2pug
    // Экранируем проблемные символы в атрибутах
    cleanedHtml = cleanedHtml
      // Заменяем неэкранированные кавычки внутри атрибутов
      .replace(/([\w-]+=)(['"])(.*?)\2/g, (match, attrName, quote, attrValue) => {
        // Экранируем кавычки внутри значения атрибута
        const escapedValue = attrValue
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&apos;');
        return `${attrName}${quote}${escapedValue}${quote}`;
      });
    
    return cleanedHtml;
  } catch (error) {
    console.error('❌ Ошибка при очистке HTML для Pug:', error.message);
    return html;
  }
}

// Функция для декодирования HTML-сущностей в атрибутах
function decodeHtmlEntities(html) {
  if (!html) return '';
  
  console.log('🔄 Декодирование HTML-сущностей в атрибутах...');
  
  // Заменяем закодированные кавычки и другие сущности
  const decoded = html
    .replace(/&amp;quot;/g, '"') // Заменяем &amp;quot; на "
    .replace(/&quot;/g, '"')     // Заменяем &quot; на "
    .replace(/&amp;apos;/g, "'") // Заменяем &amp;apos; на '
    .replace(/&apos;/g, "'")     // Заменяем &apos; на '
    .replace(/&amp;amp;/g, '&')   // Заменяем &amp;amp; на &
    .replace(/&amp;/g, '&');      // Заменяем &amp; на &
  
  return decoded;
}

// Функция для автоматического добавления маркеров //s и //e для повторяющихся элементов
function addDynamicPugMarkers(pugCode) {
  if (!pugCode) return pugCode;
  
  console.log('🔍 Анализ Pug кода для определения повторяющихся элементов...');
  
  try {
    // Разбиваем код на строки
    const lines = pugCode.split('\n');
    
    // Ищем повторяющиеся блоки кода
    const processedLines = [];
    const patterns = {};
    let inMarkedBlock = false;
    
    // Функция для определения отступа строки
    const getIndent = (line) => {
      const match = line.match(/^\s*/);
      return match ? match[0].length : 0;
    };
    
    // Проходим по всем строкам и ищем повторяющиеся паттерны
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const indent = getIndent(line);
      
      // Пропускаем пустые строки и комментарии
      if (!line.trim() || line.trim().startsWith('//')) {
        processedLines.push(line);
        continue;
      }
      
      // Ищем потенциальные повторяющиеся элементы
      if (indent === 0 && line.trim().match(/^[a-z][\w-]*(\.([\w-]+))*$/)) {
        // Проверяем, есть ли похожие элементы дальше
        let patternFound = false;
        let patternLength = 0;
        let patternCount = 0;
        
        // Собираем текущий блок до следующего элемента с тем же отступом
        const currentBlock = [line];
        let j = i + 1;
        while (j < lines.length && (getIndent(lines[j]) > indent || !lines[j].trim())) {
          currentBlock.push(lines[j]);
          j++;
        }
        
        // Если блок слишком короткий, пропускаем
        if (currentBlock.length < 2) {
          processedLines.push(line);
          continue;
        }
        
        // Проверяем, повторяется ли этот блок
        const blockStr = currentBlock.join('\n');
        if (patterns[blockStr]) {
          // Нашли повторение блока
          patternFound = true;
          patternCount = ++patterns[blockStr].count;
          patternLength = currentBlock.length;
        } else {
          // Запоминаем блок для будущих сравнений
          patterns[blockStr] = { count: 1, processed: false };
        }
        
        // Если нашли повторяющийся блок и он еще не был обработан
        if (patternFound && !patterns[blockStr].processed) {
          // Помечаем все экземпляры этого блока как обработанные
          patterns[blockStr].processed = true;
          
          // Добавляем маркер начала блока
          processedLines.push('//s');
          inMarkedBlock = true;
          
          // Добавляем содержимое блока
          processedLines.push(...currentBlock);
          
          // Пропускаем обработанные строки
          i += patternLength - 1;
          
          // Проверяем, есть ли еще экземпляры этого блока дальше
          let nextIndex = j;
          while (nextIndex < lines.length) {
            // Ищем начало потенциального совпадения
            if (lines[nextIndex].trim() === currentBlock[0].trim()) {
              // Проверяем, совпадает ли весь блок
              let matches = true;
              for (let k = 1; k < currentBlock.length; k++) {
                if (nextIndex + k >= lines.length || lines[nextIndex + k].trim() !== currentBlock[k].trim()) {
                  matches = false;
                  break;
                }
              }
              
              if (matches) {
                // Нашли еще одно совпадение, пропускаем его
                nextIndex += currentBlock.length;
                continue;
              }
            }
            nextIndex++;
          }
        } else {
          // Добавляем строку как обычно
          processedLines.push(line);
        }
      } else {
        // Добавляем строку как обычно
        processedLines.push(line);
      }
    }
    
    // Если мы находимся внутри маркированного блока, закрываем его
    if (inMarkedBlock) {
      processedLines.push('//e');
    }
    
    // Собираем результат
    return processedLines.join('\n');
  } catch (error) {
    console.error('❌ Ошибка при добавлении маркеров для dynamic_pug:', error.message);
    return pugCode; // Возвращаем исходный код в случае ошибки
  }
}