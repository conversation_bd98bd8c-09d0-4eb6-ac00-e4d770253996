import { promises as fs } from 'fs'
import { join } from 'path'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { type, data, filename } = body

    if (!type || !data) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Требуются параметры type и data'
      })
    }

    // Определяем директорию для сохранения
    const userDataDir = join(process.cwd(), 'user-data')
    
    // Создаем директорию если не существует
    try {
      await fs.access(userDataDir)
    } catch {
      await fs.mkdir(userDataDir, { recursive: true })
    }

    // Определяем имя файла на основе типа
    const fileName = filename || `${type}-${Date.now()}.json`
    const filePath = join(userDataDir, fileName)

    // Сохраняем данные в файл
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8')

    return {
      success: true,
      message: 'Настройки сохранены в файл',
      filename: fileName,
      path: filePath
    }

  } catch (error) {
    console.error('Ошибка сохранения настроек:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Ошибка сохранения настроек в файл'
    })
  }
})
