import { defineEventHand<PERSON>, readBody, createError } from 'h3'
import puppeteer from 'puppeteer'

interface UrlScreenshotRequest {
  url: string
  filename?: string
  width?: number
  height?: number
}

interface UrlScreenshotResponse {
  fileId: string
  filename: string
}

export default defineEventHandler(async (event) => {
  const { url, filename = 'screenshot', width = 1400, height = 800 } = await readBody<UrlScreenshotRequest>(event)

  if (!url) {
    throw createError({
      statusCode: 400,
      message: 'URL is required',
    })
  }

  console.log(`🔄 Создание скриншота с URL: ${url}`)

  // Запускаем браузер
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  })

  try {
    const page = await browser.newPage()

    // Устанавливаем размер окна браузера
    await page.setViewport({ width, height })

    console.log(`📐 Установлен viewport: ${width}x${height}`)

    // Переходим на указанный URL с максимальным ожиданием
    await page.goto(url, { waitUntil: 'networkidle0', timeout: 60000 })

    console.log('🌐 Страница загружена')

    // Оптимизированная базовая задержка для полной загрузки контента
    console.log('⏱️ Базовая задержка 3000ms для полной загрузки контента...')
    await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))

    // Ждем загрузки всех изображений с увеличенным timeout
    console.log('🖼️ Ожидание загрузки всех изображений с timeout 10000ms...')
    await page.evaluate(() => {
      return Promise.all(
        Array.from(document.images)
          .filter(img => !img.complete)
          .map(img => new Promise(resolve => {
            img.onload = img.onerror = resolve
            // Увеличенный timeout для изображений
            setTimeout(resolve, 10000)
          }))
      )
    })

    // УМНАЯ прокрутка для активации scroll-анимаций (имитация ручного процесса)
    console.log('📜 УМНАЯ прокрутка для активации scroll-анимаций...')
    await page.evaluate(() => {
      return new Promise((resolve) => {
        // Имитируем ручной процесс: медленная прокрутка вниз, затем быстро вверх
        const scrollToBottom = () => {
          // Медленная прокрутка вниз по частям для активации всех scroll-триггеров
          let currentScroll = 0
          const maxScroll = document.body.scrollHeight
          const scrollStep = Math.max(200, maxScroll / 10) // Прокручиваем по частям

          const scrollInterval = setInterval(() => {
            currentScroll += scrollStep
            window.scrollTo(0, Math.min(currentScroll, maxScroll))

            if (currentScroll >= maxScroll) {
              clearInterval(scrollInterval)
              // Пауза внизу для активации анимаций
              setTimeout(scrollToTop, 2000)
            }
          }, 300) // Медленная прокрутка для активации всех триггеров
        }

        // Быстрая прокрутка вверх
        const scrollToTop = () => {
          window.scrollTo(0, 0)
          setTimeout(resolve, 1500) // Пауза вверху для стабилизации
        }

        // Начинаем процесс
        setTimeout(scrollToBottom, 1000)
      })
    })

    // Скрываем элемент div.demo-button-wrapper
    await page.evaluate(() => {
      const demoButtons = document.querySelectorAll('div.demo-button-wrapper')
      demoButtons.forEach(button => {
        if (button instanceof HTMLElement) {
          button.style.display = 'none'
        }
      })
    })

    // Оптимизированная финальная стабилизация после всех операций
    console.log('⏱️ Финальная стабилизация 2000ms...')
    await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 2000)))

    // Анализ анимаций на странице
    console.log('🎭 Анализ анимаций на странице...')
    const animationInfo = await page.evaluate(() => {
      const info = {
        hasAnimations: false,
        hasJSAnimations: false,
        hasLibs: false,
        animationCount: 0,
        maxDuration: 0
      }

      // Проверяем CSS анимации
      const allElements = Array.from(document.querySelectorAll('*'))
      allElements.forEach(el => {
        const style = window.getComputedStyle(el)
        if (style.animationDuration !== '0s' || style.transitionDuration !== '0s') {
          info.hasAnimations = true
          info.animationCount++
          const animDuration = parseFloat(style.animationDuration) * 1000
          const transDuration = parseFloat(style.transitionDuration) * 1000
          info.maxDuration = Math.max(info.maxDuration, animDuration, transDuration)
        }
      })

      // Проверяем библиотеки анимаций
      info.hasLibs = !!(window.AOS || window.gsap || window.anime || window.ScrollMagic)

      // Проверяем JS анимации
      info.hasJSAnimations = !!(window.jQuery && window.jQuery.fn.animate) ||
        !!(window.requestAnimationFrame) ||
        document.querySelectorAll('[data-aos], [data-animate], .animate__animated').length > 0

      return info
    })

    // Определяем дополнительное время ожидания на основе анализа
    let additionalWaitTime = 0
    if (animationInfo.hasAnimations || animationInfo.hasJSAnimations) {
      if (animationInfo.maxDuration > 2000) {
        // Для длинных анимаций ждем дольше
        additionalWaitTime = Math.min(animationInfo.maxDuration + 1000, 5000)
      } else if (animationInfo.hasJSAnimations) {
        additionalWaitTime = 1500
      } else if (animationInfo.hasLibs) {
        // Для библиотек анимаций используем стандартную задержку
        additionalWaitTime = 1200
      } else {
        additionalWaitTime = 800
      }

      // Дополнительное время для множественных анимаций
      if (animationInfo.animationCount > 5) {
        additionalWaitTime += 300
      }
    }

    if (additionalWaitTime > 0) {
      console.log(`⏱️ Дополнительное ожидание ${additionalWaitTime}ms для анимаций...`)
      await page.evaluate((delay: number) => new Promise(resolve => setTimeout(resolve, delay)), additionalWaitTime)
    }

    // ПРИНУДИТЕЛЬНАЯ активация всех анимаций
    console.log('🎯 Принудительная активация всех анимаций...')
    await page.evaluate(() => {
      // Принудительно активируем AOS анимации
      if (window.AOS && window.AOS.refresh) {
        window.AOS.refresh()
      }

      // Принудительно активируем WOW анимации
      if (window.WOW) {
        try {
          new window.WOW().init()
        } catch (e) { }
      }

      // Принудительно активируем все элементы с data-aos
      const aosElements = document.querySelectorAll('[data-aos]')
      aosElements.forEach(el => {
        if (el instanceof HTMLElement) {
          el.classList.add('aos-animate')
        }
      })

      // Принудительно активируем все элементы с классами анимаций
      const animatedElements = document.querySelectorAll('.animate__animated, [class*="fade"], [class*="slide"], [class*="zoom"], [class*="bounce"]')
      animatedElements.forEach(el => {
        if (el instanceof HTMLElement) {
          el.style.opacity = '1'
          el.style.visibility = 'visible'
          el.style.transform = 'none'
        }
      })

      // Принудительно показываем скрытые элементы
      const hiddenElements = document.querySelectorAll('[style*="opacity: 0"], [style*="visibility: hidden"]')
      hiddenElements.forEach(el => {
        if (el instanceof HTMLElement) {
          el.style.opacity = '1'
          el.style.visibility = 'visible'
        }
      })
    })

    // Финальная стабилизация
    console.log('⏱️ Финальная стабилизация 1500ms...')
    await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 1500)))

    // Делаем скриншот всей страницы
    console.log('📸 Создание скриншота всей страницы...')
    const screenshot = await page.screenshot({
      type: 'jpeg',
      quality: 90,
      fullPage: true,
    })

    // Генерируем имя файла
    const finalFilename = `${filename}.jpg`

    // Создаем файл в Directus через fetch API
    const formData = new FormData()

    // Устанавливаем title, соответствующий имени файла
    formData.append('title', finalFilename)

    // Создаем Blob из буфера
    const blob = new Blob([screenshot], { type: 'image/jpeg' })
    formData.append('file', blob, finalFilename)

    const response = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error(`Failed to upload screenshot to Directus: ${response.statusText}`)
    }

    const data = await response.json()

    console.log(`✅ Скриншот сохранен с ID: ${data.data.id}`)

    return {
      fileId: data.data.id,
      filename: finalFilename,
    } as UrlScreenshotResponse

  } catch (error) {
    console.error('Error capturing URL screenshot:', error)
    throw createError({
      statusCode: 500,
      message: error.message || 'Failed to capture URL screenshot',
    })
  } finally {
    // Закрываем браузер
    await browser.close()
  }
})
