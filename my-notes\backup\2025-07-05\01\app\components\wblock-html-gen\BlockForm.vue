<template>
  <div class="h-full bg-white p-0 overflow-auto">
    <form class="space-y-4" @submit.prevent="saveBlock">
      <!-- Основные поля -->
      <div class="space-y-2">
        <div class="flex gap-1 mb-0">
          <div class="field w-1/4 mb-0">
            <InputText
              id="number"
              v-model="formData.number"
              placeholder="Номер"
              class="w-full text-xs [&>input]:text-xs"
              style="padding: 4px; font-size: 8px"
            />
          </div>
          <div class="field w-3/4 mb-0">
            <InputText
              id="title"
              v-model="formData.title"
              placeholder="Название"
              class="w-full text-xs"
              required
              style="padding: 4px; font-size: 10px"
            />
          </div>
        </div>        

        <div class="field mb-0" style="margin-bottom: 0">
          <Textarea
            id="description"
            v-model="formData.description"
            rows="2"
            class="w-full text-xs [&>textarea]:text-xs"

            placeholder="Описание"
            style="padding: 2px; font-size: 9px"
          />
        </div>

        <div class="field mb-0" style="margin-top: 0;margin-bottom: 0">
          <Textarea
            id="composition"
            v-model="formData.composition"
            rows="3"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Композиция"
            style="padding: 2px; font-size: 8px"
          />
        </div>

        <div class="field mb-0" style="margin-top: 0">
          <MultiSelect
            v-model="formData.block_type"
            :options="blockTypeOptions"
            display="chip"
            class="text-xs w-full p-0"
            placeholder="Выберите типы блока"
            required
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="flex gap-1">
          <div class="field w-1/3 mb-0" style="margin-bottom: 0">
            <Dropdown
              id="status"
              v-model="formData.status"
              :options="statusOptions"
              option-label="label"
              option-value="value"
              placeholder="Выберите статус"
              class="w-full text-xs"
              style="font-size: 11px"
            />
          </div>

          <div class="field w-2/3 mb-0" style="margin-bottom: 0">
            <MultiSelect
              v-model="formData.concept"
              :options="conceptOptions"
              placeholder="Выберите концепции"
              display="chip"
              class="w-full text-xs"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>

        <div class="flex gap-2">
          <div class="field w-1/2 mb-0" style="margin-bottom: 0">
            <MultiSelect
            v-model="formData.collection"
            :options="collectionOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите коллекции"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
          </div>

          <div class="field w-1/2 mb-0" style="margin-bottom: 0">
            <MultiSelect
              v-model="formData.style"
              :options="styleOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите стили"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="formData.layout"
            :options="layoutOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите макеты"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>
        <div class="field mb-2">
          <MultiSelect
            v-model="formData.welem_proto"
            :options="welemOptions"
            option-label="label"
            option-value="value"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите элементы"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>
        <div class="field mb-2">
          <MultiSelect
            v-model="formData.elements"
            :options="elementOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите типы элементов"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>        

        <div class="field mb-2">
          <MultiSelect
            v-model="formData.graphics"
            :options="graphicsOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите графику"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-0">
          <MultiSelect
            v-model="formData.features"
            :options="featuresOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите особенности"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-0">
          <div class="flex flex-col gap-2">
            <FileUpload
              mode="basic"
              :auto="true"
              accept="image/*"
              :max-file-size="1000000"
              choose-label="Эскиз"
              class="p-button-sm"
              @select="onSketchSelect"
            />
            <Image
              v-if="formData.sketch"
              :src="`http://localhost:8055/assets/${formData.sketch}`"
              alt="Эскиз"
              width="200"
              class="my"
              preview
            />
          </div>
        </div>

        <div class="field mb-0">
          <TabView
            class="text-xs"
            :pt="{
            panelcontainer: { style: 'padding:0' },
          }"
          >
            <TabPanel
              header="HTML/CSS/JS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <div class="space-y-1">
                <PrismEditorWithCopy
                  v-model="formData.html"
                  class="my-editor h-[180px] overflow-auto text-xs max-h-[180px]"
                  :highlight="highlightHtml"
                  placeholder="Введите HTML код"
                  line-numbers
                />
                <div class="flex gap-2">
                  <PrismEditorWithCopy
                    v-model="formData.css"
                    class="my-editor h-[40px] overflow-auto text-xs max-h-[40px]"
                    :highlight="highlightCss"
                    placeholder="CSS код"
                    line-numbers
                  />
                  <PrismEditorWithCopy
                    v-model="formData.js"
                    class="my-editor h-[40px] overflow-auto text-xs max-h-[40px]"
                    :highlight="highlightJs"
                    placeholder="JS код"
                    line-numbers
                  />
                </div>
              </div>
            </TabPanel>
            <TabPanel
              header="HBS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="formData.hbs"
                class="my-editor h-[220px] overflow-auto text-xs max-h-[220px]"
                :highlight="highlightHtml"
                placeholder="Введите HBS код"
                line-numbers
              />
            </TabPanel>
            <TabPanel
              header="JSON"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="formData.json"
                class="my-editor h-[220px] overflow-auto text-xs max-h-[220px]"
                :highlight="highlightJson"
                placeholder="Введите JSON код"
                line-numbers
              />
            </TabPanel>
          </TabView>
        </div>

        <div class="field mb-2">
          <Textarea
            id="notes"
            v-model="formData.notes"
            rows="1"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Заметки"
            style="padding: 2px; font-size: 9px"
          />
        </div>
      </div>

      <!-- Кнопки управления -->
      <div class="flex justify-end gap-2 mt-0">
        <Button
          label="Отмена"
          text
          class="p-button-sm"
          @click="$emit('cancel')"
        />
        <Button label="Сохранить" class="p-button-sm" type="submit" />
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue'
  import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'
  import Button from 'primevue/button'
  import InputText from 'primevue/inputtext'
  import Textarea from 'primevue/textarea'
  import Dropdown from 'primevue/dropdown'
  import MultiSelect from 'primevue/multiselect'
  import Chips from 'primevue/chips'
  import FileUpload from 'primevue/fileupload'
  import Image from 'primevue/image'
  import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'

  import 'vue-prism-editor/dist/prismeditor.min.css'
  import Prism from 'prismjs'
  import 'prismjs/components/prism-clike'
  import 'prismjs/components/prism-markup'
  import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
  import 'prismjs/themes/prism-tomorrow.css'
  import { useDirectusItems } from '#imports'
  import { useToast } from 'primevue/usetoast'
  import Toast from 'primevue/toast'

  const props = defineProps({
    block: {
      type: Object,
      default: () => ({
        number: '',
        title: '',
        description: '',
        composition: '',
        status: '',
        concept: [],
        block_type: [],
        collection: [],
        layout: [],
        elements: [],
        style: [],
        graphics: [],
        features: [],
        sketch: null,
        html: '',
        css: '',
      js: '',
      hbs: '',
      json: '',
      notes: ''
      }),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['save', 'cancel', 'close'])
  const toast = useToast()

  // Опции для выпадающих списков
  const statusOptions = [
    { label: 'Идея', value: 'idea' },
    { label: 'В разработке', value: 'in_progress' },
    { label: 'Готово', value: 'done' },
    { label: 'Архив', value: 'archived' },
  ]

  // Динамические опции
  const { getItems } = useDirectusItems()

  const conceptOptions = ref([])
  const collectionOptions = ref([])
  const blockTypeOptions = ref([])
  const layoutOptions = ref([])
  const elementOptions = ref([])
  const styleOptions = ref([])
  const graphicsOptions = ref([])
  const featuresOptions = ref([])

  // Загрузка опций из Directus
  const loadOptions = async () => {
    try {
      const items = await getItems({
        collection: 'wblock_proto',
        params: { limit: -1 },
      })

      if (Array.isArray(items)) {
        const concepts = new Set()
        const collections = new Set()
        const blockTypes = new Set()
        const layouts = new Set()
        const elements = new Set()
        const styles = new Set()
        const graphics = new Set()
        const features = new Set()

        items.forEach((item) => {
          item.concept?.forEach((c) => concepts.add(c))
          item.collection?.forEach((c) => collections.add(c))
          item.block_type?.forEach((t) => blockTypes.add(t))
          item.layout?.forEach((l) => layouts.add(l))
          item.elements?.forEach((e) => elements.add(e))
          item.style?.forEach((s) => styles.add(s))
          item.graphics?.forEach((g) => graphics.add(g))
          item.features?.forEach((f) => features.add(f))
        })

        conceptOptions.value = Array.from(concepts)
        collectionOptions.value = Array.from(collections)
        layoutOptions.value = Array.from(layouts)
        elementOptions.value = Array.from(elements)
        styleOptions.value = Array.from(styles)
        graphicsOptions.value = Array.from(graphics)
        blockTypeOptions.value = Array.from(blockTypes)
        featuresOptions.value = Array.from(features)
      }
    } catch (error) {
      console.error('Ошибка загрузки опций:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить справочники',
        life: 3000,
      })
    }
  }

  const welemOptions = ref([])

  const loadWelemOptions = async () => {
    try {
      const { getItems } = useDirectusItems()
      const elements = await getItems({
        collection: 'welem_proto',
        params: {
          limit: -1,
          fields: ['id', 'title'],
        },
      })

      if (Array.isArray(elements)) {
        welemOptions.value = elements.map((elem) => ({
          value: elem.id,
          label: elem.title || elem.id,
        }))
      }
    } catch (error) {
      console.error('Ошибка загрузки элементов:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить элементы для выбора',
        life: 3000,
      })
    }
  }

  // Данные формы
  const formData = ref({
    ...props.block,
  })

  // Загрузка данных блока при изменении пропса
  watch(
    () => props.block,
    async (newBlock) => {
      if (newBlock?.id) {
        try {
          const { getItems } = useDirectusItems()

          // Загружаем основной блок
          const [blockData] = await getItems({
            collection: 'wblock_proto',
            params: {
              filter: { id: { _eq: newBlock.id } },
              fields: ['*'],
              limit: 1,
            },
          })

          // Загружаем связанные элементы welem_proto
          const relations = await getItems({
            collection: 'wblock_proto_welem_proto',
            params: {
              filter: { wblock_proto_id: { _eq: newBlock.id } },
              fields: ['welem_proto_id'],
            },
          })

          formData.value = {
            ...blockData,
            concept: blockData.concept?.map((c) => c.id || c) || [],
            collection: blockData.collection?.map((c) => c.id || c) || [],
            layout: blockData.layout?.map((l) => l.id || l) || [],
            elements: blockData.elements?.map((e) => e.id || e) || [],
            style: blockData.style?.map((s) => s.id || s) || [],
            graphics: blockData.graphics?.map((g) => g.id || g) || [],
            features: blockData.features?.map((f) => f.id || f) || [],
            block_type: blockData.block_type?.map((t) => t.id || t) || [],
            welem_proto: relations.map((r) => r.welem_proto_id) || [],
          }
        } catch (error) {
          console.error('Error loading block data:', error)
          toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось загрузить данные блока',
            life: 3000,
          })
        }
      } else {
        // Сброс формы для нового блока
        formData.value = {
          number: '',
          title: '',
          description: '',
          composition: '',
          status: 'idea',
          concept: [],
          block_type: [],
          collection: [],
          layout: [],
          elements: [],
          style: [],
          graphics: [],
          features: [],
          welem_proto: [],
          sketch: null,
          html: '',
          notes: '',
        }
      }
    },
    { immediate: true },
  )

  // Подсветка кода
  function highlightHtml(code) {
    return Prism.highlight(code, Prism.languages.markup, 'html')
  }
  const highlightHbs = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

  // Обработка загрузки эскиза
  async function onSketchSelect(event) {
    const file = event.files[0]
    if (!file) return

    try {
      const uploadFormData = new FormData() // Изменили имя переменной
      uploadFormData.append('file', file)

      const response = await fetch('http://localhost:8055/files', {
        method: 'POST',
        body: uploadFormData, // Используем новое имя переменной
      })

      if (!response.ok) throw new Error('Ошибка загрузки файла')

      const { data } = await response.json()
      formData.value.sketch = data.id // Теперь formData.value определен корректно

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Эскиз загружен',
        life: 3000,
      })
    } catch (error) {
      console.error('Ошибка при загрузке эскиза:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить эскиз',
        life: 3000,
      })
    }
  }

  // Сохранение блока
  async function saveBlock() {
    try {
      const { updateItem, createItems, deleteItems } = useDirectusItems()

      // Подготовка данных для сохранения (без welem_proto)
      const saveData = {
        ...formData.value,
        welem_proto: undefined, // Исключаем из основного объекта
      }

      // Очистка данных от возможных вложенных объектов
      const cleanData = {
        ...saveData,
        concept: Array.isArray(saveData.concept)
          ? saveData.concept.map((c) => (typeof c === 'object' ? c.id || c : c))
          : saveData.concept,
        collection: Array.isArray(saveData.collection)
          ? saveData.collection.map((c) =>
              typeof c === 'object' ? c.id || c : c,
            )
          : saveData.collection,
        layout: Array.isArray(saveData.layout)
          ? saveData.layout.map((l) => (typeof l === 'object' ? l.id || l : l))
          : saveData.layout,
        elements: Array.isArray(saveData.elements)
          ? saveData.elements.map((e) =>
              typeof e === 'object' ? e.id || e : e,
            )
          : saveData.elements,
        style: Array.isArray(saveData.style)
          ? saveData.style.map((s) => (typeof s === 'object' ? s.id || s : s))
          : saveData.style,
        graphics: Array.isArray(saveData.graphics)
          ? saveData.graphics.map((g) =>
              typeof g === 'object' ? g.id || g : g,
            )
          : saveData.graphics,
        features: Array.isArray(saveData.features)
          ? saveData.features.map((f) =>
              typeof f === 'object' ? f.id || f : f,
            )
          : saveData.features,
        block_type: Array.isArray(saveData.block_type)
          ? saveData.block_type.map((t) =>
              typeof t === 'object' ? t.id || t : t,
            )
          : saveData.block_type,
      }

      let savedBlock
      if (props.isEdit && props.block?.id) {
        // 1. Обновляем основной блок
        savedBlock = await updateItem({
          collection: 'wblock_proto',
          id: props.block.id,
          item: cleanData,
        })

        // 2. Получаем текущие связи для блока
        const currentRelations = await getItems({
          collection: 'wblock_proto_welem_proto',
          params: {
            filter: {
              wblock_proto_id: { _eq: props.block.id },
            },
            fields: ['id'],
          },
        })

        // 3. Удаляем старые связи (если они есть)
        if (currentRelations?.length > 0) {
          const relationIds = currentRelations.map((r) => r.id)
          await deleteItems({
            collection: 'wblock_proto_welem_proto',
            items: relationIds,
          })
        }
      } else {
        // Создаем новый блок
        const result = await createItems({
          collection: 'wblock_proto',
          items: [cleanData],
        })
        savedBlock = Array.isArray(result) ? result[0] : result
      }

      // 4. Создаем новые связи M2M (только если есть элементы)
      if (formData.value.welem_proto?.length) {
        const relations = formData.value.welem_proto
          .map((welemId) => ({
            wblock_proto_id: savedBlock.id,
            welem_proto_id:
              typeof welemId === 'object' ? welemId.value : welemId,
          }))
          .filter((rel) => rel.welem_proto_id) // Фильтруем пустые ID

        if (relations.length > 0) {
          await createItems({
            collection: 'wblock_proto_welem_proto',
            items: relations,
          })
        }
      }

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: props.isEdit ? 'Блок обновлён' : 'Блок создан',
        life: 3000,
      })

      emit('save', savedBlock)
      emit('close')
    } catch (error) {
      console.error('Save error:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: `Не удалось сохранить блок: ${error.message || 'Неизвестная ошибка'}`,
        life: 5000,
      })
    }
  }

  // Инициализация при монтировании
  onMounted(() => {
    loadOptions()
    loadWelemOptions()
  })
</script>

<style scoped>
  .my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 8px;
    line-height: 1.2;
    padding: 1px;
  }

  /* optional class for removing the outline */
  .prism-editor__textarea:focus {
    outline: none;
  }
  .my {
    max-height: 120px;
  }
  
  .my img {
    object-fit: contain;
  }
</style>
