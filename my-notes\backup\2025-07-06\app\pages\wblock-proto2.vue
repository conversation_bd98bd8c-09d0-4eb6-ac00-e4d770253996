<template>
  <div class="flex h-screen">
    <!-- Основной контент -->
    <div
      class="flex-1 overflow-hidden flex flex-col"
      :class="{ 'pr-[30rem]': sidebarVisible }"
    >
      <div class="flex justify-between mb-1 p-1">
        <div class="flex gap-2">
          <Button
            v-tooltip.top="'Обновить данные'"
            icon="pi pi-refresh"
            class="p-button-rounded p-button-text p-button-sm"
            :disabled="loading"
            aria-label="Обновить"
            @click="loadData"
          />
          <span class="p-input-icon-left">
            <InputText
              v-model="globalFilterValue"
              placeholder="Поиск..."
              icon="pi pi-search"
              class="w-full"
              style="font-size: 12px"
              @input="onGlobalFilterChange"
            />
          </span>
          <MultiSelect
            v-model="selectedTags"
            :options="availableTags"
            placeholder="Фильтр по тегам"
            display="chip"
            filter
            class="w-64 text-xs"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
            @change="applyTagsFilter"
          />
          <Button
          v-tooltip.bottom="'Расширенные фильтры'"  
          icon="pi pi-filter"
            
            class="p-button-outlined text-xs"
            @click="showAdvancedFilters = !showAdvancedFilters"
          />
        </div>
        <div class="flex gap-2">
          <Button
          v-tooltip.bottom="'Вид'"  
          :icon="viewMode === 'compact' ? 'pi pi-list' : 'pi pi-table'"
            
            class="p-button-outlined text-xs"
            @click="toggleViewMode"
          />
          <Button
            v-tooltip.bottom="'Массовое добавление'"
            icon="pi pi-plus-circle"
            class="text-xs p-button-success"
            @click="openBulkCreate"
          />
          <Button
            v-tooltip.bottom="'Массовое редактирование'"
            icon="pi pi-pencil"
            class="text-xs p-button-primary"
            :disabled="!selectedItems.length"
            @click="openBulkEdit"
          />
          <Button
          v-tooltip.bottom="'Создать'"
          icon="pi pi-plus"

            class="p-button-success text-xs"
            @click="openCreateDialog"
          />
          <Button
          v-tooltip.bottom="'Анализ'"  
          icon="pi pi-cog"
            
            class="p-button-warning text-xs"
            :disabled="!selectedItems.length"
            @click="bulkAnalyzeHtml"
          />
          <Button
          v-tooltip.bottom="'Эскизы'"  
          icon="pi pi-images"
            
            class="p-button-info text-xs"
            :disabled="!selectedItems.length"
            @click="bulkUploadSketches"
          />
          <Button
          v-tooltip.bottom="'Скрины'"
          icon="pi pi-camera"

            class="p-button-secondary text-xs"
            :disabled="!selectedItems.length"
            @click="bulkGenerateScreenshots"
          />
          <Button
          v-tooltip.bottom="'Извлечь элементы'"
          icon="pi pi-objects-column"

            class="p-button-help text-xs"
            :disabled="!selectedItems.length"
            @click="extractElementsFromBlocks"
          />
          <Button
          v-tooltip.bottom="'Извлечь элементы + скрины'"
          icon="pi pi-images"

            class="p-button-warning text-xs"
            :disabled="!selectedItems.length"
            @click="extractElementsWithScreenshots"
          />
          <Button
          v-tooltip.bottom="'Дублировать выбранные'"
          icon="pi pi-copy"

            class="p-button-primary text-xs"
            :disabled="!selectedItems.length"
            @click="bulkDuplicateItems"
          />
          <Button
          v-tooltip.bottom="'Удалить выбранные'"
          icon="pi pi-trash"

            class="p-button-danger text-xs"
            :disabled="!selectedItems.length"
            @click="bulkDeleteItems"
          />
          <ProgressBar
            v-if="loading"
            mode="indeterminate"
            style="height: 6px"
            class="mt-2"
          />
        </div>
      </div>

      <UniversalFilterPanel
        v-if="showAdvancedFilters"
        :block-type-options="blockTypeOptions"
        :layout-options="layoutOptions"
        :element-options="elementOptions"
        :collection-options="collectionOptions"
        :concept-options="conceptOptions"
        :style-options="styleOptions"
        :features-options="featuresOptions"
        class="mb-2"
        @update:filters="applyAdvancedFilters"
        @filter-mode-change="updateFilterMode"
      />

      <!-- Область массовых форм -->
      <BulkFormContainer
        :visible="bulkFormVisible"
        :mode="bulkFormMode"
        collection="wblock_proto"
        :field-config="wblockBulkFieldConfig"
        :selected-items="selectedItems"
        @close="closeBulkForm"
        @saved="onBulkFormSaved"
      />

      <div class="flex-1 overflow-auto">
        <DataTable
          v-model:selection="selectedItems"
          v-model:sort-field="sortField"
          v-model:sort-order="sortOrder"
          :value="filteredItems"
          selection-mode="multiple"
          scrollable
          scroll-height="calc(100vh - 70px)"
          :virtual-scroller-options="{ itemSize: 44 }"
          filter-display="menu"
          :global-filter-fields="globalFilterFields"
          :loading="loading"
          data-key="id"
          striped-rows
          responsive-layout="scroll"
          class="p-datatable-sm text-[13px]"
          style="
            --highlight-bg: var(--primary-50);
            padding: 1px;
            font-size: 11px;
          "
          @row-select="onRowSelect"
        >
          <Column
            selection-mode="multiple"
            style="font-size: 9px; padding: 1px; width: 50px"
          />
          <Column
            field="number"
            header="№"
            :sortable="true"
            style="font-size: 9px; padding: 1px; width: 50px"
          >
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.number }}</span>
              </div>
            </template>
          </Column>

          <Column field="sketch" header="Эскиз" style="padding: 1px; width: 120px">
            <template #body="{ data }">
              <Image
                v-if="data.sketch"
                :src="`http://localhost:8055/assets/${data.sketch}`"
                alt="Эскиз"
                width="100"
                class="my"
                preview
              />
              <span v-else>-</span>
            </template>
          </Column>

          <Column field="title" header="Название" style="padding: 1px; width: 120px">
            <template #body="{ data }">
              
                <span>{{ data.title }}</span>
                
              
            </template>
          </Column>

          <Column
            field="description"
            header="Описание"
            style="padding: 1px; font-size: 9px; width: 150px"
          >
            <template #body="{ data }">
              <span class="description-cell">{{ data.description }}</span>
            
            </template>
          </Column>

          <Column
            field="description"
            header="Композиция"
            style="padding: 1px; font-size: 9px; width: 350px"
          >
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span class="composition-cell">{{ data.composition }}</span>
                <div class="flex-col items-center gap-2">
                <Button
                    label="⿻ HTML"
                    class="p-button-text p-button-sm"
                    style="width: 70px; font-size: 10px;padding:1px"
                    @click="copyToClipboard(data.html)"
                  />
                  <Button
                    label="⿻ HBS"
                    class="p-button-text p-button-sm"
                    style="width: 70px; font-size: 10px;padding:1px"
                    @click="copyToClipboard(data.hbs)"
                  />
                  <Button
                    label="⿻ JSON"
                    class="p-button-text p-button-sm"
                    style="width: 70px; font-size: 10px;padding:1px"
                    @click="copyToClipboard(data.json)"
                  />
                </div>
              </div>
            </template>
          </Column>

          <Column
            field="status"
            header="Статус"
            style="padding: 1px; width: 70px"
            :sortable="true"
          >
            <template #body="{ data }">
              <Tag
                :value="data.status"
                :severity="getStatusSeverity(data.status)"
                style="padding: 0 3px; font-size: 9px"
              />
            </template>
          </Column>

          <Column
            field="block_type"
            header="Тип блока"
            :sortable="true"
            style="padding: 1px; width: 250px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="type in data.block_type"
                  :key="type"
                  :value="type"
                  style="padding: 0 3px; font-size: 9px"
                  @click="addTagToFilter(type)"
                />
              </div>
            </template>
          </Column>

          <Column
            field="layout"
            header="Макет"
            :sortable="true"
            style="padding: 1px; width: 110px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="layout in data.layout"
                  :key="layout"
                  :value="layout"
                  style="padding: 0 3px; font-size: 9px"
                />
              </div>
            </template>
          </Column>

          <Column
            v-if="viewMode === 'full'"
            field="style"
            header="Стиль"
            :sortable="true"
            style="padding: 1px; width: 100px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="style in data.style"
                  :key="style"
                  :value="style"
                  style="padding: 0 3px; font-size: 9px"
                />
              </div>
            </template>
          </Column>

          <Column
            v-if="viewMode === 'full'"
            field="graphics"
            header="Графика"
            :sortable="true"
            style="padding: 1px; width: 120px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="graphic in data.graphics"
                  :key="graphic"
                  :value="graphic"
                  style="padding: 0 3px; font-size: 11px"
                />
              </div>
            </template>
          </Column>

          <Column
            field="collection"
            header="Коллекция"
            :sortable="true"
            style="padding: 1px; width: 100px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="collection in data.collection"
                  :key="collection"
                  :value="collection"
                  style="padding: 0 3px; font-size: 11px"
                />
              </div>
            </template>
          </Column>

          <Column
            v-if="viewMode === 'full'"
            field="elements"
            header="Элементы"
            style="padding: 1px; width: 250px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="element in data.elements"
                  :key="element"
                  :value="element"
                  style="padding: 0 3px; font-size: 11px"
                />
              </div>
            </template>
          </Column>

          <Column
            v-if="viewMode === 'full'"
            field="features"
            header="Особенности"
            style="padding: 1px; width: 200px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="feature in data.features"
                  :key="feature"
                  :value="feature"
                  style="padding: 0 3px; font-size: 11px"
                />
              </div>
            </template>
          </Column>

          <Column field="concept" header="Концепции" style="padding: 1px; width: 100px">
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="concept in data.concept"
                  :key="concept"
                  :value="concept"
                  style="padding: 0 3px; font-size: 11px"
                />
              </div>
            </template>
          </Column>

          <Column header="Действия" :exportable="false" style="width: 180px">
            <template #body="{ data }">
              <div class="flex gap-1">
                <Button
                  icon="pi pi-pencil"
                  class="p-button-text p-button-sm"
                  @click="editItem(data)"
                />
                <Button
                  icon="pi pi-copy"
                  class="p-button-text p-button-sm"
                  @click="duplicateItem(data)"
                />
                <Button
                  icon="pi pi-trash"
                  class="p-button-text p-button-sm p-button-danger"
                  @click="confirmDelete(data)"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Сайдбар редактирования -->
    <WcontSidebar
      v-model:visible="sidebarVisible"
      :collapsed="false"
      title="Редактирование блока"
      @close="sidebarVisible = false"
      @toggle-collapse="() => {}"
    >
      <div class="p-fluid">
        <!-- Базовая информация -->
        <div class="space-y-2">
          <div class="flex gap-2">
            <div class="field w-1/4">
              <InputText
                id="number"
                v-model="editingItem.number"
                required
                class="w-full"
                placeholder="Номер блока*"
                style="padding: 6px; font-size: 10px"
              />
            </div>

            <div class="field w-3/4">
              <InputText
                id="title"
                v-model="editingItem.title"
                required
                class="w-full"
                placeholder="Название*"
                style="padding: 6px; font-size: 11px"
              />
            </div>
          </div>

          <div class="field">
            <Textarea
              id="description"
              v-model="editingItem.description"
              rows="2"
              class="w-full text-xs [&>textarea]:text-xs"
              
              placeholder="Описание"
              style="padding: 4px; font-size: 10px"
            />
          </div>

          <div class="field mb-0" style="margin-top: 0">
            <Textarea
              id="composition"
              v-model="editingItem.composition"
              rows="4"
              class="w-full text-xs [&>textarea]:text-xs"
              
              placeholder="Композиция"
              style="padding: 4px; font-size: 10px"
            />
          </div>

          <div class="field mb-2" style="margin-top: 0">
            <MultiSelect
              v-model="editingItem.block_type"
              :options="blockTypeOptions"
              placeholder="Выберите типы блока"
              display="chip"
              class="text-xs w-full p-0"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
          <div class="flex gap-2">
            <div class="field w-1/3">
              <Dropdown
                id="status"
                v-model="editingItem.status"
                :options="statusOptions"
                option-label="label"
                option-value="value"
                placeholder="Выберите статус"
                class="w-full text-xs"
                style="font-size: 11px"
              />
            </div>

            <div class="field w-2/3">
              <MultiSelect
                v-model="editingItem.concept"
                :options="conceptOptions"
                placeholder="Выберите концепции"
                display="chip"
                class="w-full text-xs"
                panel-class="text-xs"
                style="font-size: 11px"
                :pt="{
                  item: { class: 'text-xs' },
                  header: { class: 'text-xs' },
                }"
              />
            </div>
          </div>

          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.collection"
              :options="collectionOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите коллекции"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.layout"
              :options="layoutOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите макеты"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <!-- Поле wpage -->
          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.wpage"
              :options="wpageOptions"
              option-label="label"
              option-value="value"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите связанные страницы"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          
          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.elements"
              :options="elementOptions"
              placeholder="Выберите типы элементов"
              display="chip"
              class="w-full text-xs"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
          
          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.features"
              :options="featuresOptions"
              placeholder="Выберите особенности"
              display="chip"
              class="w-full text-xs"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
          <div class="flex gap-2">
            <div class="field w-1/2">
              <MultiSelect
                v-model="editingItem.style"
                :options="styleOptions"
                display="chip"
                class="w-full text-xs"
                placeholder="Выберите стили"
                panel-class="text-xs"
                style="font-size: 11px"
                :pt="{
                  item: { class: 'text-xs' },
                  header: { class: 'text-xs' },
                }"
              />
            </div>

            <div class="field w-1/2">
              <MultiSelect
                v-model="editingItem.graphics"
                :options="graphicsOptions"
                display="chip"
                class="w-full text-xs"
                placeholder="Выберите графику"
                panel-class="text-xs"
                style="font-size: 11px"
                :pt="{
                  item: { class: 'text-xs' },
                  header: { class: 'text-xs' },
                }"
              />
            </div>
          </div>

          <div class="field mb-2">
            <div class="flex gap-2">
              <Image
                v-if="editingItem.sketch"
                :src="`http://localhost:8055/assets/${editingItem.sketch}`"
                alt="Эскиз"
                width="200"
                class="my"
                preview
              />
              <FileUpload
                mode="basic"
                :auto="true"
                accept="image/*"
                :max-file-size="1000000"
                choose-label="Эскиз"
                class="p-button-sm"
                @select="onSketchSelect"
              />
            </div>
          </div>
          <div class="field mb-0">
            <TabView 
              class="text-xs" 
              :pt="{
              panelcontainer: { style: 'padding:0' },
            }"
            >
              <TabPanel 
                header="HTML/CSS/JS" 
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <div class="space-y-1">
                  <PrismEditorWithCopy
                    v-model="editingItem.html"
                    editor-class="my-editor text-xs"
                    :highlight="highlightHtml"
                    placeholder="Введите HTML код"
                    field-name="HTML"
                    max-height="120px"
                  />
                  <div class="p-0 grid grid-cols-2 gap-4">
                    <div class="flex flex-col h-full">
                    <PrismEditorWithCopy
                      v-model="editingItem.css"
                      editor-class="my-editor text-xs w-full"
                      :highlight="highlightCss"
                      placeholder="CSS код"
                      field-name="CSS"
                      max-height="60px !important"
                      
                    />
                  </div>
                  <div class="flex flex-col h-full">
                    <PrismEditorWithCopy
                      v-model="editingItem.js"
                      editor-class="my-editor text-xs w-full"
                      :highlight="highlightJs"
                      placeholder="JS код"
                      field-name="JavaScript"
                      max-height="60px !important"
                      
                    />
                  </div>
                  </div>
                </div>
              </TabPanel>
              <TabPanel
                header="HBS"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <PrismEditorWithCopy
                  v-model="editingItem.hbs"
                  editor-class="my-editor text-xs"
                  :highlight="highlightHtml"
                  placeholder="Введите HBS код"
                  field-name="HBS"
                  max-height="200px"
                />
              </TabPanel>
              <TabPanel
                header="JSON"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <PrismEditorWithCopy
                  v-model="editingItem.json"
                  editor-class="my-editor text-xs"
                  :highlight="highlightJson"
                  placeholder="Введите JSON код"
                  field-name="JSON"
                  max-height="200px"
                />
              </TabPanel>

              <TabPanel
                header="Редактор"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <div>
                  <!-- Быстрое добавление переменных -->
                  <div class="quick-variables flex flex-wrap gap-1 mb-2">
                    <div
                      v-for="varType in quickVariableTypes"
                      :key="varType"
                      class="relative"
                    >
                      <Button
                        :label="varType"
                        class="p-button-sm p-button-outlined"
                        style="font-size: 9px; padding: 2px 6px"
                        @click="addQuickVariable(varType)"
                      />
                      <!-- Счетчик использованных переменных -->
                      <span
                        v-if="getVariableCount(varType) > 0"
                        class="absolute -top-1 -right-1 bg-blue-500 text-white rounded-full text-xs w-4 h-4 flex items-center justify-center"
                        style="font-size: 8px; min-width: 16px; min-height: 16px"
                      >
                        {{ getVariableCount(varType) }}
                      </span>
                    </div>
                  </div>

                  <div v-for="(field, index) in parsedJsonFields" :key="index" class="flex gap-1 items-start">
                    <div class="w-1/5">
                      <InputText
                        v-model="field.key"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px"
                        placeholder="Переменная"
                        @input="updateJsonFromFields"
                      />
                    </div>
                    <div class="w-3/5 relative group mb-0">
                      <Textarea
                        v-if="isTextVariable(field.key)"
                        v-model="field.value"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px; padding-right: 5px;"
                        placeholder="Значение"
                        rows="1"
                        @input="updateJsonFromFields"
                      />
                      <InputText
                        v-else
                        v-model="field.value"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px; padding-right: 5px;"
                        placeholder="Значение"
                        @input="updateJsonFromFields"
                      />
                    </div>
                    <div class="w-6">
                      <Button
                        icon="pi pi-copy"
                        class="group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                        style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); right: 4px; top: 4px;"
                        @click="copyToClipboard(field.value)"
                      />
                    </div>
                    <div class="w-1/12 text-xs text-gray-500 text-center pt-1">
                      {{ field.value ? field.value.length : 0 }}
                    </div>
                    <div class="w-1/12 flex gap-1 pt-1">
                      <Button
                        icon="pi pi-clone"
                        class="p-button-text p-button-sm"
                        style="width: 16px; height: 16px; padding: 0"
                        @click="duplicateJsonField(index)"
                      />
                      <Button
                        icon="pi pi-trash"
                        class="p-button-text p-button-sm p-button-danger"
                        style="width: 16px; height: 16px; padding: 0"
                        @click="removeJsonField(index)"
                      />
                    </div>
                  </div>

                  <div class="flex justify-between items-center mt-2">
                    <Button
                      label="+ Добавить поле"
                      class="p-button-sm p-button-text"
                      @click="addJsonField"
                    />
                    <span class="text-xs text-gray-500">
                      Всего переменных: {{ parsedJsonFields.length }}
                    </span>
                  </div>
                </div>
              </TabPanel>

              <TabPanel
                header="Картинки"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <div class="grid grid-cols-2 gap-1">
                  <div v-for="(imageData, index) in extractedImagesWithVars" :key="index" class="border rounded p-1">
                    <Image
                      :src="imageData.url"
                      alt="Изображение из JSON"
                      width="200"
                      preview
                      class="w-full mb-2"
                    />
                    <div class="space-y-1">
                      <div class="text-xs font-semibold text-gray-700">
                        Переменная: {{ imageData.variable }}
                      </div>
                      <div class="flex">
                        <InputText
                          v-model="imageData.url"
                          class="w-full text-xs"
                          style="font-size: 10px; padding: 4px"
                          placeholder="URL изображения"
                          @input="updateImageInJson(imageData.variable, imageData.url)"
                        />
                        <div class="w-6">
                          <Button
                            icon="pi pi-copy"
                            class="group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                            style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); right: 4px; top: 4px;"
                            @click="copyToClipboard(imageData.url)"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <p v-if="!extractedImagesWithVars.length" class="text-xs text-gray-500">
                    Изображения не найдены в JSON (поиск по полям image, imageBackground)
                  </p>
                </div>
              </TabPanel>
            </TabView>
          </div>
          <div class="field mb-2">
            <Textarea
              id="notes"
              v-model="editingItem.notes"
              rows="1"
              
              class="w-full text-xs [&>textarea]:text-xs"
              placeholder="Заметки"
              style="padding: 4px; font-size: 10px"
            />
          </div>
          <!-- DataTable для связанных элементов -->
          <div class="field mb-2">
            <div class="text-xs font-semibold mb-1">Связанные элементы</div>
            <DataTable
              :value="relatedWelements"
              size="small"
              class="text-xs"
              :pt="{
                table: { style: 'font-size: 10px' },
                header: { style: 'padding: 2px' },
                bodyRow: { style: 'height: 30px' },
                bodyCell: { style: 'padding: 2px' }
              }"
            >
              <Column field="sketch" header="" style="width: 50px">
                <template #body="{ data }">
                  <Image
                    v-if="data.sketch"
                    :src="`http://localhost:8055/assets/${data.sketch}?width=40&height=40&fit=cover`"
                    alt="Sketch"
                    class="w-10 h-auto rounded"
                    preview
                  />
                </template>
              </Column>
              <Column field="number" header="№" sortable style="width: 80px" />
              <Column field="title" header="Название" sortable />
              <Column field="elem_type" header="Типы" style="width: 120px">
                <template #body="{ data }">
                  <div class="flex flex-wrap gap-1">
                    <Tag
                      v-for="type in data.elem_type"
                      :key="type"
                      :value="type"
                      style="padding: 0 3px; font-size: 9px"
                    />
                  </div>
                </template>
              </Column>
            </DataTable>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-0">
          <Button
            label="Отмена"
            icon="pi pi-times"
            class="p-button-sm"
            @click="closeSidebar"
          />
          <Button
            label="Сохранить"
            icon="pi pi-check"
            class="p-button-sm"
            :loading="saving"
            @click="saveItem"
          />
        </div>
      </div>
    </WcontSidebar>

    <!-- Диалог подтверждения удаления -->
    <ConfirmDialog />

    <!-- Уведомления -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
  import 'vue-prism-editor/dist/prismeditor.min.css'
  import Prism from 'prismjs'
  import 'prismjs/components/prism-clike'
  import 'prismjs/components/prism-markup'
  import 'prismjs/components/prism-css'
  import 'prismjs/components/prism-javascript'
  import 'prismjs/components/prism-json'
  import 'prismjs/themes/prism-tomorrow.css'

  import { ref, computed, onMounted } from 'vue'
  import { useDirectusItems } from '#imports'
  import { useConfirm } from 'primevue/useconfirm'
  import { useToast } from 'primevue/usetoast'
  import Image from 'primevue/image'
  import Button from 'primevue/button'
  import InputText from 'primevue/inputtext'
  import Textarea from 'primevue/textarea'
  import Dropdown from 'primevue/dropdown'
  import MultiSelect from 'primevue/multiselect'
  import FileUpload from 'primevue/fileupload'
  import UniversalFilterPanel from '~/components/UniversalFilterPanel.vue'
  import BulkFormContainer from '~/components/BulkFormContainer.vue'

  import TabView from 'primevue/tabview'
  import TabPanel from 'primevue/tabpanel'

  interface WBlock {
    id?: string
    number: string
    status: string
    title: string
    description?: string
    concept?: string[]
    block_type?: string[]
    layout?: string[]
    style?: string[]
    elements?: string[]
    element_count?: number
    composition?: string
    graphics?: string[]
    collection?: string[]
    features?: string[]
    notes?: string
    sketch?: string
    wpage?: string[]
    welem_proto?: string[]
    html?: string
    css?: string
    js?: string
    hbs?: string
    json?: string
    sort?: number
    date_created?: string
    user_created?: string
    date_updated?: string
    user_updated?: string
  }

  // API и утилиты
  const { getItems, createItems, updateItem, deleteItems } = useDirectusItems()
  const confirm = useConfirm()
  const toast = useToast()

  // Методы
  const onRowSelect = (event: { data: WBlock }) => {
    // Удалено автоматическое открытие сайдбара при выборе строки
    // editItem(event.data)
  }

  // Состояние
  const items = ref<WBlock[]>([])
  const loading = ref(false)
  const saving = ref(false)
  const sidebarVisible = ref(false)
  const viewMode = ref<'compact' | 'full'>('compact')
  const selectedItem = ref<WBlock | null>(null)
  const selectedItems = ref<WBlock[]>([])
  const globalFilterValue = ref('')
  const selectedTags = ref<string[]>([])
  const showAdvancedFilters = ref(false)

  // Состояние для массовых форм
  const bulkFormVisible = ref(false)
  const bulkFormMode = ref<'create' | 'edit'>('create')

  // Состояние расширенных фильтров
  const advancedFilters = ref({
    blockTypes: [],
    layouts: [],
    elements: [],
    collections: [],
    concepts: [],
    styles: [],
    features: [],
    mode: 'or'
  })

  // Редактирование
  const editingItem = ref<WBlock>({
    number: '',
    status: '',
    title: '',
    description: '',
    composition: '',
    notes: '',
    block_type: [],
    elements: [],
    layout: [],
    style: [],
    graphics: [],
    collection: [],
    concept: [],
    sketch: '',
    wpage: [],
    welem_proto: [],
    html: '',
    css: '',
    js: '',
    hbs: '',
    json: '',
  })

  // Опции
  const statusOptions = [
    { label: 'Идея', value: 'idea' },
    { label: 'В разработке', value: 'in_progress' },
    { label: 'Готово', value: 'done' },
    { label: 'Архив', value: 'archived' },
  ]

  // Опции из Directus
  const conceptOptions = ref<string[]>([])
  const blockTypeOptions = ref<string[]>([])
  const elementOptions = ref<string[]>([])
  const layoutOptions = ref<string[]>([])
  const styleOptions = ref<string[]>([])
  const graphicsOptions = ref<string[]>([])
  const collectionOptions = ref<string[]>([])
  const featuresOptions = ref<string[]>([])

  // Загрузка опций из коллекции wblock_proto
  const loadOptions = async () => {
    try {
      const items = await getItems({
        collection: 'wblock_proto',
        params: {
          limit: -1,
          fields: [
            'concept',
            'block_type',
            'elements',
            'layout',
            'style',
            'graphics',
            'collection',
            'features',
          ],
        },
      })

      if (Array.isArray(items)) {
        const concepts = new Set()
        const blockTypes = new Set()
        const elements = new Set()
        const layouts = new Set()
        const styles = new Set()
        const graphics = new Set()
        const collections = new Set()
        const features = new Set()

        items.forEach((item) => {
          item.concept?.forEach((c) => concepts.add(c))
          item.block_type?.forEach((t) => blockTypes.add(t))
          item.elements?.forEach((e) => elements.add(e))
          item.layout?.forEach((l) => layouts.add(l))
          item.style?.forEach((s) => styles.add(s))
          item.graphics?.forEach((g) => graphics.add(g))
          item.collection?.forEach((c) => collections.add(c))
          item.features?.forEach((f) => features.add(f))
        })

        conceptOptions.value = Array.from(concepts)
        blockTypeOptions.value = Array.from(blockTypes)
        elementOptions.value = Array.from(elements)
        layoutOptions.value = Array.from(layouts)
        styleOptions.value = Array.from(styles)
        graphicsOptions.value = Array.from(graphics)
        collectionOptions.value = Array.from(collections)
        featuresOptions.value = Array.from(features)
      }
    } catch (error) {
      console.error('Error loading options:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить справочники',
      })
    }
  }

  // Сортировка
  const sortField = ref('number')
  const sortOrder = ref(1)

  const welemOptions = ref([])
  const wpageOptions = ref([])
  const relatedWelements = ref([])

  // Состояние для редактора JSON
  const parsedJsonFields = ref<Array<{key: string, value: string}>>([])

  // Быстрые переменные для добавления
  const quickVariableTypes = ['title', 'image', 'imageBackground', 'url', 'linkText', 'icon', 'text', 'excerpt']

  const loadWelemOptions = async () => {
    try {
      const { getItems } = useDirectusItems()
      const elements = await getItems({
        collection: 'welem_proto',
        params: {
          limit: -1,
          fields: ['id', 'title'],
        },
      })

      if (Array.isArray(elements)) {
        welemOptions.value = elements.map((elem) => ({
          value: elem.id,
          label: elem.title || elem.id,
        }))
      }
    } catch (error) {
      console.error('Ошибка загрузки элементов:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить элементы для выбора',
        life: 3000,
      })
    }
  }

  const loadWpageOptions = async () => {
    try {
      const { getItems } = useDirectusItems()
      const pages = await getItems({
        collection: 'wpage',
        params: {
          limit: -1,
          fields: ['id', 'number', 'title'],
        },
      })

      wpageOptions.value = pages.map((page) => ({
        label: `${page.number} - ${page.title}`,
        value: page.id,
      }))
    } catch (error) {
      console.error('Ошибка загрузки страниц:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить страницы для выбора',
        life: 3000,
      })
    }
  }

  // Вычисляемые свойства
  const availableTags = computed(() => {
    const tags = new Set<string>()
    items.value.forEach((item) => {
      item.concept?.forEach((tag) => tags.add(tag))
      item.block_type?.forEach((tag) => tags.add(tag))
      item.elements?.forEach((tag) => tags.add(tag))
    })
    return Array.from(tags)
  })

  const filteredItems = computed(() => {
    let result = [...items.value]

    // Фильтр по тегам
    if (selectedTags.value.length > 0) {
      result = result.filter((item) => {
        const itemTags = [
          ...(item.concept || []),
          ...(item.block_type || []),
          ...(item.elements || []),
        ]
        return selectedTags.value.some((tag) => itemTags.includes(tag))
      })
    }

    // Расширенные фильтры
    if (hasAdvancedFilters.value) {
      result = result.filter((item) => {
        // Функция проверки соответствия элемента фильтру
        const matchesFilter = (itemValues, filterValues) => {
          if (!filterValues.length) return true
          if (!itemValues?.length) return false
          
          return advancedFilters.value.mode === 'or'
            ? filterValues.some(filter => itemValues.includes(filter))
            : filterValues.every(filter => itemValues.includes(filter))
        }
        
        // Проверка по всем типам фильтров
        const blockTypesMatch = matchesFilter(item.block_type, advancedFilters.value.blockTypes)
        const layoutsMatch = matchesFilter(item.layout, advancedFilters.value.layouts)
        const elementsMatch = matchesFilter(item.elements, advancedFilters.value.elements)
        const collectionsMatch = matchesFilter(item.collection, advancedFilters.value.collections)
        const conceptsMatch = matchesFilter(item.concept, advancedFilters.value.concepts)
        const stylesMatch = matchesFilter(item.style, advancedFilters.value.styles)
        const featuresMatch = matchesFilter(item.features, advancedFilters.value.features)
        
        return blockTypesMatch && layoutsMatch && elementsMatch && 
               collectionsMatch && conceptsMatch && stylesMatch && featuresMatch
      })
    }

    // Глобальный поиск
    if (globalFilterValue.value) {
      const searchValue = globalFilterValue.value.toLowerCase()
      result = result.filter(
        (item) =>
          (item.title?.toLowerCase() || '').includes(searchValue) ||
          (item.number?.toLowerCase() || '').includes(searchValue) ||
          (item.composition?.toLowerCase() || '').includes(searchValue),
      )
    }

    return result
  })
  
  // Проверка наличия активных расширенных фильтров
  const hasAdvancedFilters = computed(() => {
    return [
      advancedFilters.value.blockTypes,
      advancedFilters.value.layouts,
      advancedFilters.value.elements,
      advancedFilters.value.collections,
      advancedFilters.value.concepts,
      advancedFilters.value.styles,
      advancedFilters.value.features
    ].some(filters => filters.length > 0)
  })

  const globalFilterFields = ['title', 'number', 'composition']

  // Конфигурация полей для массовых форм wblock-proto2
  const wblockBulkFieldConfig = computed(() => [
    {
      name: 'block_type',
      type: 'multiselect' as const,
      placeholder: 'Типы блоков',
      options: blockTypeOptions.value,
      class: 'field-full'
    },
    {
      name: 'collection',
      type: 'multiselect' as const,
      placeholder: 'Коллекции',
      options: collectionOptions.value,
      class: 'field-full'
    },
    {
      name: 'concept',
      type: 'multiselect' as const,
      placeholder: 'Концепции',
      options: conceptOptions.value,
      class: 'field-full'
    },
    {
      name: 'style',
      type: 'multiselect' as const,
      placeholder: 'Стили',
      options: styleOptions.value,
      class: 'field-full'
    },
    {
      name: 'html',
      type: 'prism' as const,
      placeholder: 'HTML код',
      class: 'field-full'
    },
    {
      name: 'css',
      type: 'prism' as const,
      placeholder: 'CSS код',
      class: 'field-full'
    },
    {
      name: 'js',
      type: 'prism' as const,
      placeholder: 'JavaScript код',
      class: 'field-full'
    },
    {
      name: 'sketch',
      type: 'file' as const,
      placeholder: 'Эскиз (изображение)',
      class: 'field-full'
    }
  ])

  const highlightHtml = (code: string) => {
    return Prism.highlight(code, Prism.languages.markup, 'html')
  }

  const highlightCss = (code: string) => {
    return Prism.highlight(code, Prism.languages.css, 'css')
  }

  const highlightJs = (code: string) => {
    return Prism.highlight(code, Prism.languages.javascript, 'javascript')
  }

  const highlightJson = (code: string) => {
    return Prism.highlight(code, Prism.languages.json, 'json')
  }

  // Методы
  const loadData = async () => {
    loading.value = true
    try {
      const [blocks, welems] = await Promise.all([
        getItems({
          collection: 'wblock_proto',
          params: {
            limit: -1,
            sort: [sortField.value],
            fields: ['*', 'html'],
          },
        }),
        getItems({
          collection: 'welem_proto',
          params: {
            limit: -1,
            fields: ['id', 'title'],
          },
        }),
      ])

      console.log('Loaded blocks:', blocks) // Debug output

      items.value = blocks
      welemOptions.value = welems.map((w) => ({
        value: w.id,
        label: w.title || w.id,
      }))
    } catch (error) {
      console.error('Error loading data:', error)
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to load blocks data',
        life: 3000,
      })
    } finally {
      loading.value = false
    }
  }

  const saveItem = async () => {
    saving.value = true
    try {
      const { id, welem_proto, wpage, ...saveData } = editingItem.value

      if (id) {
        // Обновление существующего блока
        await updateItem({
          collection: 'wblock_proto',
          id,
          item: saveData,
        })
        await saveRelations(id, welem_proto || [])
        await saveWpageRelations(id, wpage || [])
      } else {
        // Создание нового блока
        const result = await createItems({
          collection: 'wblock_proto',
          items: [saveData],
        })
        const newId = Array.isArray(result) ? result[0]?.id : result?.id
        if (newId) {
          if (welem_proto?.length) {
            await saveRelations(newId, welem_proto)
          }
          if (wpage?.length) {
            await saveWpageRelations(newId, wpage)
          }
        }
      }

      await loadData()
      closeSidebar()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: id ? 'Блок обновлен' : 'Блок создан',
        life: 3000,
      })
    } catch (error) {
      console.error('Save error:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось сохранить блок',
        life: 5000,
      })
    } finally {
      saving.value = false
    }
  }

  const editItem = async (item: WBlock) => {
    try {
      const [blockData] = await getItems({
        collection: 'wblock_proto',
        params: {
          filter: { id: { _eq: item.id } },
          fields: ['*'],
          limit: 1,
        },
      })

      const relations = await loadBlockRelations(item.id!)
      const wpageRelations = await loadWpageRelations(item.id!)

      // Загружаем связанные элементы для отображения в DataTable
      await loadRelatedElements(item.id!)

      editingItem.value = {
        ...blockData,
        welem_proto: relations,
        wpage: wpageRelations,
      }

      // Парсим JSON для редактора
      parseJsonToFields()

      sidebarVisible.value = true
    } catch (error) {
      console.error('Error loading block:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить данные блока',
        life: 3000,
      })
    }
  }

  const duplicateItem = async (item: WBlock) => {
    const duplicate = { ...item }
    delete duplicate.id
    duplicate.number = `${duplicate.number}-copy`
    duplicate.title = `${duplicate.title} (копия)`

    try {
      await createItems({
        collection: 'wblock_proto',
        items: [duplicate],
      })
      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Блок скопирован',
      })
    } catch (error) {
      console.error('Error duplicating item:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось скопировать блок',
      })
    }
  }

  const confirmDelete = (item: WBlock) => {
    confirm.require({
      message: 'Вы уверены, что хотите удалить этот блок?',
      header: 'Подтверждение удаления',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteItem(item),
    })
  }

  const deleteItem = async (item: WBlock) => {
    try {
      await deleteItems({
        collection: 'wblock_proto',
        items: [item.id!],
      })
      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Блок удален',
      })
    } catch (error) {
      console.error('Error deleting item:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось удалить блок',
      })
    }
  }

  const openCreateDialog = () => {
    editingItem.value = {
      number: '',
      status: '',
      title: '',
    }
    sidebarVisible.value = true
  }

  const closeSidebar = () => {
    sidebarVisible.value = false
    relatedWelements.value = []
    editingItem.value = {
      number: '',
      status: '',
      title: '',
    }
  }

  const toggleViewMode = () => {
    viewMode.value = viewMode.value === 'compact' ? 'full' : 'compact'
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.add({
      severity: 'success',
      summary: 'Скопировано',
      detail: text.includes('http') ? 'URL скопирован в буфер обмена' : 'Текст скопирован в буфер обмена',
      life: 2000,
    })
  }

  const addTagToFilter = (tag: string) => {
    if (!selectedTags.value.includes(tag)) {
      selectedTags.value.push(tag)
    }
  }

  const onGlobalFilterChange = () => {
    // Обработка изменения глобального фильтра
  }

  const applyTagsFilter = () => {
    // Обработка изменения фильтра по тегам
  }
  
  // Применение расширенных фильтров
  const applyAdvancedFilters = (filters) => {
    advancedFilters.value = filters
  }
  
  // Обновление режима фильтрации
  const updateFilterMode = (mode) => {
    advancedFilters.value.mode = mode
  }

  const onSketchSelect = async (event: any) => {
    const file = event.files[0]
    if (file) {
      try {
        const formData = new FormData()
        formData.append('file', file)

        const response = await fetch('http://localhost:8055/files', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          throw new Error('Ошибка загрузки файла')
        }

        const data = await response.json()
        editingItem.value.sketch = data.data.id

        // toast.add({
        //   severity: 'success',
        //   summary: 'Успешно',
        //   detail: 'Файл загружен',
        // })
      } catch (error) {
        console.error('Ошибка при загрузке файла:', error)
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Не удалось загрузить файл',
        })
      }
    }
  }

  const getStatusSeverity = (status: string): string => {
    const severityMap: Record<string, string> = {
      idea: 'info',
      in_progress: 'warning',
      done: 'success',
      archived: 'danger',
    }
    return severityMap[status] || 'info'
  }

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  // Массовое дублирование выбранных элементов
  const bulkDuplicateItems = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true
      
      // Получаем полные данные выбранных записей
      const selectedIds = selectedItems.value.map(item => item.id)
      const fullRecords = await getItems({
        collection: 'wblock_proto',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: ['*'],
        },
      })

      // Подготавливаем данные для дублирования
      const duplicates = fullRecords.map(item => {
        const duplicate = { ...item }
        delete duplicate.id
        duplicate.number = `${duplicate.number}-copy`
        duplicate.title = `${duplicate.title} (копия)`
        return duplicate
      })

      // Создаем дубликаты
      await createItems({
        collection: 'wblock_proto',
        items: duplicates,
      })

      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Дублировано ${duplicates.length} блоков`,
      })
    } catch (error) {
      console.error('Error duplicating items:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось дублировать блоки',
      })
    } finally {
      loading.value = false
    }
  }

  // Массовое удаление выбранных элементов
  const bulkDeleteItems = () => {
    if (!selectedItems.value.length) return

    confirm.require({
      message: `Вы уверены, что хотите удалить ${selectedItems.value.length} выбранных блоков?`,
      header: 'Подтверждение удаления',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          loading.value = true
          const selectedIds = selectedItems.value.map(item => item.id)
          
          await deleteItems({
            collection: 'wblock_proto',
            items: selectedIds,
          })
          
          await loadData()
          selectedItems.value = []
          
          toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: `Удалено ${selectedIds.length} блоков`,
          })
        } catch (error) {
          console.error('Error deleting items:', error)
          toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось удалить блоки',
          })
        } finally {
          loading.value = false
        }
      }
    })
  }

  // Функции для массовых форм
  function openBulkCreate() {
    bulkFormMode.value = 'create'
    bulkFormVisible.value = true
  }

  function openBulkEdit() {
    if (selectedItems.value.length === 0) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Выберите блоки для редактирования',
        life: 3000
      })
      return
    }
    bulkFormMode.value = 'edit'
    bulkFormVisible.value = true
  }

  function closeBulkForm() {
    bulkFormVisible.value = false
  }

  function onBulkFormSaved(savedItems: any[]) {
    // Обновляем список блоков
    loadData()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Обработано ${savedItems.length} блоков`,
      life: 3000
    })
  }

  // Функции для JSON редактора
  const parseJsonToFields = () => {
    if (!editingItem.value.json) {
      parsedJsonFields.value = []
      return
    }

    try {
      const json = JSON.parse(editingItem.value.json)
      parsedJsonFields.value = Object.entries(json).map(([key, value]) => ({
        key,
        value: typeof value === 'string' ? value : JSON.stringify(value)
      }))
    } catch (error) {
      console.error('Error parsing JSON:', error)
      parsedJsonFields.value = []
    }
  }

  const updateJsonFromFields = () => {
    try {
      const json = {}
      parsedJsonFields.value.forEach(field => {
        if (field.key.trim()) {
          try {
            json[field.key] = JSON.parse(field.value)
          } catch {
            json[field.key] = field.value
          }
        }
      })
      editingItem.value.json = JSON.stringify(json, null, 2)
    } catch (error) {
      console.error('Error updating JSON:', error)
    }
  }

  const addJsonField = () => {
    parsedJsonFields.value.push({ key: '', value: '' })
  }

  const removeJsonField = (index: number) => {
    parsedJsonFields.value.splice(index, 1)
    updateJsonFromFields()
  }

  const addQuickVariable = (varType: string) => {
    // Логика нумерации: image, image2, image3... (без image1)
    const existingFields = parsedJsonFields.value.filter(field =>
      field.key === varType || field.key.match(new RegExp(`^${varType}\\d+$`))
    )

    let newKey = varType
    if (existingFields.length > 0) {
      // Если уже есть переменные этого типа, находим следующий номер
      let counter = 2
      newKey = `${varType}${counter}`
      while (parsedJsonFields.value.some(field => field.key === newKey)) {
        counter++
        newKey = `${varType}${counter}`
      }
    }

    const newField = {
      key: newKey,
      value: ''
    }

    parsedJsonFields.value.push(newField)
    updateJsonFromFields()
  }

  const getVariableCount = (varType: string) => {
    return parsedJsonFields.value.filter(field =>
      field.key === varType || field.key.startsWith(`${varType}2`) || field.key.startsWith(`${varType}3`)
    ).length
  }

  const duplicateJsonField = (index: number) => {
    const originalField = parsedJsonFields.value[index]
    const baseKey = originalField.key.replace(/\d+$/, '') // убираем цифры в конце

    // Логика нумерации: image, image2, image3... (без image1)
    const existingFields = parsedJsonFields.value.filter(field =>
      field.key === baseKey || field.key.startsWith(baseKey)
    )

    let newKey = baseKey
    if (existingFields.length > 1) {
      // Если уже есть дубликаты, находим следующий номер
      const numbers = existingFields
        .map(field => {
          const match = field.key.match(/(\d+)$/)
          return match ? parseInt(match[1]) : (field.key === baseKey ? 1 : 0)
        })
        .filter(num => num > 0)
        .sort((a, b) => a - b)

      const nextNumber = numbers.length > 0 ? Math.max(...numbers) + 1 : 2
      newKey = `${baseKey}${nextNumber}`
    } else if (existingFields.length === 1) {
      newKey = `${baseKey}2`
    }

    const duplicatedField = {
      key: newKey,
      value: originalField.value
    }

    parsedJsonFields.value.splice(index + 1, 0, duplicatedField)
    updateJsonFromFields()
  }

  const isTextVariable = (key: string) => {
    return key.toLowerCase().includes('text') ||
           key.toLowerCase().includes('excerpt') ||
           key.toLowerCase().includes('description')
  }

  // Вычисляемое свойство для извлечения изображений из JSON
  const extractedImagesWithVars = computed(() => {
    if (!editingItem.value.json) return []

    try {
      const json = JSON.parse(editingItem.value.json)
      const images = []

      const extractImages = (obj, prefix = '') => {
        for (const [key, value] of Object.entries(obj)) {
          const fullKey = prefix ? `${prefix}.${key}` : key

          if (typeof value === 'string' && (key.includes('image') || key.includes('Image'))) {
            if (value.startsWith('http') || value.startsWith('/')) {
              images.push({
                variable: fullKey,
                url: value
              })
            }
          } else if (typeof value === 'object' && value !== null) {
            extractImages(value, fullKey)
          }
        }
      }

      extractImages(json)
      return images
    } catch (error) {
      return []
    }
  })

  const updateImageInJson = (variable: string, newUrl: string) => {
    try {
      const json = JSON.parse(editingItem.value.json)
      const keys = variable.split('.')
      let current = json

      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]]
      }

      const lastKey = keys[keys.length - 1]
      current[lastKey] = newUrl

      editingItem.value.json = JSON.stringify(json, null, 2)
      parseJsonToFields()
    } catch (error) {
      console.error('Error updating image in JSON:', error)
    }
  }

  // Инициализация
  onMounted(async () => {
    await Promise.all([loadData(), loadOptions(), loadWelemOptions(), loadWpageOptions()])
  })

  const loadBlockRelations = async (blockId: string) => {
    try {
      const relations = await getItems({
        collection: 'wblock_proto_welem_proto',
        params: {
          filter: { wblock_proto_id: { _eq: blockId } },
          fields: ['welem_proto_id'],
        },
      })
      return relations.map((r) => r.welem_proto_id)
    } catch (error) {
      console.error('Error loading relations:', error)
      return []
    }
  }

  const loadWpageRelations = async (blockId: string) => {
    try {
      const relations = await getItems({
        collection: 'wpage_wblock_proto',
        params: {
          filter: { wblock_proto_id: { _eq: blockId } },
          fields: ['wpage_id'],
        },
      })
      return relations.map((r) => r.wpage_id)
    } catch (error) {
      console.error('Error loading wpage relations:', error)
      return []
    }
  }

  const loadRelatedElements = async (blockId: string) => {
    try {
      const relations = await getItems({
        collection: 'wblock_proto_welem_proto',
        params: {
          filter: { wblock_proto_id: { _eq: blockId } },
          fields: ['welem_proto_id.id', 'welem_proto_id.number', 'welem_proto_id.title', 'welem_proto_id.elem_type', 'welem_proto_id.sketch'],
        },
      })
      relatedWelements.value = relations.map((r) => r.welem_proto_id).filter(Boolean)
    } catch (error) {
      console.error('Error loading related elements:', error)
      relatedWelements.value = []
    }
  }

  const saveRelations = async (blockId: string, welemIds: string[]) => {
    try {
      // Удаляем старые связи
      const currentRelations = await getItems({
        collection: 'wblock_proto_welem_proto',
        params: {
          filter: { wblock_proto_id: { _eq: blockId } },
          fields: ['id'],
        },
      })

      if (currentRelations.length > 0) {
        await deleteItems({
          collection: 'wblock_proto_welem_proto',
          items: currentRelations.map((r) => r.id),
        })
      }

      // Добавляем новые
      if (welemIds.length > 0) {
        await createItems({
          collection: 'wblock_proto_welem_proto',
          items: welemIds.map((id) => ({
            wblock_proto_id: blockId,
            welem_proto_id: id,
          })),
        })
      }
    } catch (error) {
      console.error('Error saving relations:', error)
      throw error
    }
  }

  const saveWpageRelations = async (blockId: string, wpageIds: string[]) => {
    try {
      // Удаляем старые связи
      const currentRelations = await getItems({
        collection: 'wpage_wblock_proto',
        params: {
          filter: { wblock_proto_id: { _eq: blockId } },
          fields: ['id'],
        },
      })

      if (currentRelations.length > 0) {
        await deleteItems({
          collection: 'wpage_wblock_proto',
          items: currentRelations.map((r) => r.id),
        })
      }

      // Добавляем новые
      if (wpageIds.length > 0) {
        await createItems({
          collection: 'wpage_wblock_proto',
          items: wpageIds.map((id) => ({
            wpage_id: id,
            wblock_proto_id: blockId,
          })),
        })
      }
    } catch (error) {
      console.error('Error saving wpage relations:', error)
      throw error
    }
  }

  const bulkAnalyzeHtml = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true

      // Получаем полные данные выбранных записей
      const { getItems, updateItem } = useDirectusItems()
      const selectedIds = selectedItems.value.map((item) => item.id)

      const fullRecords = await getItems({
        collection: 'wblock_proto',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: [
            'id',
            'number',
            'title',
            'html',
            'layout',
            'elements',
            'graphics',
            'composition',
          ],
        },
      })

      // Отправляем на анализ только записи с HTML
      const recordsWithHtml = fullRecords.filter((item) => item.html)

      if (recordsWithHtml.length === 0) {
        toast.add({
          severity: 'warn',
          summary: 'Предупреждение',
          detail: 'У выбранных записей отсутствует HTML для анализа',
          life: 3000,
        })
        return
      }

      const { results } = await $fetch('/api/batch-analyze-html', {
        method: 'POST',
        body: { records: recordsWithHtml },
      })

      // Обновляем записи с результатами анализа и генерации HBS/JSON
      for (const result of results) {
        const record = recordsWithHtml.find(r => r.id === result.id)

        // Подготавливаем данные для обновления
        const updateData = {
          layout: [...new Set(result.layout)],
          elements: [...new Set(result.elements)],
          graphics: [...new Set(result.graphics)],
          composition: result.treeStructure,
        }

        // Генерируем HBS и JSON для записи
        if (record?.html?.trim()) {
          try {
            const templateResult = await $fetch('/api/convert-html-template', {
              method: 'POST',
              body: {
                html: record.html,
                format: 'handlebars',
                blockName: record.title || '',
                blockNumber: record.number || ''
              }
            })

            if (templateResult.success) {
              updateData.hbs = templateResult.template
              updateData.json = JSON.stringify(templateResult.jsonData, null, 2)
            }
          } catch (templateError) {
            console.warn(`Ошибка генерации HBS/JSON для записи ${record.id}:`, templateError)
          }
        }

        await updateItem({
          collection: 'wblock_proto',
          id: result.id,
          item: updateData,
        })
      }

      await loadData()
      selectedItems.value = []
      toast.add({
        severity: 'success',
        summary: 'Готово',
        detail: `Обновлено ${results.length} записей с анализом HTML и генерацией HBS/JSON`,
        life: 3000,
      })
    } catch (error) {
      console.error('Ошибка массового анализа:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось выполнить анализ',
        life: 5000,
      })
    } finally {
      loading.value = false
    }
  }

  const bulkUploadSketches = async () => {
    if (!selectedItems.value.length) return

    // Создаем невидимый input для выбора файлов
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.multiple = true
    fileInput.accept = 'image/*'
    
    fileInput.onchange = async (event) => {
      const files = event.target.files
      if (!files || files.length === 0) return
      
      try {
        loading.value = true
        const { updateItem } = useDirectusItems()
        
        // Получаем ID выбранных элементов
        const selectedIds = selectedItems.value.map(item => item.id)
        
        // Загружаем файлы и обновляем записи
        let successCount = 0
        let errorCount = 0
        
        for (let i = 0; i < Math.min(files.length, selectedIds.length); i++) {
          const file = files[i]
          const itemId = selectedIds[i]
          
          try {
            // Загружаем файл в Directus
            const formData = new FormData()
            formData.append('file', file)
            
            const response = await fetch('http://localhost:8055/files', {
              method: 'POST',
              body: formData,
            })
            
            if (!response.ok) {
              throw new Error(`Ошибка загрузки файла ${file.name}`)
            }
            
            const data = await response.json()
            const fileId = data.data.id
            
            // Обновляем запись с новым sketch ID
            await updateItem({
              collection: 'wblock_proto',
              id: itemId,
              item: {
                sketch: fileId
              },
            })
            
            successCount++
          } catch (error) {
            console.error(`Ошибка при обработке файла ${i+1}:`, error)
            errorCount++
          }
        }
        
        // Обновляем данные в таблице
        await loadData()
        
        // Показываем уведомление о результате
        toast.add({
          severity: 'success',
          summary: 'Загрузка завершена',
          detail: `Успешно: ${successCount}, Ошибок: ${errorCount}`,
          life: 3000,
        })
      } catch (error) {
        console.error('Ошибка массовой загрузки:', error)
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: error.message || 'Не удалось выполнить загрузку',
          life: 5000,
        })
      } finally {
        loading.value = false
      }
    }
    
    // Имитируем клик по input для открытия диалога выбора файлов
    fileInput.click()
  }

  const bulkGenerateScreenshots = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true
      const { getItems, updateItem } = useDirectusItems()

      // Получаем полные данные выбранных записей
      const selectedIds = selectedItems.value.map(item => item.id)

      const fullRecords = await getItems({
        collection: 'wblock_proto',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: ['id', 'number', 'title', 'html', 'css', 'js'],
        },
      })

      // Фильтруем записи с HTML
      const recordsWithHtml = fullRecords.filter(item => item.html?.trim())

      if (recordsWithHtml.length === 0) {
        toast.add({
          severity: 'warn',
          summary: 'Предупреждение',
          detail: 'У выбранных записей отсутствует HTML для генерации скриншотов',
          life: 3000,
        })
        return
      }

      let successCount = 0
      let errorCount = 0

      // Генерируем скриншоты для каждой записи
      for (const record of recordsWithHtml) {
        try {
          const blockTitle = record.title || 'Untitled Block'
          const blockNumber = record.number || 'block'

          // Создаем полный HTML для скриншота
          const blockFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${blockTitle}</title>
  ${record.css || ''}
</head>
<body>
  ${record.html}
  ${record.js || ''}
</body>
</html>`

          // Создаем скриншот
          const response = await $fetch('/api/capture-html-screenshot-temp', {
            method: 'POST',
            body: {
              html: blockFullHtml,
              width: 1400,
              height: 800
            },
            responseType: 'blob'
          })

          // Загружаем скриншот в Directus
          const blob = response as Blob
          const sanitizedTitle = blockTitle.replace(/[^a-zA-Z0-9\-_]/g, '_')
          const file = new File([blob], `block_${blockNumber}_${sanitizedTitle}.png`, { type: 'image/png' })

          const formData = new FormData()
          formData.append('file', file)

          const uploadResponse = await fetch('http://localhost:8055/files', {
            method: 'POST',
            body: formData,
          })

          if (!uploadResponse.ok) {
            throw new Error(`Ошибка загрузки файла для записи ${record.id}`)
          }

          const { data } = await uploadResponse.json()

          // Обновляем запись с новым sketch ID
          await updateItem({
            collection: 'wblock_proto',
            id: record.id,
            item: {
              sketch: data.id
            },
          })

          successCount++
        } catch (error) {
          console.error(`Ошибка при генерации скриншота для записи ${record.id}:`, error)
          errorCount++
        }
      }

      // Обновляем данные в таблице
      await loadData()
      selectedItems.value = []

      // Показываем уведомление о результате
      toast.add({
        severity: 'success',
        summary: 'Генерация скриншотов завершена',
        detail: `Успешно: ${successCount}, Ошибок: ${errorCount}`,
        life: 3000,
      })
    } catch (error) {
      console.error('Ошибка массовой генерации скриншотов:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось выполнить генерацию скриншотов',
        life: 5000,
      })
    } finally {
      loading.value = false
    }
  }

  // Функция для извлечения элементов из выбранных блоков
  const extractElementsFromBlocks = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true

      // Получаем полные данные выбранных записей через composable
      const { getItems, createItems } = useDirectusItems()
      const selectedIds = selectedItems.value.map((item) => item.id)

      const fullRecords = await getItems({
        collection: 'wblock_proto',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: [
            'id',
            'number',
            'title',
            'html',
            'css',
            'js',
            'collection'
          ],
        },
      })

      // Фильтруем записи с HTML
      const recordsWithHtml = fullRecords.filter((item) => item.html?.trim())

      if (recordsWithHtml.length === 0) {
        toast.add({
          severity: 'warn',
          summary: 'Предупреждение',
          detail: 'У выбранных записей отсутствует HTML для извлечения элементов',
          life: 3000,
        })
        return
      }

      // Вызываем API для извлечения элементов
      const { results } = await $fetch('/api/extract-elements-from-blocks', {
        method: 'POST',
        body: { records: recordsWithHtml },
      })

      // Обрабатываем результаты и создаем элементы
      let totalCreatedElements = 0
      const blockElementMap = new Map()

      for (const blockResult of results) {
        if (
          !blockResult.success ||
          !blockResult.elementsToCreate ||
          blockResult.elementsToCreate.length === 0
        ) {
          continue
        }

        try {
          // Создаем элементы через composable
          const createdElements = await createItems({
            collection: 'welem_proto',
            items: blockResult.elementsToCreate,
          })

          const elementsArray = Array.isArray(createdElements) ? createdElements : [createdElements]
          totalCreatedElements += elementsArray.length

          // Сохраняем связи для M2M
          blockElementMap.set(blockResult.blockId, elementsArray.map(el => el.id))

          console.log(`Создано ${elementsArray.length} элементов для блока ${blockResult.blockTitle}`)
        } catch (error) {
          console.error(`Ошибка создания элементов для блока ${blockResult.blockTitle}:`, error)
        }
      }

      // Создаем M2M связи
      if (blockElementMap.size > 0) {
        const relations = []
        for (const [blockId, elementIds] of blockElementMap) {
          for (const elementId of elementIds) {
            relations.push({
              wblock_proto_id: blockId,
              welem_proto_id: elementId
            })
          }
        }

        if (relations.length > 0) {
          await createItems({
            collection: 'wblock_proto_welem_proto',
            items: relations
          })
          console.log(`Создано ${relations.length} связей M2M`)
        }
      }

      await loadData()
      selectedItems.value = []

      toast.add({
        severity: 'success',
        summary: 'Готово',
        detail: `Извлечено ${totalCreatedElements} элементов из ${recordsWithHtml.length} блоков`,
        life: 3000,
      })
    } catch (error) {
      console.error('Ошибка при извлечении элементов из блоков:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось извлечь элементы',
        life: 5000,
      })
    } finally {
      loading.value = false
    }
  }

  // Функция для извлечения элементов из выбранных блоков с генерацией скриншотов
  const extractElementsWithScreenshots = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true

      // Получаем полные данные выбранных записей через composable
      const { getItems, createItems, updateItem } = useDirectusItems()
      const selectedIds = selectedItems.value.map((item) => item.id)

      const fullRecords = await getItems({
        collection: 'wblock_proto',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: [
            'id',
            'number',
            'title',
            'html',
            'css',
            'js',
            'collection'
          ],
        },
      })

      // Фильтруем записи с HTML
      const recordsWithHtml = fullRecords.filter((item) => item.html?.trim())

      if (recordsWithHtml.length === 0) {
        toast.add({
          severity: 'warn',
          summary: 'Предупреждение',
          detail: 'У выбранных записей отсутствует HTML для извлечения элементов',
          life: 3000,
        })
        return
      }

      // Вызываем API для извлечения элементов с данными для скриншотов
      const { results } = await $fetch('/api/extract-elements-from-blocks-with-screenshots', {
        method: 'POST',
        body: { records: recordsWithHtml },
      })

      // Обрабатываем результаты и создаем элементы
      let totalCreatedElements = 0
      let totalScreenshots = 0
      const blockElementMap = new Map()

      // Для batch создания скриншотов
      const allElementsForScreenshots = []
      const elementToCreatedElementMap = new Map()

      for (const blockResult of results) {
        if (
          !blockResult.success ||
          !blockResult.elementsToCreate ||
          blockResult.elementsToCreate.length === 0
        ) {
          continue
        }

        try {
          // Создаем элементы через composable (без скриншотов пока)
          const elementsToCreateWithoutScreenshots = blockResult.elementsToCreate.map(element => {
            const { _screenshotData, ...elementData } = element
            return elementData
          })

          // Разбиваем на батчи по 50 элементов для избежания ошибки "request entity too large"
          const BATCH_SIZE = 50
          const allCreatedElements = []

          for (let i = 0; i < elementsToCreateWithoutScreenshots.length; i += BATCH_SIZE) {
            const batch = elementsToCreateWithoutScreenshots.slice(i, i + BATCH_SIZE)
            console.log(`Создание батча ${Math.floor(i / BATCH_SIZE) + 1} для блока ${blockResult.blockTitle}: ${batch.length} элементов`)

            try {
              const batchResult = await createItems({
                collection: 'welem_proto',
                items: batch,
              })

              const batchElements = Array.isArray(batchResult) ? batchResult : [batchResult]
              allCreatedElements.push(...batchElements)

              console.log(`✅ Батч ${Math.floor(i / BATCH_SIZE) + 1} создан для блока ${blockResult.blockTitle}: ${batchElements.length} элементов`)
            } catch (batchError) {
              console.error(`❌ Ошибка создания батча ${Math.floor(i / BATCH_SIZE) + 1} для блока ${blockResult.blockTitle}:`, batchError)
              throw batchError
            }
          }

          const elementsArray = allCreatedElements
          totalCreatedElements += elementsArray.length

          // Сохраняем связи для M2M
          blockElementMap.set(blockResult.blockId, elementsArray.map(el => el.id))

          console.log(`Создано ${elementsArray.length} элементов для блока ${blockResult.blockTitle} в ${Math.ceil(elementsToCreateWithoutScreenshots.length / BATCH_SIZE)} батчах`)

          // Собираем данные для batch создания скриншотов
          for (let i = 0; i < elementsArray.length; i++) {
            const createdElement = elementsArray[i]
            const originalElementData = blockResult.elementsToCreate[i]

            if (originalElementData._screenshotData) {
              const elementForScreenshot = {
                html: originalElementData._screenshotData.blockFullHtml,
                selector: originalElementData._screenshotData.selector,
                elementTitle: originalElementData._screenshotData.elementTitle,
                elementNumber: originalElementData._screenshotData.elementNumber,
                elementHtml: originalElementData._screenshotData.elementHtml || originalElementData.html // Точный HTML код элемента из _screenshotData или fallback
              }

              console.log(`📤 Подготовка элемента для скриншота: ${elementForScreenshot.elementTitle}`)
              console.log(`📋 HTML элемента: ${elementForScreenshot.elementHtml.substring(0, 80)}...`)
              console.log(`🔧 Селектор: ${elementForScreenshot.selector}`)

              allElementsForScreenshots.push(elementForScreenshot)
              elementToCreatedElementMap.set(elementForScreenshot, createdElement)
            }
          }

        } catch (error) {
          console.error(`Ошибка создания элементов для блока ${blockResult.blockTitle}:`, error)
        }
      }

      // Создаем все скриншоты в batch режиме
      if (allElementsForScreenshots.length > 0) {
        try {
          console.log(`📸 Создание ${allElementsForScreenshots.length} скриншотов элементов в batch режиме...`)

          const batchResponse = await $fetch('/api/capture-batch-element-screenshots', {
            method: 'POST',
            body: {
              elements: allElementsForScreenshots,
              width: 1400,
              height: 800
            }
          })

          console.log(`✅ Batch скриншоты созданы: ${batchResponse.successCount}/${allElementsForScreenshots.length} успешно, ${batchResponse.skippedCount} пропущено за ${batchResponse.totalTime}ms`)

          // Обновляем элементы с ID скриншотов
          for (let i = 0; i < batchResponse.results.length; i++) {
            const screenshotResult = batchResponse.results[i]
            const elementForScreenshot = allElementsForScreenshots[i]
            const createdElement = elementToCreatedElementMap.get(elementForScreenshot)

            if (screenshotResult.success && screenshotResult.fileId && createdElement) {
              try {
                await updateItem({
                  collection: 'welem_proto',
                  id: createdElement.id,
                  item: {
                    sketch: screenshotResult.fileId
                  },
                })

                totalScreenshots++
                console.log(`✅ Скриншот привязан к элементу ${screenshotResult.elementTitle}`)
              } catch (updateError) {
                console.error(`❌ Ошибка обновления элемента ${screenshotResult.elementTitle}:`, updateError)
              }
            } else if (!screenshotResult.success) {
              console.warn(`⚠️ Скриншот не создан для элемента ${screenshotResult.elementTitle}: ${screenshotResult.error}`)
            }
          }

        } catch (batchError) {
          console.error('❌ Ошибка batch создания скриншотов:', batchError)
        }
      }

      // Создаем M2M связи
      if (blockElementMap.size > 0) {
        const relations = []
        for (const [blockId, elementIds] of blockElementMap) {
          for (const elementId of elementIds) {
            relations.push({
              wblock_proto_id: blockId,
              welem_proto_id: elementId
            })
          }
        }

        if (relations.length > 0) {
          await createItems({
            collection: 'wblock_proto_welem_proto',
            items: relations
          })
          console.log(`Создано ${relations.length} связей M2M`)
        }
      }

      await loadData()
      selectedItems.value = []

      toast.add({
        severity: 'success',
        summary: 'Готово',
        detail: `Извлечено ${totalCreatedElements} элементов из ${recordsWithHtml.length} блоков, создано ${totalScreenshots} скриншотов`,
        life: 3000,
      })
    } catch (error) {
      console.error('Ошибка при извлечении элементов с скриншотами:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось извлечь элементы с скриншотами',
        life: 5000,
      })
    } finally {
      loading.value = false
    }
  }

</script>
<style scoped>
  .my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 9px;
    line-height: 1.4;
    padding: 2px;
  }

  /* optional class for removing the outline */
  .prism-editor__textarea:focus {
    outline: none;
  }
  
  .description-cell, .composition-cell {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 100px;
  }
  
  .my {
    max-height: 120px;
  }
  
  .my img  {
    object-fit: contain !important;
    width:auto;
    height:130px;
  }
  .p-tabview-panels {

    padding: 0 !important;
}
</style>
