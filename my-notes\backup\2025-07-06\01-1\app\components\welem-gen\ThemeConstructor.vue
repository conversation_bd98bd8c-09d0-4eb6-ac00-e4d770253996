<template>
  <div class="theme-constructor">
    <!-- Компактный заголовок -->
    <div class="header-section p-2 bg-surface-0 dark:bg-surface-900 border rounded mb-2">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <h4 class="text-sm font-semibold">Конструктор тем</h4>
          <ToggleButton
            v-model="isEditMode"
            on-label="Редактирование"
            off-label="Выбор"
            class="text-xs"
          />
        </div>
        <div class="flex items-center gap-1">
          <Dropdown
            v-model="selectedTheme"
            :options="availableThemes"
            option-label="name"
            option-value="id"
            placeholder="Тема"
            class="text-xs w-32"
            @change="loadTheme"
          />
          <Button
            icon="pi pi-plus"
            class="text-xs p-button-outlined p-1"
            @click="createNewTheme"
            v-tooltip="'Новая тема'"
          />
          <Button
            icon="pi pi-save"
            class="text-xs p-button-success p-1"
            :disabled="!currentTheme"
            @click="saveTheme"
            v-tooltip="'Сохранить'"
          />
        </div>
      </div>
    </div>

    <!-- Режим выбора (компактный) -->
    <div v-if="!isEditMode" class="selection-mode">
      <div class="grid grid-cols-2 lg:grid-cols-4 gap-2">
        <div
          v-for="theme in availableThemes"
          :key="theme.id"
          class="theme-card border rounded p-2 bg-white cursor-pointer transition-all"
          :class="{ 'ring-2 ring-blue-500': selectedThemes.includes(theme.id) }"
          @click="toggleThemeSelection(theme.id)"
        >
          <div class="flex items-center gap-2 mb-1">
            <div
              class="w-4 h-4 rounded"
              :style="{ backgroundColor: theme.colors?.primary || '#3b82f6' }"
            ></div>
            <span class="text-sm font-medium truncate">{{ theme.name }}</span>
            <Checkbox
              :model-value="selectedThemes.includes(theme.id)"
              binary
              class="ml-auto"
              @click.stop
            />
          </div>
          <p class="text-xs text-gray-500 mb-2 truncate">{{ theme.description }}</p>
          <div class="flex gap-1">
            <div
              v-for="(color, key) in Object.entries(theme.colors || {}).slice(0, 4)"
              :key="key"
              class="w-3 h-3 rounded border"
              :style="{ backgroundColor: color[1] }"
              :title="color[0]"
            ></div>
          </div>
        </div>
      </div>

      <!-- Кнопка применения -->
      <div class="mt-3 text-center">
        <Button
          label="Применить выбранные темы"
          icon="pi pi-check"
          class="text-sm"
          :disabled="selectedThemes.length === 0"
          @click="applySelectedThemes"
        />
      </div>
    </div>

    <!-- Режим редактирования -->
    <div v-else-if="currentTheme" class="theme-editor grid grid-cols-1 lg:grid-cols-2 gap-4">
      <!-- Левая панель - настройки -->
      <div class="settings-panel space-y-4">
        <!-- Основная информация -->
        <div class="theme-info border rounded-lg p-4 bg-white">
          <h5 class="text-sm font-semibold mb-3">Информация о теме</h5>
          <div class="space-y-2">
            <div>
              <label class="text-xs text-gray-600">Название:</label>
              <InputText
                v-model="currentTheme.name"
                class="w-full text-sm"
                @input="markAsModified"
              />
            </div>
            <div>
              <label class="text-xs text-gray-600">Описание:</label>
              <Textarea
                v-model="currentTheme.description"
                class="w-full text-xs"
                rows="2"
                @input="markAsModified"
              />
            </div>
            <div class="grid grid-cols-2 gap-2">
              <div>
                <label class="text-xs text-gray-600">Версия:</label>
                <InputText
                  v-model="currentTheme.version"
                  class="w-full text-xs"
                  @input="markAsModified"
                />
              </div>
              <div>
                <label class="text-xs text-gray-600">Автор:</label>
                <InputText
                  v-model="currentTheme.author"
                  class="w-full text-xs"
                  @input="markAsModified"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Цветовая палитра -->
        <div class="color-palette border rounded-lg p-4 bg-white">
          <h5 class="text-sm font-semibold mb-3">Цветовая палитра</h5>
          <div class="grid grid-cols-2 gap-3">
            <div v-for="(color, key) in currentTheme.colors" :key="key">
              <label class="text-xs text-gray-600 capitalize">{{ key }}:</label>
              <div class="flex items-center gap-2">
                <ColorPicker
                  v-model="currentTheme.colors[key]"
                  class="w-8 h-8"
                  @change="markAsModified"
                />
                <InputText
                  v-model="currentTheme.colors[key]"
                  class="flex-1 text-xs"
                  @input="markAsModified"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Типографика -->
        <div class="typography border rounded-lg p-4 bg-white">
          <h5 class="text-sm font-semibold mb-3">Типографика</h5>
          <div class="space-y-3">
            <!-- Семейства шрифтов -->
            <div>
              <label class="text-xs text-gray-600">Основной шрифт:</label>
              <div class="flex gap-2">
                <Dropdown
                  v-model="currentTheme.typography.fontFamily.primary"
                  :options="fontFamilyOptions"
                  option-label="label"
                  option-value="value"
                  class="flex-1 text-xs"
                  @change="markAsModified"
                />
                <InputText
                  v-model="customFontFamily"
                  placeholder="Или введите свой"
                  class="flex-1 text-xs"
                  @input="updateCustomFont"
                />
              </div>
            </div>
            
            <!-- Размеры шрифтов -->
            <div>
              <label class="text-xs text-gray-600 mb-2 block">Размеры шрифтов:</label>
              <div class="grid grid-cols-2 gap-2">
                <div v-for="(size, key) in currentTheme.typography.fontSize" :key="key">
                  <label class="text-xs text-gray-500">{{ key }}:</label>
                  <InputText
                    v-model="currentTheme.typography.fontSize[key]"
                    class="w-full text-xs"
                    @input="markAsModified"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Отступы -->
        <div class="spacing border rounded-lg p-4 bg-white">
          <h5 class="text-sm font-semibold mb-3">Отступы</h5>
          <div class="grid grid-cols-3 gap-2">
            <div v-for="(space, key) in currentTheme.spacing" :key="key">
              <label class="text-xs text-gray-600">{{ key }}:</label>
              <InputText
                v-model="currentTheme.spacing[key]"
                class="w-full text-xs"
                @input="markAsModified"
              />
            </div>
          </div>
        </div>

        <!-- Скругления -->
        <div class="border-radius border rounded-lg p-4 bg-white">
          <h5 class="text-sm font-semibold mb-3">Скругления</h5>
          <div class="grid grid-cols-3 gap-2">
            <div v-for="(radius, key) in currentTheme.borderRadius" :key="key">
              <label class="text-xs text-gray-600">{{ key }}:</label>
              <InputText
                v-model="currentTheme.borderRadius[key]"
                class="w-full text-xs"
                @input="markAsModified"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Правая панель - превью -->
      <div class="preview-panel">
        <!-- Выбор размера превью -->
        <div class="preview-controls mb-4">
          <div class="flex items-center gap-2">
            <label class="text-xs text-gray-600">Размер превью:</label>
            <SelectButton
              v-model="previewSize"
              :options="previewSizeOptions"
              option-label="label"
              option-value="value"
              class="text-xs"
              @change="updatePreview"
            />
          </div>
        </div>

        <!-- Превью темы -->
        <div class="theme-preview border rounded-lg overflow-hidden bg-white">
          <iframe
            ref="previewFrame"
            :style="{ 
              width: '100%', 
              height: previewHeight + 'px',
              minHeight: '400px'
            }"
            frameborder="0"
            sandbox="allow-same-origin allow-scripts"
          />
        </div>
      </div>
    </div>

    <!-- Сообщение если тема не выбрана -->
    <div v-else class="no-theme text-center py-12">
      <i class="pi pi-palette text-4xl text-gray-400 mb-4"></i>
      <h5 class="text-lg font-medium text-gray-600 mb-2">Выберите или создайте тему</h5>
      <p class="text-sm text-gray-500">Используйте dropdown выше для выбора существующей темы или создайте новую</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useToast } from 'primevue/usetoast'
import { 
  themeDefinitions,
  saveCustomThemes,
  loadCustomThemes,
  applyThemeToHtml,
  type ThemeDefinition
} from '~/utils/advanced-theme-mappings'

// Props
interface Props {
  selectedElements: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['theme-applied'])

// Composables
const toast = useToast()

// Refs
const previewFrame = ref<HTMLIFrameElement>()
const selectedTheme = ref<string>('')
const currentTheme = ref<ThemeDefinition | null>(null)
const isModified = ref(false)
const customFontFamily = ref('')
const previewSize = ref('standard')
const previewHeight = ref(600)
const isEditMode = ref(false)
const selectedThemes = ref<string[]>([])

// Options
const fontFamilyOptions = [
  { label: 'Inter', value: 'Inter, system-ui, sans-serif' },
  { label: 'Roboto', value: 'Roboto, sans-serif' },
  { label: 'Open Sans', value: 'Open Sans, sans-serif' },
  { label: 'Lato', value: 'Lato, sans-serif' },
  { label: 'Poppins', value: 'Poppins, sans-serif' },
  { label: 'Montserrat', value: 'Montserrat, sans-serif' },
  { label: 'Source Sans Pro', value: 'Source Sans Pro, sans-serif' }
]

const previewSizeOptions = [
  { label: 'Мини', value: 'mini' },
  { label: 'Стандарт', value: 'standard' },
  { label: 'Макси', value: 'maxi' }
]

// Computed
const availableThemes = computed(() => {
  const custom = loadCustomThemes()
  return [...themeDefinitions, ...custom]
})

// Methods
const createNewTheme = () => {
  const newTheme: ThemeDefinition = {
    id: `theme-${Date.now()}`,
    name: 'Новая тема',
    description: 'Описание новой темы',
    version: '1.0.0',
    author: 'Пользователь',
    preview: '',
    enabled: true,
    colors: {
      primary: '#3b82f6',
      secondary: '#6b7280',
      accent: '#10b981',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1f2937',
      textSecondary: '#6b7280',
      border: '#e5e7eb',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    },
    typography: {
      fontFamily: {
        primary: 'Inter, system-ui, sans-serif',
        secondary: 'Inter, system-ui, sans-serif',
        monospace: 'JetBrains Mono, monospace'
      },
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem'
      },
      fontWeight: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      },
      lineHeight: {
        tight: 1.25,
        normal: 1.5,
        relaxed: 1.75
      }
    },
    spacing: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      '2xl': '3rem'
    },
    borderRadius: {
      none: '0',
      sm: '0.25rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem',
      full: '9999px'
    },
    shadows: {
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
      '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
    },
    rules: []
  }
  
  currentTheme.value = newTheme
  selectedTheme.value = newTheme.id
  markAsModified()
  updatePreview()
}

const loadTheme = () => {
  if (!selectedTheme.value) return
  
  const theme = availableThemes.value.find(t => t.id === selectedTheme.value)
  if (theme) {
    currentTheme.value = JSON.parse(JSON.stringify(theme))
    updatePreview()
  }
}

const saveTheme = () => {
  if (!currentTheme.value) return
  
  const customThemes = loadCustomThemes()
  const existingIndex = customThemes.findIndex(t => t.id === currentTheme.value!.id)
  
  if (existingIndex >= 0) {
    customThemes[existingIndex] = currentTheme.value
  } else {
    customThemes.push(currentTheme.value)
  }
  
  saveCustomThemes(customThemes)
  isModified.value = false
  
  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Тема сохранена',
    life: 3000
  })
}

const markAsModified = () => {
  isModified.value = true
}

const updateCustomFont = () => {
  if (customFontFamily.value && currentTheme.value) {
    currentTheme.value.typography.fontFamily.primary = customFontFamily.value
    markAsModified()
  }
}

const toggleThemeSelection = (themeId: string) => {
  const index = selectedThemes.value.indexOf(themeId)
  if (index > -1) {
    selectedThemes.value.splice(index, 1)
  } else {
    selectedThemes.value.push(themeId)
  }
}

const applySelectedThemes = () => {
  const themes = availableThemes.value.filter(theme => selectedThemes.value.includes(theme.id))

  // Эмитим событие с выбранными темами
  emit('theme-applied', {
    selectedThemes: selectedThemes.value,
    themes,
    elements: props.selectedElements
  })
}

const updatePreview = () => {
  if (!currentTheme.value || !previewFrame.value) return
  
  // Генерируем HTML для превью в зависимости от размера
  let previewHtml = ''
  
  switch (previewSize.value) {
    case 'mini':
      previewHtml = generateMiniPreview()
      previewHeight.value = 300
      break
    case 'standard':
      previewHtml = generateStandardPreview()
      previewHeight.value = 600
      break
    case 'maxi':
      previewHtml = generateMaxiPreview()
      previewHeight.value = 800
      break
  }
  
  const { html, css } = applyThemeToHtml(previewHtml, currentTheme.value)
  
  const fullHtml = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Theme Preview</title>
      <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
      <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"><\/script>
      <style>
        body {
          padding: 20px;
          font-family: ${currentTheme.value.typography.fontFamily.primary};
          background: ${currentTheme.value.colors.background};
          color: ${currentTheme.value.colors.text};
        }
        ${css}
      </style>
    </head>
    <body>
      ${html}
    </body>
    </html>
  `
  
  previewFrame.value.srcdoc = fullHtml
}

const generateMiniPreview = () => {
  return `
    <div class="container">
      <div class="color-palette mb-4">
        <h5>Цветовая палитра</h5>
        <div class="d-flex gap-2">
          <div class="p-2 rounded" style="background: ${currentTheme.value?.colors.primary}; color: white;">Primary</div>
          <div class="p-2 rounded" style="background: ${currentTheme.value?.colors.secondary}; color: white;">Secondary</div>
          <div class="p-2 rounded" style="background: ${currentTheme.value?.colors.accent}; color: white;">Accent</div>
        </div>
      </div>
      
      <div class="typography mb-4">
        <h1>Заголовок H1</h1>
        <h2>Заголовок H2</h2>
        <h3>Заголовок H3</h3>
        <p>Обычный текст параграфа</p>
      </div>
      
      <div class="buttons mb-4">
        <button class="btn btn-primary me-2">Основная кнопка</button>
        <button class="btn btn-secondary me-2">Вторичная кнопка</button>
        <button class="btn btn-outline-primary">Контурная кнопка</button>
      </div>
    </div>
  `
}

const generateStandardPreview = () => {
  return generateMiniPreview() + `
    <div class="container">
      <div class="cards mb-4">
        <h5>Карточки</h5>
        <div class="row">
          <div class="col-md-4">
            <div class="card">
              <div class="card-body">
                <h5 class="card-title">Заголовок карточки</h5>
                <p class="card-text">Текст карточки с описанием.</p>
                <a href="#" class="btn btn-primary">Действие</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="forms mb-4">
        <h5>Формы</h5>
        <form>
          <div class="mb-3">
            <label class="form-label">Email</label>
            <input type="email" class="form-control" placeholder="<EMAIL>">
          </div>
          <div class="mb-3">
            <label class="form-label">Сообщение</label>
            <textarea class="form-control" rows="3"></textarea>
          </div>
          <button type="submit" class="btn btn-primary">Отправить</button>
        </form>
      </div>
    </div>
  `
}

const generateMaxiPreview = () => {
  return generateStandardPreview() + `
    <div class="container">
      <div class="alerts mb-4">
        <h5>Уведомления</h5>
        <div class="alert alert-primary">Информационное уведомление</div>
        <div class="alert alert-success">Успешное уведомление</div>
        <div class="alert alert-warning">Предупреждение</div>
        <div class="alert alert-danger">Ошибка</div>
      </div>
      
      <div class="navigation mb-4">
        <h5>Навигация</h5>
        <nav class="navbar navbar-expand-lg navbar-light bg-light">
          <div class="container-fluid">
            <a class="navbar-brand" href="#">Бренд</a>
            <div class="navbar-nav">
              <a class="nav-link active" href="#">Главная</a>
              <a class="nav-link" href="#">О нас</a>
              <a class="nav-link" href="#">Контакты</a>
            </div>
          </div>
        </nav>
      </div>
      
      <div class="tables mb-4">
        <h5>Таблицы</h5>
        <table class="table">
          <thead>
            <tr>
              <th>Название</th>
              <th>Значение</th>
              <th>Статус</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Элемент 1</td>
              <td>Значение 1</td>
              <td><span class="badge bg-success">Активен</span></td>
            </tr>
            <tr>
              <td>Элемент 2</td>
              <td>Значение 2</td>
              <td><span class="badge bg-warning">Ожидание</span></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  `
}

// Watchers
watch(() => currentTheme.value, () => {
  if (currentTheme.value) {
    updatePreview()
  }
}, { deep: true })

// Lifecycle
onMounted(() => {
  // Автоматически загружаем первую тему
  if (availableThemes.value.length > 0) {
    selectedTheme.value = availableThemes.value[0].id
    loadTheme()
  }
})
</script>

<style scoped>
.theme-constructor {
  max-height: 90vh;
  overflow-y: auto;
}

.color-palette .grid > div {
  transition: all 0.2s ease;
}

.color-palette .grid > div:hover {
  transform: scale(1.02);
}

.preview-panel {
  position: sticky;
  top: 0;
}
</style>
