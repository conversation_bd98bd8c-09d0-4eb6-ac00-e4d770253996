<template>
  <div class="converter-container">
    <div class="grid">
      <!-- Левая панель с вводом HTML -->
      <div class="col-12 md:col-6 p-2">
        <div class="p-inputgroup mb-2">
          <span class="p-inputgroup-addon">HTML</span>
          <Textarea 
            v-model="htmlInput" 
            rows="10" 
            class="w-full" 
            placeholder="Вставьте HTML код сюда..."
            auto-resize
          />
        </div>
        <div class="flex justify-content-between mb-2">
          <div>
            <label for="template-format" class="block mb-1">Формат шаблона</label>
            <Dropdown 
              id="template-format"
              v-model="templateFormat" 
              :options="formatOptions" 
              option-label="label" 
              option-value="value"
              placeholder="Выберите формат"
              class="w-full md:w-14rem"
            />
          </div>
          <div class="flex align-items-end">
            <Button 
              label="Конвертировать" 
              icon="pi pi-refresh" 
              :disabled="!htmlInput.trim()"
              :loading="converting"
              @click="convertHtml"
            />
          </div>
        </div>
      </div>
      
      <!-- Правая панель с результатами -->
      <div class="col-12 md:col-6 p-2">
        <TabView>
          <TabPanel header="Шаблон">
            <div class="p-2 border-1 border-round surface-border">
              <div class="flex justify-content-between mb-2">
                <h3 class="m-0">{{ templateFormat === 'handlebars' ? 'Handlebars' : 'Pug' }}</h3>
                <Button 
                  icon="pi pi-copy" 
                  text 
                  :disabled="!templateOutput"
                  tooltip="Копировать в буфер обмена"
                  tooltip-position="left"
                  @click="copyToClipboard(templateOutput)"
                />
              </div>
              <Divider class="my-2" />
              <pre class="template-output">{{ templateOutput }}</pre>
            </div>
          </TabPanel>
          <TabPanel header="JSON">
            <div class="p-2 border-1 border-round surface-border">
              <div class="flex justify-content-between mb-2">
                <h3 class="m-0">JSON данные</h3>
                <Button 
                  icon="pi pi-copy" 
                  text 
                  :disabled="!jsonOutput"
                  tooltip="Копировать в буфер обмена"
                  tooltip-position="left"
                  @click="copyToClipboard(jsonOutput)"
                />
              </div>
              <Divider class="my-2" />
              <pre class="json-output">{{ jsonOutput }}</pre>
            </div>
          </TabPanel>
        </TabView>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useToast } from 'primevue/usetoast';

// Состояние компонента
const htmlInput = ref('');
const templateFormat = ref('handlebars');
const templateOutput = ref('');
const jsonOutput = ref('');
const converting = ref(false);

// Опции формата шаблона
const formatOptions = [
  { label: 'Handlebars', value: 'handlebars' },
  { label: 'Pug', value: 'pug' }
];

// Toast для уведомлений
const toast = useToast();

// Функция конвертации HTML
async function convertHtml() {
  if (!htmlInput.value.trim()) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Введите HTML код для конвертации',
      life: 3000
    });
    return;
  }
  
  converting.value = true;
  
  try {
    // Вызываем API для конвертации
    const response = await fetch('/api/convert-html', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        html: htmlInput.value,
        format: templateFormat.value
      })
    });
    
    if (!response.ok) {
      throw new Error(`Ошибка HTTP: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      templateOutput.value = result.template || '';
      jsonOutput.value = JSON.stringify(result.data, null, 2) || '{}';
      
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'HTML успешно конвертирован',
        life: 3000
      });
    } else {
      throw new Error(result.error || 'Неизвестная ошибка при конвертации');
    }
  } catch (error) {
    console.error('Ошибка при конвертации HTML:', error);
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: `Не удалось конвертировать HTML: ${error.message}`,
      life: 5000
    });
  } finally {
    converting.value = false;
  }
}

// Функция копирования в буфер обмена
function copyToClipboard(text) {
  if (!text) return;
  
  navigator.clipboard.writeText(text)
    .then(() => {
      toast.add({
        severity: 'info',
        summary: 'Скопировано',
        detail: 'Текст скопирован в буфер обмена',
        life: 2000
      });
    })
    .catch(err => {
      console.error('Ошибка при копировании в буфер обмена:', err);
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось скопировать текст',
        life: 3000
      });
    });
}
</script>

<style scoped>
.template-output,
.json-output {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 0.9rem;
}
</style>