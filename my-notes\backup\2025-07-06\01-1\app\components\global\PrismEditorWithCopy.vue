<template>
  <div class="prism-editor-container">
    <PrismEditor
      :model-value="modelValue"
      :class="computedEditorClass"
      :highlight="highlight"
      :placeholder="placeholder"
      :line-numbers="lineNumbers"
      :style="computedStyle"
      @update:model-value="$emit('update:modelValue', $event)"
      @input="$emit('input', $event)"
    />
    <div class="button-container">
      <slot name="additional-buttons" />
      <Button
        v-if="showCopyButton"
        v-tooltip.left="copyTooltip"
        icon="pi pi-copy"
        class="copy-button"
        size="small"
        text
        @click="copyToClipboard"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { PrismEditor } from 'vue-prism-editor'
  import { useToast } from 'primevue/usetoast'
  import Button from 'primevue/button'

  interface Props {
    modelValue: string
    editorClass?: string
    highlight?: (code: string) => string
    placeholder?: string
    lineNumbers?: boolean
    style?: string | object
    showCopyButton?: boolean
    fieldName?: string
    maxHeight?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    editorClass: 'my-editor',
    placeholder: '',
    lineNumbers: true,
    style: '',
    showCopyButton: true,
    fieldName: 'код',
    maxHeight: '180px'
  })

  const computedEditorClass = computed(() => {
    return `${props.editorClass} prism-editor-global`
  })

  const computedStyle = computed(() => {
    const baseStyle = {
      maxHeight: props.maxHeight,
      overflow: 'auto'
    }

    if (typeof props.style === 'string') {
      return `${props.style}; max-height: ${props.maxHeight}; overflow: auto;`
    } else if (typeof props.style === 'object') {
      return { ...baseStyle, ...props.style }
    }

    return baseStyle
  })

  const emit = defineEmits<{
    'update:modelValue': [value: string]
    'input': [event: Event]
  }>()

  const toast = useToast()

  const copyTooltip = computed(() => `Копировать ${props.fieldName}`)

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(props.modelValue)
      toast.add({
        severity: 'success',
        summary: 'Скопировано',
        detail: `Содержимое поля ${props.fieldName} скопировано в буфер обмена`,
        life: 2000,
      })
    } catch (error) {
      console.error('Ошибка копирования:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось скопировать в буфер обмена',
        life: 3000,
      })
    }
  }
</script>

<style>
  /* Глобальные стили для PrismEditor */
  .prism-editor-global.my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3 !important;
    color: #666 !important;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace !important;
    font-size: 10px !important;
    line-height: 1.4 !important;
    padding: 2px !important;
    max-height: 180px !important;
    overflow: auto !important;
  }

  /* optional class for removing the outline */
  .prism-editor-global .prism-editor__textarea:focus {
    outline: none !important;
  }
</style>

<style scoped>
  /* Контейнер для позиционирования */
  .prism-editor-container {
    position: relative;
    display: block;
  }

  /* Контейнер для кнопок */
  .button-container {
    position: absolute;
    top: 2px;
    right: 2px;
    z-index: 10;
    display: flex;
    gap: 2px;
    align-items: center;
  }

  /* Стили для кнопки копирования */
  .copy-button {
    padding: 1px !important;
    font-size: 10px !important;
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .copy-button:hover {
    opacity: 1;
  }

  /* Стили для дополнительных кнопок */
  .button-container :deep(.p-button) {
    padding: 1px !important;
    font-size: 10px !important;
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .button-container :deep(.p-button:hover) {
    opacity: 1;
  }
</style>
