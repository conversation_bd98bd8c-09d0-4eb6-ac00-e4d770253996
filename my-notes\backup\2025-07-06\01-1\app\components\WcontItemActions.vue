<template>
  <div class="flex gap-1">
    <Button 
      v-for="action in filteredActions" 
      :key="action.label"
      :label="action.icon" 
      :class="action.buttonClass"
      @click="action.handler"
      :title="action.tooltip"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Button from 'primevue/button'
import type { WcontItem } from '~/types/wconts'

interface ActionConfig {
  label: string
  icon: string
  buttonClass: string
  handler: () => void
  tooltip?: string
  visible?: boolean
}

const props = defineProps<{
  item: WcontItem
  actions?: Partial<{
    edit: boolean
    duplicate: boolean
    convert: boolean
    delete: boolean
  }>
}>()

const emit = defineEmits<{
  (e: 'edit', item: WcontItem): void
  (e: 'duplicate', item: WcontItem): void
  (e: 'convert', item: WcontItem): void
  (e: 'delete', item: WcontItem): void
}>()

const defaultActions = computed<ActionConfig[]>(() => [
  {
    label: 'edit',
    icon: '✏️',
    buttonClass: 'p-button-rounded p-button-warning p-button-sm',
    handler: () => emit('edit', props.item),
    tooltip: 'Редактировать',
    visible: props.actions?.edit ?? true
  },
  {
    label: 'duplicate',
    icon: '📋',
    buttonClass: 'p-button-rounded p-button-info p-button-sm',
    handler: () => emit('duplicate', props.item),
    tooltip: 'Дублировать',
    visible: props.actions?.duplicate ?? true
  },
  {
    label: 'convert',
    icon: '🔄',
    buttonClass: 'p-button-rounded p-button-success p-button-sm',
    handler: () => emit('convert', props.item),
    tooltip: 'Конвертировать',
    visible: props.actions?.convert ?? true
  },
  {
    label: 'delete',
    icon: '🗑️',
    buttonClass: 'p-button-rounded p-button-danger p-button-sm',
    handler: () => emit('delete', props.item),
    tooltip: 'Удалить',
    visible: props.actions?.delete ?? true
  }
])

const filteredActions = computed(() => 
  defaultActions.value.filter(action => action.visible)
)
</script>
