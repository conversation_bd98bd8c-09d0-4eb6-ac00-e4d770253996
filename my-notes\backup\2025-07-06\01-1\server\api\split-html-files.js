// server/api/split-html-files.js
import { defineEventHandler, readBody, createError } from 'h3';
import { load } from 'cheerio';
import { analyzeHtml } from '../utils/htmlAnalyzer';
import { htmlToHandlebarsAndJson, decodeHtmlEntities } from '../utils/htmlToTemplate.js';
import html2pug from 'html2pug';

// URL Directus сервера
const DIRECTUS_URL = 'http://localhost:8055';

export default defineEventHandler(async (event) => {
  try {
    const { fileIds, splitOption = 1, sourceCollection = 'directus_files' } = await readBody(event);

    console.log('Получены fileIds для разделения:', fileIds);
    console.log('Выбран вариант разделения:', splitOption);
    console.log('Источник данных:', sourceCollection);

    // Результаты обработки
    const results = [];
    let processedFiles = 0;
    let totalBlocks = 0;

    // Для каждого файла
    for (const fileId of fileIds) {
      try {
        console.log(`🔍 Обработка записи с ID: ${fileId}`);

        let htmlContent = '';
        let fileName = '';
        let fileNumber = '';
        let fileTags = [];
        let cssContent = '';
        let jsContent = '';

        if (sourceCollection === 'wpage') {
          // Работаем с коллекцией wpage
          console.log(`📥 Запрос данных из коллекции wpage: ${DIRECTUS_URL}/items/wpage/${fileId}`);
          const pageResponse = await fetch(`${DIRECTUS_URL}/items/wpage/${fileId}`);

          if (!pageResponse.ok) {
            console.error(`❌ Не удалось получить данные страницы: HTTP ${pageResponse.status}`);
            results.push({
              fileId,
              success: false,
              error: `Не удалось получить данные страницы (HTTP ${pageResponse.status})`
            });
            continue;
          }

          const pageData = await pageResponse.json();
          const page = pageData.data;

          htmlContent = page.html || '';
          fileName = page.title || `page-${fileId}`;
          fileNumber = page.number || fileId;
          fileTags = page.tags || [];
          cssContent = page.css || '';
          jsContent = page.js || '';

          console.log(`📋 Получена информация о странице: title=${fileName}, number=${fileNumber}, tags=${fileTags.join(', ')}`);
          console.log(`📄 Получено HTML содержимое, длина: ${htmlContent.length} символов`);
          console.log(`🎨 CSS содержимое, длина: ${cssContent.length} символов`);
          console.log(`⚡ JS содержимое, длина: ${jsContent.length} символов`);
        } else {
          // Работаем с коллекцией directus_files (оригинальная логика)
          console.log(`📥 Запрос содержимого файла: ${DIRECTUS_URL}/assets/${fileId}`);
          const fileContentResponse = await fetch(`${DIRECTUS_URL}/assets/${fileId}`);

          if (!fileContentResponse.ok) {
            console.error(`❌ Не удалось получить содержимое файла: HTTP ${fileContentResponse.status}`);
            results.push({
              fileId,
              success: false,
              error: `Не удалось получить содержимое файла (HTTP ${fileContentResponse.status})`
            });
            continue;
          }

          // Получаем информацию о файле из Directus для получения title и tags
          console.log(`📥 Запрос информации о файле: ${DIRECTUS_URL}/files/${fileId}`);
          const fileInfoResponse = await fetch(`${DIRECTUS_URL}/files/${fileId}`);

          if (fileInfoResponse.ok) {
            const fileInfo = await fileInfoResponse.json();
            // Используем title файла вместо ID
            fileName = fileInfo.data.title || `file-${fileId}`;
            // Получаем номер файла из поля number, если оно существует
            fileNumber = fileInfo.data.number || fileId;
            // Получаем теги файла, если они существуют
            fileTags = fileInfo.data.tags || [];
            console.log(`📋 Получена информация о файле: title=${fileName}, number=${fileNumber}, tags=${fileTags.join(', ')}`);
          } else {
            // Если не удалось получить информацию о файле, используем ID как запасной вариант
            fileName = `file-${fileId}`;
            fileNumber = fileId;
            console.warn(`⚠️ Не удалось получить информацию о файле, используем ID: ${fileName}`);
          }

          // Получаем HTML содержимое
          htmlContent = await fileContentResponse.text();
          console.log(`📄 Получено HTML содержимое, длина: ${htmlContent.length} символов`);
        }

        // 4. Разделяем HTML на блоки
        console.log('✂️ Разделение HTML на блоки...');
        let extractedCss = '';
        let extractedJs = '';

        if (sourceCollection === 'wpage') {
          // Для wpage используем CSS и JS из полей записи
          extractedCss = cssContent;
          extractedJs = jsContent;
        } else {
          // Для files извлекаем CSS и JS из HTML
          const splitResult = splitHtmlIntoBlocks(htmlContent, splitOption);
          extractedCss = splitResult.cssContent;
          extractedJs = splitResult.jsContent;
        }

        const { blocks, blockTypeInfo } = splitHtmlIntoBlocks(htmlContent, splitOption);
        console.log(`✅ HTML разделен на ${blocks.length} блоков`);
        console.log(`✅ Используется CSS: ${extractedCss.length} символов, JS: ${extractedJs.length} символов`);

        if (blocks.length === 0) {
          console.warn('⚠️ Не найдено блоков для разделения в HTML');
          results.push({
            fileId,
            fileName,
            success: true,
            blocksExtracted: 0,
            blocksAdded: 0,
            message: 'Не найдено блоков для разделения'
          });
          continue;
        }

        // Проверяем, что у нас есть информация о типах для всех блоков
        if (blockTypeInfo.length < blocks.length) {
          console.warn(`⚠️ Недостаточно информации о типах блоков: ${blockTypeInfo.length} типов для ${blocks.length} блоков`);
          // Дополняем массив типов значением 'Контент' для недостающих блоков
          while (blockTypeInfo.length < blocks.length) {
            blockTypeInfo.push('Контент');
          }
        }

        // Вывод информации о каждом блоке
        blocks.forEach((block, idx) => {
          console.log(`📌 Блок ${idx + 1}: длина ${block.length} символов`);
          console.log(`   Первые 100 символов: ${block.substring(0, 100)}...`);
        });

        // 5. Подготавливаем блоки и возвращаем их для обработки на стороне клиента
        const blocksToCreate = [];

        // 6. Обрабатываем каждый блок
        for (let i = 0; i < blocks.length; i++) {
          const block = blocks[i];

          // Пропускаем пустые или слишком маленькие блоки
          if (!block.trim() || block.length < 10) {
            console.log(`⏭️ Пропускаем блок ${i + 1}: слишком маленький (${block.length} символов)`);
            continue;
          }

          // Используем существующую функцию анализа HTML
          console.log(`🔎 Анализ блока ${i + 1}...`);
          const analysis = analyzeHtml(block);
          console.log(`✅ Анализ блока ${i + 1} завершен:`,
            `layout: ${Array.from(analysis.layout || []).length} элементов,`,
            `elements: ${Array.from(analysis.elements || []).length} элементов,`,
            `graphics: ${Array.from(analysis.graphics || []).length} элементов,`,
            `features: ${Array.from(analysis.features || []).length} элементов`);

          // Форматируем порядковый номер как двузначное число
          const blockNumber = String(i + 1).padStart(2, '0');

          // Определяем тип блока
          const blockType = blockTypeInfo[i] || 'Контент';

          // Создаем новый элемент для wblock_proto
          const newBlock = {
            title: `${fileName} - ${blockNumber}`,
            number: `${fileNumber}-${blockNumber}`,
            status: 'idea', // По умолчанию "Идея"
            html: decodeHtmlEntities(block), // Декодируем HTML-сущности в атрибутах
            composition: analysis.treeStructure || '',
            layout: Array.from(analysis.layout || []),
            elements: Array.from(analysis.elements || []),
            graphics: Array.from(analysis.graphics || []),
            features: Array.from(analysis.features || []),
            block_type: [blockType], // Устанавливаем тип блока в зависимости от тега
            date_updated: new Date().toISOString(),
            // Добавляем CSS и JS
            css: extractedCss || '',
            js: extractedJs || '',
            // Устанавливаем collection на основе тегов файла
            collection: fileTags.length > 0 ? fileTags : [],
            type: blockType,
            tags: fileTags,
            file: fileId
          };

          // Пробуем сконвертировать HTML в handlebars и JSON, но не блокируем создание блока при ошибке
          try {
            console.log(`🔄 Генерация handlebars и JSON для блока ${i + 1}...`);
            const result = htmlToHandlebarsAndJson(block, fileName, blockNumber);

            if (result.success) {
              // Добавляем результаты конвертации только если она успешна
              newBlock.hbs = result.hbsTemplate || '';
              newBlock.json = JSON.stringify(result.jsonData || {}, null, 2);
              console.log(`✅ Успешно сгенерирован шаблон и JSON для блока ${blockNumber}`);
            } else {
              console.warn(`⚠️ Проблема при генерации шаблона для блока ${blockNumber}: ${result.error || 'Неизвестная ошибка'}`);
              console.log(`⚙️ Продолжаем с сохранением блока без шаблона и JSON...`);
            }
          } catch (convErr) {
            console.error(`❌ Ошибка при конвертации блока ${blockNumber}:`, convErr);
            console.log(`⚙️ Продолжаем с сохранением блока без шаблона и JSON...`);
          }

          console.log(`🏷️ Установлена коллекция: ${newBlock.collection.join(', ') || 'не указана'}`);
          console.log(`📝 Добавлены CSS (${extractedCss.length} символов) и JS (${extractedJs.length} символов)`);
          console.log(`🔄 HTML блока декодирован для корректного отображения атрибутов`);
          console.log(`🏷️ Установлен тип блока: ${blockType}`);
          console.log(`📝 Подготовлен блок для создания: ${newBlock.title}`);

          // Добавляем блок в список для возврата клиенту
          blocksToCreate.push(newBlock);
          totalBlocks++;
        }

        // Вместо сохранения, возвращаем блоки клиенту
        results.push({
          fileId,
          fileName,
          success: true,
          blocksExtracted: blocks.length,
          blocksToCreate: blocksToCreate, // Передаем данные для создания блоков на клиенте
        });

        processedFiles++;

      } catch (fileError) {
        console.error(`❌ Ошибка при обработке файла ${fileId}:`, fileError);
        results.push({
          fileId,
          success: false,
          error: fileError.message
        });
      }
    }

    console.log(`🏁 Итоговый результат: обработано ${processedFiles} файлов`);

    return {
      success: true,
      processedFiles,
      results
    };

  } catch (error) {
    console.error('❌ Общая ошибка при разделении HTML файлов:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message
    });
  }
});

// Функция для разделения HTML на блоки
function splitHtmlIntoBlocks(html, splitOption = 1) {
  console.log('🔪 Начало разделения HTML на блоки...');
  console.log(`🔧 Используется вариант разделения: ${splitOption}`);

  if (!html || html.length < 100) {
    console.warn('⚠️ HTML пустой или слишком короткий для разделения');
    return { blocks: [], blockTypeInfo: [], cssContent: '', jsContent: '' };
  }

  // Очищаем HTML от ненужных элементов
  console.log('🧹 Очистка HTML...');
  html = cleanHtml(html);
  console.log(`✅ HTML очищен, итоговая длина: ${html.length} символов`);

  // Используем cheerio для парсинга HTML
  console.log('🔍 Парсинг HTML с помощью cheerio...');

  // Определяем префикс пути в зависимости от splitOption
  let pathPrefix;
  if (splitOption === 2) {
    pathPrefix = 'https://fm-demo.ru/html2/';
  } else if (splitOption === 3) {
    pathPrefix = 'https://fm-demo.ru/html2/';
  } else {
    pathPrefix = 'https://fm-demo.ru/html/';
  }

  console.log(`🔗 Используется префикс для CSS/JS: ${pathPrefix}`);

  // Сначала извлечем CSS и JavaScript код перед удалением тегов
  let cssContent = '';
  let jsContent = '';

  try {
    // Создаем отдельный экземпляр cheerio для извлечения CSS и JS
    const $extract = load(html, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    });

    console.log('🔍 Извлечение CSS из тегов link и style...');



    // Извлекаем CSS из тегов link и обрабатываем относительные пути
    $extract('link[rel="stylesheet"], link[rel="preconnect"]').each((i, el) => {
      // Проверяем наличие относительного пути в href
      const href = $extract(el).attr('href');

      if (href && !href.startsWith('http://') && !href.startsWith('https://') && !href.startsWith('//')) {
        // Добавляем префикс к относительному пути
        $extract(el).attr('href', `${pathPrefix}${href}`);
        console.log(`🔗 Добавлен префикс к CSS пути: ${href} -> ${pathPrefix}${href}`);
      }

      cssContent += $extract.html(el) + '\n';
    });

    // Извлекаем CSS из тегов style
    $extract('style').each((i, el) => {
      cssContent += $extract.html(el) + '\n';
    });

    console.log(`✅ Извлечено ${cssContent.length} символов CSS`);

    console.log('🔍 Извлечение JavaScript из тегов script...');

    // Извлекаем JavaScript из тегов script и обрабатываем относительные пути
    $extract('script').each((i, el) => {
      // Проверяем наличие относительного пути в src
      const src = $extract(el).attr('src');

      if (src && !src.startsWith('http://') && !src.startsWith('https://') && !src.startsWith('//')) {
        // Добавляем префикс к относительному пути
        $extract(el).attr('src', `${pathPrefix}${src}`);
        console.log(`🔗 Добавлен префикс к JS пути: ${src} -> ${pathPrefix}${src}`);
      }

      jsContent += $extract.html(el) + '\n';
    });

    console.log(`✅ Извлечено ${jsContent.length} символов JavaScript`);
  } catch (extractError) {
    console.error('❌ Ошибка при извлечении CSS и JavaScript:', extractError);
  }
  try {
    // Настраиваем cheerio для сохранения оригинальных кавычек в атрибутах
    const $ = load(html, {
      decodeEntities: false, // Отключаем декодирование HTML-сущностей
      xmlMode: false,       // Не используем XML режим
      _useHtmlParser2: true // Используем htmlparser2 для лучшей совместимости
    });

    // Удаляем теги script и link
    console.log('🗑️ Удаление тегов script и link...');
    $('script').remove();
    $('link').remove();

    // Сохраняем HTML со всеми комментариями
    console.log('📋 Сохраняем HTML с комментариями...');
    const cleanedHtml = $.html();

    // Определяем разделители и типы блоков в зависимости от splitOption
    let dividers, blockTypes;

    if (splitOption === 2) {
      // Вариант 2: header, div.section, footer
      dividers = ['header', 'div.section', 'footer'];
      blockTypes = {
        'header': 'Шапка',
        'div.section': 'Контент',
        'footer': 'Подвал'
      };
    } else if (splitOption === 3) {
      // Вариант 3: header, section, div, footer
      dividers = ['header', 'section', 'div', 'footer'];
      blockTypes = {
        'header': 'Шапка',
        'section': 'Контент',
        'div': 'Контент',
        'footer': 'Подвал'
      };
    } else {
      // Вариант 1: header, section, footer (по умолчанию)
      dividers = ['header', 'section', 'footer'];
      blockTypes = {
        'header': 'Шапка',
        'section': 'Контент',
        'footer': 'Подвал'
      };
    }



    console.log(`🔍 Используются селекторы для разделения: ${dividers.join(', ')}`);

    // Массив для хранения информации о типе каждого блока
    const blockTypeInfo = [];

    console.log(`🔍 Поиск блоков по ${dividers.length} селекторам...`);

    // Загружаем очищенный HTML с сохранением комментариев и оригинальных кавычек
    const $clean = load(cleanedHtml, {
      decodeEntities: false, // Отключаем декодирование HTML-сущностей
      xmlMode: false,       // Не используем XML режим
      _useHtmlParser2: true // Используем htmlparser2 для лучшей совместимости
    });

    // Выводим структуру верхнего уровня документа для отладки
    const topLevelElements = [];
    $clean('body > *').each((i, el) => {
      topLevelElements.push(`${el.name}${el.attribs.id ? '#' + el.attribs.id : ''}${el.attribs.class ? '.' + el.attribs.class.replace(/\s+/g, '.') : ''}`);
    });
    console.log(`🔍 Элементы верхнего уровня: ${topLevelElements.join(', ')}`);

    // Собираем блоки
    const blocks = [];

    // Собираем все элементы сразу, чтобы избежать проблем с удалением из DOM
    const allElements = [];

    dividers.forEach(selector => {
      try {
        const elements = $clean(selector);
        console.log(`🔍 Найдено ${elements.length} элементов по селектору "${selector}"`);

        elements.each((i, element) => {
          // Проверяем, что элемент достаточно большой, чтобы быть блоком
          const html = $clean(element).html();
          if (html && html.length > 10) {
            console.log(`✅ Найден блок по селектору "${selector}" (элемент ${i + 1}), длина: ${html.length} символов`);

            // Определяем тип блока на основе селектора
            const blockType = blockTypes[selector] || 'Контент';
            console.log(`🏷️ Тип блока: ${blockType}`);

            // Добавляем элемент в массив для последующей обработки
            allElements.push({
              element: element,
              selector: selector,
              blockType: blockType,
              html: html
            });
          } else if (html) {
            console.log(`⏭️ Пропущен элемент по селектору "${selector}" (элемент ${i + 1}), слишком короткий: ${html.length} символов`);
          }
        });
      } catch (e) {
        console.warn(`⚠️ Проблема с селектором "${selector}":`, e.message);
      }
    });

    // Теперь обрабатываем все найденные элементы
    allElements.forEach(item => {
      // Добавляем весь элемент вместе с его обрамляющим тегом с сохранением оригинальных кавычек
      // Декодируем HTML-сущности в атрибутах
      const elementHtml = decodeHtmlEntities($clean.html(item.element));
      blocks.push(elementHtml);

      // Сохраняем информацию о типе блока
      blockTypeInfo.push(item.blockType);
    });

    // Добавляем оставшуюся часть, если она достаточно большая (только для варианта 1)
    if (splitOption === 1) {
      const remainingBody = $clean('body').html();
      if (remainingBody && remainingBody.length > 100) {
        console.log(`✅ Добавляем оставшуюся часть body, длина: ${remainingBody.length} символов`);
        // Не оборачиваем в дополнительный div, используем содержимое как есть
        const remainingHtml = decodeHtmlEntities(remainingBody);
        blocks.push(remainingHtml);
        // Для оставшейся части body используем тип 'Контент'
        blockTypeInfo.push('Контент');
        console.log(`🏷️ Тип блока для оставшейся части: Контент`);
      } else if (remainingBody) {
        console.log(`⏭️ Оставшаяся часть body слишком короткая: ${remainingBody ? remainingBody.length : 0} символов`);
      }
    }

    // Если не нашли ни одного блока, возвращаем весь HTML как один блок
    if (blocks.length === 0 && html.length > 100) {
      console.log('⚠️ Не найдено блоков по селекторам, возвращаем весь HTML как один блок');
      // Используем оригинальный HTML с сохранением комментариев и кавычек
      // Декодируем HTML-сущности в атрибутах
      const fixedHtml = decodeHtmlEntities(cleanedHtml);
      blocks.push(fixedHtml);
      // Для всего HTML как одного блока используем тип 'Контент'
      blockTypeInfo.push('Контент');
      console.log(`🏷️ Тип блока для всего HTML: Контент`);
    }

    console.log(`🏁 Итоговое количество найденных блоков: ${blocks.length}`);
    // Возвращаем объект с блоками, информацией о типах и извлеченным CSS/JS
    return {
      blocks,
      blockTypeInfo,
      cssContent,
      jsContent
    };

  } catch (error) {
    console.error('❌ Ошибка при разделении HTML на блоки:', error);
    // В случае ошибки просто возвращаем весь HTML как один блок с типом 'Контент'
    if (html.length > 100) {
      // Создаем новый экземпляр cheerio с настройками для сохранения оригинальных кавычек
      const $error = load(html, {
        decodeEntities: false,
        xmlMode: false,
        _useHtmlParser2: true
      });
      // Декодируем HTML-сущности в атрибутах
      const errorHtml = decodeHtmlEntities($error.html());
      return {
        blocks: [errorHtml],
        blockTypeInfo: ['Контент'],
        cssContent: '',
        jsContent: ''
      };
    } else {
      return {
        blocks: [],
        blockTypeInfo: [],
        cssContent: '',
        jsContent: ''
      };
    }
  }
}

// Функция для очистки HTML
function cleanHtml(html) {
  // Если html не определен или пуст
  if (!html) {
    console.warn('⚠️ Входной HTML пустой или не определен');
    return '';
  }

  // Удаляем DOCTYPE, если есть
  const withoutDoctype = html.replace(/<!DOCTYPE[^>]*>/i, '');

  // Проверяем наличие тегов html, head и body
  if (!withoutDoctype.includes('<html') && !withoutDoctype.includes('<body')) {
    console.log('🔧 HTML не содержит тегов html или body, оборачиваем в базовую структуру');
    // Если нет html или body, оборачиваем контент
    return `<html><head><title>Generated</title></head><body>${withoutDoctype}</body></html>`;
  }

  return withoutDoctype;
}

// Используем функцию decodeHtmlEntities из модуля htmlToTemplate.js

// Функция для очистки HTML от внешних тегов html, head и body перед конвертацией в Pug
function cleanHtmlForPug(html) {
  if (!html) return { html: '', placeholders: new Map() };

  console.log('🧹 Очистка HTML от внешних тегов для конвертации в Pug...');

  try {
    // Создаем карту для хранения всех сложных атрибутов
    const attributePlaceholders = new Map();
    let placeholderIndex = 0;

    // Функция для создания уникального токена
    const createPlaceholder = (content, prefix = 'JSON') => {
      const token = `__${prefix}_PLACEHOLDER_${placeholderIndex++}__`;
      attributePlaceholders.set(token, content);
      return token;
    };

    // Предварительная обработка проблемных атрибутов с JSON-данными
    let processedHtml = html;

    // Улучшенное регулярное выражение для более точного поиска атрибутов
    // Это выражение лучше обрабатывает вложенные кавычки и сложные структуры
    const attrRegex = /(\S+)=(['"])((?:(?!\2)[^\\]|\\.)*)\2/gs;

    // Обрабатываем HTML, заменяя сложные атрибуты на плейсхолдеры
    processedHtml = processedHtml.replace(attrRegex, (match, attrName, quote, attrValue) => {
      // Пропускаем, если атрибут уже был обработан
      if (attrValue.includes('__') && attrValue.includes('_PLACEHOLDER_')) {
        return match;
      }

      // Проверяем, является ли значение атрибута JSON-объектом
      // Более надежная проверка на JSON структуру
      if ((attrValue.includes('{') && attrValue.includes('}')) ||
        (attrValue.includes('[') && attrValue.includes(']'))) {
        try {
          // Пробуем распарсить как JSON для проверки
          JSON.parse(attrValue);
          console.log(`🔍 Обнаружен валидный JSON атрибут: ${attrName}`);
        } catch (e) {
          // Если не валидный JSON, но содержит фигурные скобки, все равно обрабатываем
          console.log(`🔍 Обнаружен атрибут с JSON-подобной структурой: ${attrName}`);
        }
        const token = createPlaceholder(attrValue, 'JSON');
        return `${attrName}=${quote}${token}${quote}`;
      }

      // Проверяем, содержит ли значение атрибута кавычки
      if (attrValue.includes('\'') || attrValue.includes('"')) {
        console.log(`🔍 Обнаружен атрибут с кавычками: ${attrName}`);
        const token = createPlaceholder(attrValue, 'QUOTE');
        return `${attrName}=${quote}${token}${quote}`;
      }

      // Проверяем, содержит ли значение атрибута HTML-теги
      if (attrValue.includes('<') && attrValue.includes('>')) {
        console.log(`🔍 Обнаружен атрибут с HTML: ${attrName}`);
        const token = createPlaceholder(attrValue, 'HTML');
        return `${attrName}=${quote}${token}${quote}`;
      }

      // Проверяем на наличие специальных символов, которые могут вызвать проблемы при конвертации
      if (/[\(\)\{\}\[\]\:\;\,\/\\]/.test(attrValue)) {
        console.log(`🔍 Обнаружен атрибут со специальными символами: ${attrName}`);
        const token = createPlaceholder(attrValue, 'SPECIAL');
        return `${attrName}=${quote}${token}${quote}`;
      }

      return match;
    });

    // Обработка data-* атрибутов, которые часто содержат сложные данные
    processedHtml = processedHtml.replace(/data-[\w-]+=(['"])(.*?)\1/gs, (match, quote, value) => {
      // Пропускаем, если значение уже является плейсхолдером
      if (value.includes('__') && value.includes('_PLACEHOLDER_')) {
        return match;
      }

      // Специальная обработка для data-slider-options и подобных атрибутов с JSON
      if (match.includes('data-slider-options') || match.includes('data-options')) {
        const attrName = match.split('=')[0];
        console.log(`🔍 Обнаружен важный data-атрибут с JSON: ${attrName}`);
        const token = createPlaceholder(value, 'JSON_DATA');
        return `${attrName}=${quote}${token}${quote}`;
      }

      // Если значение содержит сложные структуры, заменяем его на плейсхолдер
      if (/[\{\}\[\]\"\'\/\\]/.test(value)) {
        const attrName = match.split('=')[0];
        console.log(`🔍 Обнаружен data-атрибут со сложным значением: ${attrName}`);
        const token = createPlaceholder(value, 'DATA');
        return `${attrName}=${quote}${token}${quote}`;
      }

      return match;
    });

    const $ = load(processedHtml, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    });

    // Если есть тег body, извлекаем его содержимое
    let cleanedHtml;
    if ($('body').length) {
      console.log('✅ Найден тег body, извлекаем его содержимое');
      cleanedHtml = $('body').html() || processedHtml;
    } else {
      // Если нет тег body, используем исходный HTML
      console.log('ℹ️ Тег body не найден, используем исходный HTML');
      cleanedHtml = processedHtml;
    }

    // Экранируем проблемные символы в атрибутах, которые не были заменены на плейсхолдеры
    cleanedHtml = cleanedHtml
      // Заменяем неэкранированные кавычки внутри атрибутов
      .replace(/(\S+)=(['"])(.*?)\2/gs, (match, attrName, quote, attrValue) => {
        // Пропускаем плейсхолдеры
        if (attrValue.includes('__') && attrValue.includes('_PLACEHOLDER_')) {
          return match;
        }
        // Экранируем кавычки и другие проблемные символы внутри значения атрибута
        const escapedValue = attrValue
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&apos;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;');
        return `${attrName}=${quote}${escapedValue}${quote}`;
      });

    console.log(`✅ Подготовлено ${attributePlaceholders.size} сложных атрибутов для конвертации в Pug`);

    return {
      html: cleanedHtml,
      placeholders: attributePlaceholders
    };
  } catch (error) {
    console.error('❌ Ошибка при очистке HTML для Pug:', error.message);
    return {
      html: html,
      placeholders: new Map()
    };
  }
}

// Используем функцию htmlToHandlebarsAndJson из модуля htmlToTemplate.js





