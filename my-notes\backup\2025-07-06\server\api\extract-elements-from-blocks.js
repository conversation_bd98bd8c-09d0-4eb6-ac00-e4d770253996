// server/api/extract-elements-from-blocks.js
import { defineEventHandler, readBody, createError } from 'h3';
import { load } from 'cheerio';
import { generateTreeStructure } from '../utils/htmlAnalyzer';
import { htmlToHandlebarsAndJson } from '../utils/htmlToTemplate.js';
import { getElementTypes } from '../utils/elementTypeMapping';

export default defineEventHandler(async (event) => {
  try {
    const { records } = await readBody(event);

    console.log('Получены записи блоков для извлечения элементов:', records.length);

    // Результаты обработки
    const results = [];
    let processedBlocks = 0;
    let totalElements = 0;

    // Для каждого блока
    for (const block of records) {
      try {
        console.log(`🔍 Обработка блока: ${block.title} (ID: ${block.id})`);

        console.log(`📋 Информация о блоке: title=${block.title}, number=${block.number}`);
        
        // 2. Проверяем наличие HTML
        if (!block.html || block.html.trim().length < 20) {
          console.warn('⚠️ HTML блока пустой или слишком короткий');
          results.push({
            blockId: block.id,
            blockTitle: block.title,
            success: true,
            elementsExtracted: 0,
            elementsAdded: 0,
            message: 'HTML блока пустой или слишком короткий'
          });
          continue;
        }

        // 3. Извлекаем элементы из HTML блока
        console.log('✂️ Извлечение элементов из HTML блока...');
        const elements = extractElementsFromHtml(block.html);
        console.log(`✅ HTML обработан, найдено ${elements.length} элементов`);

        if (elements.length === 0) {
          console.warn('⚠️ Не найдено элементов для извлечения в HTML блока');
          results.push({
            blockId: block.id,
            blockTitle: block.title,
            success: true,
            elementsExtracted: 0,
            elementsAdded: 0,
            message: 'Не найдено элементов для извлечения'
          });
          continue;
        }
        
        // 4. Подготавливаем элементы для создания
        const elementsToCreate = [];
        
        // Счетчики для уникальных названий
        const typeCounters = {};
        const titleCounters = {};
        
        // 5. Обрабатываем каждый элемент
        for (let i = 0; i < elements.length; i++) {
          const element = elements[i];
          
          // Пропускаем пустые или слишком маленькие элементы
          if (!element.html.trim() || element.html.length < 10) {
            console.log(`⏭️ Пропускаем элемент ${i + 1}: слишком маленький (${element.html.length} символов)`);
            continue;
          }
          
          console.log(`🔎 Анализ элемента ${i + 1} типов "${element.types.join(', ')}"...`);

          // Генерируем структуру DOM дерева для поля composition
          const treeStructure = generateTreeStructure(element.html);
          
          // Используем первый тип для нумерации (основной тип)
          const primaryType = element.types[0];

          // Инициализируем счетчики для типа элемента
          if (!typeCounters[primaryType]) {
            typeCounters[primaryType] = 0;
          }
          typeCounters[primaryType]++;

          // Создаем базовое название на основе блока
          const baseTitle = `${block.title} ${primaryType}`;

          // Инициализируем счетчик для названия
          if (!titleCounters[baseTitle]) {
            titleCounters[baseTitle] = 0;
          }
          titleCounters[baseTitle]++;

          // Форматируем порядковые номера как двузначные числа
          const typeNumber = String(typeCounters[primaryType]).padStart(2, '0');
          const instanceNumber = String(titleCounters[baseTitle]).padStart(2, '0');

          // Создаем уникальные title и number на основе блока
          const elementTitle = titleCounters[baseTitle] > 1
            ? `${baseTitle} ${instanceNumber}`
            : baseTitle;

          const elementNumber = titleCounters[baseTitle] > 1
            ? `${block.number}-${typeNumber}-${instanceNumber}`
            : `${block.number}-${typeNumber}`;
          
          // Создаем новый элемент для welem_proto
          const newElement = {
            title: elementTitle,
            number: elementNumber,
            status: 'idea', // По умолчанию "Идея"
            html: element.html, // HTML уже декодирован из записи блока
            composition: treeStructure || '', // Структура DOM дерева
            elem_type: element.types, // Все типы элемента (массив для поля типа "теги")
            date_updated: new Date().toISOString(),
            // CSS и JS берем из полей блока (уже готовые)
            css: block.css || '',
            js: block.js || '',
            // Collection берем из поля блока
            collection: block.collection || [],
            tags: block.collection || [],
            // Связываем с исходным блоком
            source_block: block.id
          };
          
          // Пробуем сконвертировать HTML в handlebars и JSON
          try {
            console.log(`🔄 Генерация handlebars и JSON для элемента ${elementTitle}...`);
            const result = htmlToHandlebarsAndJson(element.html, elementTitle, elementNumber);
            
            if (result.success) {
              newElement.hbs = result.hbsTemplate || '';
              newElement.json = JSON.stringify(result.jsonData || {}, null, 2);
              console.log(`✅ Успешно сгенерирован шаблон и JSON для элемента ${elementTitle}`);
            } else {
              console.warn(`⚠️ Проблема при генерации шаблона для элемента ${elementTitle}: ${result.error || 'Неизвестная ошибка'}`);
            }
          } catch (convErr) {
            console.error(`❌ Ошибка при конвертации элемента ${elementTitle}:`, convErr);
          }
          
          console.log(`🏷️ Установлена коллекция: ${newElement.collection.join(', ') || 'не указана'}`);
          console.log(`📝 Подготовлен элемент для создания: ${newElement.title}`);
          
          // Добавляем элемент в список для возврата клиенту
          elementsToCreate.push(newElement);
          totalElements++;
        }
        
        // Возвращаем элементы клиенту
        results.push({
          blockId: block.id,
          blockTitle: block.title,
          success: true,
          elementsExtracted: elements.length,
          elementsToCreate: elementsToCreate,
        });

        processedBlocks++;

      } catch (blockError) {
        console.error(`❌ Ошибка при обработке блока ${block.id}:`, blockError);
        results.push({
          blockId: block.id,
          success: false,
          error: blockError.message
        });
      }
    }
    
    console.log(`🏁 Итоговый результат: обработано ${processedBlocks} блоков, извлечено ${totalElements} элементов`);
    
    return {
      success: true,
      processedBlocks,
      results
    };
    
  } catch (error) {
    console.error('❌ Общая ошибка при извлечении элементов из блоков:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message
    });
  }
});

// Функция для извлечения элементов из HTML блока
function extractElementsFromHtml(html) {
  console.log('🔪 Начало извлечения элементов из HTML блока...');

  if (!html || html.length < 20) {
    console.warn('⚠️ HTML пустой или слишком короткий для извлечения элементов');
    return [];
  }

  try {
    // Настраиваем cheerio для сохранения оригинальных кавычек в атрибутах
    const $ = load(html, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    });

    // Массив для хранения найденных элементов
    const elements = [];

    console.log('🔍 Поиск элементов по типам...');

    // Извлекаем все элементы, которые могут быть интересными
    const allElements = $('*').toArray();
    console.log(`🔍 Найдено ${allElements.length} элементов для анализа`);

    // Обрабатываем каждый элемент
    allElements.forEach((element, index) => {
      try {
        const $element = $(element);
        const elementHtml = $.html(element);

        // Пропускаем слишком маленькие элементы
        if (!elementHtml || elementHtml.length < 20) {
          return;
        }

        // Получаем информацию об элементе
        const tagName = element.name.toLowerCase();
        const classes = $element.attr('class') || '';

        // Определяем типы элемента (может быть несколько)
        const elementTypes = getElementTypes($element, $);

        // Пропускаем элементы, для которых не найдено ни одного типа
        if (elementTypes.length === 0) {
          return;
        }

        // Проверяем, не добавляли ли мы уже этот элемент (точное совпадение HTML)
        const isDuplicate = elements.some(existingElement =>
          existingElement.html === elementHtml
        );

        // НЕ проверяем вложенность - мы хотим извлекать как составные элементы,
        // так и их вложенные примитивы как отдельные записи
        if (!isDuplicate) {
          console.log(`✅ Найден элемент типов "${elementTypes.join(', ')}" (${tagName}${classes ? '.' + classes.replace(/\s+/g, '.') : ''})`);

          elements.push({
            html: elementHtml,
            types: elementTypes, // Сохраняем все найденные типы
            selector: `${tagName}${classes ? '.' + classes.replace(/\s+/g, '.') : ''}`
          });
        }
      } catch (e) {
        console.warn(`⚠️ Проблема при обработке элемента ${index}:`, e.message);
      }
    });

    console.log(`🏁 Итоговое количество найденных элементов: ${elements.length}`);

    return elements;

  } catch (error) {
    console.error('❌ Ошибка при извлечении элементов:', error);
    return [];
  }
}
