<script setup lang="ts">
	const { name } = defineProps({
		name: {
			type: String,
			default: 'Wblock',
		},
	})

	// const emit = defineEmits(['addToCart'])

	// // Use the addToCart function like this:
	// // Eg-1: addToCart('pizza', 10)
	// // Eg-2: (event) => addToCart('pizza', 10, event)
	// const addToCart = (item, quantity /*, event*/) => {
	// 	emit('addToCart', { item, quantity })
	// }
</script> 
<template>
    
</template>
<style scoped></style>
