// import { defineEventHandler } from 'h3';
// import pug from 'pug';

// export default defineEventHandler(async (event) => {
//   console.log('event.req.body:', event.req.body);
//   const body = event.req.body; // Получаем данные из запроса
//   const { template, data } = body; 

//   try {
//     const compiledTemplate = pug.compile(template); 
//     const renderedHtml = compiledTemplate(data);
//     return { html: renderedHtml };
//   } catch (error) {
//     console.error('❌ Ошибка при рендеринге Pug:', error);
//     return { error: error.message };
//   }
// });

// этот работает
import { defineEventHandler } from 'h3';
import pug from 'pug';


export default defineEventHandler(async (event) => {
  try {    
    const body = event.req.body;
    const { template, data } = body; 
    const compiledTemplate = pug.compile(template); 
    const renderedHtml = compiledTemplate(data);    
    return { html: renderedHtml };
  } catch (error) {    
    return { error: error.message };
  }
});
