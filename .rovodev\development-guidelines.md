# Руководство по разработке

## Общие принципы

### 1. Подход к разработке
- **Анализ перед действием**: Всегда изучайте существующий код и архитектуру перед внесением изменений
- **Инкрементальные изменения**: Вносите изменения небольшими, проверяемыми шагами
- **Переиспользование кода**: Ищите возможности использовать существующие компоненты и утилиты
- **Следование паттернам**: Придерживайтесь установленных в проекте архитектурных решений

### 2. Стандарты кодирования
- **Язык**: TypeScript с частичной типизацией (strict mode отключен)
- **Стиль**: Composition API с `<script setup>`
- **Форматирование**: ESLint + Prettier
- **Комментарии**: На русском языке для документации, английские названия переменных

## Структура компонентов

### Базовый шаблон Vue компонента

```vue
<template>
  <div class="component-wrapper">
    <!-- Основной контент -->
    <div class="main-content">
      <!-- Toolbar -->
      <div class="flex justify-between mb-1 p-1">
        <div class="flex gap-2">
          <!-- Поиск и фильтры -->
          <InputText
            v-model="globalFilterValue"
            placeholder="Поиск..."
            class="w-full text-xs"
            @input="onGlobalFilterChange"
          />
          <MultiSelect
            v-model="selectedTags"
            :options="availableTags"
            placeholder="Фильтр по тегам"
            display="chip"
            class="w-64 text-xs"
          />
        </div>
        <div class="flex gap-2">
          <!-- Действия -->
          <Button
            v-tooltip.bottom="'Создать'"
            icon="pi pi-plus"
            class="p-button-success text-xs"
            @click="openCreateDialog"
          />
        </div>
      </div>

      <!-- Основное содержимое -->
      <DataTable
        v-model:selection="selectedItems"
        :value="filteredItems"
        :loading="loading"
        selection-mode="multiple"
        data-key="id"
        class="p-datatable-sm"
      >
        <!-- Колонки таблицы -->
      </DataTable>
    </div>
  </div>
</template>

<script setup lang="ts">
// 1. Импорты
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useDirectusItems } from '#directus/composables'

// 2. Типы и интерфейсы
interface ComponentProps {
  // Определение props
}

interface ComponentEmits {
  // Определение emits
}

// 3. Props и emits
const props = defineProps<ComponentProps>()
const emit = defineEmits<ComponentEmits>()

// 4. Composables
const toast = useToast()
const { getItems, createItems } = useDirectusItems('collection_name')

// 5. Реактивные переменные
const loading = ref(false)
const selectedItems = ref([])
const globalFilterValue = ref('')
const items = ref([])

// 6. Вычисляемые свойства
const filteredItems = computed(() => {
  if (!globalFilterValue.value) return items.value
  
  return items.value.filter(item => 
    item.title?.toLowerCase().includes(globalFilterValue.value.toLowerCase())
  )
})

// 7. Методы
const loadData = async () => {
  loading.value = true
  try {
    const data = await getItems()
    items.value = data || []
  } catch (error) {
    console.error('Ошибка загрузки данных:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить данные'
    })
  } finally {
    loading.value = false
  }
}

const openCreateDialog = () => {
  // Логика открытия диалога
}

// 8. Lifecycle hooks
onMounted(() => {
  loadData()
})

// 9. Watchers (если необходимо)
// watch(someRef, (newVal) => {
//   // Логика наблюдения
// })
</script>

<style scoped>
/* Локальные стили */
.component-wrapper {
  /* Стили компонента */
}
</style>
```

## Работа с данными

### 1. Directus интеграция

```typescript
// Базовый паттерн работы с Directus
const { getItems, createItems, updateItem, deleteItem } = useDirectusItems('collection_name')

// Загрузка с фильтрами
const loadFilteredData = async (filters: any = {}) => {
  try {
    const data = await getItems({
      fields: ['*'],
      filter: filters,
      sort: ['number', 'title'],
      limit: 100
    })
    return data
  } catch (error) {
    console.error('Ошибка загрузки:', error)
    throw error
  }
}

// Создание элемента
const createItem = async (itemData: any) => {
  try {
    const newItem = await createItems([itemData])
    toast.add({
      severity: 'success',
      summary: 'Успех',
      detail: 'Элемент создан'
    })
    return newItem[0]
  } catch (error) {
    console.error('Ошибка создания:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось создать элемент'
    })
    throw error
  }
}
```

### 2. Серверные API вызовы

```typescript
// Универсальная функция для API вызовов
const callApi = async <T>(endpoint: string, data: any): Promise<T> => {
  try {
    const response = await $fetch<T>(`/api/${endpoint}`, {
      method: 'POST',
      body: data
    })
    return response
  } catch (error) {
    console.error(`Ошибка API ${endpoint}:`, error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка API',
      detail: error.message || 'Произошла ошибка'
    })
    throw error
  }
}

// Специфичные API функции
const generateScreenshot = async (html: string, options: any = {}) => {
  return callApi('capture-html-screenshot', { html, options })
}

const analyzeHtml = async (html: string) => {
  return callApi('analyze-html', { html })
}
```

## Стилизация и UI

### 1. Классы TailwindCSS

```vue
<template>
  <!-- Основные контейнеры -->
  <div class="flex h-screen">
    <div class="flex-1 overflow-hidden flex flex-col">
      
      <!-- Toolbar -->
      <div class="flex justify-between mb-1 p-1">
        <!-- Содержимое toolbar -->
      </div>
      
      <!-- Основной контент -->
      <div class="flex-1 overflow-auto">
        <!-- DataTable или другой контент -->
      </div>
    </div>
    
    <!-- Боковая панель -->
    <div v-if="sidebarVisible" class="w-[30rem] border-l border-surface-200">
      <!-- Содержимое сайдбара -->
    </div>
  </div>
</template>
```

### 2. PrimeVue компоненты

```vue
<template>
  <!-- Стандартные размеры и стили -->
  <Button
    v-tooltip.bottom="'Описание действия'"
    icon="pi pi-plus"
    class="p-button-success text-xs"
    @click="handleAction"
  />
  
  <InputText
    v-model="searchValue"
    placeholder="Поиск..."
    class="w-full text-xs"
    style="font-size: 12px"
  />
  
  <MultiSelect
    v-model="selectedItems"
    :options="availableOptions"
    display="chip"
    class="text-xs w-64"
    panel-class="text-xs"
    :pt="{
      item: { class: 'text-xs' },
      header: { class: 'text-xs' }
    }"
  />
  
  <DataTable
    v-model:selection="selectedItems"
    :value="items"
    :loading="loading"
    selection-mode="multiple"
    data-key="id"
    striped-rows
    class="p-datatable-sm text-[13px]"
    style="--highlight-bg: var(--primary-50); font-size: 11px"
  >
    <!-- Колонки -->
  </DataTable>
</template>
```

## Обработка ошибок

### 1. Клиентская обработка

```typescript
// Централизованная обработка ошибок
const handleError = (error: any, context: string) => {
  console.error(`[${context}] Ошибка:`, error)
  
  let message = 'Произошла неизвестная ошибка'
  
  if (error.response?.status === 404) {
    message = 'Ресурс не найден'
  } else if (error.response?.status === 403) {
    message = 'Недостаточно прав доступа'
  } else if (error.response?.status >= 500) {
    message = 'Ошибка сервера'
  } else if (error.message) {
    message = error.message
  }
  
  toast.add({
    severity: 'error',
    summary: 'Ошибка',
    detail: message,
    life: 5000
  })
}

// Использование в async функциях
const performAction = async () => {
  try {
    loading.value = true
    const result = await someApiCall()
    // Обработка успешного результата
  } catch (error) {
    handleError(error, 'performAction')
  } finally {
    loading.value = false
  }
}
```

### 2. Серверная обработка

```typescript
// server/api/example.post.ts
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    
    // Валидация
    if (!body.requiredField) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Обязательное поле отсутствует'
      })
    }
    
    // Основная логика
    const result = await processData(body)
    
    return {
      success: true,
      data: result
    }
    
  } catch (error) {
    console.error('API Error:', error)
    
    if (error.statusCode) {
      throw error // Перебрасываем уже обработанную ошибку
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Внутренняя ошибка сервера'
    })
  }
})
```

## Тестирование и отладка

### 1. Логирование

```typescript
// Структурированное логирование
const logger = {
  info: (message: string, data?: any) => {
    console.log(`[INFO] ${message}`, data || '')
  },
  
  error: (message: string, error?: any) => {
    console.error(`[ERROR] ${message}`, error || '')
  },
  
  debug: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, data || '')
    }
  }
}

// Использование
logger.info('Загрузка данных начата')
logger.error('Ошибка при загрузке', error)
logger.debug('Состояние компонента', { loading: loading.value, items: items.value.length })
```

### 2. Отладочные инструменты

```typescript
// Отладочные функции для development режима
const debugTools = {
  // Вывод состояния компонента
  logState: () => {
    if (process.env.NODE_ENV === 'development') {
      console.table({
        loading: loading.value,
        selectedItems: selectedItems.value.length,
        filteredItems: filteredItems.value.length,
        globalFilter: globalFilterValue.value
      })
    }
  },
  
  // Симуляция задержки для тестирования
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Генерация тестовых данных
  generateTestData: (count: number) => {
    return Array.from({ length: count }, (_, i) => ({
      id: i + 1,
      title: `Тестовый элемент ${i + 1}`,
      description: `Описание для элемента ${i + 1}`
    }))
  }
}
```

## Производительность

### 1. Оптимизация компонентов

```typescript
// Debounced поиск
import { debounce } from 'lodash-es'

const debouncedSearch = debounce((searchTerm: string) => {
  performSearch(searchTerm)
}, 300)

// Виртуализация для больших списков
const virtualScrollerOptions = {
  itemSize: 44,
  showLoader: true,
  loading: loading.value,
  lazy: true
}

// Ленивая загрузка компонентов
const LazyComponent = defineAsyncComponent(() => import('./HeavyComponent.vue'))
```

### 2. Управление памятью

```typescript
// Очистка ресурсов
onUnmounted(() => {
  // Отмена подписок
  if (subscription) {
    subscription.unsubscribe()
  }
  
  // Очистка таймеров
  if (timer) {
    clearInterval(timer)
  }
  
  // Очистка event listeners
  window.removeEventListener('resize', handleResize)
})

// Использование shallowRef для больших объектов
const largeDataSet = shallowRef([])
```

## Безопасность

### 1. Валидация данных

```typescript
// Валидация на клиенте
const validateFormData = (data: any) => {
  const errors: string[] = []
  
  if (!data.title?.trim()) {
    errors.push('Название обязательно')
  }
  
  if (data.title?.length > 255) {
    errors.push('Название слишком длинное')
  }
  
  if (data.email && !isValidEmail(data.email)) {
    errors.push('Некорректный email')
  }
  
  return errors
}

// Санитизация HTML
const sanitizeHtml = (html: string) => {
  // Используйте библиотеку типа DOMPurify
  return html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
}
```

### 2. Безопасные API вызовы

```typescript
// Ограничение размера данных
const MAX_UPLOAD_SIZE = 10 * 1024 * 1024 // 10MB

const validateFileSize = (file: File) => {
  if (file.size > MAX_UPLOAD_SIZE) {
    throw new Error('Файл слишком большой')
  }
}

// Таймауты для API вызовов
const apiCallWithTimeout = async (endpoint: string, data: any, timeout = 30000) => {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)
  
  try {
    const response = await $fetch(endpoint, {
      method: 'POST',
      body: data,
      signal: controller.signal
    })
    return response
  } finally {
    clearTimeout(timeoutId)
  }
}
```

## Документация кода

### 1. JSDoc комментарии

```typescript
/**
 * Загружает данные из Directus с применением фильтров
 * @param filters - Объект фильтров для запроса
 * @param options - Дополнительные опции запроса
 * @returns Promise с массивом элементов
 */
const loadData = async (
  filters: Record<string, any> = {},
  options: { limit?: number; sort?: string[] } = {}
): Promise<any[]> => {
  // Реализация
}

/**
 * Компонент для управления файлами с возможностями массового редактирования
 * @example
 * <FileManager
 *   :collection="'directus_files'"
 *   :filters="{ type: 'image' }"
 *   @item-selected="handleSelection"
 * />
 */
```

### 2. README для компонентов

```markdown
# ComponentName

## Описание
Краткое описание назначения компонента.

## Props
| Название | Тип | По умолчанию | Описание |
|----------|-----|--------------|----------|
| items | Array | [] | Массив элементов для отображения |
| loading | Boolean | false | Состояние загрузки |

## Events
| Название | Параметры | Описание |
|----------|-----------|----------|
| item-selected | item: Object | Срабатывает при выборе элемента |

## Slots
| Название | Описание |
|----------|----------|
| header | Заголовок компонента |
| footer | Подвал компонента |

## Использование
```vue
<ComponentName
  :items="items"
  :loading="loading"
  @item-selected="handleSelection"
>
  <template #header>
    <h2>Заголовок</h2>
  </template>
</ComponentName>
```

Это руководство поможет поддерживать единообразие и качество кода в проекте.