# Архитектура компонентов проекта

## Иерархия компонентов

### Страницы верхнего уровня (Pages)
```
app/pages/
├── files2.vue              # Управление файлами
├── wpages.vue              # Управление страницами  
├── wblock-proto2.vue       # Прототипы блоков
├── welem-proto.vue         # Прототипы элементов
├── wjson.vue               # JSON данные
├── graphics.vue            # Генератор графики
├── wblock-html-gen.vue     # HTML генератор блоков
├── wblock-page-gen.vue     # Генератор страниц
├── wblock-gen2.vue         # Расширенный генератор блоков
└── welem-gen2.vue          # Расширенный генератор элементов
```

### Универсальные компоненты (Shared)
```
app/components/
├── UniversalDataTable.vue       # Универсальная таблица
├── BulkFormContainer.vue        # Контейнер массовых форм
├── ContentLibraryConstructor.vue # Конструктор контента
├── UniversalFilterPanel.vue     # Панель фильтров
├── UniversalSidebarForm.vue     # Боковая форма
├── UniversalItemActions.vue     # Действия с элементами
└── UniversalFullTextDialog.vue  # Полнотекстовый диалог
```

### Специализированные компоненты
```
app/components/
├── wblock-html-gen/
│   ├── ElementsPanel.vue        # Панель элементов
│   ├── Preview.vue              # Предпросмотр
│   ├── ElementEditForm.vue      # Форма редактирования элемента
│   └── StructurePanel.vue       # Панель структуры
├── wblock-page-gen/
│   ├── BlockForm.vue            # Форма блока
│   └── JsonEditForm.vue         # Редактор JSON
├── wblock-gen/
│   └── BlockCombinationPreview.vue # Предпросмотр комбинаций
└── welem-gen/
    ├── CombinationPreview.vue   # Предпросмотр комбинаций элементов
    ├── ContentConstructor.vue   # Конструктор контента
    ├── ThemeConstructor.vue     # Конструктор тем
    └── VariationConstructor.vue # Конструктор вариаций
```

## Паттерны компонентов

### 1. Страничный компонент (Page Component Pattern)

**Структура:**
```vue
<template>
  <div class="flex h-screen">
    <!-- Основной контент -->
    <div class="flex-1 overflow-hidden flex flex-col" :class="{ 'pr-[30rem]': sidebarVisible }">
      
      <!-- Toolbar -->
      <div class="flex justify-between mb-1 p-1">
        <div class="flex gap-2">
          <!-- Поиск и фильтры -->
        </div>
        <div class="flex gap-2">
          <!-- Действия -->
        </div>
      </div>

      <!-- Массовые формы -->
      <BulkFormContainer
        :visible="bulkFormVisible"
        :mode="bulkFormMode"
        :collection="collection"
        :field-config="fieldConfig"
        :selected-items="selectedItems"
        @close="closeBulkForm"
        @saved="onBulkFormSaved"
      />

      <!-- Основная таблица -->
      <DataTable
        v-model:selection="selectedItems"
        :value="filteredItems"
        <!-- конфигурация таблицы -->
      />
    </div>

    <!-- Боковая панель (опционально) -->
    <div v-if="sidebarVisible" class="sidebar">
      <!-- Содержимое сайдбара -->
    </div>
  </div>
</template>

<script setup lang="ts">
// Стандартные импорты
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useDirectusItems } from '#directus/composables'

// Состояние
const loading = ref(false)
const selectedItems = ref([])
const globalFilterValue = ref('')
const bulkFormVisible = ref(false)
const bulkFormMode = ref<'create' | 'edit'>('create')

// Вычисляемые свойства
const filteredItems = computed(() => {
  // Логика фильтрации
})

// Методы
const loadData = async () => {
  // Загрузка данных
}

const openBulkCreate = () => {
  // Открытие массового создания
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
```

### 2. Универсальный компонент (Universal Component Pattern)

**Пример BulkFormContainer:**
```vue
<template>
  <div v-if="visible" class="bulk-form-container">
    <!-- Плавающий заголовок -->
    <div class="floating-header">
      <span class="header-text">
        {{ mode === 'create' ? 'Массовое добавление' : 'Массовое редактирование' }}
      </span>
      <div class="header-buttons">
        <!-- Кнопки управления -->
      </div>
    </div>

    <!-- Контейнер форм -->
    <div class="bulk-forms-scroll">
      <div class="bulk-forms-container">
        <div v-for="(form, index) in forms" :key="form.id || index" class="bulk-form-card">
          <!-- Содержимое формы -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  mode: 'create' | 'edit'
  collection: string
  fieldConfig: BulkFormField[]
  selectedItems: any[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  saved: [items: any[]]
}>()

// Логика компонента
</script>
```

### 3. Генераторный компонент (Generator Component Pattern)

**Структура для генераторов (wblock-gen2, welem-gen2):**
```vue
<template>
  <div class="flex flex-col gap-4 p-1">
    <!-- Заголовок и режимы работы -->
    <div class="flex items-center justify-between p-3 bg-gradient-to-r">
      <div>
        <h1 class="text-xl font-bold">Название генератора</h1>
        <p class="text-sm text-gray-600">Описание возможностей</p>
      </div>
      <div class="flex gap-2">
        <Button
          v-for="mode in workModes"
          :key="mode.value"
          :label="mode.label"
          :class="[
            'text-xs px-3 py-2',
            currentMode === mode.value ? 'p-button-info' : 'p-button-outlined'
          ]"
          @click="setWorkMode(mode.value)"
        />
      </div>
    </div>

    <!-- Toolbar -->
    <div class="flex items-center gap-2 p-2 bg-surface-0 border rounded-lg">
      <!-- Поля ввода и кнопки действий -->
    </div>

    <!-- Панели режимов работы -->
    <div v-if="currentMode === 'combination'" class="p-3 bg-surface-0 border rounded-lg">
      <!-- Содержимое режима комбинаций -->
    </div>

    <div v-else-if="currentMode === 'content'" class="p-3 bg-surface-0 border rounded-lg">
      <!-- Содержимое режима контента -->
    </div>

    <!-- Стандартные группы (для базового режима) -->
    <div v-if="currentMode === 'standard'" class="flex flex-col gap-2">
      <div v-for="group in dataGroups" :key="group.id" class="flex items-center gap-2 p-2 bg-surface-0 border rounded-lg">
        <!-- Редакторы JSON/HBS -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Режимы работы
const workModes = [
  { value: 'standard', label: 'Стандартный' },
  { value: 'combination', label: 'Комбинации' },
  { value: 'content', label: 'Контент' }
]

const currentMode = ref('standard')

// Методы переключения режимов
const setWorkMode = (mode: string) => {
  currentMode.value = mode
}
</script>
```

## Композиция и переиспользование

### 1. Composables паттерн

**Общие composables:**
```typescript
// useDataManagement.ts
export function useDataManagement(collection: string) {
  const loading = ref(false)
  const items = ref([])
  const selectedItems = ref([])

  const loadData = async () => {
    loading.value = true
    try {
      const { data } = await useDirectusItems(collection)
      items.value = data.value || []
    } finally {
      loading.value = false
    }
  }

  const bulkDelete = async (itemIds: string[]) => {
    // Логика массового удаления
  }

  return {
    loading: readonly(loading),
    items: readonly(items),
    selectedItems,
    loadData,
    bulkDelete
  }
}
```

### 2. Слоты и шаблоны

**Универсальная таблица с слотами:**
```vue
<template>
  <DataTable>
    <template v-for="(column, index) in columns" :key="index" #[`body.${column.field}`]="slotProps">
      <slot 
        :name="`body.${column.field}`" 
        :data="slotProps.data" 
        :column="column"
      >
        {{ column.body ? column.body(slotProps.data) : slotProps.data[column.field] }}
      </slot>
    </template>
  </DataTable>
</template>
```

### 3. Провайдеры состояния

**Глобальное состояние через provide/inject:**
```typescript
// В родительском компоненте
provide('bulkOperations', {
  selectedItems,
  bulkDelete,
  bulkEdit
})

// В дочернем компоненте
const bulkOps = inject('bulkOperations')
```

## Специализированные паттерны

### 1. Drag & Drop компоненты

**Паттерн для wblock-gen2 (доски):**
```vue
<template>
  <div
    v-for="(boardBlock, blockIndex) in board.blocks"
    :key="boardBlock.id"
    class="board-block"
    draggable="true"
    @dragstart="onDragStart($event, boardBlock.id, boardIndex)"
    @dragover.prevent
    @drop="onDrop($event, blockIndex, boardIndex)"
  >
    <!-- Содержимое блока -->
  </div>
</template>

<script setup lang="ts">
const onDragStart = (event: DragEvent, blockId: string, boardIndex: number) => {
  event.dataTransfer?.setData('text/plain', JSON.stringify({ blockId, boardIndex }))
}

const onDrop = (event: DragEvent, targetIndex: number, targetBoardIndex: number) => {
  const data = JSON.parse(event.dataTransfer?.getData('text/plain') || '{}')
  // Логика перемещения
}
</script>
```

### 2. Многорежимные компоненты

**Паттерн переключения режимов:**
```vue
<template>
  <div class="two-screens-container" :class="{ 'single-screen': workMode === 'standard' }">
    <!-- Первый экран: Конструктор -->
    <div v-if="workMode === 'constructor'" class="screen-one h-screen">
      <ContentLibraryConstructor />
    </div>

    <!-- Второй экран: DataTable -->
    <div class="screen-two flex h-screen">
      <!-- Основной контент -->
    </div>
  </div>
</template>

<script setup lang="ts">
const workMode = ref<'standard' | 'constructor'>('standard')
</script>

<style scoped>
.two-screens-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.single-screen {
  grid-template-columns: 1fr;
}
</style>
```

### 3. Динамические панели

**Паттерн изменяемых размеров:**
```vue
<template>
  <div class="flex h-[calc(100vh-1rem)]">
    <!-- Левая панель -->
    <div class="flex" :style="{ width: leftSidebarWidth }">
      <!-- Содержимое -->
    </div>

    <!-- Дополнительная панель -->
    <div
      v-if="isElementFormVisible"
      class="flex flex-col"
      :style="{
        width: elementSidebarWidth,
        minWidth: '250px',
        maxWidth: '400px'
      }"
    >
      <!-- Содержимое -->
    </div>

    <!-- Основная область -->
    <div class="flex flex-col" :style="{ width: canvasWidth }">
      <!-- Содержимое -->
    </div>
  </div>
</template>

<script setup lang="ts">
const leftSidebarWidth = ref('400px')
const elementSidebarWidth = ref('300px')
const isElementFormVisible = ref(false)

const canvasWidth = computed(() => {
  let width = '100%'
  if (leftSidebarWidth.value) {
    width = `calc(${width} - ${leftSidebarWidth.value})`
  }
  if (isElementFormVisible.value && elementSidebarWidth.value) {
    width = `calc(${width} - ${elementSidebarWidth.value})`
  }
  return width
})
</script>
```

## Интеграционные паттерны

### 1. API интеграция

**Стандартный паттерн вызова API:**
```typescript
const callApi = async (endpoint: string, data: any) => {
  try {
    const response = await $fetch(`/api/${endpoint}`, {
      method: 'POST',
      body: data
    })
    return response
  } catch (error) {
    console.error(`Ошибка API ${endpoint}:`, error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: error.message
    })
    throw error
  }
}
```

### 2. Directus интеграция

**Паттерн работы с Directus:**
```typescript
const { getItems, createItems, updateItem, deleteItem } = useDirectusItems(collection)

const loadItems = async () => {
  try {
    const items = await getItems({
      fields: ['*'],
      filter: { status: { _eq: 'published' } },
      sort: ['number', 'title']
    })
    return items
  } catch (error) {
    console.error('Ошибка загрузки:', error)
  }
}
```

## Рекомендации по разработке

### 1. Создание нового компонента
1. Определить тип компонента (страничный, универсальный, специализированный)
2. Выбрать подходящий паттерн
3. Реализовать базовую структуру
4. Добавить типизацию
5. Интегрировать с существующими системами

### 2. Расширение существующего компонента
1. Изучить текущую архитектуру
2. Следовать установленным паттернам
3. Не нарушать существующие интерфейсы
4. Добавлять новые возможности через props/slots

### 3. Оптимизация производительности
1. Использовать виртуализацию для больших списков
2. Применять debounce для поиска
3. Кешировать вычисляемые свойства
4. Ленивая загрузка компонентов

Эта архитектура обеспечивает гибкость, переиспользование кода и масштабируемость проекта.