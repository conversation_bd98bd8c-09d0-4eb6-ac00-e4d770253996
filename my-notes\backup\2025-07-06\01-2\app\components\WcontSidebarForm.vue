<template>
  <div class="grid grid-cols-1 gap-4 p-4">
    <div class="grid grid-cols-2 gap-2">
      <div class="field">
        <label for="art" class="block mb-1">Артикул</label>
        <InputText id="art" v-model="localItem.art" class="w-full" />
      </div>
      <div class="field">
        <label for="name" class="block mb-1">Название</label>
        <InputText id="name" v-model="localItem.name" class="w-full" />
      </div>
    </div>

    <div class="field">
      <label for="description" class="block mb-1">Описание</label>
      <Textarea id="description" v-model="localItem.description" rows="3" class="w-full" />
    </div>

    <div class="field">
      <label for="title" class="block mb-1">Заголовок</label>
      <InputText id="title" v-model="localItem.title" class="w-full" />
    </div>

    <div class="field">
      <label for="subtitle" class="block mb-1">Подзаголовок</label>
      <InputText id="subtitle" v-model="localItem.subtitle" class="w-full" />
    </div>

    <div class="field">
      <label for="excerpt" class="block mb-1">Краткое описание</label>
      <Textarea id="excerpt" v-model="localItem.excerpt" rows="3" class="w-full" />
    </div>

    <div class="field">
      <label for="text" class="block mb-1">Полный текст</label>
      <Textarea id="text" v-model="localItem.text" rows="5" class="w-full" />
    </div>

    <div class="grid grid-cols-2 gap-2">
      <div class="field">
        <label for="url" class="block mb-1">URL</label>
        <InputText id="url" v-model="localItem.url" class="w-full" />
      </div>
      <div class="field">
        <label for="linkText" class="block mb-1">Текст ссылки</label>
        <InputText id="linkText" v-model="localItem.linkText" class="w-full" />
      </div>
    </div>

    <div class="grid grid-cols-2 gap-2">
      <div class="field">
        <label for="number" class="block mb-1">Номер</label>
        <InputNumber id="number" v-model="localItem.number" class="w-full" />
      </div>
      <div class="field">
        <label for="inscription" class="block mb-1">Надпись</label>
        <InputText id="inscription" v-model="localItem.inscription" class="w-full" />
      </div>
    </div>

    <div class="grid grid-cols-2 gap-2">
      <div class="field">
        <label for="icon" class="block mb-1">Иконка</label>
        <InputText id="icon" v-model="localItem.icon" class="w-full" />
      </div>
      <div class="field">
        <label for="product" class="block mb-1">Продукт</label>
        <InputText id="product" v-model="localItem.product" class="w-full" />
      </div>
    </div>

    <div class="grid grid-cols-2 gap-2">
      <div class="field">
        <label for="category" class="block mb-1">Категория</label>
        <InputText id="category" v-model="localItem.category" class="w-full" />
      </div>
      <div class="field">
        <label for="additional" class="block mb-1">Дополнительно</label>
        <InputText id="additional" v-model="localItem.additional" class="w-full" />
      </div>
    </div>

    <div class="field">
      <label for="tags" class="block mb-1">Теги</label>
      <InputChips id="tags" v-model="localItem.tags" class="w-full" />
    </div>

    <div class="grid grid-cols-2 gap-2">
      <div class="field">
        <label for="image" class="block mb-1">Основное изображение</label>
        <InputText id="image" v-model="localItem.image" class="w-full" />
        <img v-if="localItem.image" :src="localItem.image" class="mt-2 w-full h-32 object-cover rounded" />
      </div>
      <div class="field">
        <label for="imageBackground" class="block mb-1">Фоновое изображение</label>
        <InputText id="imageBackground" v-model="localItem.imageBackground" class="w-full" />
        <img v-if="localItem.imageBackground" :src="localItem.imageBackground" class="mt-2 w-full h-32 object-cover rounded" />
      </div>
    </div>

    <div class="field">
      <label class="block mb-1">Вложенные элементы</label>
      <ItemsEditor v-model="localItem.items" />
    </div>

    <div class="flex justify-end gap-2 mt-8">
      <Button 
        label="Отмена" 
        icon="pi pi-times" 
        severity="secondary" 
        @click="$emit('cancel')" 
      />
      <Button 
        :label="props.item?.id ? 'Обновить' : 'Создать'" 
        icon="pi pi-check" 
        @click="handleSubmit" 
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import InputNumber from 'primevue/inputnumber'
import InputChips from 'primevue/inputchips'
import Textarea from 'primevue/textarea'
import ItemsEditor from '~/components/ItemsEditor.vue'
import type { WcontItem } from '~/types/wconts'
import { createEmptyWcontItem } from '~/types/wconts'

const props = defineProps<{
  item?: WcontItem
}>()

const emit = defineEmits<{
  (e: 'update:item', item: WcontItem): void
  (e: 'cancel'): void
  (e: 'save', item: WcontItem): void
}>()

const localItem = ref<WcontItem>(props.item ? { ...props.item } : createEmptyWcontItem())

// Computed для отслеживания изменений
const hasChanges = computed(() => {
  if (!props.item) return false
  return Object.keys(props.item).some(key => 
    JSON.stringify(props.item[key]) !== JSON.stringify(localItem.value[key])
  )
})

watch(() => props.item, (newItem) => {
  if (newItem) {
    localItem.value = { ...newItem }
  }
}, { deep: true })

function handleSubmit() {
  console.error('🚨 SUBMIT TRIGGERED');
  
  // Проверка валидности данных
  if (!localItem.value || !localItem.value.id) {
    console.error('❌ Некорректные данные для сохранения');
    return;
  }

  try {
    // Эмит события save с полными данными
    emit('save', localItem.value);
    
    console.log('🟢 Emit save successful');
  } catch (error) {
    console.error('❌ Ошибка при эмите:', error);
  }
}
</script>

<style scoped>
.field {
  margin-bottom: 0.75rem;
}
</style>
