import { readFile, writeFile } from 'fs/promises';
import { join, resolve } from 'path';
import { existsSync } from 'fs';
import yaml from 'js-yaml'; // Импортируем библиотеку для работы с YAML

export default defineEventHandler(async (event) => {
  const notesDir = resolve(process.cwd(), 'my-notes');
  const path = event.context.params.path;
  const filePath = resolve(notesDir, path);

  // Проверка безопасности пути
  if (!filePath.startsWith(notesDir)) {
    throw createError({
      statusCode: 403,
      message: 'Access denied',
    });
  }

  if (event.method === 'GET') {
    // Проверка существования файла
    if (!existsSync(filePath)) {
      throw createError({
        statusCode: 404,
        message: 'File not found',
      });
    }

    try {
      const content = await readFile(filePath, 'utf-8');

      // Извлекаем YAML метаданные
      let yamlData = null;
      const yamlStart = content.indexOf('---\n');
      const yamlEnd = content.indexOf('\n---');

      if (yamlStart !== -1 && yamlEnd !== -1) {
        const yamlString = content.substring(yamlStart + 4, yamlEnd);
        try {
          yamlData = yaml.load(yamlString);
        } catch (error) {
          console.error('Error parsing YAML:', error);
        }
      }

      return { content, yamlData };
    } catch (error) {
      throw createError({
        statusCode: 500,
        message: 'Error reading file',
      });
    }
  }

  if (event.method === 'PUT') {
    try {
      const body = await readBody(event);
      await writeFile(filePath, body.content, 'utf-8');
      return { success: true };
    } catch (error) {
      throw createError({
        statusCode: 500,
        message: 'Error saving file',
      });
    }
  }

  throw createError({
    statusCode: 405,
    message: 'Method not allowed',
  });
});
