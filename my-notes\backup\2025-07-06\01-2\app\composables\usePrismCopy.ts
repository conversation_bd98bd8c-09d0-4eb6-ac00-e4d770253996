import { useToast } from 'primevue/usetoast'

export const usePrismCopy = () => {
  const toast = useToast()

  const copyToClipboard = async (text: string, fieldName: string = 'код') => {
    try {
      await navigator.clipboard.writeText(text)
      toast.add({
        severity: 'success',
        summary: 'Скопировано',
        detail: `Содержимое поля ${fieldName} скопировано в буфер обмена`,
        life: 2000,
      })
    } catch (error) {
      console.error('Ошибка копирования:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось скопировать в буфер обмена',
        life: 3000,
      })
    }
  }

  return {
    copyToClipboard
  }
}
