<template>
  <div class="two-screens-container" :class="{ 'single-screen': workMode === 'standard' }">
    <!-- ПЕРВЫЙ ЭКРАН: Конструктор контента (100vh) -->
    <div v-if="workMode === 'constructor'" class="screen-one h-screen">
      <ContentLibraryConstructor
        :selected-items="selectedItems"
        :forms-count="formsCount"
        @content-applied="handleContentApplied"
        @primitives-updated="handlePrimitivesUpdated"
        @apply-to-forms="handleApplyToForms"
        @forms-count-changed="handleFormsCountChanged"
      />
    </div>

    <!-- ВТОРОЙ ЭКРАН: DataTable (100vh) -->
    <div class="screen-two flex h-screen">
      <!-- Основной контент -->
      <div
        class="flex-1 overflow-hidden flex flex-col"
        :class="{ 'pr-[30rem]': sidebarVisible }"
      >
      <div class="flex justify-between mb-1 p-1">
        <div class="flex gap-2">
          <Button
            v-tooltip.top="'Обновить данные'"
            icon="pi pi-refresh"
            class="p-button-rounded p-button-text p-button-sm"
            :disabled="loading"
            aria-label="Обновить"
            @click="loadData"
          />
          <span class="p-input-icon-left">
            <InputText
              v-model="globalFilterValue"
              placeholder="Поиск..."
              icon="pi pi-search"
              class="w-full"
              style="font-size: 12px"
              @input="onGlobalFilterChange"
            />
          </span>
          <MultiSelect
            v-model="selectedTags"
            :options="availableTags"
            placeholder="Фильтр по тегам"
            display="chip"
            class="w-64 text-xs"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
            @change="applyTagsFilter"
          />
        </div>
        <div class="flex gap-2">
          <!-- Переключатель конструктора -->
          <div class="flex gap-1 mr-2">
            <Button
              v-tooltip.bottom="workMode === 'constructor' ? 'Скрыть конструктор' : 'Показать конструктор'"
              :class="['text-xs', workMode === 'constructor' ? 'p-button-info' : 'p-button-outlined']"
              :icon="workMode === 'constructor' ? 'pi pi-eye-slash' : 'pi pi-cog'"
              :label="workMode === 'constructor' ? 'Скрыть' : 'Конструктор'"
              @click="workMode = workMode === 'constructor' ? 'standard' : 'constructor'"
            />
          </div>

          <Button
            v-tooltip.bottom="'Массовое добавление'"
            icon="pi pi-plus-circle"
            class="text-xs p-button-success"
            @click="openBulkCreate"
          />
          <Button
            v-tooltip.bottom="'Массовое редактирование'"
            icon="pi pi-pencil"
            class="text-xs p-button-primary"
            :disabled="!selectedItems.length"
            @click="openBulkEdit"
          />
          <Button
            v-tooltip.bottom="'Создать'"
            icon="pi pi-plus"
            class="p-button-success text-xs"
            @click="openCreateDialog"
          />
          <Button
            v-tooltip.bottom="'Дублировать выбранные'"
            icon="pi pi-copy"
            class="p-button-primary text-xs"
            :disabled="!selectedItems.length"
            @click="bulkDuplicateItems"
          />
          <Button
            v-tooltip.bottom="'Удалить выбранные'"
            icon="pi pi-trash"
            class="p-button-danger text-xs"
            :disabled="!selectedItems.length"
            @click="bulkDeleteItems"
          />
          <ProgressBar
            v-if="loading"
            mode="indeterminate"
            style="height: 6px"
            class="mt-2"
          />
        </div>
      </div>

      <!-- Область массовых форм -->
      <BulkFormContainer
        :visible="bulkFormVisible"
        :mode="bulkFormMode"
        collection="wjson"
        :field-config="wjsonBulkFieldConfig"
        :selected-items="selectedItems"
        @close="closeBulkForm"
        @saved="onBulkFormSaved"
      />

      <!-- DataTable на всю высоту -->
      <div class="datatable-fullscreen flex-1" style="height: calc(100vh - 120px); overflow: auto;">
        <DataTable
          v-model:selection="selectedItems"
          v-model:sort-field="sortField"
          v-model:sort-order="sortOrder"
          v-model:expanded-rows="expandedRows"
          :value="filteredItems"
          selection-mode="multiple"
          scrollable
          scroll-height="calc(100vh - 70px)"
          :virtual-scroller-options="{ itemSize: 44 }"
          filter-display="menu"
          :global-filter-fields="globalFilterFields"
          :loading="loading"
          data-key="id"
          striped-rows
          responsive-layout="scroll"
          class="p-datatable-sm text-[13px] main-datatable"
          style="
            --highlight-bg: var(--primary-50);
            padding: 1px;
            font-size: 11px;
          "
          @row-select="onRowSelect"
        >
          <Column
            selection-mode="multiple"
            style="font-size: 9px; padding: 1px; width: 50px"
          />
          <Column
            :expander="true"
            style="width: 30px"
          />
          <Column
            field="art"
            header="№"
            :sortable="true"
            style="font-size: 9px; padding: 1px; width: 80px"
          >
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.art }}</span>
              </div>
            </template>
          </Column>

          <Column field="title" header="Название" :sortable="true" style="padding: 1px; width: 200px">
            <template #body="{ data }">
              <span>{{ data.title }}</span>
            </template>
          </Column>

          <Column
            field="description"
            header="Описание"
            style="padding: 1px; font-size: 9px; width: 250px"
          >
            <template #body="{ data }">
              <span class="description-cell">{{ data.description }}</span>
            </template>
          </Column>

          <Column
            field="tags"
            header="Теги"
            :sortable="true"
            style="padding: 1px; width: 200px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="tag in data.tags"
                  :key="tag"
                  :value="tag"
                  style="padding: 0 3px; font-size: 11px"
                />
              </div>
            </template>
          </Column>

          <Column
            field="json"
            header="JSON"
            style="padding: 1px; font-size: 9px; width: 300px"
          >
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span class="json-cell">{{ truncateJson(data.json) }}</span>
                <Button
                  label="⿻"
                  class="p-button-text p-button-sm"
                  style="width: 20px; font-size: 12px;padding:1px"
                  @click="copyToClipboard(data.json)"
                />
              </div>
            </template>
          </Column>

          <Column header="Действия" :exportable="false" style="width: 180px">
            <template #body="{ data }">
              <div class="flex gap-1">
                <Button
                  icon="pi pi-pencil"
                  class="p-button-text p-button-sm"
                  @click="editItem(data)"
                />
                <Button
                  icon="pi pi-copy"
                  class="p-button-text p-button-sm"
                  @click="duplicateItem(data)"
                />
                <Button
                  icon="pi pi-trash"
                  class="p-button-text p-button-sm p-button-danger"
                  @click="confirmDelete(data)"
                />
              </div>
            </template>
          </Column>

          <Column header="Переменные" style="width: 200px">
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="variable in extractVariables(data.json).slice(0, 10)"
                  :key="variable"
                  :value="variable"
                  severity="info"
                  style="padding: 0 2px; font-size: 9px"
                />
                <span v-if="extractVariables(data.json).length > 15" class="text-xs text-gray-500">
                  +{{ extractVariables(data.json).length - 15 }}
                </span>
              </div>
            </template>
          </Column>

          <!-- Шаблон подчиненной таблицы (табличное оформление) -->
          <template #expansion="{ data }">
            <div class="p-1">
              <div class="overflow-x-auto overflow-y-hidden" style="max-height: 120px; max-width: 100%;">
                <table class="border-collapse" style="min-width: fit-content; font-size: 9px; table-layout: fixed;">
                  <thead>
                    <tr class="bg-gray-50">
                      <th
                        v-for="(variable, index) in parseJsonToVariables(data.json)"
                        :key="index"
                        class="border border-gray-200 text-left font-mono font-semibold truncate"
                        style="width: 156px; padding: 2px 4px; font-size: 9px;"
                        :title="variable.key"
                      >
                        {{ variable.key }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        v-for="(variable, index) in parseJsonToVariables(data.json)"
                        :key="index"
                        class="border border-gray-200 align-top relative group"
                        style="width: 156px; padding: 2px 4px; position: relative;"
                      >
                        <!-- Кнопка копирования в каждой ячейке -->
                        <Button
                          icon="pi pi-copy"
                          class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                          style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); border-radius: 2px;"
                          @click="copyToClipboard(variable.value)"
                        />

                        <!-- Изображение если это image переменная -->
                        <div v-if="isImageVariable(variable.key) && isValidImageUrl(variable.value)" class="mb-1">
                          <Image
                            :src="variable.value"
                            alt="Preview"
                            preview
                            class="w-full"
                            style="width: 140px; height: 50px; object-fit: contain; border-radius: 2px; border: 1px solid #e5e7eb;"
                          />
                        </div>

                        <!-- Значение с ограничением текста -->
                        <div
                          class="cursor-pointer hover:bg-gray-100 p-1 rounded"
                          style="max-height: 60px; overflow: hidden; line-height: 1.1; font-size: 9px; word-break: break-word;"
                          @click="showFullTextDialog(variable.value)"
                        >
                          {{ truncateText(variable.value, 100) }}
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </template>
        </DataTable>
      </div>
    </div>

    <!-- Сайдбар редактирования -->
    <WcontSidebar
      v-model:visible="sidebarVisible"
      :collapsed="false"
      title="Редактирование JSON"
      @close="sidebarVisible = false"
      @toggle-collapse="() => {}"
    >
      <div class="p-fluid">
        <!-- Базовая информация -->
        <div class="space-y-2">
          <div class="flex gap-2">
            <div class="field w-1/3">
              <InputText
                v-model="editingItem.art"
                required
                class="w-full"
                placeholder="Артикул*"
                style="padding: 6px; font-size: 10px"
              />
            </div>

            <div class="field w-2/3">
              <InputText
                v-model="editingItem.title"
                required
                class="w-full"
                placeholder="Название*"
                style="padding: 6px; font-size: 11px"
              />
            </div>
          </div>

          <div class="field">
            <Textarea
              v-model="editingItem.description"
              rows="2"
              class="w-full text-xs [&>textarea]:text-xs"
              placeholder="Описание"
              style="padding: 4px; font-size: 10px"
            />
          </div>

          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.tags"
              :options="tagOptions"
              placeholder="Выберите теги"
              display="chip"
              class="text-xs w-full p-0"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.wpage"
              :options="wpageOptions"
              option-label="label"
              option-value="value"
              display="chip"
              class="w-full text-xs"
              placeholder="Связанные страницы"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.wblock_proto"
              :options="wblockOptions"
              option-label="label"
              option-value="value"
              display="chip"
              class="w-full text-xs"
              placeholder="Связанные блоки"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-0">
            <TabView
              class="text-xs"
              :pt="{
                panelcontainer: { style: 'padding:0' },
              }"
            >
              <TabPanel
                header="JSON"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <PrismEditorWithCopy
                  v-model="editingItem.json"
                  editor-class="my-editor text-xs"
                  :highlight="highlightJson"
                  placeholder="Введите JSON данные"
                  field-name="JSON"
                  style="min-height:600px;"
                />
              </TabPanel>

              <TabPanel
                header="Редактор"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <div>
                  <!-- Быстрое добавление переменных -->
                  <div class="quick-variables flex flex-wrap gap-1 mb-2">
                    <div
                      v-for="varType in quickVariableTypes"
                      :key="varType"
                      class="relative"
                    >
                      <Button
                        :label="varType"
                        class="p-button-sm p-button-outlined"
                        style="font-size: 9px; padding: 2px 6px"
                        @click="addQuickVariable(varType)"
                      />
                      <!-- Счетчик использованных переменных -->
                      <span
                        v-if="getVariableCount(varType) > 0"
                        class="absolute -top-1 -right-1 bg-blue-500 text-white rounded-full text-xs w-4 h-4 flex items-center justify-center"
                        style="font-size: 8px; min-width: 16px; min-height: 16px"
                      >
                        {{ getVariableCount(varType) }}
                      </span>
                    </div>
                  </div>

                  <div v-for="(field, index) in parsedJsonFields" :key="index" class="flex gap-1 items-start">
                    <div class="w-1/5">
                      <InputText
                        v-model="field.key"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px"
                        placeholder="Переменная"
                        @input="updateJsonFromFields"
                      />
                    </div>
                    <div class="w-3/5 relative group mb-0">
                      <Textarea
                        v-if="isTextVariable(field.key)"
                        v-model="field.value"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px; padding-right: 5px;"
                        placeholder="Значение"
                        rows="2"
                        @input="updateJsonFromFields"
                      />
                      <InputText
                        v-else
                        v-model="field.value"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px; padding-right: 5px;"
                        placeholder="Значение"
                        @input="updateJsonFromFields"
                      />
                      
                      
                    </div>
                    <div class="w-6">
                    <Button
                        icon="pi pi-copy"
                        class="group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                        style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); right: 4px; top: 4px;"
                        @click="copyToClipboard(field.value)"
                      />
                    </div>
                    <div class="w-1/12 text-xs text-gray-500 text-center pt-1">
                      {{ field.value ? field.value.length : 0 }}
                    </div>
                    <div class="w-1/12 flex gap-1 pt-1">
                      <Button
                        icon="pi pi-clone"
                        class="p-button-text p-button-sm"
                        style="width: 16px; height: 16px; padding: 0"
                        @click="duplicateJsonField(index)"
                      />
                      <Button
                        icon="pi pi-trash"
                        class="p-button-text p-button-sm p-button-danger"
                        style="width: 16px; height: 16px; padding: 0"
                        @click="removeJsonField(index)"
                      />
                    </div>
                  </div>
                  <div class="flex justify-between items-center mt-2">
                    <Button
                      label="+ Добавить поле"
                      class="p-button-sm p-button-text"
                      @click="addJsonField"
                    />
                    <span class="text-xs text-gray-500">
                      Всего переменных: {{ parsedJsonFields.length }}
                    </span>
                  </div>
                </div>
              </TabPanel>

              <TabPanel
                header="Картинки"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <div class=" grid grid-cols-2 gap-1">
                  <div v-for="(imageData, index) in extractedImagesWithVars" :key="index" class="border rounded p-1">
                    <Image
                      :src="imageData.url"
                      alt="Изображение из JSON"
                      width="200"
                      preview
                      class="w-full mb-2"
                    />
                    <div class="space-y-1">
                      <div class="text-xs font-semibold text-gray-700">
                        Переменная: {{ imageData.variable }}
                      </div>
                      <div class="flex">
                      <InputText
                        v-model="imageData.url"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px"
                        placeholder="URL изображения"
                        @input="updateImageInJson(imageData.variable, imageData.url)"
                      />
                      <div class="w-6">
                    <Button
                        icon="pi pi-copy"
                        class="group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                        style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); right: 4px; top: 4px;"
                        @click="copyToClipboard(imageData.url)"
                      />
                    </div>
                    </div>
                    </div>
                  </div>
                  <p v-if="!extractedImagesWithVars.length" class="text-xs text-gray-500">
                    Изображения не найдены в JSON (поиск по полям image, imageBackground)
                  </p>
                </div>
              </TabPanel>
            </TabView>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-4">
          <Button
            label="Отмена"
            icon="pi pi-times"
            class="p-button-sm"
            @click="closeSidebar"
          />
          <Button
            label="Сохранить"
            icon="pi pi-check"
            class="p-button-sm"
            :loading="saving"
            @click="saveItem"
          />
        </div>
      </div>
    </WcontSidebar>

    <!-- Диалог подтверждения удаления -->
    <ConfirmDialog />

    <!-- Диалог для просмотра полного текста -->
    <Dialog
      v-model:visible="fullTextDialogVisible"
      header="Полный текст"
      :modal="true"
      :style="{ width: '50vw' }"
      :breakpoints="{ '960px': '75vw', '641px': '90vw' }"
    >
      <div class="p-4">
        <pre class="whitespace-pre-wrap text-sm">{{ fullTextContent }}</pre>
      </div>
    </Dialog>

      <!-- Уведомления -->
      <Toast />
    </div>
    <!-- Закрытие второго экрана -->
  </div>
  <!-- Закрытие контейнера двух экранов -->
</template>

<script setup lang="ts">
  import 'vue-prism-editor/dist/prismeditor.min.css'
  import Prism from 'prismjs'
  import 'prismjs/components/prism-json'
  import 'prismjs/themes/prism-tomorrow.css'

  import { ref, computed, onMounted } from 'vue'
  import { useDirectusItems } from '#imports'
  import { useConfirm } from 'primevue/useconfirm'
  import { useToast } from 'primevue/usetoast'
  import Button from 'primevue/button'
  import InputText from 'primevue/inputtext'
  import Textarea from 'primevue/textarea'
  import MultiSelect from 'primevue/multiselect'
  import Tag from 'primevue/tag'
  import DataTable from 'primevue/datatable'
  import Column from 'primevue/column'
  import ProgressBar from 'primevue/progressbar'
  import ConfirmDialog from 'primevue/confirmdialog'
  import Toast from 'primevue/toast'
  import TabView from 'primevue/tabview'
  import TabPanel from 'primevue/tabpanel'
  import Image from 'primevue/image'
  import Dialog from 'primevue/dialog'
  import WcontSidebar from '~/components/WcontSidebar.vue'
  import BulkFormContainer from '~/components/BulkFormContainer.vue'
  import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'
  import ContentLibraryConstructor from '~/components/ContentLibraryConstructor.vue'

  interface WJson {
    id?: string
    art: string
    title: string
    description?: string
    tags?: string[]
    json?: string
    wpage?: string[]
    wblock_proto?: string[]
    date_created?: string
    user_created?: string
    date_updated?: string
    user_updated?: string
  }

  // API и утилиты
  const { getItems, createItems, updateItem, deleteItems } = useDirectusItems()
  const confirm = useConfirm()
  const toast = useToast()

  // Состояние
  const items = ref<WJson[]>([])
  const loading = ref(false)
  const saving = ref(false)
  const sidebarVisible = ref(false)
  const selectedItems = ref<WJson[]>([])
  const globalFilterValue = ref('')
  const selectedTags = ref<string[]>([])
  const expandedRows = ref<any[]>([])

  // Состояние для массовых форм
  const bulkFormVisible = ref(false)
  const bulkFormMode = ref<'create' | 'edit'>('create')

  // Состояние для диалога полного текста
  const fullTextDialogVisible = ref(false)
  const fullTextContent = ref('')

  // Режим работы страницы
  const workMode = ref<'standard' | 'constructor'>('standard')

  // Количество форм для конструктора
  const formsCount = ref(0)

  // Редактирование
  const editingItem = ref<WJson>({
    art: '',
    title: '',
    description: '',
    tags: [],
    json: '',
    wpage: [],
    wblock_proto: [],
  })

  // Сортировка по умолчанию по art
  const sortField = ref('art')
  const sortOrder = ref(1)

  // Опции для фильтров и сайдбара
  const tagOptions = ref<string[]>([])
  const wpageOptions = ref<any[]>([])
  const wblockOptions = ref<any[]>([])
  const relatedWpages = ref<any[]>([])
  const relatedWblocks = ref<any[]>([])

  // Вычисляемые свойства
  const availableTags = computed(() => {
    const tags = new Set<string>()
    items.value.forEach((item) => {
      item.tags?.forEach((tag) => tags.add(tag))
    })
    return Array.from(tags)
  })

  const filteredItems = computed(() => {
    let result = [...items.value]

    // Фильтр по тегам
    if (selectedTags.value.length > 0) {
      result = result.filter((item) => {
        return selectedTags.value.some((tag) =>
          item.tags?.includes(tag)
        )
      })
    }

    // Глобальный поиск
    if (globalFilterValue.value) {
      const searchValue = globalFilterValue.value.toLowerCase()
      result = result.filter(
        (item) =>
          (item.title?.toLowerCase() || '').includes(searchValue) ||
          (item.art?.toLowerCase() || '').includes(searchValue) ||
          (item.description?.toLowerCase() || '').includes(searchValue) ||
          (item.json?.toLowerCase() || '').includes(searchValue),
      )
    }

    return result
  })

  const globalFilterFields = ['title', 'art', 'description', 'json']

  // Состояние для редактора JSON
  const parsedJsonFields = ref<Array<{key: string, value: string}>>([])

  // Быстрые переменные для добавления
  const quickVariableTypes = ['title', 'image', 'imageBackground', 'url', 'linkText', 'icon', 'text', 'excerpt']

  // Вычисляемые свойства для сайдбара
  const extractedImages = computed(() => {
    if (!editingItem.value.json) return []
    try {
      const json = JSON.parse(editingItem.value.json)
      const images: string[] = []

      // Рекурсивный поиск изображений
      const findImages = (obj: any) => {
        if (typeof obj === 'object' && obj !== null) {
          for (const [key, value] of Object.entries(obj)) {
            if ((key.toLowerCase().includes('image') || key.toLowerCase().includes('img')) &&
                typeof value === 'string' &&
                (value.startsWith('http') || value.startsWith('/'))) {
              images.push(value)
            } else if (typeof value === 'object') {
              findImages(value)
            }
          }
        }
      }

      findImages(json)
      return [...new Set(images)] // убираем дубликаты
    } catch {
      return []
    }
  })

  const extractedImagesWithVars = computed(() => {
    if (!editingItem.value.json) return []
    try {
      const json = JSON.parse(editingItem.value.json)
      const images: Array<{url: string, variable: string}> = []

      // Рекурсивный поиск изображений с сохранением пути к переменной
      const findImages = (obj: any, path = '') => {
        if (typeof obj === 'object' && obj !== null) {
          for (const [key, value] of Object.entries(obj)) {
            const currentPath = path ? `${path}.${key}` : key
            if ((key.toLowerCase().includes('image') || key.toLowerCase().includes('img')) &&
                typeof value === 'string' &&
                (value.startsWith('http') || value.startsWith('/'))) {
              images.push({ url: value, variable: currentPath })
            } else if (typeof value === 'object') {
              findImages(value, currentPath)
            }
          }
        }
      }

      findImages(json)
      return images
    } catch {
      return []
    }
  })

  // Конфигурация полей для массовых форм wjson (убираем дублирование с базовыми полями)
  const wjsonBulkFieldConfig = computed(() => [
    {
      name: 'art',
      type: 'text' as const,
      placeholder: 'Артикул',
      class: 'field-full'
    },
    {
      name: 'description',
      type: 'textarea' as const,
      placeholder: 'Описание',
      class: 'field-full'
    },
    {
      name: 'tags',
      type: 'multiselect' as const,
      placeholder: 'Теги',
      options: availableTags.value,
      class: 'field-full'
    },
    {
      name: 'json',
      type: 'prism' as const,
      placeholder: 'JSON данные',
      class: 'field-full'
    }
  ])

  // Утилиты
  const truncateJson = (json: string) => {
    if (!json) return ''
    return json.length > 100 ? json.substring(0, 100) + '...' : json
  }

  const extractVariables = (json: string) => {
    if (!json) return []
    try {
      const variables = new Set<string>()
      const regex = /"([^"]+)":/g
      let match
      while ((match = regex.exec(json)) !== null) {
        variables.add(match[1])
      }
      return Array.from(variables)
    } catch {
      return []
    }
  }

  // Функция для парсинга JSON в переменные для подчиненной таблицы
  const parseJsonToVariables = (json: string) => {
    if (!json) return []
    try {
      const parsed = JSON.parse(json)
      const variables: Array<{key: string, value: string}> = []

      const flattenObject = (obj: any, prefix = '') => {
        for (const [key, value] of Object.entries(obj)) {
          const fullKey = prefix ? `${prefix}.${key}` : key
          if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
            flattenObject(value, fullKey)
          } else {
            variables.push({
              key: fullKey,
              value: typeof value === 'string' ? value : JSON.stringify(value)
            })
          }
        }
      }

      flattenObject(parsed)
      return variables
    } catch {
      return []
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text || '')
    toast.add({
      severity: 'info',
      summary: 'Скопировано',
      detail: 'JSON скопирован в буфер обмена',
      life: 3000,
    })
  }

  // Функции для подчиненной таблицы
  const isImageVariable = (key: string) => {
    return key.toLowerCase().includes('image') || key.toLowerCase().includes('img')
  }

  const isValidImageUrl = (url: string) => {
    return typeof url === 'string' && (url.startsWith('http') || url.startsWith('/'))
  }

  const truncateText = (text: string, maxLength: number) => {
    if (!text || text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  const showFullText = (text: string) => {
    // Используем простой alert для показа полного текста (можно заменить на модальное окно)
    alert(text)
  }

  const showFullTextDialog = (text: string) => {
    fullTextContent.value = text
    fullTextDialogVisible.value = true
  }

  // Функция подсветки JSON
  const highlightJson = (code: string) => {
    return Prism.highlight(code, Prism.languages.json, 'json')
  }

  // Функции для JSON редактора
  const parseJsonToFields = () => {
    if (!editingItem.value.json) {
      parsedJsonFields.value = []
      return
    }

    try {
      const json = JSON.parse(editingItem.value.json)
      const fields: Array<{key: string, value: string}> = []

      const flattenObject = (obj: any, prefix = '') => {
        for (const [key, value] of Object.entries(obj)) {
          const fullKey = prefix ? `${prefix}.${key}` : key
          if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
            flattenObject(value, fullKey)
          } else {
            fields.push({
              key: fullKey,
              value: typeof value === 'string' ? value : JSON.stringify(value)
            })
          }
        }
      }

      flattenObject(json)
      parsedJsonFields.value = fields
    } catch {
      parsedJsonFields.value = []
    }
  }

  const updateJsonFromFields = () => {
    try {
      const result: any = {}

      parsedJsonFields.value.forEach(field => {
        if (!field.key) return

        const keys = field.key.split('.')
        let current = result

        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) {
            current[keys[i]] = {}
          }
          current = current[keys[i]]
        }

        const lastKey = keys[keys.length - 1]
        try {
          // Пытаемся парсить как JSON, если не получается - оставляем как строку
          current[lastKey] = JSON.parse(field.value)
        } catch {
          current[lastKey] = field.value
        }
      })

      editingItem.value.json = JSON.stringify(result, null, 2)
    } catch (error) {
      console.error('Error updating JSON from fields:', error)
    }
  }

  const addJsonField = () => {
    parsedJsonFields.value.push({ key: '', value: '' })
  }

  const removeJsonField = (index: number) => {
    parsedJsonFields.value.splice(index, 1)
    updateJsonFromFields()
  }

  const duplicateJsonField = (index: number) => {
    const originalField = parsedJsonFields.value[index]
    const baseKey = originalField.key.replace(/\d+$/, '') // убираем цифры в конце

    // Логика нумерации: image, image2, image3... (без image1)
    const existingFields = parsedJsonFields.value.filter(field =>
      field.key === baseKey || field.key.match(new RegExp(`^${baseKey}\\d+$`))
    )

    let newKey = baseKey
    if (existingFields.length > 0) {
      // Если уже есть переменные этого типа, находим следующий номер
      let counter = 2
      newKey = `${baseKey}${counter}`
      while (parsedJsonFields.value.some(field => field.key === newKey)) {
        counter++
        newKey = `${baseKey}${counter}`
      }
    }

    const duplicatedField = {
      key: newKey,
      value: originalField.value
    }

    parsedJsonFields.value.splice(index + 1, 0, duplicatedField)
    updateJsonFromFields()
  }

  const isTextVariable = (key: string) => {
    return key.toLowerCase().includes('text') ||
           key.toLowerCase().includes('excerpt') ||
           key.toLowerCase().includes('description')
  }

  const getVariableCount = (varType: string) => {
    return parsedJsonFields.value.filter(field =>
      field.key === varType || field.key.startsWith(`${varType}2`) || field.key.startsWith(`${varType}3`)
    ).length
  }

  const addQuickVariable = (varType: string) => {
    // Логика нумерации: image, image2, image3... (без image1)
    const existingFields = parsedJsonFields.value.filter(field =>
      field.key === varType || field.key.match(new RegExp(`^${varType}\\d+$`))
    )

    let newKey = varType
    if (existingFields.length > 0) {
      // Если уже есть переменные этого типа, находим следующий номер
      let counter = 2
      newKey = `${varType}${counter}`
      while (parsedJsonFields.value.some(field => field.key === newKey)) {
        counter++
        newKey = `${varType}${counter}`
      }
    }

    const newField = {
      key: newKey,
      value: ''
    }

    parsedJsonFields.value.push(newField)
    updateJsonFromFields()
  }

  const updateImageInJson = (variablePath: string, newUrl: string) => {
    try {
      const json = JSON.parse(editingItem.value.json || '{}')
      const keys = variablePath.split('.')
      let current = json

      // Навигируемся к нужному объекту
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {}
        }
        current = current[keys[i]]
      }

      // Обновляем значение
      const lastKey = keys[keys.length - 1]
      current[lastKey] = newUrl

      // Обновляем JSON в форме
      editingItem.value.json = JSON.stringify(json, null, 2)

      // Обновляем поля редактора
      parseJsonToFields()
    } catch (error) {
      console.error('Error updating image in JSON:', error)
    }
  }

  // Обработчик применения контента из конструктора
  const handleContentApplied = (data: any) => {
    console.log('Content applied:', data)
    toast.add({
      severity: 'success',
      summary: 'Контент применен',
      detail: 'Контент успешно применен к выбранным элементам',
      life: 3000,
    })
    // Обновляем данные после применения контента
    loadData()
  }

  // Обработчик обновления примитивов
  const handlePrimitivesUpdated = (data: any) => {
    console.log('Primitives updated:', data)
    toast.add({
      severity: 'info',
      summary: 'Примитивы обновлены',
      detail: 'База примитивов успешно обновлена',
      life: 3000,
    })
  }

  // Обработчик применения к формам
  const handleApplyToForms = (data: any) => {
    console.log('Apply to forms:', data)
    // Здесь будет логика передачи данных в IntelligentForms
    // Пока просто логируем
  }

  // Обработчик изменения количества форм
  const handleFormsCountChanged = (count: number) => {
    formsCount.value = count
  }

  // Методы
  const onRowSelect = (event: { data: WJson }) => {
    // Удалено автоматическое открытие сайдбара при выборе строки
  }

  const loadData = async () => {
    loading.value = true
    try {
      const jsonItems = await getItems({
        collection: 'wjson',
        params: {
          limit: -1,
          sort: [sortField.value],
          fields: ['*'],
        },
      })

      console.log('Loaded JSON items:', jsonItems)
      items.value = jsonItems
    } catch (error) {
      console.error('Error loading data:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить данные JSON',
        life: 3000,
      })
    } finally {
      loading.value = false
    }
  }

  const onGlobalFilterChange = () => {
    // Обработка изменения глобального фильтра
  }

  const applyTagsFilter = () => {
    // Обработка изменения фильтра по тегам
  }

  const openCreateDialog = () => {
    editingItem.value = {
      art: '',
      title: '',
      description: '',
      tags: [],
      json: '',
      wpage: [],
      wblock_proto: [],
    }
    sidebarVisible.value = true
  }

  const closeSidebar = () => {
    sidebarVisible.value = false
    parsedJsonFields.value = []
    editingItem.value = {
      art: '',
      title: '',
      description: '',
      tags: [],
      json: '',
      wpage: [],
      wblock_proto: [],
    }
  }

  const saveItem = async () => {
    saving.value = true
    try {
      const { id, wpage, wblock_proto, ...saveData } = editingItem.value

      if (id) {
        // Обновление существующего JSON
        await updateItem({
          collection: 'wjson',
          id,
          item: saveData,
        })
        await saveWpageRelations(id, wpage || [])
        await saveWblockRelations(id, wblock_proto || [])
      } else {
        // Создание нового JSON
        const result = await createItems({
          collection: 'wjson',
          items: [saveData],
        })
        const newId = Array.isArray(result) ? result[0]?.id : result?.id
        if (newId) {
          if (wpage?.length) await saveWpageRelations(newId, wpage)
          if (wblock_proto?.length) await saveWblockRelations(newId, wblock_proto)
        }
      }

      await loadData()
      closeSidebar()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: id ? 'JSON обновлен' : 'JSON создан',
        life: 3000,
      })
    } catch (error) {
      console.error('Save error:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось сохранить JSON',
        life: 5000,
      })
    } finally {
      saving.value = false
    }
  }

  const editItem = async (item: WJson) => {
    try {
      const [jsonData] = await getItems({
        collection: 'wjson',
        params: {
          filter: { id: { _eq: item.id } },
          fields: ['*'],
          limit: 1,
        },
      })

      const wpageRelations = await loadWpageRelations(item.id!)
      const wblockRelations = await loadWblockRelations(item.id!)

      editingItem.value = {
        ...jsonData,
        wpage: wpageRelations,
        wblock_proto: wblockRelations,
      }

      // Форматируем JSON если он есть
      if (editingItem.value.json) {
        try {
          const parsed = JSON.parse(editingItem.value.json)
          editingItem.value.json = JSON.stringify(parsed, null, 2)
        } catch {
          // Если JSON невалидный, оставляем как есть
        }
      }

      parseJsonToFields() // Парсим JSON для редактора
      sidebarVisible.value = true
    } catch (error) {
      console.error('Error loading JSON item:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить данные JSON',
        life: 3000,
      })
    }
  }

  // Методы для работы с M2M связями
  const loadWpageRelations = async (jsonId: string) => {
    try {
      const relations = await getItems({
        collection: 'wpage_wjson',
        params: {
          filter: { wjson_id: { _eq: jsonId } },
          fields: ['wpage_id'],
        },
      })
      return relations.map((r: any) => r.wpage_id)
    } catch (error) {
      console.error('Error loading wpage relations:', error)
      return []
    }
  }

  const loadWblockRelations = async (jsonId: string) => {
    try {
      const relations = await getItems({
        collection: 'wblock_proto_wjson',
        params: {
          filter: { wjson_id: { _eq: jsonId } },
          fields: ['wblock_proto_id'],
        },
      })
      return relations.map((r: any) => r.wblock_proto_id)
    } catch (error) {
      console.error('Error loading wblock relations:', error)
      return []
    }
  }

  // Функции для сохранения M2M связей
  const saveWpageRelations = async (jsonId: string, wpageIds: string[]) => {
    try {
      // Удаляем старые связи
      const currentRelations = await getItems({
        collection: 'wpage_wjson',
        params: {
          filter: { wjson_id: { _eq: jsonId } },
          fields: ['id'],
        },
      })

      if (currentRelations.length > 0) {
        await deleteItems({
          collection: 'wpage_wjson',
          items: currentRelations.map((r: any) => r.id),
        })
      }

      // Добавляем новые
      if (wpageIds.length > 0) {
        await createItems({
          collection: 'wpage_wjson',
          items: wpageIds.map((id) => ({
            wjson_id: jsonId,
            wpage_id: id,
          })),
        })
      }
    } catch (error) {
      console.error('Error saving wpage relations:', error)
      throw error
    }
  }

  const saveWblockRelations = async (jsonId: string, wblockIds: string[]) => {
    try {
      // Удаляем старые связи
      const currentRelations = await getItems({
        collection: 'wblock_proto_wjson',
        params: {
          filter: { wjson_id: { _eq: jsonId } },
          fields: ['id'],
        },
      })

      if (currentRelations.length > 0) {
        await deleteItems({
          collection: 'wblock_proto_wjson',
          items: currentRelations.map((r: any) => r.id),
        })
      }

      // Добавляем новые
      if (wblockIds.length > 0) {
        await createItems({
          collection: 'wblock_proto_wjson',
          items: wblockIds.map((id) => ({
            wjson_id: jsonId,
            wblock_proto_id: id,
          })),
        })
      }
    } catch (error) {
      console.error('Error saving wblock relations:', error)
      throw error
    }
  }

  const duplicateItem = async (item: WJson) => {
    const duplicate = { ...item }
    delete duplicate.id
    duplicate.art = `${duplicate.art}-copy`
    duplicate.title = `${duplicate.title} (копия)`

    try {
      await createItems({
        collection: 'wjson',
        items: [duplicate],
      })
      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'JSON скопирован',
      })
    } catch (error) {
      console.error('Error duplicating item:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось скопировать JSON',
      })
    }
  }

  const confirmDelete = (item: WJson) => {
    confirm.require({
      message: 'Вы уверены, что хотите удалить этот JSON?',
      header: 'Подтверждение удаления',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteItem(item),
    })
  }

  const deleteItem = async (item: WJson) => {
    try {
      await deleteItems({
        collection: 'wjson',
        items: [item.id!],
      })
      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'JSON удален',
      })
    } catch (error) {
      console.error('Error deleting item:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось удалить JSON',
      })
    }
  }

  const bulkDuplicateItems = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true

      // Получаем полные данные выбранных записей
      const selectedIds = selectedItems.value.map(item => item.id)
      const fullRecords = await getItems({
        collection: 'wjson',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: ['*'],
        },
      })

      // Подготавливаем данные для дублирования
      const duplicates = fullRecords.map(item => {
        const duplicate = { ...item }
        delete duplicate.id
        duplicate.art = `${duplicate.art}-copy`
        duplicate.title = `${duplicate.title} (копия)`
        return duplicate
      })

      // Создаем дубликаты
      await createItems({
        collection: 'wjson',
        items: duplicates,
      })

      await loadData()
      selectedItems.value = []
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Дублировано ${duplicates.length} JSON записей`,
      })
    } catch (error) {
      console.error('Error duplicating items:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось дублировать JSON записи',
      })
    } finally {
      loading.value = false
    }
  }

  const bulkDeleteItems = () => {
    if (!selectedItems.value.length) return

    confirm.require({
      message: `Вы уверены, что хотите удалить ${selectedItems.value.length} выбранных JSON записей?`,
      header: 'Подтверждение удаления',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          loading.value = true
          const selectedIds = selectedItems.value.map(item => item.id)

          await deleteItems({
            collection: 'wjson',
            items: selectedIds,
          })

          await loadData()
          selectedItems.value = []

          toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: `Удалено ${selectedIds.length} JSON записей`,
          })
        } catch (error) {
          console.error('Error deleting items:', error)
          toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось удалить JSON записи',
          })
        } finally {
          loading.value = false
        }
      }
    })
  }

  // Функции для массовых форм
  function openBulkCreate() {
    bulkFormMode.value = 'create'
    bulkFormVisible.value = true
  }

  function openBulkEdit() {
    if (selectedItems.value.length === 0) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Выберите JSON записи для редактирования',
        life: 3000
      })
      return
    }
    bulkFormMode.value = 'edit'
    bulkFormVisible.value = true
  }

  function closeBulkForm() {
    bulkFormVisible.value = false
  }

  function onBulkFormSaved(savedItems: any) {
    // Обновляем список JSON записей
    loadData()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Обработано ${savedItems.length} JSON записей`,
      life: 3000
    })
  }

  // Загрузка опций для сайдбара
  const loadTagOptions = async () => {
    try {
      const items = await getItems({
        collection: 'wjson',
        params: {
          limit: -1,
          fields: ['tags'],
        },
      })

      if (Array.isArray(items)) {
        const tags = new Set()
        items.forEach((item: any) => {
          item.tags?.forEach((tag: any) => tags.add(tag))
        })
        tagOptions.value = Array.from(tags) as string[]
      }
    } catch (error) {
      console.error('Error loading tag options:', error)
    }
  }

  const loadWpageOptions = async () => {
    try {
      const pages = await getItems({
        collection: 'wpage',
        params: {
          limit: -1,
          fields: ['id', 'title', 'number'],
        },
      })

      if (Array.isArray(pages)) {
        wpageOptions.value = pages.map((page: any) => ({
          value: page.id,
          label: `${page.number} - ${page.title}` || page.id,
        }))
      }
    } catch (error) {
      console.error('Error loading wpage options:', error)
    }
  }

  const loadWblockOptions = async () => {
    try {
      const blocks = await getItems({
        collection: 'wblock_proto',
        params: {
          limit: -1,
          fields: ['id', 'title', 'number'],
        },
      })

      if (Array.isArray(blocks)) {
        wblockOptions.value = blocks.map((block: any) => ({
          value: block.id,
          label: `${block.number} - ${block.title}` || block.id,
        }))
      }
    } catch (error) {
      console.error('Error loading wblock options:', error)
    }
  }

  // Инициализация
  onMounted(async () => {
    await Promise.all([
      loadData(),
      loadTagOptions(),
      loadWpageOptions(),
      loadWblockOptions()
    ])
  })
</script>

<style scoped>
.two-screens-container {
  height: 200vh; /* Два экрана по 100vh каждый */
  overflow-y: auto;
}

/* Когда конструктор выключен - только один экран */
.two-screens-container.single-screen {
  height: 100vh; /* Только один экран */
}

.screen-one {
  height: 100vh;
  width: 100%;
}

.screen-two {
  height: 100vh;
  width: 100%;
}
</style>

<style scoped>
.description-cell {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 10px;
}

.json-cell {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 10px;
  font-family: monospace;
}

:deep(.p-datatable-sm .p-datatable-tbody > tr > td) {
  padding: 0.25rem;
  font-size: 11px;
}

:deep(.p-tag) {
  font-size: 9px;
  padding: 0.1rem 0.3rem;
}

:deep(.p-button-sm) {
  padding: 0.25rem 0.5rem;
  font-size: 10px;
}

:deep(.p-multiselect .p-multiselect-label) {
  font-size: 11px;
}

:deep(.p-inputtext) {
  font-size: 11px;
}

/* Стили для подчиненной таблицы */
.expansion-container {
  max-width: 100%;
  overflow: hidden;
}

.expansion-container table {
  font-size: 9px;
  border-collapse: collapse;
  table-layout: fixed;
}

.expansion-container th,
.expansion-container td {
  border: 1px solid #e5e7eb;
  padding: 2px 4px;
  font-size: 9px;
  width: 156px;
  position: relative;
}

.expansion-container th {
  background: #f9fafb;
  font-weight: 600;
}

/* Ограничение прокрутки только для подчиненной таблицы */
.expansion-container .overflow-x-auto {
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
}

/* Стили для кнопок копирования в ячейках */
.expansion-container .group:hover .group-hover\:opacity-100 {
  opacity: 1 !important;
}

/* Стили для кнопки копирования в поле */
.group:hover .group-hover\:opacity-100 {
  opacity: 1 !important;
}

/* Стили для быстрых переменных */
.quick-variables {
  background: #f8fafc;
  border-radius: 6px;
  padding: 8px;
}

/* Стили для счетчиков переменных */
.variable-counter {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: 600;
}

/* Стили для textarea в редакторе */
:deep(.p-inputtextarea) {
  resize: vertical;
  min-height: 40px;
}

/* Предотвращение растягивания основной DataTable */
.main-datatable {
  table-layout: fixed;
  width: 100%;
}

.main-datatable :deep(.p-datatable-table) {
  table-layout: fixed;
  width: 100%;
}

.main-datatable :deep(.p-datatable-tbody > tr > td) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Исключение для расширенных строк */
.main-datatable :deep(.p-datatable-row-expansion) {
  overflow: visible;
  white-space: normal;
}
</style>
