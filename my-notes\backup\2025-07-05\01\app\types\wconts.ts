export interface WcontItem {
  id?: number | string
  art?: string
  name?: string
  description?: string
  tags?: string[]
  title?: string
  subtitle?: string
  excerpt?: string
  text?: string
  url?: string
  linkText?: string
  number?: number
  inscription?: string
  icon?: string
  product?: string
  category?: string
  additional?: string
  image?: string
  imageBackground?: string
  imageLogo?: string
  imageIcon?: string
  items?: WcontItem[]
}

export type ViewMode = 'compact' | 'full'

export function createEmptyWcontItem(): WcontItem {
  return {
    art: '',
    name: '',
    description: '',
    tags: [],
    title: '',
    subtitle: '',
    excerpt: '',
    text: '',
    url: '',
    linkText: '',
    number: undefined,
    inscription: '',
    icon: '',
    product: '',
    category: '',
    additional: '',
    image: '',
    imageBackground: '',
    imageLogo: '',
    imageIcon: '',
    items: []
  }
}
