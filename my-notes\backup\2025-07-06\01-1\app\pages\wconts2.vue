<template>
  <div class="flex h-screen">
    <!-- Основной контент -->
    <div class="flex-1 overflow-hidden flex flex-col" :class="{ 'pr-[30rem]': sidebarVisible }">
      <div class="flex justify-between mb-4 p-4">
        <div class="flex gap-2">
          <span class="p-input-icon-left">
            <i class="pi pi-search" />
            <InputText v-model="globalFilterValue" placeholder="Поиск..." @input="onGlobalFilterChange" />
          </span>
          <MultiSelect 
            v-model="selectedTags" 
            :options="availableTags" 
            placeholder="Фильтр по тегам" 
            display="chip" 
            class="w-64"
            @change="applyTagsFilter"
          />
        </div>
        <div class="flex gap-2">
          <Button 
            :icon="viewMode === 'compact' ? 'pi pi-list' : 'pi pi-table'" 
            :label="viewMode === 'compact' ? 'Полный вид' : 'Краткий вид'" 
            class="p-button-outlined" 
            @click="toggleViewMode"
          />
        </div>
      </div>
      
      <div class="flex justify-between items-center mb-4">
        <div class="flex items-center gap-2">
          <Button 
            label="Режим" 
            icon="pi pi-table" 
            class="p-button-secondary" 
            @click="toggleViewMode"
          />
          <span>{{ viewMode === 'compact' ? 'Компактный' : 'Полный' }} режим</span>
        </div>
        <!-- Остальные элементы управления -->
      </div>
      
      <div class="flex-1 overflow-auto">
        <DataTable 
          v-model:selection="selectedItem" 
          v-model:expanded-rows="expandedRows"
          :value="filteredItems" 
          selection-mode="single" 
          :paginator="true" 
          :rows="10"
          :rows-per-page-options="[5, 10, 20, 50]" 
          :sort-field="sortField"
          :sort-order="sortOrder"
          filter-display="menu"
          :global-filter-fields="globalFilterFields"
          :loading="loading"
          data-key="id"
          striped-rows
          responsive-layout="scroll"
          class="p-datatable-sm text-[13px]"
          :expanded-rows="expandedRows"
          @row-select="onRowSelect"
          @row-unselect="onRowUnselect"
          @row-expand="onRowExpand"
          @row-collapse="onRowCollapse"
        >
          <template #empty>
            <div class="text-center p-4">Нет данных для отображения</div>
          </template>
          
          <template #loading>
            <div class="text-center p-4">Загрузка данных...</div>
          </template>
          
          <template #expansion="slotProps">
            <div class="p-4 bg-surface-50">
              <DataTable :value="slotProps.data.items" class="p-datatable-sm text-[13px]">
                <Column field="title" header="Title">
                  <template #body="{ data }">
                    <div class="truncate-text cursor-pointer" @click="openNestedItemFullTextDialog(data)">
                      {{ truncateText(data.title) }}
                      <Button
                        v-if="data.title?.length > 255" 
                        label="👁️" 
                        class="p-button-text p-button-sm" 
                        @click.stop="openNestedItemFullTextDialog(data)" 
                      />
                    </div>
                  </template>
                </Column>
                
                <Column field="excerpt" header="Excerpt">
                  <template #body="{ data }">
                    <div class="truncate-text cursor-pointer" @click="openNestedItemFullTextDialog(data)">
                      {{ truncateText(data.excerpt) }}
                      <Button
                        v-if="data.excerpt?.length > 255" 
                        label="👁️" 
                        class="p-button-text p-button-sm" 
                        @click.stop="openNestedItemFullTextDialog(data)" 
                      />
                    </div>
                  </template>
                </Column>
                
                <Column field="text" header="Text">
                  <template #body="{ data }">
                    <div class="truncate-text cursor-pointer" @click="openNestedItemFullTextDialog(data)">
                      {{ truncateText(data.text) }}
                      <Button
                        v-if="data.text?.length > 255" 
                        label="👁️" 
                        class="p-button-text p-button-sm" 
                        @click.stop="openNestedItemFullTextDialog(data)" 
                      />
                    </div>
                  </template>
                </Column>
                
                <Column field="url" header="URL" />
                <Column field="linkText" header="Link Text" />
                <Column field="image" header="Image">
                  <template #body="{ data }">
                    <img v-if="data.image" :src="data.image" class="w-12 h-12 object-cover rounded" >
                    <span v-else>-</span>
                  </template>
                </Column>
              </DataTable>
            </div>
          </template>
          
          <!-- Всегда отображаемые колонки -->
          <Column :expander="true" header-style="width: 3rem" />
          
          <Column field="art" header="Art" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.art }}</span>
                <Button label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.art)" />
              </div>
            </template>
          </Column>
          
          <Column field="name" header="Name" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <NuxtLink :to="`/wcont/${encodeURIComponent(data.name || '')}`" class="text-primary hover:underline">
                  {{ truncateText(data.name) }}
                </NuxtLink>
                <Button label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.name)" />
                <Button
                  v-if="data.name?.length > 255" 
                  label="👁️" 
                  class="p-button-text p-button-sm" 
                  @click="openFullTextDialog(data)" 
                />
              </div>
            </template>
          </Column>
          
          <Column field="description" header="Description" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.description || '-' }}</span>
                <Button v-if="data.description" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.description)" />
                <Button
                  v-if="data.description && data.description.length > 255" 
                  label="👁️" 
                  class="p-button-text p-button-sm" 
                  @click="openFullTextDialog(data)" 
                />
              </div>
            </template>
          </Column>
          
          <Column field="tags" header="Tags" :sortable="false">
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="tag in data.tags" :key="tag" :value="tag" 
                  class="mr-1 cursor-pointer" @click="addTagToFilter(tag)" />
                <Button v-if="data.tags?.length" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.tags)" />
              </div>
            </template>
          </Column>
          
          <!-- Дополнительные колонки, отображаемые только в полном режиме -->
          <Column v-if="viewMode === 'full'" field="title" header="Title" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.title || '-' }}</span>
                <Button v-if="data.title" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.title)" />
                <Button
                  v-if="data.title && data.title.length > 255" 
                  label="👁️" 
                  class="p-button-text p-button-sm" 
                  @click="openFullTextDialog(data)" 
                />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="subtitle" header="Subtitle" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.subtitle || '-' }}</span>
                <Button v-if="data.subtitle" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.subtitle)" />
                <Button
                  v-if="data.subtitle && data.subtitle.length > 255" 
                  label="👁️" 
                  class="p-button-text p-button-sm" 
                  @click="openFullTextDialog(data)" 
                />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="excerpt" header="Excerpt" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.excerpt || '-' }}</span>
                <Button v-if="data.excerpt" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.excerpt)" />
                <Button
                  v-if="data.excerpt && data.excerpt.length > 255" 
                  label="👁️" 
                  class="p-button-text p-button-sm" 
                  @click="openFullTextDialog(data)" 
                />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="text" header="Text" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span class="truncate max-w-xs">{{ data.text ? (data.text.length > 100 ? data.text.substring(0, 100) + '...' : data.text) : '-' }}</span>
                <Button v-if="data.text" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.text)" />
                <Button
                  v-if="data.text && data.text.length > 255" 
                  label="👁️" 
                  class="p-button-text p-button-sm" 
                  @click="openFullTextDialog(data)" 
                />
              </div>
            </template>
          </Column>
          
          <Column field="actions" header="Actions" :sortable="false">
            <template #body="{ data }">
              <WcontItemActions 
                :item="data"
                @edit="openEditSidebar"
                @duplicate="duplicateItem"
                @convert="convertToWJson"
                @delete="confirmItemDeletion"
              />
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Новый компонент сайдбара -->
    <WcontSidebar 
      v-model:visible="sidebarVisible" 
      :full-width="false"
    >
      <WcontSidebarForm
        :item="editingItem"
        :form-fields="[
          { 
            name: 'art', 
            label: 'Артикул', 
            component: InputText 
          },
          { 
            name: 'name', 
            label: 'Название', 
            component: InputText 
          },
          // Добавьте другие поля
        ]"
        @save="saveItem"
        @cancel="sidebarVisible = false"
      />
    </WcontSidebar>

    <!-- Универсальный диалог полного текста -->
    <FullTextDialog 
      v-model="fullTextDialogVisible"
      :title="selectedFullTextItem?.name || selectedFullTextItem?.title"
      :text="selectedFullTextItem?.text"
      :items="selectedFullTextItem?.items"
    />

    <!-- Диалог подтверждения удаления -->
    <Dialog 
      v-model:visible="deleteItemConfirmation" 
      :modal="true" 
      :header="`Удаление записи ${itemToDelete?.name}`" 
      :style="{ width: '450px' }"
    >
      <div class="flex flex-col gap-4">
        <p>Вы уверены, что хотите удалить запись?</p>
        <div class="flex gap-2">
          <Button label="Удалить" class="p-button-danger" @click="deleteItem" />
          <Button label="Отмена" class="p-button-secondary" @click="deleteItemConfirmation = false" />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue'
import type { VNode } from 'vue'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'
import type { DataTableSlots } from 'primevue/datatable'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import InputNumber from 'primevue/inputnumber'
import Textarea from 'primevue/textarea'
import Sidebar from 'primevue/sidebar'
import Dialog from 'primevue/dialog'
import Chips from 'primevue/chips'
import Select from 'primevue/select'
import MultiSelect from 'primevue/multiselect'
import Tag from 'primevue/tag'
import Toast from 'primevue/toast'
import InputChips from 'primevue/inputchips'
import WcontSidebar from '~/components/WcontSidebar.vue'
import WcontSidebarForm from '~/components/WcontSidebarForm.vue'
import FullTextDialog from '~/components/FullTextDialog.vue'
import WcontItemActions from '~/components/WcontItemActions.vue'
import type { ViewMode, WcontItem } from '~/types/wconts'

// Расширенные типы для слотов DataTable
interface CustomDataTableSlots extends Partial<DataTableSlots> {
  body_art?: (scope: { data: WcontItem }) => VNode
  body_name?: (scope: { data: WcontItem }) => VNode
  body_description?: (scope: { data: WcontItem }) => VNode
  body_tags?: (scope: { data: WcontItem }) => VNode
}

const toast = useToast()
const { 
  getItems, 
  createItems, 
  updateItem, 
  deleteItems 
} = useDirectusItems()

// Типизированный ответ от Directus
interface DirectusResponse<T> {
  data: T[] | null
}

// Типы для более строгой типизации
interface WcontItem {
  id?: number | null
  art?: string
  name?: string
  description?: string
  tags?: string[]
  title?: string
  subtitle?: string
  excerpt?: string
  text?: string
  url?: string
  linkText?: string
  number?: number
  inscription?: string
  icon?: string
  product?: string
  category?: string
  additional?: string
  image?: string
  imageBackground?: string
  imageLogo?: string
  imageIcon?: string
  imageGallery?: string[]
  items?: WcontItem[]
}

// Состояние
const items = ref<WcontItem[]>([])
const loading = ref(true)
const viewMode = ref<ViewMode>('compact') // Определение viewMode
const sortField = ref('art')
const sortOrder = ref(1) // 1 - asc, -1 - desc
const globalFilterValue = ref('')
const selectedTags = ref<string[]>([])
const availableTags = ref<string[]>([])
const selectedItem = ref<WcontItem | null>(null)
const sidebarVisible = ref(false)
const sidebarCollapsed = ref(false)
const editingItem = ref<WcontItem>({})
const expandedRows = ref({})
const fullTextDialogVisible = ref(false)
const selectedFullTextItem = ref<WcontItem | null>(null)
const fullTextDialogTitle = ref('')
const fullTextDialogSubtitle = ref('')
const fullText = ref('')
const fullTextDialogItems = ref<WcontItem[]>([])
const itemsDialogVisible = ref(false)
const currentItems = ref<WcontItem[]>([])
const galleryDialogVisible = ref(false)
const currentGallery = ref<string[]>([])
const sidebarTitle = ref('Редактирование записи')
const deleteItemConfirmation = ref(false)
const itemToDelete = ref<WcontItem | null>(null)

const globalFilterFields: (keyof WcontItem)[] = [
  'art', 
  'name', 
  'description', 
  'tags', 
  'title', 
  'subtitle', 
  'excerpt', 
  'text',
  'url',
  'linkText',
  'number',
  'inscription',
  'icon',
  'product',
  'category',
  'additional'
]

// Создаем слоты для DataTable
const slots: CustomDataTableSlots = {
  body_art: ({ data }) => h('div', { class: 'flex items-center gap-2' }, [
    h('span', data.art),
    h(Button, { 
      label: '⿻', 
      class: 'p-button-text p-button-sm', 
      onClick: () => copyToClipboard(data.art) 
    })
  ]),
  body_name: ({ data }) => h('div', { class: 'flex items-center gap-2' }, [
    h('a', { 
      href: `/wcont/${encodeURIComponent(data.name || '')}`, 
      class: 'text-primary hover:underline' 
    }, data.name),
    h(Button, { 
      label: '⿻', 
      class: 'p-button-text p-button-sm', 
      onClick: () => copyToClipboard(data.name) 
    })
  ]),
  body_description: ({ data }) => h('div', { class: 'flex items-center gap-2' }, [
    h('span', truncateText(data.description)),
    h(Button, { 
      label: '⿻', 
      class: 'p-button-text p-button-sm', 
      onClick: () => copyToClipboard(data.description) 
    }),
    h(Button, { 
      label: '...', 
      class: 'p-button-text p-button-sm', 
      onClick: () => openFullTextDialog(data) 
    })
  ]),
  body_tags: ({ data }) => h('div', { class: 'flex flex-wrap gap-1' }, [
    ...(data.tags || []).map(tag => h(Tag, { 
      value: tag, 
      class: 'mr-1', 
      onClick: () => addTagToFilter(tag) 
    })),
    data.tags?.length ? h(Button, { 
      label: '⿻', 
      class: 'p-button-text p-button-sm', 
      onClick: () => copyToClipboard(data.tags?.join(', ')) 
    }) : null
  ])
}

// Методы
function truncateText(text?: string, maxLength = 255): string {
  return text && text.length > maxLength 
    ? text.substring(0, maxLength) + '...' 
    : text || '-'
}

function copyToClipboard(text: string | undefined): void {
  if (!text) return

  navigator.clipboard.writeText(text)
    .then(() => {
      toast.add({ 
        severity: 'success', 
        summary: 'Копирование', 
        detail: 'Текст скопирован в буфер обмена', 
        life: 2000 
      })
    })
    .catch(err => {
      console.error('Ошибка копирования:', err)
      toast.add({ 
        severity: 'error', 
        summary: 'Ошибка', 
        detail: 'Не удалось скопировать текст', 
        life: 2000 
      })
    })
}

function addTagToFilter(tag: string): void {
  if (!selectedTags.value.includes(tag)) {
    selectedTags.value.push(tag)
    applyTagsFilter()
  }
}

function applyTagsFilter(): void {
  filteredItems.value = items.value.filter(item => 
    selectedTags.value.length === 0 || 
    selectedTags.value.some(tag => item.tags?.includes(tag))
  )
}

function openFullTextDialog(item: WcontItem): void {
  selectedFullTextItem.value = item
  fullTextDialogVisible.value = true
}

function openNestedItemFullTextDialog(item: WcontItem): void {
  selectedFullTextItem.value = item
  fullTextDialogVisible.value = true
}

async function fetchItems(): Promise<void> {
  loading.value = true
  try {
    const { getItems } = useDirectusItems()
    const response = await getItems({
      collection: 'wcont', 
      params: {
        fields: ['*'], 
        sort: ['art']
      }
    })
    
    // Проверка и установка данных
    if (Array.isArray(response)) {
      items.value = response
    } else if (response && typeof response === 'object' && 'data' in response) {
      items.value = response.data
    } else {
      console.error('Нет данных в ответе:', response)
      items.value = []
    }
    
    // Генерация тегов
    const tags = new Set<string>()
    items.value.forEach(item => {
      if (item.tags && Array.isArray(item.tags)) {
        item.tags.forEach(tag => tags.add(tag))
      }
    })
    availableTags.value = Array.from(tags)
    
    // Обновляем отфильтрованные элементы
    filteredItems.value = [...items.value]
    
  } catch (error) {
    console.error('Ошибка при загрузке данных:', error)
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось загрузить данные', life: 3000 })
    items.value = []
    filteredItems.value = []
  } finally {
    loading.value = false
  }
}

function onGlobalFilterChange(): void {
  const filterValue = globalFilterValue.value.toLowerCase()
  filteredItems.value = items.value.filter(item => 
    globalFilterFields.some(field => {
      const value = item[field]
      return value && String(value).toLowerCase().includes(filterValue)
    })
  )
}

function createEmptyWcontItem(): WcontItem {
  return {
    id: undefined,
    art: '',
    name: '',
    description: '',
    tags: [],
    subtitle: '',
    text: '',
    image: '',
    items: []
  }
}

function openEditSidebar(item?: WcontItem): void {
  editingItem.value = item ? { ...item } : createEmptyWcontItem()
  sidebarVisible.value = true
}

async function saveItem(item?: WcontItem): Promise<void> {
  try {
    loading.value = true;
    
    // Если передан item, используем его, иначе берем editingItem
    const itemToSave = item || editingItem.value;
    
    if (!itemToSave || !itemToSave.id) {
      throw new Error('Некорректные данные для сохранения');
    }
    
    console.log('🔄 Сохраняем:', itemToSave);
    
    const { updateItem } = useDirectusItems();
    
    // Обновляем в API
    await updateItem({
      collection: 'wcont',
      id: itemToSave.id,
      item: itemToSave
    });
    
    // Обновляем локальный массив с принудительным обновлением реактивности
    const index = items.value.findIndex(i => i.id === itemToSave.id);
    if (index !== -1) {
      items.value[index] = { ...itemToSave };
      
      // Обновляем также filteredItems
      const filteredIndex = filteredItems.value.findIndex(i => i.id === itemToSave.id);
      if (filteredIndex !== -1) {
        filteredItems.value[filteredIndex] = { ...itemToSave };
      }
      
      console.log('✅ Локальный массив обновлен');
    }
    
    toast.add({ 
      severity: 'success', 
      summary: 'Успех', 
      detail: 'Запись успешно обновлена', 
      life: 3000 
    });
    
    // Закрываем сайдбар
    sidebarVisible.value = false;
  } catch (error) {
    console.error('❌ Ошибка при сохранении:', error);
    toast.add({ 
      severity: 'error', 
      summary: 'Ошибка', 
      detail: 'Не удалось сохранить запись', 
      life: 3000 
    });
  } finally {
    loading.value = false;
  }
}

const onRowSelect = (event: { data: WcontItem }): void => {
  console.log('Выбрана строка:', event.data)
}

const onRowUnselect = (event: { data: WcontItem }): void => {
  console.log('Отменён выбор строки:', event.data)
}

const onRowExpand = (event: { data: WcontItem }): void => {
  console.log('Строка развёрнута:', event.data)
}

const onRowCollapse = (event: { data: WcontItem }): void => {
  console.log('Строка свёрнута:', event.data)
}

async function duplicateItem(item: WcontItem): Promise<void> {
  try {
    loading.value = true
    const { createItems } = useDirectusItems()
    
    const newItem: WcontItem = { ...item }
    delete newItem.id
    newItem.art = `${newItem.art || ''}-copy`
    newItem.name = `${newItem.name || ''} (копия)`
    
    const result = await createItems({
      collection: 'wcont',
      items: [newItem]
    })
    
    if (result && Array.isArray(result) && result.length > 0) {
      items.value.push(result[0])
      toast.add({ 
        severity: 'success', 
        summary: 'Успех', 
        detail: 'Запись успешно дублирована', 
        life: 3000 
      })
    }
  } catch (error) {
    console.error('Ошибка при дублировании записи:', error)
    toast.add({ 
      severity: 'error', 
      summary: 'Ошибка', 
      detail: 'Не удалось дублировать запись', 
      life: 3000 
    })
  } finally {
    loading.value = false
  }
}

async function convertToWJson(item: WcontItem): Promise<void> {
  try {
    loading.value = true
    const { createItems } = useDirectusItems()
    
    const jsonData = {
      title: item.title,
      subtitle: item.subtitle,
      excerpt: item.excerpt,
      image: item.image,
      linkText: item.linkText,
      text: item.text,
      items: item.items || []
    }

    const result = await createItems({
      collection: 'wjson',
      items: [{ title: item.name, json: jsonData }]
    })
    
    if (result && Array.isArray(result) && result.length > 0) {
      toast.add({ 
        severity: 'success', 
        summary: 'Успех', 
        detail: 'Запись успешно конвертирована в wjson', 
        life: 3000 
      })
    }
  } catch (error) {
    console.error('Ошибка при конвертировании записи:', error)
    toast.add({ 
      severity: 'error', 
      summary: 'Ошибка', 
      detail: 'Не удалось конвертировать запись', 
      life: 3000 
    })
  } finally {
    loading.value = false
  }
}

function confirmItemDeletion(item: WcontItem): void {
  itemToDelete.value = item
  deleteItemConfirmation.value = true
}

function toggleViewMode(): void {
  viewMode.value = viewMode.value === 'compact' ? 'full' : 'compact'
}

const filteredItems = ref<WcontItem[]>([])

onMounted(fetchItems)
</script>
