<template>
  <div class="space-y-4">
    <div v-for="(item, index) in items" :key="index" class="border p-4 rounded">
      <div class="flex justify-between mb-2">
        <h3 class="text-lg font-medium">Элемент {{ index + 1 }}</h3>
        <Button icon="❌" class="p-button-danger p-button-sm" @click="removeItem(index)" />
      </div>
      
      <div class="grid grid-cols-1 gap-4">
        <div class="field">
          <label class="block mb-1">Title</label>
          <InputText v-model="items[index].title" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Subtitle</label>
          <InputText v-model="items[index].subtitle" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Excerpt</label>
          <Textarea v-model="items[index].excerpt" rows="2" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Text</label>
          <Textarea v-model="items[index].text" rows="3" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">URL</label>
          <InputText v-model="items[index].url" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Link Text</label>
          <InputText v-model="items[index].linkText" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Number</label>
          <InputNumber v-model="items[index].number" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Inscription</label>
          <InputText v-model="items[index].inscription" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Icon</label>
          <InputText v-model="items[index].icon" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Product</label>
          <InputText v-model="items[index].product" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Category</label>
          <InputText v-model="items[index].category" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Additional</label>
          <InputText v-model="items[index].additional" class="w-full" />
        </div>
        
        <div class="field">
          <label class="block mb-1">Image URL</label>
          <InputText v-model="items[index].image" class="w-full" />
          <img v-if="items[index].image" :src="items[index].image" class="mt-2 w-full h-32 object-cover rounded" >
        </div>
        
        <div class="field">
          <label class="block mb-1">Background Image URL</label>
          <InputText v-model="items[index].imageBackground" class="w-full" />
          <img v-if="items[index].imageBackground" :src="items[index].imageBackground" class="mt-2 w-full h-32 object-cover rounded" >
        </div>
        
        <div class="field">
          <label class="block mb-1">Logo Image URL</label>
          <InputText v-model="items[index].imageLogo" class="w-full" />
          <img v-if="items[index].imageLogo" :src="items[index].imageLogo" class="mt-2 w-full h-32 object-cover rounded" >
        </div>
        
        <div class="field">
          <label class="block mb-1">Icon Image URL</label>
          <InputText v-model="items[index].imageIcon" class="w-full" />
          <img v-if="items[index].imageIcon" :src="items[index].imageIcon" class="mt-2 w-full h-32 object-cover rounded" >
        </div>
        
        <div class="field">
          <label class="block mb-1">Nested Items</label>
          <ItemsEditor v-model="items[index].nestedItems" />
        </div>
      </div>
    </div>
    
    <Button label="Добавить элемент" icon="➕" class="p-button-outlined" @click="addItem" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import InputNumber from 'primevue/inputnumber';
import Textarea from 'primevue/textarea';
import type { WcontItem } from '~/types/wconts';

const props = withDefaults(defineProps<{
  modelValue?: WcontItem[]
}>(), {
  modelValue: () => []
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: WcontItem[]): void
}>()

const items = ref<WcontItem[]>(props.modelValue);

const addItem = () => {
  items.value.push({
    title: '',
    subtitle: '',
    excerpt: '',
    text: '',
    url: '',
    linkText: '',
    number: null,
    inscription: '',
    icon: '',
    product: '',
    category: '',
    additional: '',
    image: '',
    imageBackground: '',
    imageLogo: '',
    imageIcon: '',
    imageGallery: [],
    nestedItems: []
  });
  emit('update:modelValue', items.value);
};

const removeItem = (index: number) => {
  items.value.splice(index, 1);
  emit('update:modelValue', items.value);
};

watch(() => props.modelValue, (newVal) => {
  items.value = newVal || [];
}, { deep: true });
</script>

<style scoped>
.field {
  margin-bottom: 1rem;
}

:deep(.p-inputtext),
:deep(.p-inputnumber),
:deep(.p-textarea) {
  font-size: 13px;
}
</style>