﻿import { defineEventHandler, readBody, createError } from 'h3'
import puppeteer from 'puppeteer'
import fs from 'fs/promises'
import path from 'path'
import os from 'os'

interface BlockItem {
  html: string
  css: string
  js: string
  filename: string
}

interface UnifiedScreenshotRequest {
  blocks: BlockItem[]
  sourceFilename: string
}

interface ScreenshotResult {
  fileId: string
  filename: string
  success: boolean
}

interface UnifiedScreenshotResponse {
  results: ScreenshotResult[]
  successCount: number
  totalTime: number
}

export default defineEventHandler(async (event) => {
  const { blocks, sourceFilename }: UnifiedScreenshotRequest = await readBody(event)

  if (!blocks || !Array.isArray(blocks) || blocks.length === 0) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Blocks array is required and must not be empty'
    })
  }

  console.log(`📸 Создание унифицированных скриншотов для ${blocks.length} блоков из файла ${sourceFilename}...`)
  const startTime = Date.now()

  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  })

  try {
    // НОВЫЙ ПОДХОД: Создаем индивидуальные скриншоты каждого блока
    // Используем ту же логику, что работает для страниц (98% успех)
    const results: ScreenshotResult[] = []

    for (let i = 0; i < blocks.length; i++) {
      const block = blocks[i]

      try {
        console.log(`📸 Создание индивидуального скриншота блока ${i + 1}/${blocks.length}: ${block.filename}`)

        // Создаем новую страницу для каждого блока (как в capture-html-screenshot)
        const page = await browser.newPage()
        await page.setViewport({ width: 1400, height: 800 })

        // Создаем полный HTML документ для этого блока (как для страниц)
        const blockFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Block ${i + 1} - ${block.filename}</title>
  ${block.css || ''}
</head>
<body>
  ${block.html || ''}
  ${block.js || ''}
</body>
</html>`

        console.log(`📄 Блок ${i + 1}: HTML документ создан, размер ${blockFullHtml.length} символов`)

        // Загружаем HTML контент с максимальным ожиданием (как в capture-html-screenshot)
        await page.setContent(blockFullHtml, { waitUntil: 'networkidle0', timeout: 60000 })

        console.log(`📄 Блок ${i + 1}: HTML контент загружен`)

        // Оптимизированная базовая задержка для полной загрузки контента
        console.log(`⏱️ Блок ${i + 1}: базовая задержка 3000ms для полной загрузки контента...`)
        await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))

        // Ждем загрузки всех изображений с увеличенным timeout
        console.log(`🖼️ Блок ${i + 1}: Ожидание загрузки всех изображений с timeout 10000ms...`)
        await page.evaluate(() => {
          return Promise.all(
            Array.from(document.images)
              .filter(img => !img.complete)
              .map(img => new Promise(resolve => {
                img.onload = img.onerror = resolve
                // Увеличенный timeout для изображений
                setTimeout(resolve, 10000)
              }))
          )
        })

        // УМНАЯ прокрутка для активации scroll-анимаций (имитация ручного процесса)
        console.log(`📜 Блок ${i + 1}: УМНАЯ прокрутка для активации scroll-анимаций...`)
        await page.evaluate(() => {
          return new Promise((resolve) => {
            // Имитируем ручной процесс: медленная прокрутка вниз, затем быстро вверх
            const scrollToBottom = () => {
              // Медленная прокрутка вниз по частям для активации всех scroll-триггеров
              let currentScroll = 0
              const maxScroll = document.body.scrollHeight
              const scrollStep = Math.max(200, maxScroll / 10) // Прокручиваем по частям

              const scrollInterval = setInterval(() => {
                currentScroll += scrollStep
                window.scrollTo(0, Math.min(currentScroll, maxScroll))

                if (currentScroll >= maxScroll) {
                  clearInterval(scrollInterval)
                  // Пауза внизу для активации анимаций
                  setTimeout(scrollToTop, 2000)
                }
              }, 300) // Медленная прокрутка для активации всех триггеров
            }

            // Быстрая прокрутка вверх
            const scrollToTop = () => {
              window.scrollTo(0, 0)
              setTimeout(resolve, 1500) // Пауза вверху для стабилизации
            }

            // Начинаем процесс
            setTimeout(scrollToBottom, 1000)
          })
        })

        // Анализ анимаций на странице (как в capture-html-screenshot)
        console.log(`🎭 Блок ${i + 1}: Анализ анимаций на странице...`)
        const animationInfo = await page.evaluate(() => {
          const info = {
            hasAnimations: false,
            hasJSAnimations: false,
            hasLibs: false,
            animationCount: 0,
            maxDuration: 0
          }

          // Проверяем CSS анимации
          const allElements = Array.from(document.querySelectorAll('*'))
          allElements.forEach(el => {
            const style = window.getComputedStyle(el)
            if (style.animationDuration !== '0s' || style.transitionDuration !== '0s') {
              info.hasAnimations = true
              info.animationCount++
              const animDuration = parseFloat(style.animationDuration) * 1000
              const transDuration = parseFloat(style.transitionDuration) * 1000
              info.maxDuration = Math.max(info.maxDuration, animDuration, transDuration)
            }
          })

          // Проверяем библиотеки анимаций
          info.hasLibs = !!(window.AOS || window.gsap || window.anime || window.ScrollMagic)

          // Проверяем JS анимации
          info.hasJSAnimations = !!(window.jQuery && window.jQuery.fn.animate) ||
            !!(window.requestAnimationFrame) ||
            document.querySelectorAll('[data-aos], [data-animate], .animate__animated').length > 0

          return info
        })

        // Определяем дополнительное время ожидания на основе анализа
        let additionalWaitTime = 0
        if (animationInfo.hasAnimations || animationInfo.hasJSAnimations) {
          if (animationInfo.maxDuration > 2000) {
            // Для длинных анимаций ждем дольше
            additionalWaitTime = Math.min(animationInfo.maxDuration + 1000, 5000)
          } else if (animationInfo.hasJSAnimations) {
            additionalWaitTime = 1500
          } else if (animationInfo.hasLibs) {
            // Для библиотек анимаций используем стандартную задержку
            additionalWaitTime = 1200
          } else {
            additionalWaitTime = 800
          }

          // Дополнительное время для множественных анимаций
          if (animationInfo.animationCount > 5) {
            additionalWaitTime += 300
          }
        }

        if (additionalWaitTime > 0) {
          console.log(`⏱️ Блок ${i + 1}: Дополнительное ожидание ${additionalWaitTime}ms для анимаций...`)
          await page.evaluate((delay: number) => new Promise(resolve => setTimeout(resolve, delay)), additionalWaitTime)
        }

        // Оптимизированная финальная стабилизация после всех операций
        console.log(`⏱️ Блок ${i + 1}: финальная стабилизация 2000ms...`)
        await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 2000)))

        // ДИАГНОСТИКА: Проверяем состояние страницы перед созданием скриншота
        console.log(`🔍 Блок ${i + 1}: ДИАГНОСТИКА: Анализ состояния страницы...`)
        const pageAnalysis = await page.evaluate(() => {
          const analysis = {
            totalImages: document.images.length,
            loadedImages: Array.from(document.images).filter(img => img.complete).length,
            failedImages: Array.from(document.images).filter(img => !img.complete).length,
            totalElements: document.querySelectorAll('*').length,
            visibleElements: Array.from(document.querySelectorAll('*')).filter(el => {
              const style = window.getComputedStyle(el)
              return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0'
            }).length,
            documentReadyState: document.readyState,
            bodyHeight: document.body.scrollHeight,
            viewportHeight: window.innerHeight,
            currentScrollPosition: window.pageYOffset
          }

          // Проверяем конкретные библиотеки
          analysis.libraries = {
            jquery: !!window.jQuery,
            aos: !!window.AOS,
            gsap: !!window.gsap,
            bootstrap: !!window.bootstrap || !!window.Bootstrap
          }

          return analysis
        })

        console.log(`📊 Блок ${i + 1}: ДИАГНОСТИКА РЕЗУЛЬТАТЫ:`)
        console.log(`   📷 Изображения: ${pageAnalysis.loadedImages}/${pageAnalysis.totalImages} загружено (${pageAnalysis.failedImages} не загружено)`)
        console.log(`   👁️ Элементы: ${pageAnalysis.visibleElements}/${pageAnalysis.totalElements} видимо (${Math.round(pageAnalysis.visibleElements / pageAnalysis.totalElements * 100)}%)`)
        console.log(`   📄 Состояние документа: ${pageAnalysis.documentReadyState}`)
        console.log(`   📏 Высота: ${pageAnalysis.bodyHeight}px, Viewport: ${pageAnalysis.viewportHeight}px, Scroll: ${pageAnalysis.currentScrollPosition}px`)
        console.log(`   📚 Библиотеки:`, pageAnalysis.libraries)
        console.log(`⏱️ Анимации - ${animationInfo.hasAnimations}, JS анимации - ${animationInfo.hasJSAnimations}, библиотеки - ${animationInfo.hasLibs}, количество - ${animationInfo.animationCount}, макс. длительность - ${animationInfo.maxDuration}ms`)

        // Создаем скриншот с правильной высотой
        console.log(`📸 Блок ${i + 1}: Создание скриншота с оптимальной высотой...`)

        // Сначала пробуем найти body элемент для правильной высоты
        const bodyElement = await page.$('body')
        let screenshot

        if (bodyElement) {
          const boundingBox = await bodyElement.boundingBox()
          if (boundingBox && boundingBox.width > 0 && boundingBox.height > 0) {
            console.log(`📏 Блок ${i + 1}: Используем размеры body: ${boundingBox.width}x${boundingBox.height}px`)
            screenshot = await bodyElement.screenshot({
              type: 'jpeg',
              quality: 90,
              omitBackground: false
            })
          } else {
            console.log(`📏 Блок ${i + 1}: Body имеет нулевые размеры, используем fullPage`)
            screenshot = await page.screenshot({
              type: 'jpeg',
              quality: 90,
              fullPage: true,
              omitBackground: false
            })
          }
        } else {
          console.log(`📏 Блок ${i + 1}: Body не найден, используем fullPage`)
          screenshot = await page.screenshot({
            type: 'jpeg',
            quality: 90,
            fullPage: true,
            omitBackground: false
          })
        }

        console.log(`📸 Блок ${i + 1}: скриншот создан, размер ${screenshot.length} байт`)

        // Закрываем страницу для освобождения памяти
        await page.close()

        // Загружаем в Directus
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
        const finalFilename = `${block.filename}_${timestamp}.jpg`

        const fileId = await uploadToDirectus(screenshot, finalFilename)

        results.push({
          fileId,
          filename: finalFilename,
          success: true
        })

        console.log(`✅ Блок ${i + 1} загружен в Directus: ${fileId}`)

      } catch (error) {
        console.error(`❌ Ошибка создания скриншота блока ${i + 1}:`, error)
        results.push({
          fileId: '',
          filename: block.filename,
          success: false
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const totalTime = Date.now() - startTime

    console.log(`✅ Унифицированные скриншоты завершены: ${successCount}/${blocks.length} успешно за ${totalTime}ms`)

    return {
      results,
      successCount,
      totalTime
    } as UnifiedScreenshotResponse

  } catch (error) {
    console.error('❌ Ошибка создания унифицированных скриншотов:', error)
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to create unified screenshots'
    })
  } finally {
    // Закрываем браузер
    await browser.close()
  }
})

// Функция загрузки в Directus
async function uploadToDirectus(screenshot: Buffer, filename: string): Promise<string> {
  const formData = new FormData()
  formData.append('title', filename)

  const blob = new Blob([screenshot], { type: 'image/jpeg' })
  formData.append('file', blob, filename)

  const response = await fetch('http://localhost:8055/files', {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error(`Failed to upload to Directus: ${response.statusText}`)
  }

  const data = await response.json()
  return data.data.id
}
