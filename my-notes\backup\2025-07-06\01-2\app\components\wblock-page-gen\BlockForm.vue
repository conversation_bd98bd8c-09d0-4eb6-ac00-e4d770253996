<template>
  <div class="p-1 h-full flex flex-col">
    <div class="flex-1 overflow-auto">
      <div class="space-y-1">
        <div class="flex gap-1 mb-0">
          <div class="field w-1/4 mb-0" style="margin-bottom: 0">
            <InputText
              id="number"
              v-model="formData.number"
              placeholder="Номер"
              class="w-full text-xs"
              style="padding: 4px; font-size: 8px"
            />
          </div>
          <div class="field w-3/4 mb-0" style="margin-bottom: 0">
            <InputText
              id="title"
              v-model="formData.title"
              placeholder="Название"
              class="w-full text-xs"
              required
              style="padding: 4px; font-size: 10px"
            />
          </div>
        </div>

        <div class="field mb-0" style="margin-bottom: 0">
          <Textarea
            id="description"
            v-model="formData.description"
            rows="2"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Описание"
            style="padding: 2px; font-size: 9px"
          />
        </div>

        <div class="field mb-0" style="margin-top: 0;margin-bottom: 0">
          <Textarea
            id="composition"
            v-model="formData.composition"
            rows="3"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Композиция"
            style="padding: 2px; font-size: 8px"
          />
        </div>

        <div class="field mb-0" style="margin-top: 0">
          <MultiSelect
            v-model="formData.block_type"
            :options="blockTypeOptions"
            placeholder="Выберите типы блока"
            display="chip"
            class="text-xs w-full p-0"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="flex gap-1">
          <div class="field w-1/3 mb-0" style="margin-bottom: 0">
            <Dropdown
              id="status"
              v-model="formData.status"
              :options="statusOptions"
              option-label="label"
              option-value="value"
              placeholder="Выберите статус"
              class="w-full text-xs"
              style="font-size: 11px"
            />
          </div>

          <div class="field w-2/3 mb-0" style="margin-bottom: 0">
            <MultiSelect
              v-model="formData.concept"
              :options="conceptOptions"
              placeholder="Выберите концепции"
              display="chip"
              class="w-full text-xs"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>
        <div class="flex gap-2">
          <div class="field w-1/2 mb-0" style="margin-bottom: 0">
            <MultiSelect
            v-model="formData.collection"
            :options="collectionOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите коллекции"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
          </div>

          <div class="field w-1/2 mb-0" style="margin-bottom: 0">
            <MultiSelect
              v-model="formData.style"
              :options="styleOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите стили"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>
       

        <div class="field mb-2">
          <MultiSelect
            v-model="formData.layout"
            :options="layoutOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите макеты"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="formData.elements"
            :options="elementOptions"
            placeholder="Выберите типы элементов"
            display="chip"
            class="w-full text-xs"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>
       
         
          <div class="field mb-2">
            <MultiSelect
              v-model="formData.graphics"
              :options="graphicsOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите графику"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-0">
          <MultiSelect
            v-model="formData.features"
            :options="featuresOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите особенности"
            panel-class="text-xs"
            style="font-size: 11px; padding: 0"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },              
            }"
          />
        </div>
        

        <div class="field mb-0">
          <div class="flex gap-2">
            <Image
              v-if="formData.sketch"
              :src="`http://localhost:8055/assets/${formData.sketch}`"
              alt="Эскиз"
              width="200"
              class="my"
              preview
            />
            <FileUpload
              mode="basic"
              :auto="true"
              accept="image/*"
              :max-file-size="1000000"
              choose-label="Эскиз"
              class="p-button-sm"
              @select="onSketchSelect"
            />
          </div>
        </div>

        <div class="field mb-0">
          <TabView
            class="text-xs"
            :pt="{
            panelcontainer: { style: 'padding:0' },
          }"
          >
            <TabPanel
              header="HTML/CSS/JS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <div class="space-y-1">
                <PrismEditorWithCopy
                  v-model="formData.html"
                  class="my-editor h-[180px] overflow-auto text-xs max-h-[180px]"
                  :highlight="highlightHtml"
                  placeholder="Введите HTML код"
                  line-numbers
                />
                <div class="flex gap-2">
                  <PrismEditorWithCopy
                    v-model="formData.css"
                    class="my-editor h-[40px] overflow-auto text-xs max-h-[40px]"
                    :highlight="highlightCss"
                    placeholder="CSS код"
                    line-numbers
                  />
                  <PrismEditorWithCopy
                    v-model="formData.js"
                    class="my-editor h-[40px] overflow-auto text-xs max-h-[40px]"
                    :highlight="highlightJs"
                    placeholder="JS код"
                    line-numbers
                  />
                </div>
              </div>
            </TabPanel>
            <TabPanel
              header="HBS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="formData.hbs"
                class="my-editor h-[220px] overflow-auto text-xs max-h-[220px]"
                :highlight="highlightHtml"
                placeholder="Введите HBS код"
                line-numbers
              />
            </TabPanel>
            <TabPanel
              header="JSON"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="formData.json"
                class="my-editor h-[220px] overflow-auto text-xs max-h-[220px]"
                :highlight="highlightJson"
                placeholder="Введите JSON код"
                line-numbers
              />
            </TabPanel>
          </TabView>
        </div>

        <div class="field mb-2">
          <Textarea
            id="notes"
            v-model="formData.notes"
            rows="1"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Заметки"
            style="padding: 2px; font-size: 9px"
          />
        </div>
      </div>
    </div>

    <div class="flex justify-end gap-2 mt-0">
      <Button
        label="Отмена"
        icon="pi pi-times"
        class="p-button-sm"
        @click="$emit('cancel')"
      />
      <Button
        label="Сохранить"
        icon="pi pi-check"
        class="p-button-sm"
        :loading="saving"
        @click="handleSave"
      />
    </div>
  </div>
</template>




<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Dropdown from 'primevue/dropdown'
import MultiSelect from 'primevue/multiselect'
import FileUpload from 'primevue/fileupload'
import Image from 'primevue/image'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import 'vue-prism-editor/dist/prismeditor.min.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'

const props = defineProps({
  block: {
    type: Object,
    default: () => ({
      number: '',
      title: '',
      description: '',
      composition: '',
      status: '',
      block_type: [],
      concept: [],
      collection: [],
      layout: [],
      elements: [],
      style: [],
      graphics: [],
      features: [],
      sketch: null,
      html: '',
      css: '',
      js: '',
      hbs: '',
      json: '',
      notes: ''
    }),
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['save', 'cancel'])
const toast = useToast()

// Опции для выпадающих списков
const statusOptions = [
  { label: 'Идея', value: 'idea' },
  { label: 'В разработке', value: 'in_progress' },
  { label: 'Готово', value: 'done' },
  { label: 'Архив', value: 'archived' },
]

// Опции из Directus
const conceptOptions = ref<string[]>([])
const blockTypeOptions = ref<string[]>([])
const elementOptions = ref<string[]>([])
const layoutOptions = ref<string[]>([])
const styleOptions = ref<string[]>([])
const graphicsOptions = ref<string[]>([])
const collectionOptions = ref<string[]>([])
  const featuresOptions = ref([])

// Загрузка опций из коллекции wblock_proto
const loadOptions = async () => {
  try {
    const { getItems } = useDirectusItems()
    const items = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: [
          'concept',
          'block_type',
          'elements',
          'layout',
          'style',
          'graphics',
          'collection',
          'features',
        ],
      },
    })

    if (Array.isArray(items)) {
      const concepts = new Set()
      const blockTypes = new Set()
      const elements = new Set()
      const layouts = new Set()
      const styles = new Set()
      const graphics = new Set()
      const collections = new Set()
      const features = new Set()
      items.forEach((item) => {
        item.concept?.forEach((c) => concepts.add(c))
        item.block_type?.forEach((t) => blockTypes.add(t))
        item.elements?.forEach((e) => elements.add(e))
        item.layout?.forEach((l) => layouts.add(l))
        item.style?.forEach((s) => styles.add(s))
        item.graphics?.forEach((g) => graphics.add(g))
        item.collection?.forEach((c) => collections.add(c))
        item.features?.forEach((f) => features.add(f))
      })

      conceptOptions.value = Array.from(concepts)
      blockTypeOptions.value = Array.from(blockTypes)
      elementOptions.value = Array.from(elements)
      layoutOptions.value = Array.from(layouts)
      styleOptions.value = Array.from(styles)
      graphicsOptions.value = Array.from(graphics)
      collectionOptions.value = Array.from(collections)
      featuresOptions.value = Array.from(features)
    }
  } catch (error) {
    console.error('Error loading options:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить справочники',
    })
  }
}

// Данные формы
const formData = ref({
  ...props.block,
})

// Loading state
const saving = ref(false)

// Syntax highlighting functions
const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightHbs = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

// File upload handler
const onSketchSelect = (event: any) => {
  const file = event.files[0]
  if (file) {
    // Handle file upload logic here
    console.log('File selected:', file)
  }
}

// Save function
const saveBlock = () => {
  emit('save', { ...formData.value })
}

const handleSave = () => {
  emit('save', { ...formData.value })
}

// Watch for prop changes
watch(() => props.block, (newBlock) => {
  if (newBlock && Object.keys(newBlock).length > 0) {
    formData.value = { ...newBlock }
  }
}, { immediate: true, deep: true })

onMounted(async () => {
  await loadOptions()
  if (props.block && Object.keys(props.block).length > 0) {
    formData.value = { ...props.block }
  }
})
</script>

<style scoped>
.my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 8px;
    line-height: 1.2;
    padding: 1px;
  }

.my-editor .prism-editor__textarea:focus {
  outline: none;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: var(--text-color);
}

.block-form-tabs :deep(.p-tabview-panels) {
  padding: 0;
  height: calc(100% - 60px);
  overflow: auto;
}

.block-form-tabs :deep(.p-tabview-panel) {
  height: 100%;
}
.my {
    max-height: 120px;
  }
  
  .my img {
    object-fit: contain;
  }
</style>
