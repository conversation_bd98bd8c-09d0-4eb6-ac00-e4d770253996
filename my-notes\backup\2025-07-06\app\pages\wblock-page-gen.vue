<template>
  <div class="flex flex-col gap-4 p-1">
    <div class="flex h-[calc(100vh-1rem)]">
      <!-- Левый сайдбар с DataTable для wblock_proto -->
      <div class="flex" :style="{ width: leftSidebarWidth }">
        <div class="flex flex-col w-full bg-surface-50 dark:bg-surface-800 border-r border-surface-200 dark:border-surface-700">

          <!-- Toolbar для блоков -->
          <div class="structure-toolbar toolbar-container flex items-center p-1 border-b border-surface-200 dark:border-surface-700 bg-white">
            <div class="flex items-center gap-1">
              <!-- Кнопка обновления -->
              <Button
                v-tooltip.top="'Обновить данные'"
                icon="pi pi-refresh"
                class="p-button-sm p-button-text"
                @click="loadOptions"
              />

              <!-- Поиск с выпадающим окном -->
              <div class="relative">
                <Button
                  icon="pi pi-search"
                  class="p-button-sm p-button-text"
                  data-search-button
                  @click="toggleBlockSearch"
                />
                <div v-if="showBlockSearch" class="search-dropdown">
                  <InputText
                    v-model="blockProtoSearch"
                    placeholder="Поиск блоков..."
                    class="w-full text-xs"
                    style="font-size: 12px"
                  />
                </div>
              </div>

              <!-- Фильтры с выпадающим MultiSelect -->
              <div class="relative">
                <Button
                  icon="pi pi-filter"
                  class="p-button-sm p-button-text"
                  data-filters-button
                  @click="toggleBlockFilters"
                />
                <div v-if="showBlockFilters" class="filters-dropdown">
                  <MultiSelect
                    v-model="blockProtoSelectedTags"
                    :options="blockProtoAvailableTags"
                    filter
                    placeholder="Типы блоков"
                    class="w-48 text-xs"
                    display="chip"
                    panel-class="text-xs"
                    :pt="{
                      item: { class: 'text-xs' },
                      header: { class: 'text-xs' },
                    }"
                  />
                </div>
              </div>
            </div>

            <!-- Действия -->
            <div class="flex items-center gap-1">
              <Button
                v-tooltip.top="'Добавить блок'"
                icon="pi pi-plus"
                class="p-button-sm p-button-text"
                @click="openBlockForm()"
              />
              <Button
                v-tooltip.top="'Редактировать'"
                icon="pi pi-pencil"
                class="p-button-sm p-button-text"
                :disabled="!blockProtoSelectedItem"
                @click="openBlockForm(blockProtoSelectedItem)"
              />
              <Button
                v-tooltip.top="'Дублировать'"
                icon="pi pi-copy"
                class="p-button-sm p-button-text"
                :disabled="!blockProtoSelectedItem"
                @click="duplicateBlockProto(blockProtoSelectedItem)"
              />
              <Button
                v-tooltip.top="'Удалить'"
                icon="pi pi-trash"
                class="p-button-sm p-button-text p-button-danger"
                :disabled="!blockProtoSelectedItem"
                @click="deleteBlockProto(blockProtoSelectedItem)"
              />
            </div>
          </div>

          <!-- DataTable для wblock_proto -->
          <div class="flex-1 overflow-hidden">
            <DataTable
              v-model:selection="blockProtoSelectedItem"
              :value="filteredBlockProtoData"
              scrollable
              scroll-height="flex"
              :virtual-scroller-options="{ itemSize: 32 }"
              size="small"
              class="text-xxs h-full"

              striped-rows
              style="--highlight-bg: var(--primary-50); padding: 1px; font-size: 11px"
              sort-field="number"
              :sort-order="1"
              responsive-layout="scroll"
              selection-mode="single"
              @row-click="onBlockProtoRowClick"
              @row-dblclick="(event) => openBlockForm(event.data)"
            >
              <Column field="number" header="№" :sortable="true" style="font-size: 9px; padding: 1px; width: 60px" />
              <Column field="title" header="Название" :sortable="true" style="font-size: 9.5px; padding: 1px; width: 150px"/>

              <Column field="sketch" header="Эскиз" style="padding: 1px; width: 80px">
                <template #body="{ data }">
                  <Image
                    v-if="data.sketch"
                    :src="`http://localhost:8055/assets/${data.sketch}`"
                    alt="Эскиз"
                    width="60"
                    class="my"
                    preview
                  />
                </template>
              </Column>

              <Column header="+" style="padding: 1px; width: 40px">
                <template #body="{ data }">
                  <Button
                    v-tooltip.top="'Добавить на холст'"
                    icon="pi pi-plus"
                    style="padding: 1px"
                    text
                    severity="secondary"
                    class="p-button p-component p-button-icon-only p-button-secondary p-button-text p-button-sm"
                    @click="addBlockProtoToCanvas(data)"
                  />
                </template>
              </Column>

            </DataTable>
          </div>
        </div>
      </div>

      <!-- Форма редактирования блока между левым сайдбаром и центром -->
      <div
        v-if="isBlockFormVisible"
        class="flex flex-col bg-surface-50 dark:bg-surface-800 border-r border-surface-200 dark:border-surface-700 h-full"
        :style="{
          width: blockSidebarWidth,
          minWidth: '350px',
          maxWidth: '450px'
        }"
      >
        <div
          class="flex justify-between items-center p-2 border-b border-surface-200 dark:border-surface-700 bg-white flex-shrink-0"
        >
          <h3 class="text-sm font-semibold">
            {{ selectedBlockProto ? 'Редактировать блок' : 'Добавить блок' }}
          </h3>
          <Button
            icon="pi pi-times"
            text
            class="p-button-sm"
            @click="closeBlockForm"
          />
        </div>
        <div class="flex-1 overflow-hidden">
          <BlockForm
            :block="selectedBlockProto || {}"
            :is-edit="!!selectedBlockProto"
            @save="handleBlockFormSave"
            @cancel="closeBlockForm"
          />
        </div>
      </div>

      <!-- Область предпросмотра и кода -->
      <div class="flex flex-col flex-1 p-1" :style="{ width: canvasWidth }">


        <!-- Область предпросмотра страницы -->
        <div class="flex-grow p-0 bg-surface-0 dark:bg-surface-900">
          <!-- Header с полями и viewport переключениями -->
          <div class="flex items-center gap-1 pb-0 pt-0">
            <div class="flex items-center gap-1 flex-grow text-xs">
              <InputText
                  id="number"
                  v-model="pageInfo.number"
                  placeholder="Номер"
                  class="mr-1 w-20 text-xs [&>input]:text-xs"
                  style="font-size: 11px"
                />
              <InputText v-model="pageInfo.title" placeholder="Название страницы" class="mr-1 text-xs [&>input]:text-xs flex-grow" style="font-size: 12px" />
              <MultiSelect
                v-model="pageInfo.tags"
                :options="pageTagsOptions"
                display="chip"
                class="text-xs mr-1 w-32"
                filter
                placeholder="Теги"
                panel-class="text-xs"
                :pt="{
                    item: { class: 'text-xs' },
                    header: { class: 'text-xs' },
                  }"
              />
              <MultiSelect
                v-model="pageInfo.page_type"
                :options="pageTypeOptions"
                display="chip"
                class="text-xs mr-1 w-32"
                filter
                placeholder="Тип"
                panel-class="text-xs"
                :pt="{
                    item: { class: 'text-xs' },
                    header: { class: 'text-xs' },
                  }"
              />
            </div>

            <!-- Device Switcher -->
            <div class="flex items-center gap-1">
              <Button
                v-for="device in devices"
                :key="device.name"
                :icon="device.icon"
                :class="{
                  'bg-primary-100 dark:bg-primary-900':
                    activeDevice === device.name,
                }"
                text
                rounded
                size="small"
                @click="activeDevice = device.name"
              />
            </div>

            <!-- Action buttons -->
            <div class="flex items-center gap-1">
              <Button class="text-xs" icon="pi pi-download"  @click="downloadPage" />
              <Button class="text-xs" icon="pi pi-eye"  @click="previewPage" />
              <Button v-tooltip.bottom="'Страницу'" class="text-xs" icon="pi pi-save" @click="savePage" />
              <Button v-tooltip.bottom="'Блоки'" class="text-xs" icon="pi pi-save" @click="saveWblockItems" />
              <Button v-tooltip.bottom="'Страницу и блоки'" class="text-xs" icon="pi pi-save" @click="savePageAndBlocks" />
            </div>
          </div>

          <!-- Preview area with fixed height -->
          <div class="flex justify-center" style="height: 650px; overflow: hidden;">
            <div
              v-if="renderedHtml"
              :class="{
                'mx-auto max-w-[375px] border rounded-lg': activeDevice === 'mobile',
                'mx-auto max-w-[768px] border rounded-lg': activeDevice === 'tablet',
                'mx-auto max-w-[1440px] border rounded-lg': activeDevice === 'desktop',
              }"
              style="width: 100%; height: 100%;">
              <iframe :srcdoc="getFullHtml()" class="w-full h-full border-0 rounded-lg" />
            </div>
            <div v-else class="flex items-center justify-center h-full text-surface-500">
              <div class="text-center">
                <i class="pi pi-file-o text-4xl mb-2"/>
                <p>Добавьте блоки для создания страницы</p>
              </div>
            </div>
          </div>

        </div>

        <!-- Панель с кодом -->
        <div class="border rounded-lg bg-surface-0 dark:bg-surface-900 mt-1" style="height: 250px;">
          <TabView
          class="p-0 h-full"
          :pt="{
              panelcontainer: { style: 'padding:0; height: calc(100% - 40px);' },
            }"
          >
            <TabPanel
            header="HBS/JSON"
            value="hbs-json"
            :pt="{
              header: { class: 'p-0' },
              headerAction: { class: 'text-xs p-0' },
              content: { class: 'p-0' }
            }"
            >
              <div class="p-0 h-100">
                <!-- Header with reset button -->
                <div class="flex justify-between items-center mb-2">

                  <Button
                    v-if="isEditingManually"
                    icon="pi pi-refresh"
                    label="Сбросить"
                    size="small"
                    text
                    class="text-xs"
                    @click="resetManualEditing"
                  />
                </div>

                <!-- Groups container -->
                <div class="overflow-auto h-[200px] max-h-[200px]" >
                  <div v-if="!canvasGroups?.length" class="flex items-center justify-center h-full text-surface-500">
                    <div class="text-center">
                      <i class="pi pi-inbox text-2xl mb-2"/>
                      <p class="text-xs">Добавьте блоки в Canvas для редактирования</p>
                    </div>
                  </div>

                  <div v-else class="space-y-1">
                    <div
                      v-for="(group, index) in canvasGroups"
                      :key="`group-${index}`"
                      class="border rounded-lg p-0.5 bg-surface-50"
                    >
                      <div class="flex items-center justify-between mb-1">
                        <h4 class="text-xs font-medium">
                          {{ index + 1 }}:
                          <span class="text-primary">{{ group.whbs?.title || 'HBS' }}</span>
                          <span v-if="group.wjson"> + </span>
                          <span v-if="group.wjson" class="text-blue-600">{{ group.wjson?.title || 'JSON' }}</span>
                        </h4>
                      </div>

                      <div class="grid grid-cols-2 gap-2">
                        <!-- HBS Editor -->
                        <div class="flex flex-col">

                          <PrismEditorWithCopy
                            :model-value="getGroupHbs(index)"
                            class="my-editor overflow-auto"
                            style="height: 100px;"
                            :highlight="highlightHtml"
                            placeholder="Handlebars шаблон"
                            @update:model-value="(value) => updateGroupHbs(index, value)"
                          />
                        </div>

                        <!-- JSON Editor -->
                        <div class="flex flex-col">

                          <PrismEditorWithCopy
                            :model-value="getGroupJson(index)"
                            class="my-editor overflow-auto"
                            style="height: 100px;"
                            :highlight="highlightJson"
                            placeholder="JSON данные"
                            @update:model-value="(value) => updateGroupJson(index, value)"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabPanel>
            <TabPanel
            header="HTML"
            value="html"
            :pt="{
              header: { class: 'p-0' },
              headerAction: { class: 'text-xs p-0' },
              content: { class: 'p-0' }
            }"
            >
              <PrismEditorWithCopy
                v-model="renderedHtml"
                class="w-full text-xs p-2 border rounded my-editor h-[180px] overflow-auto max-h-[180px]"
                :highlight="highlightHtml"
                placeholder="Сгенерированный HTML"
                line-numbers
                readonly
              />
            </TabPanel>
            <TabPanel
            header="CSS/JS"
            value="css-js"
            :pt="{
              header: { class: 'p-0' },
              headerAction: { class: 'text-xs p-0' },
              content: { class: 'p-0' }
            }"
            >
              <div class="p-0 grid grid-cols-2 gap-4">
                <div class="flex flex-col h-full">
                  <PrismEditorWithCopy
                    v-model="customCss"
                    class="my-editor flex-grow h-[150px] overflow-auto"
                    :highlight="highlightCss"
                    placeholder="CSS ссылки и стили"
                  />
                  <div class="flex justify-end mt-1">
                    <Button
                      class="text-xs ms-1"
                      label="BS"
                      @click="addBootstrap"
                    />
                    <Button class="text-xs ms-1" label="VJ" @click="addVj" />
                  </div>
                </div>
                <div class="flex flex-col h-full">
                  <PrismEditorWithCopy
                    v-model="customJs"
                    class="my-editor flex-grow h-[150px] overflow-auto"
                    :highlight="highlightJs"
                    placeholder="JavaScript скрипты"
                  />
                </div>
              </div>
            </TabPanel>
            <TabPanel
            header="Info"
            value="info"
            :pt="{
              header: { class: 'p-0' },
              headerAction: { class: 'text-xs p-0' },
              content: { class: 'p-0' }
            }"
            >
              <div class="p-2">
                <label class="block text-xs font-medium mb-1">Описание страницы:</label>
                <textarea
                  v-model="pageInfo.description"
                  class="w-full h-[160px] p-2 text-xs border border-surface-300 rounded resize-none"
                  placeholder="Введите описание страницы..."
                />
              </div>
            </TabPanel>
          </TabView>
        </div>
      </div>



      <!-- Дополнительный правый сайдбар для редактирования JSON -->
      <div
        v-if="isJsonFormVisible"
        class="flex flex-col bg-surface-50 dark:bg-surface-800 border-l border-r border-surface-200 dark:border-surface-700 h-full"
        :style="{
          width: jsonSidebarWidth,
          minWidth: '300px',
          maxWidth: '400px',
          position: 'relative',
          zIndex: 10
        }"
      >
        <div
          class="flex justify-between items-center p-2 border-b border-surface-200 dark:border-surface-700 bg-white flex-shrink-0"
        >
          <h3 class="text-sm font-semibold">
            {{ selectedJsonForEdit ? 'Редактировать JSON' : 'Добавить JSON' }}
          </h3>
          <Button
            icon="pi pi-times"
            text
            class="p-button-sm"
            @click="closeJsonForm"
          />
        </div>
        <div class="flex-1 overflow-hidden">
          <JsonForm
            :json="selectedJsonForEdit || {}"
            :is-edit="!!selectedJsonForEdit"
            @save="handleJsonFormSave"
            @cancel="closeJsonForm"
          />
        </div>
      </div>

      <!-- Правый сайдбар с контейнером для редактирования -->
      <div class="flex" :style="{ width: rightSidebarWidth }">
        <div class="flex flex-col w-full bg-surface-50 dark:bg-surface-800 border-l border-surface-200 dark:border-surface-700">
          <!-- Верхняя часть - Canvas для выбранных шаблонов и JSON -->
          <div class="flex-none h-[35vh] border-b border-surface-200 dark:border-surface-700">


            <!-- Canvas для сборки -->
            <div class="p-0 h-full overflow-auto">
              <Canvas
                ref="canvasRef"
                style="font-size: 9px; padding: 0; width: 100%;"
                @update:groups="onCanvasGroupsUpdate"
              />
            </div>
          </div>

          <!-- Нижняя часть - DataTable для wjson -->
          <div class="flex-1">

            <!-- Toolbar для JSON -->
            <div class="structure-toolbar toolbar-container flex items-center p-1 border-b border-surface-200 dark:border-surface-700 bg-white">
              <div class="flex items-center gap-1">
                <!-- Кнопка обновления -->
                <Button
                  v-tooltip.top="'Обновить данные'"
                  icon="pi pi-refresh"
                  class="p-button-sm p-button-text"
                  @click="loadOptions"
                />

                <!-- Поиск с выпадающим окном -->
                <div class="relative">
                  <Button
                    icon="pi pi-search"
                    class="p-button-sm p-button-text"
                    data-search-button
                    @click="toggleJsonSearch"
                  />
                  <div v-if="showJsonSearch" class="search-dropdown">
                    <InputText
                      v-model="jsonSearch"
                      placeholder="Поиск JSON..."
                      class="w-full text-xs"
                      style="font-size: 12px"
                    />
                  </div>
                </div>

                <!-- Фильтры с выпадающим MultiSelect -->
                <div class="relative">
                  <Button
                    icon="pi pi-filter"
                    class="p-button-sm p-button-text"
                    data-filters-button
                    @click="toggleJsonFilters"
                  />
                  <div v-if="showJsonFilters" class="filters-dropdown">
                    <MultiSelect
                      v-model="jsonSelectedTags"
                      :options="jsonAvailableTags"
                      filter
                      placeholder="Теги JSON"
                      class="w-48 text-xs"
                      display="chip"
                      panel-class="text-xs"
                      :pt="{
                        item: { class: 'text-xs' },
                        header: { class: 'text-xs' },
                      }"
                    />
                  </div>
                </div>
              </div>

              <!-- Действия -->
              <div class="flex items-center gap-1">
                <Button
                  v-tooltip.top="'Добавить JSON'"
                  icon="pi pi-plus"
                  class="p-button-sm p-button-text"
                  @click="openJsonForm()"
                />
                <Button
                  v-tooltip.top="'Редактировать'"
                  icon="pi pi-pencil"
                  class="p-button-sm p-button-text"
                  :disabled="!selectedJsonItem"
                  @click="openJsonForm(selectedJsonItem)"
                />
                <Button
                  v-tooltip.top="'Дублировать'"
                  icon="pi pi-copy"
                  class="p-button-sm p-button-text"
                  :disabled="!selectedJsonItem"
                  @click="duplicateJson"
                />
                <Button
                  v-tooltip.top="'Удалить'"
                  icon="pi pi-trash"
                  class="p-button-sm p-button-text p-button-danger"
                  :disabled="!selectedJsonItem"
                  @click="deleteJson"
                />
              </div>
            </div>

            <!-- DataTable для wjson -->
            <div class="h-full overflow-hidden">
              <DataTable
                v-model:selection="selectedJsonItem"
                :value="filteredJsonData"
                scrollable
                scroll-height="flex"
                :virtual-scroller-options="{ itemSize: 32 }"
                size="small"
                class="text-xxs h-full"

                striped-rows
                responsive-layout="scroll"
                selection-mode="single"
                sort-field="art"
        :sort-order="1"
                style="--highlight-bg: var(--primary-50); padding: 1px; font-size: 11px"
                @row-click="onJsonRowClick"
                @row-dblclick="(event) => openJsonForm(event.data)"
              >
                <Column field="art" header="№" sortable style="font-size: 9px; padding: 1px; width: 60px" />
                <Column field="title" header="Название" :sortable="true" style="font-size: 9.5px; padding: 1px; width: 150px" />
                <Column field="description" header="Описание" style="font-size: 9px; padding: 1px; width: 120px" />

                <Column header="+" style="padding: 1px; width: 40px">
                  <template #body="{ data }">
                    <Button
                      v-tooltip.top="'Добавить на холст'"
                      icon="pi pi-plus"
                      style="padding: 1px"
                      text
                      class="p-button p-component p-button-icon-only p-button-secondary p-button-text p-button-sm"
                      @click="addJsonToCanvas(data)"
                    />
                  </template>
                </Column>

              </DataTable>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Toast />
    <Dialog v-model:visible="showPreview" modal header="Предпросмотр страницы" :style="{ width: '90vw', height: '90vh' }">
      <iframe :srcdoc="getFullHtml()" class="w-full h-full border-0" />
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import MultiSelect from 'primevue/multiselect'
import Tag from 'primevue/tag'
import Image from 'primevue/image'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import Dialog from 'primevue/dialog'
import Toast from 'primevue/toast'
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'
import 'vue-prism-editor/dist/prismeditor.min.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'
import handlebars from 'handlebars/dist/handlebars.min.js'
import { BOOTSTRAP_CSS, BOOTSTRAP_JS, VJ_CSS, VJ_JS } from '~/constants/scripts'
import Canvas from '~/components/Canvas.vue'
import BlockForm from '~/components/wblock-page-gen/BlockForm.vue'
import JsonForm from '~/components/wblock-page-gen/JsonForm.vue'

interface Element {
  id: string
  name: string
  type: string
  template: string
}

interface WBlock {
  id: number
  number: string
  title: string
  description: string
  block_type: string
  html: string
  css: string
  js: string
  hbs: string
  sketch?: string
  concepts?: string[]
  layouts?: string[]
  elements?: string[]
  styles?: string[]
  graphics?: string[]
  collections?: string[]
}

interface WJson {
  id: number
  art: string
  title: string
  description: string
  tags: string[]
  json: string
}

const toast = useToast()
const { $directus } = useNuxtApp()

// Panel width management
const leftSidebarWidth = ref('300px')
const rightSidebarWidth = ref('300px')
const canvasWidth = ref('900px')
const elementSidebarWidth = ref('300px')
const blockSidebarWidth = ref('300px')
const jsonSidebarWidth = ref('300px')

// Функция для обновления ширины панелей при открытии/закрытии дополнительных сайдбаров
const updatePanelWidths = () => {
  if (isBlockFormVisible.value && isJsonFormVisible.value) {
    // Оба дополнительных сайдбара открыты
    leftSidebarWidth.value = '150px'
    rightSidebarWidth.value = '150px'
    blockSidebarWidth.value = '250px'
    jsonSidebarWidth.value = '250px'
    canvasWidth.value = 'calc(100vw - 800px)'
  } else if (isBlockFormVisible.value) {
    // Только форма блока открыта
    leftSidebarWidth.value = '150px'
    rightSidebarWidth.value = '300px'
    blockSidebarWidth.value = '300px'
    canvasWidth.value = 'calc(100vw - 750px)'
  } else if (isJsonFormVisible.value) {
    // Только форма JSON открыта
    leftSidebarWidth.value = '300px'
    rightSidebarWidth.value = '150px'
    jsonSidebarWidth.value = '300px'
    canvasWidth.value = 'calc(100vw - 750px)'
  } else {
    // Никаких дополнительных сайдбаров
    leftSidebarWidth.value = '300px'
    rightSidebarWidth.value = '300px'
    canvasWidth.value = 'calc(100vw - 600px)'
  }
}

// Data states
const wblockProto = ref<WBlock[]>([])
const wjsonData = ref<WJson[]>([])
const selectedTemplates = ref<WBlock[]>([])
const selectedWjson = ref<WJson[]>([])
const generatedHtml = ref('')
const cssLinks = ref('')
const jsScripts = ref('')
const renderedHtml = ref('')
const customCss = ref('')
const customJs = ref('')

// HBS/JSON tab variables
const currentHbs = ref('')
const currentJson = ref('')

// Editable HBS/JSON variables
const editableHbs = ref('')
const editableJson = ref('')

// Простые computed свойства для отображения Canvas содержимого
const displayHbs = computed(() => {
  if (!canvasGroups.value?.length) return ''
  return canvasGroups.value
    .map((group, index) => {
      const hbs = group.whbs?.hbs || ''
      return hbs ? `<!-- Group ${index + 1}: ${group.whbs?.title || 'Untitled'} -->\n${hbs}` : ''
    })
    .filter(Boolean)
    .join('\n\n')
})

const displayJson = computed(() => {
  if (!canvasGroups.value?.length) return ''
  return canvasGroups.value
    .map((group, index) => {
      if (group.wjson?.json) {
        try {
          const jsonData = typeof group.wjson.json === 'string' ? JSON.parse(group.wjson.json) : group.wjson.json
          return `// Group ${index + 1}: ${group.wjson.title || 'Untitled'}\n${JSON.stringify(jsonData, null, 2)}`
        } catch (err) {
          return `// Group ${index + 1}: Error - ${err.message}`
        }
      }
      return null
    })
    .filter(Boolean)
    .join('\n\n')
})

// Search and filter states
const blockProtoSearch = ref('')
const jsonSearch = ref('')
const blockProtoSelectedTags = ref([])
const blockProtoAvailableTags = ref([])
const blockProtoSelectedItem = ref(null)
const selectedJsonItem = ref(null)
const selectedProtoTypes = ref([])
const selectedJsonTypes = ref([])
const jsonSelectedTags = ref([])
const jsonAvailableTags = ref([])

// Sidebar visibility states
const isBlockFormVisible = ref(false)
const isJsonFormVisible = ref(false)
const selectedBlockProto = ref(null)
const selectedJsonForEdit = ref(null)

// Dropdown visibility states for toolbar
const showBlockSearch = ref(false)
const showBlockFilters = ref(false)
const showJsonSearch = ref(false)
const showJsonFilters = ref(false)



// Debounced search values for performance
const debouncedBlockSearch = ref('')
const debouncedJsonSearch = ref('')

// Debounce timers
let blockSearchTimeout: NodeJS.Timeout | null = null
let jsonSearchTimeout: NodeJS.Timeout | null = null

// Watch for search changes with debouncing
watch(blockProtoSearch, (newValue) => {
  if (blockSearchTimeout) {
    clearTimeout(blockSearchTimeout)
  }
  blockSearchTimeout = setTimeout(() => {
    debouncedBlockSearch.value = newValue
  }, 300) // 300ms delay
})

watch(jsonSearch, (newValue) => {
  if (jsonSearchTimeout) {
    clearTimeout(jsonSearchTimeout)
  }
  jsonSearchTimeout = setTimeout(() => {
    debouncedJsonSearch.value = newValue
  }, 300) // 300ms delay
})

// View mode states
const viewMode = ref('full')
const jsonViewMode = ref('full')

// Device viewport states
const devices = [
  { name: 'desktop', icon: 'pi pi-desktop', width: '100%', height: '600px' },
  { name: 'tablet', icon: 'pi pi-tablet', width: '768px', height: '500px' },
  { name: 'mobile', icon: 'pi pi-mobile', width: '375px', height: '400px' },
]
const activeDevice = ref('desktop')

// Page info
const pageInfo = ref({
  number: '',
  title: '',
  description: '',
  page_type: [],
  tags: []
})

// Canvas and preview states
const canvasRef = ref(null)
const canvasGroups = ref([])
const activeGroupIndex = ref(null)
const showPreview = ref(false)
const previewHtml = ref('')

// Page type options - будут загружаться из коллекции wpage
const pageTypeOptions = ref([])

// Page tags options - будут загружаться из коллекции wpage
const pageTagsOptions = ref([])

// Filter options
const protoTypeOptions = ref([])
const jsonTypeOptions = ref([])

const addBootstrap = () => {
  if (!customCss.value.includes(BOOTSTRAP_CSS)) {
    customCss.value += (customCss.value ? '\n' : '') + BOOTSTRAP_CSS
  }
  if (!customJs.value.includes(BOOTSTRAP_JS)) {
    customJs.value += (customJs.value ? '\n' : '') + BOOTSTRAP_JS
  }
}

const addVj = () => {
  if (!customCss.value.includes(VJ_CSS)) {
    customCss.value += (customCss.value ? '\n' : '') + VJ_CSS
  }
  if (!customJs.value.includes(VJ_JS)) {
    customJs.value += (customJs.value ? '\n' : '') + VJ_JS
  }
}

// View mode toggles
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'compact' ? 'full' : 'compact'
}

const toggleJsonViewMode = () => {
  jsonViewMode.value = jsonViewMode.value === 'compact' ? 'full' : 'compact'
}

// Canvas management
const clearCanvas = () => {
  canvasRef.value?.clear()
}

const removeGroup = (index: number) => {
  canvasGroups.value.splice(index, 1)
  if (activeGroupIndex.value === index) activeGroupIndex.value = null
}

// Panel width management
// const updatePanelWidths = () => {
  //   const totalWidth = window.innerWidth
  //   canvasWidth.value = `${totalWidth - parseInt(leftSidebarWidth.value) - parseInt(rightSidebarWidth.value) - 40}px`
  // }

// Оптимизированные computed свойства с мемоизацией
const filteredBlockProtoData = computed(() => {
  if (!wblockProto.value?.length) return []

  const search = blockProtoSearch.value?.toLowerCase()
  const selectedTags = blockProtoSelectedTags.value

  // Если нет фильтров, возвращаем первые 100 элементов для производительности
  if (!search && !selectedTags?.length) {
    return wblockProto.value
  }

  return wblockProto.value.filter(item => {
    // Быстрая проверка поиска
    if (search && !item.title?.toLowerCase().includes(search) &&
        !item.description?.toLowerCase().includes(search)) {
      return false
    }

    // Быстрая проверка тегов
    if (selectedTags.length > 0 &&
        (!item.block_type || !item.block_type.some(type => selectedTags.includes(type)))) {
      return false
    }

    return true
  })
})

const filteredJsonData = computed(() => {
  if (!wjsonData.value?.length) return []

  const search = jsonSearch.value?.toLowerCase()
  const selectedTags = jsonSelectedTags.value

  // Если нет фильтров, возвращаем первые 100 элементов для производительности
  if (!search && !selectedTags?.length) {
    return wjsonData.value
  }

  return wjsonData.value.filter(item => {
    // Быстрая проверка поиска
    if (search && !item.title?.toLowerCase().includes(search) &&
        !item.description?.toLowerCase().includes(search) &&
        !item.art?.toLowerCase().includes(search)) {
      return false
    }

    // Быстрая проверка тегов
    if (selectedTags.length > 0 &&
        (!item.tags || !item.tags.some(tag => selectedTags.includes(tag)))) {
      return false
    }

    return true
  }) // Ограничиваем результаты для производительности
})

// Computed для текущего устройства
const currentDevice = computed(() => {
  return devices.find(device => device.name === activeDevice.value) || devices[0]
})

// Флаг для отслеживания источника изменений
const isEditingManually = ref(false)

// Оптимизированный watch с дебаунсингом
let canvasUpdateTimeout: NodeJS.Timeout | null = null
watch(canvasGroups, () => {
  if (!isEditingManually.value) {
    // Дебаунсинг для предотвращения частых обновлений
    if (canvasUpdateTimeout) {
      clearTimeout(canvasUpdateTimeout)
    }
    canvasUpdateTimeout = setTimeout(() => {
      updateEditableFieldsFromCanvas()
      generateHtmlFromCanvas()
    }, 100) // 100ms задержка
  }
}, { deep: true, flush: 'post' })

// Функции для обработки редактирования HBS и JSON
const onHbsEdit = (value: string) => {
  isEditingManually.value = true
  editableHbs.value = value
  generateHtmlFromEditableFields()
}

const onJsonEdit = (value: string) => {
  isEditingManually.value = true
  editableJson.value = value
  generateHtmlFromEditableFields()
}

// Функция для обновления редактируемых полей из Canvas
const updateEditableFieldsFromCanvas = () => {
  editableHbs.value = displayHbs.value
  editableJson.value = displayJson.value
}

// Функция генерации HTML из редактируемых полей
const generateHtmlFromEditableFields = () => {
  if (editableHbs.value && editableJson.value) {
    try {
      // Парсим JSON, убирая комментарии
      const cleanJson = editableJson.value.replace(/\/\/.*$/gm, '').trim()
      const jsonData = JSON.parse(cleanJson)

      const template = handlebars.compile(editableHbs.value)
      renderedHtml.value = template(jsonData)
    } catch (err) {
      console.error('Ошибка рендеринга из редактируемых полей:', err)
      renderedHtml.value = `<div style="color:red; padding:10px; border:1px solid red;">Ошибка рендеринга: ${err.message}</div>`
    }
  } else if (editableHbs.value) {
    renderedHtml.value = editableHbs.value
  } else {
    // Fallback to Canvas generation
    generateHtmlFromCanvas()
  }
}

// Функция для сброса режима ручного редактирования
const resetManualEditing = () => {
  isEditingManually.value = false
  updateEditableFieldsFromCanvas()
  generateHtmlFromCanvas()
}

// Toggle functions for toolbar dropdowns
const toggleBlockSearch = () => {
  showBlockSearch.value = !showBlockSearch.value
  if (showBlockSearch.value) showBlockFilters.value = false
}

const toggleBlockFilters = () => {
  showBlockFilters.value = !showBlockFilters.value
  if (showBlockFilters.value) showBlockSearch.value = false
}

const toggleJsonSearch = () => {
  showJsonSearch.value = !showJsonSearch.value
  if (showJsonSearch.value) showJsonFilters.value = false
}

const toggleJsonFilters = () => {
  showJsonFilters.value = !showJsonFilters.value
  if (showJsonFilters.value) showJsonSearch.value = false
}

// Handle click outside to close dropdowns
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const searchDropdown = target.closest('.search-dropdown')
  const filtersDropdown = target.closest('.filters-dropdown')
  const searchButton = target.closest('[data-search-button]')
  const filtersButton = target.closest('[data-filters-button]')

  if (!searchDropdown && !searchButton) {
    showBlockSearch.value = false
    showJsonSearch.value = false
  }
  if (!filtersDropdown && !filtersButton) {
    showBlockFilters.value = false
    showJsonFilters.value = false
  }
}





// Функции для работы с отдельными группами
const getGroupHbs = (index: number) => {
  const group = canvasGroups.value[index]
  return group?.whbs?.hbs || ''
}

const getGroupJson = (index: number) => {
  const group = canvasGroups.value[index]
  if (group?.wjson?.json) {
    try {
      const jsonData = typeof group.wjson.json === 'string' ? JSON.parse(group.wjson.json) : group.wjson.json
      return JSON.stringify(jsonData, null, 2)
    } catch (err) {
      return `// Error: ${err.message}`
    }
  }
  return ''
}

const updateGroupHbs = (index: number, value: string) => {
  isEditingManually.value = true
  if (canvasGroups.value[index]?.whbs) {
    canvasGroups.value[index].whbs.hbs = value
    generateHtmlFromCanvas()
  }
}

const updateGroupJson = (index: number, value: string) => {
  isEditingManually.value = true
  if (canvasGroups.value[index]?.wjson) {
    try {
      // Проверяем валидность JSON
      const parsedJson = JSON.parse(value)
      canvasGroups.value[index].wjson.json = parsedJson
      generateHtmlFromCanvas()
    } catch (err) {
      console.error('Invalid JSON:', err)
      // Показываем ошибку в HTML
      renderedHtml.value = `<div style="color:red; padding:10px; border:1px solid red;">Группа ${index + 1} - Ошибка JSON: ${err.message}</div>`
    }
  }
}

// Функция генерации HTML из Canvas групп
const generateHtmlFromCanvas = () => {
  if (!canvasGroups.value?.length) {
    renderedHtml.value = ''
    return
  }

  let html = ''
  canvasGroups.value.forEach((group, index) => {
    if (group.whbs?.hbs && group.wjson?.json) {
      try {
        const template = handlebars.compile(group.whbs.hbs)
        const jsonData = typeof group.wjson.json === 'string' ? JSON.parse(group.wjson.json) : group.wjson.json
        html += `<!-- Group ${index + 1}: ${group.whbs.title} + ${group.wjson.title} -->\n`
        html += template(jsonData)
        html += '\n\n'
      } catch (err) {
        console.error('Ошибка рендеринга Handlebars:', err)
        html += `<div style="color:red; padding:10px; border:1px solid red;">Группа ${index + 1} - Ошибка рендеринга: ${err.message}</div>\n\n`
      }
    } else if (group.whbs?.hbs) {
      html += `<!-- Group ${index + 1}: ${group.whbs.title} (без данных) -->\n`
      html += group.whbs.hbs
      html += '\n\n'
    }
  })

  renderedHtml.value = html.trim()
}

// Функция генерации HTML для конкретной группы
const generateHtmlForGroup = (group: any, index: number) => {
  if (group.whbs?.hbs && group.wjson?.json) {
    try {
      const template = handlebars.compile(group.whbs.hbs)
      const jsonData = typeof group.wjson.json === 'string' ? JSON.parse(group.wjson.json) : group.wjson.json
      return template(jsonData)
    } catch (err) {
      console.error(`Ошибка рендеринга Handlebars для группы ${index + 1}:`, err)
      return `<div style="color:red; padding:10px; border:1px solid red;">Группа ${index + 1} - Ошибка рендеринга: ${err.message}</div>`
    }
  } else if (group.whbs?.hbs) {
    return group.whbs.hbs
  }
  return ''
}

// Функция анализа HTML через API (адаптированная из wblock-html-gen)
const analyzeHtmlContent = async (htmlContent: string) => {
  try {
    const { layout, elements, graphics, features, treeStructure } = await $fetch(
      '/api/analyze-html',
      {
        method: 'POST',
        body: { html: htmlContent },
      },
    )

    return {
      composition: treeStructure || '',
      layout: layout || [],
      elements: elements || [],
      graphics: graphics || [],
      features: features || []
    }
  } catch (error) {
    console.error('Error analyzing HTML:', error)
    return {
      composition: '',
      layout: [],
      elements: [],
      graphics: [],
      features: []
    }
  }
}

// Функция создания скриншота HTML контента (одиночный режим)
const generateScreenshot = async (htmlContent: string, filename: string = 'screenshot') => {
  try {
    console.log(`📸 Создание скриншота: ${filename}...`)

    const response = await $fetch('/api/capture-html-screenshot', {
      method: 'POST',
      body: {
        html: htmlContent,
        filename: filename,
        width: 1400,
        height: 800
      }
    })

    console.log(`✅ Скриншот создан: ${response.filename}, ID: ${response.fileId}`)
    return response.fileId

  } catch (error) {
    console.error(`❌ Ошибка создания скриншота ${filename}:`, error)
    throw error
  }
}

// Функция создания множественных скриншотов (batch режим)
const generateBatchScreenshots = async (screenshots: Array<{html: string, filename: string}>) => {
  try {
    console.log(`📸 Создание ${screenshots.length} скриншотов в batch режиме...`)

    const response = await $fetch('/api/capture-batch-screenshots', {
      method: 'POST',
      body: {
        screenshots: screenshots.map(item => ({
          html: item.html,
          filename: item.filename,
          width: 1400,
          height: 800
        }))
      }
    })

    console.log(`✅ Batch скриншоты созданы: ${response.successCount}/${screenshots.length} успешно за ${response.totalTime}ms`)

    // Возвращаем массив fileId в том же порядке
    return response.results.map(result => result.success ? result.fileId : null)

  } catch (error) {
    console.error(`❌ Ошибка создания batch скриншотов:`, error)
    throw error
  }
}

// Умная функция выбора режима создания скриншотов
const generateScreenshotsOptimal = async (screenshots: Array<{html: string, filename: string}>) => {
  // Если скриншот один - используем одиночный режим
  if (screenshots.length === 1) {
    console.log('📸 Используем одиночный режим для 1 скриншота')
    const fileId = await generateScreenshot(screenshots[0].html, screenshots[0].filename)
    return [fileId]
  }

  // Если скриншотов несколько - используем batch режим
  console.log(`📸 Используем batch режим для ${screenshots.length} скриншотов`)
  return await generateBatchScreenshots(screenshots)
}

// Syntax highlighting functions
const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

// Watch for window resize
watch(() => [leftSidebarWidth.value, rightSidebarWidth.value], () => {
  updatePanelWidths()
})

// Функция загрузки данных удалена - используется loadOptions



// Load options for dropdowns
const loadOptions = async () => {
  try {
    const { getItems } = useDirectusItems()

    // Load wblock_proto data
    const protoItems = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: ['*']
      }
    })
    wblockProto.value = protoItems || []

    // Извлекаем уникальные теги из block_type для блоков
    const allTags = new Set()
    wblockProto.value.forEach(item => {
      if (item.block_type) {
        item.block_type.forEach(tag => allTags.add(tag))
      }
    })
    blockProtoAvailableTags.value = Array.from(allTags)

    // Load wjson data (ограничиваем количество для производительности)
    const jsonItems = await getItems({
      collection: 'wjson',
      params: {
        limit: -1, // Ограничиваем количество записей
        fields: ['*']
      }
    })
    wjsonData.value = jsonItems || []

    // Extract unique block types for filtering
    const protoTypes = [...new Set(wblockProto.value.map(item => item.block_type).filter(Boolean))]
    protoTypeOptions.value = protoTypes.map(type => ({ label: type, value: type }))

    // Extract unique tags from wjson data
    const allJsonTags = new Set()
    wjsonData.value.forEach(item => {
      if (item.tags && Array.isArray(item.tags)) {
        item.tags.forEach(tag => allJsonTags.add(tag))
      }
    })
    jsonAvailableTags.value = Array.from(allJsonTags)

    // Load wpage data for page type and tags options
    const wpageItems = await getItems({
      collection: 'wpage',
      params: {
        limit: -1,
        fields: ['wpage_type', 'tags']
      }
    })

    // Extract unique page types from wpage collection
    const allPageTypes = new Set()
    wpageItems?.forEach(item => {
      if (item.wpage_type && Array.isArray(item.wpage_type)) {
        item.wpage_type.forEach(type => allPageTypes.add(type))
      }
    })
    pageTypeOptions.value = Array.from(allPageTypes)

    // Extract unique tags from wpage collection
    const allPageTags = new Set()
    wpageItems?.forEach(item => {
      if (item.tags && Array.isArray(item.tags)) {
        item.tags.forEach(tag => allPageTags.add(tag))
      }
    })
    pageTagsOptions.value = Array.from(allPageTags)

    // Toast уведомление об успешном обновлении
    toast.add({
      severity: 'success',
      summary: 'Данные обновлены',
      detail: `Загружено: ${wblockProto.value.length} блоков, ${wjsonData.value.length} JSON записей`,
      life: 3000,
    })

  } catch (error) {
    console.error('Error loading data:', error)
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось обновить данные', life: 3000 })
  }
}

// Event handlers for old functionality (keeping for compatibility)
const onBlockProtoRowClickOld = (event: any) => {
  const block = event.data
  if (!selectedTemplates.value.find(t => t.id === block.id)) {
    selectedTemplates.value.push(block)
  }
}

const onJsonRowClickOld = (event: any) => {
  const json = event.data
  if (!selectedWjson.value.find(j => j.id === json.id)) {
    selectedWjson.value.push(json)
  }
}

const removeTemplate = (index: number) => {
  selectedTemplates.value.splice(index, 1)
}

const removeWjson = (index: number) => {
  selectedWjson.value.splice(index, 1)
}

// Canvas event handlers
// Обновляем функцию onCanvasGroupsUpdate и watch для canvasGroups для автоматической генерации HTML при изменении групп.
const onCanvasGroupsUpdate = (groups) => {
  canvasGroups.value = groups;
  generatePageHtml();
};

// Drag and drop handlers
const dragStartBlockProto = (event: DragEvent, block: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('whbs', JSON.stringify({
      id: block.id,
      number: block.number,
      title: block.title,
      hbs: block.hbs,
      block_type: block.block_type,
    }));
    event.dataTransfer.effectAllowed = 'copy';
  }
}

const dragStartJson = (event: DragEvent, json: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('wjson', JSON.stringify({
      id: json.id,
      art: json.art,
      title: json.title,
      json: typeof json.json === 'string' ? json.json : JSON.stringify(json.json),
      tags: json.tags,
    }));
    event.dataTransfer.effectAllowed = 'copy';
  }
}

// Add to canvas handlers
// Обновляем функцию addBlockProtoToCanvas для добавления данных из поля hbs
const addBlockProtoToCanvas = (block: any) => {
  canvasRef.value?.handleAddToCanvas({
    type: 'whbs',
    data: {
      id: block.id,
      number: block.number,
      title: block.title,
      description: block.description,
      hbs: block.hbs,
      css: block.css,
      js: block.js,
      block_type: block.block_type,
    }
  });

  // Добавляем CSS и JS в центральные поля страницы без комментариев и дублирования
  if (block.css && !customCss.value.includes(block.css)) {
    customCss.value += (customCss.value ? '\n' : '') + block.css
  }

  if (block.js && !customJs.value.includes(block.js)) {
    customJs.value += (customJs.value ? '\n' : '') + block.js
  }
}

// Обновляем функцию addJsonToCanvas для подключения json к hbs
const addJsonToCanvas = (json: any) => {
  canvasRef.value?.handleAddToCanvas({
    type: 'wjson',
    data: {
      id: json.id,
      art: json.art,
      title: json.title,
      json: typeof json.json === 'string' ? json.json : JSON.stringify(json.json),
      tags: json.tags,
    }
  });
}

// HTML generation
// Функция для ручной генерации HTML (кнопка "Генерировать")
const generatePageHtml = () => {
  generateHtmlFromCanvas()
}

// Preview functionality (новая логика как в wblock-html-gen)
const previewPage = () => {
  if (!renderedHtml.value) {
    generatePageHtml()
  }

  // Генерируем полный HTML документ
  const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${pageInfo.value.title || 'Generated Page'}</title>
  ${customCss.value || ''}
</head>
<body>
  ${renderedHtml.value}
  ${customJs.value || ''}
</body>
</html>`

  // Открываем в новой вкладке
  const blob = new Blob([fullHtml], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  window.open(url, '_blank')
}

// Download functionality (новая логика как в wblock-html-gen)
const downloadPage = () => {
  if (!renderedHtml.value) {
    generatePageHtml()
  }

  // Генерируем полный HTML документ
  const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${pageInfo.value.title || 'Generated Page'}</title>
  ${customCss.value || ''}
</head>
<body>
  ${renderedHtml.value}
  ${customJs.value || ''}
</body>
</html>`

  // Скачиваем файл
  const blob = new Blob([fullHtml], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${pageInfo.value.title || 'generated-page'}.html`
  a.click()
  URL.revokeObjectURL(url)
}

// Save functionality
const savePage = async (returnId = false) => {
  try {
    const { createItems, updateItem } = useDirectusItems()

    // Создаем скриншот страницы
    console.log('📸 Создание скриншота страницы...')
    let sketchFileId = null

    try {
      // Генерируем полный HTML для скриншота
      const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${pageInfo.value.title || 'Generated Page'}</title>
  ${customCss.value || ''}
</head>
<body>
  ${renderedHtml.value}
  ${customJs.value || ''}
</body>
</html>`

      const filename = `page_${pageInfo.value.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`
      sketchFileId = await generateScreenshot(fullHtml, filename)
      console.log('✅ Скриншот страницы создан:', sketchFileId)
    } catch (screenshotError) {
      console.error('⚠️ Ошибка создания скриншота страницы:', screenshotError)
      // Продолжаем сохранение без скриншота
    }

    const pageData = {
      title: pageInfo.value.title || '',
      description: pageInfo.value.description || '',
      number: pageInfo.value.number || '',
      html: renderedHtml.value,
      css: customCss.value,
      js: customJs.value,
      wpage_type: pageInfo.value.page_type,
      tags: pageInfo.value.tags,
      sketch: sketchFileId // Добавляем скриншот
    }

    const savedPage = await createItems({
      collection: 'wpage',
      items: [pageData]
    })

    console.log('✅ Страница сохранена:', savedPage)

    // Если нужно вернуть ID для связывания
    if (returnId && savedPage) {
      const pageId = Array.isArray(savedPage) ? savedPage[0].id : savedPage.id
      console.log('🔗 Возвращаем ID страницы для связывания:', pageId)
      return pageId
    }

    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Page saved successfully',
      life: 3000
    })

    return null

  } catch (error) {
    console.error('Error saving page:', error)
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to save page',
      life: 3000
    })
    throw error
  }
}

// Функция сохранения блоков в wblock_proto (новая логика)
const saveWblockItems = async (pageId = null) => {
  try {
    console.log('🔄 Начинаем сохранение групп в wblock_proto...')

    if (!canvasGroups.value?.length) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Нет групп для сохранения',
        life: 3000
      })
      return
    }

    const { createItems } = useDirectusItems()
    const blocksToSave = []

    // Обрабатываем каждую группу
    for (let index = 0; index < canvasGroups.value.length; index++) {
      const group = canvasGroups.value[index]

      console.log(`📦 Обрабатываем группу ${index + 1}:`, group)

      // Проверяем наличие необходимых данных
      if (!group.whbs?.hbs) {
        console.log(`⚠️ Группа ${index + 1} пропущена - нет HBS данных`)
        continue
      }

      // Генерируем HTML для группы
      const groupHtml = generateHtmlForGroup(group, index)
      console.log(`🔧 HTML для группы ${index + 1}:`, groupHtml.substring(0, 100) + '...')

      // Анализируем HTML через API
      console.log(`🔍 Анализируем HTML для группы ${index + 1}...`)
      const analysisResult = await analyzeHtmlContent(groupHtml)
      console.log(`✅ Результат анализа для группы ${index + 1}:`, analysisResult)

      // Формируем номер и название
      const groupNumber = String(index + 1).padStart(2, '0') // 01, 02, 03...
      const blockNumber = pageInfo.value.number ? `${pageInfo.value.number}-${groupNumber}` : groupNumber
      const blockTitle = pageInfo.value.title ? `${pageInfo.value.title} - ${groupNumber}` : `Группа ${groupNumber}`

      // Создаем скриншот блока
      console.log(`📸 Создание скриншота для группы ${index + 1}...`)
      let blockSketchFileId = null

      try {
        // Генерируем полный HTML для скриншота блока
        const blockFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${blockTitle}</title>
  ${customCss.value || ''}
</head>
<body>
  ${groupHtml}
  ${customJs.value || ''}
</body>
</html>`

        const blockFilename = `block_${blockNumber}_${group.whbs.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`
        blockSketchFileId = await generateScreenshot(blockFullHtml, blockFilename)
        console.log(`✅ Скриншот блока ${index + 1} создан:`, blockSketchFileId)
      } catch (screenshotError) {
        console.error(`⚠️ Ошибка создания скриншота блока ${index + 1}:`, screenshotError)
        // Продолжаем сохранение без скриншота
      }

      // Получаем JSON данные группы
      const groupJsonData = group.wjson?.json ?
        (typeof group.wjson.json === 'string' ? group.wjson.json : JSON.stringify(group.wjson.json, null, 2)) :
        ''

      // Формируем данные для сохранения
      const blockData = {
        number: blockNumber,
        title: blockTitle,
        description: group.whbs.description || '',
        hbs: group.whbs.hbs,
        json: groupJsonData,
        html: groupHtml,
        css: customCss.value || '',
        js: customJs.value || '',
        collection: pageInfo.value.tags || [],
        composition: analysisResult.composition,
        layout: analysisResult.layout,
        elements: analysisResult.elements,
        graphics: analysisResult.graphics,
        features: analysisResult.features,
        sketch: blockSketchFileId // Добавляем скриншот блока
      }

      console.log(`💾 Данные для сохранения группы ${index + 1}:`, blockData)
      blocksToSave.push(blockData)
    }

    if (blocksToSave.length === 0) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Нет подходящих групп для сохранения',
        life: 3000
      })
      return
    }

    console.log(`💾 Сохраняем ${blocksToSave.length} блоков в wblock_proto...`)

    // Сохраняем в коллекцию wblock_proto
    const savedBlocks = await createItems({
      collection: 'wblock_proto',
      items: blocksToSave
    })

    console.log('✅ Блоки успешно сохранены!', savedBlocks)

    // Создаем связи с изначальными wblock_proto и wjson
    if (savedBlocks) {
      const savedBlocksArray = Array.isArray(savedBlocks) ? savedBlocks : [savedBlocks]

      for (let i = 0; i < savedBlocksArray.length; i++) {
        const savedBlock = savedBlocksArray[i]
        const originalGroup = canvasGroups.value[i]

        if (!savedBlock?.id) continue

        console.log(`🔗 Создаем связи для блока ${savedBlock.id}...`)

        // Связь с изначальным wblock_proto (если есть)
        if (originalGroup?.whbs?.id) {
          console.log(`🔗 Создание связи wblock_proto_wblock_proto для блока ${savedBlock.id}...`)
          await createItems({
            collection: 'wblock_proto_wblock_proto',
            items: {
              wblock_proto_id: savedBlock.id,
              related_wblock_proto_id: originalGroup.whbs.id
            }
          })
        }

        // Связь с изначальным wjson (если есть)
        if (originalGroup?.wjson?.id) {
          console.log(`🔗 Создание связи wblock_proto_wjson для блока ${savedBlock.id}...`)
          await createItems({
            collection: 'wblock_proto_wjson',
            items: {
              wblock_proto_id: savedBlock.id,
              wjson_id: originalGroup.wjson.id
            }
          })
        }

        console.log(`✅ Связи для блока ${savedBlock.id} созданы!`)
      }
    }

    // Если передан pageId, возвращаем ID блоков для связывания
    if (pageId && savedBlocks) {
      const blockIds = Array.isArray(savedBlocks) ? savedBlocks.map(block => block.id) : [savedBlocks.id]
      console.log('🔗 Возвращаем ID блоков для связывания:', blockIds)
      return blockIds
    }

    toast.add({
      severity: 'success',
      summary: 'Успех',
      detail: `Сохранено ${blocksToSave.length} блоков в wblock_proto`,
      life: 3000
    })

    return null

  } catch (error) {
    console.error('❌ Ошибка сохранения блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блоки',
      life: 3000
    })
    throw error
  }
}

// Функция сохранения страницы и блоков (оптимизированная с batch скриншотами)
const savePageAndBlocks = async () => {
  try {
    console.log('🚀 Начинаем сохранение страницы и блоков с связыванием...')

    // Проверяем наличие необходимых данных
    if (!pageInfo.value.title || !canvasGroups.value?.length) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Введите название страницы и добавьте хотя бы одну группу!',
        life: 3000
      })
      return
    }

    const { createItems } = useDirectusItems()

    // --- 1. Подготовка всех скриншотов для batch обработки ---
    console.log('� Подготовка скриншотов для batch обработки...')

    const screenshotsToCreate = []

    // Скриншот страницы
    const pageFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${pageInfo.value.title || 'Generated Page'}</title>
  ${customCss.value || ''}
</head>
<body>
  ${renderedHtml.value}
  ${customJs.value || ''}
</body>
</html>`

    const pageFilename = `page_${pageInfo.value.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`
    screenshotsToCreate.push({
      html: pageFullHtml,
      filename: pageFilename
    })

    // Скриншоты блоков
    const validGroups = canvasGroups.value.filter(group => group.whbs?.hbs && group.wjson?.json)

    for (let index = 0; index < validGroups.length; index++) {
      const group = validGroups[index]
      const groupHtml = generateHtmlForGroup(group, index)

      const groupNumber = String(index + 1).padStart(2, '0')
      const blockNumber = pageInfo.value.number ? `${pageInfo.value.number}-${groupNumber}` : groupNumber
      const blockTitle = pageInfo.value.title ? `${pageInfo.value.title} - ${groupNumber}` : `Группа ${groupNumber}`

      const blockFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${blockTitle}</title>
  ${customCss.value || ''}
</head>
<body>
  ${groupHtml}
  ${customJs.value || ''}
</body>
</html>`

      const blockFilename = `block_${blockNumber}_${group.whbs.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`
      screenshotsToCreate.push({
        html: blockFullHtml,
        filename: blockFilename
      })
    }

    // --- 2. Создание всех скриншотов одновременно ---
    console.log(`📸 Создание ${screenshotsToCreate.length} скриншотов в batch режиме...`)
    let screenshotFileIds = []

    try {
      screenshotFileIds = await generateScreenshotsOptimal(screenshotsToCreate)
      console.log('✅ Все скриншоты созданы:', screenshotFileIds)
    } catch (screenshotError) {
      console.error('⚠️ Ошибка создания скриншотов:', screenshotError)
      // Продолжаем сохранение без скриншотов
      screenshotFileIds = new Array(screenshotsToCreate.length).fill(null)
    }

    // --- 3. Сохранение страницы с скриншотом ---
    console.log('📄 Сохраняем страницу в wpage...')

    const pageData = {
      title: pageInfo.value.title || '',
      description: pageInfo.value.description || '',
      number: pageInfo.value.number || '',
      html: renderedHtml.value,
      css: customCss.value,
      js: customJs.value,
      wpage_type: pageInfo.value.page_type,
      tags: pageInfo.value.tags,
      sketch: screenshotFileIds[0] // Первый скриншот - страница
    }

    const savedPage = await createItems({
      collection: 'wpage',
      items: [pageData]
    })

    const pageId = Array.isArray(savedPage) ? savedPage[0].id : savedPage.id
    console.log('✅ Страница сохранена с ID:', pageId)

    // --- 4. Сохранение блоков с скриншотами ---
    console.log('📦 Сохраняем блоки в wblock_proto...')

    const blocksToSave = []

    for (let index = 0; index < validGroups.length; index++) {
      const group = validGroups[index]
      const groupHtml = generateHtmlForGroup(group, index)

      // Анализируем HTML через API
      const analysisResult = await analyzeHtmlContent(groupHtml)

      const groupNumber = String(index + 1).padStart(2, '0')
      const blockNumber = pageInfo.value.number ? `${pageInfo.value.number}-${groupNumber}` : groupNumber
      const blockTitle = pageInfo.value.title ? `${pageInfo.value.title} - ${groupNumber}` : `Группа ${groupNumber}`

      const groupJsonData = group.wjson?.json ?
        (typeof group.wjson.json === 'string' ? group.wjson.json : JSON.stringify(group.wjson.json, null, 2)) :
        ''

      const blockData = {
        number: blockNumber,
        title: blockTitle,
        description: group.whbs.description || '',
        hbs: group.whbs.hbs,
        json: groupJsonData,
        html: groupHtml,
        css: customCss.value || '',
        js: customJs.value || '',
        collection: pageInfo.value.tags || [],
        composition: analysisResult.composition,
        layout: analysisResult.layout,
        elements: analysisResult.elements,
        graphics: analysisResult.graphics,
        features: analysisResult.features,
        sketch: screenshotFileIds[index + 1] // Скриншоты блоков начинаются с индекса 1
      }

      blocksToSave.push(blockData)
    }

    const savedBlocks = await createItems({
      collection: 'wblock_proto',
      items: blocksToSave
    })

    const blockIds = Array.isArray(savedBlocks) ? savedBlocks.map(block => block.id) : [savedBlocks.id]
    console.log('✅ Блоки сохранены с ID:', blockIds)

    // --- 5. Связывание страницы и блоков ---
    console.log('🔗 Создаем связи в wpage_wblock_proto...')

    const linkData = blockIds.map(blockId => ({
      wpage_id: pageId,
      wblock_proto_id: blockId
    }))

    await createItems({
      collection: 'wpage_wblock_proto',
      items: linkData
    })

    console.log('✅ Связи успешно созданы!')

    toast.add({
      severity: 'success',
      summary: 'Успех',
      detail: `Страница и ${blockIds.length} блоков сохранены и связаны!`,
      life: 3000
    })

  } catch (error) {
    console.error('❌ Ошибка сохранения страницы и блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить страницу и блоки',
      life: 3000
    })
  }
}

// Block form functions
const openBlockForm = (block = null) => {
  selectedBlockProto.value = block
  isBlockFormVisible.value = true
  updatePanelWidths()
}

const closeBlockForm = () => {
  isBlockFormVisible.value = false
  selectedBlockProto.value = null
  updatePanelWidths()
}

const handleBlockFormSave = async (blockData) => {
  try {
    const { createItems, updateItem } = useDirectusItems()

    if (selectedBlockProto.value) {
      // Update existing block
      await updateItem({
        collection: 'wblock_proto',
        id: selectedBlockProto.value.id,
        item: blockData
      })
      toast.add({ severity: 'success', summary: 'Успех', detail: 'Блок обновлен', life: 3000 })
    } else {
      // Create new block
      await createItems({
        collection: 'wblock_proto',
        items: [blockData]
      })
      toast.add({ severity: 'success', summary: 'Успех', detail: 'Блок создан', life: 3000 })
    }

    closeBlockForm()
    await loadOptions()
  } catch (error) {
    console.error('Error saving block:', error)
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось сохранить блок', life: 3000 })
  }
}

const duplicateBlockProto = async () => {
  if (!blockProtoSelectedItem.value) return

  try {
    const { createItems } = useDirectusItems()

    const duplicatedBlock = {
      ...blockProtoSelectedItem.value,
      id: undefined,
      title: `${blockProtoSelectedItem.value.title} (копия)`,
      number: blockProtoSelectedItem.value.number + 1000
    }

    await createItems({
      collection: 'wblock_proto',
      items: [duplicatedBlock]
    })

    toast.add({ severity: 'success', summary: 'Успех', detail: 'Блок дублирован', life: 3000 })
    await loadOptions()
  } catch (error) {
    console.error('Error duplicating block:', error)
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось дублировать блок', life: 3000 })
  }
}

const deleteBlockProto = async () => {
  if (!blockProtoSelectedItem.value) return

  try {
    const { deleteItems } = useDirectusItems()

    await deleteItems({
      collection: 'wblock_proto',
      ids: [blockProtoSelectedItem.value.id]
    })

    toast.add({ severity: 'success', summary: 'Успех', detail: 'Блок удален', life: 3000 })
    blockProtoSelectedItem.value = null
    await loadOptions()
  } catch (error) {
    console.error('Error deleting block:', error)
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось удалить блок', life: 3000 })
  }
}

// JSON form functions
const openJsonForm = (json = null) => {
  selectedJsonForEdit.value = json
  isJsonFormVisible.value = true
  updatePanelWidths()
}

const closeJsonForm = () => {
  isJsonFormVisible.value = false
  selectedJsonForEdit.value = null
  updatePanelWidths()
}

const handleJsonFormSave = async (jsonData) => {
  try {
    const { createItems, updateItem } = useDirectusItems()

    if (selectedJsonForEdit.value) {
      // Update existing JSON
      await updateItem({
        collection: 'wjson',
        id: selectedJsonForEdit.value.id,
        item: jsonData
      })
      toast.add({ severity: 'success', summary: 'Успех', detail: 'JSON обновлен', life: 3000 })
    } else {
      // Create new JSON
      await createItems({
        collection: 'wjson',
        items: [jsonData]
      })
      toast.add({ severity: 'success', summary: 'Успех', detail: 'JSON создан', life: 3000 })
    }

    closeJsonForm()
    await loadOptions()
  } catch (error) {
    console.error('Error saving JSON:', error)
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось сохранить JSON', life: 3000 })
  }
}

const duplicateJson = async () => {
  if (!selectedJsonItem.value) return

  try {
    const { createItems } = useDirectusItems()

    const duplicatedJson = {
      ...selectedJsonItem.value,
      id: undefined,
      title: `${selectedJsonItem.value.title} (копия)`,
      art: `${selectedJsonItem.value.art}_copy`
    }

    await createItems({
      collection: 'wjson',
      items: [duplicatedJson]
    })

    toast.add({ severity: 'success', summary: 'Успех', detail: 'JSON дублирован', life: 3000 })
    await loadOptions()
  } catch (error) {
    console.error('Error duplicating JSON:', error)
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось дублировать JSON', life: 3000 })
  }
}

const deleteJson = async () => {
  if (!selectedJsonItem.value) return

  try {
    const { deleteItems } = useDirectusItems()

    await deleteItems({
      collection: 'wjson',
      ids: [selectedJsonItem.value.id]
    })

    toast.add({ severity: 'success', summary: 'Успех', detail: 'JSON удален', life: 3000 })
    selectedJsonItem.value = null
    await loadOptions()
  } catch (error) {
    console.error('Error deleting JSON:', error)
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось удалить JSON', life: 3000 })
  }
}



// Row selection handlers
const onBlockProtoRowClick = (event) => {
  blockProtoSelectedItem.value = event.data
}

const onJsonRowClick = (event) => {
  selectedJsonItem.value = event.data
}



// Дебаунсинг для title чтобы избежать частых обновлений iframe
const debouncedTitle = ref('')
let titleUpdateTimeout: NodeJS.Timeout | null = null

watch(() => pageInfo.value.title, (newTitle) => {
  if (titleUpdateTimeout) {
    clearTimeout(titleUpdateTimeout)
  }
  titleUpdateTimeout = setTimeout(() => {
    debouncedTitle.value = newTitle
  }, 1000) // 1 секунда задержки
}, { immediate: true })

// Функция получения полного HTML
const getFullHtml = () => {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${debouncedTitle.value || 'Generated Page'}</title>
  ${customCss.value || ''}
</head>
<body>
  ${renderedHtml.value}
  ${customJs.value || ''}
</body>
</html>`
}

// Initialize component
onMounted(async () => {
  await loadOptions()
  updatePanelWidths()
  generateHtmlFromCanvas() // Генерируем HTML при загрузке
  updateEditableFieldsFromCanvas() // Инициализируем редактируемые поля

  window.addEventListener('resize', updatePanelWidths)
  document.addEventListener('click', handleClickOutside)
})

// Cleanup
onUnmounted(() => {
  window.removeEventListener('resize', updatePanelWidths)
  document.removeEventListener('click', handleClickOutside)
})


</script>

<style scoped>
.my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 10px;
    line-height: 1.4;
    padding: 2px;
  }

.prism-editor__textarea:focus {
  outline: none;
}



/* Стили для сайдбаров */
.flex > div {
  transition: width 0.3s ease;
  /* Убираем возможность resize */
  resize: none !important;
}

/* Убираем resize для всех компонентов */
* {
  resize: none !important;
}

/* Ensure forms are visible */
.flex.flex-col {
  position: relative;
  z-index: 1;
}

/* Убираем горизонтальные скроллбары в toolbar */
.toolbar-container {
  overflow: visible !important;
  position: relative;
}

.toolbar-container .flex {
  flex-wrap: nowrap !important;
  overflow: visible !important;
}

.toolbar-container .flex > * {
  flex-shrink: 0;
  min-width: 0;
}

/* Dropdown styles for toolbar */
.search-dropdown,
.filters-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  z-index: 9999 !important;
  background: white !important;
  border: 2px solid #007bff !important;
  border-radius: 6px;
  padding: 12px;
  min-width: 280px;
  max-width: 400px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25) !important;
  display: block !important;
  visibility: visible !important;
}

.search-dropdown .p-inputtext,
.filters-dropdown .p-multiselect {
  min-height: 36px;
}

.search-dropdown input,
.filters-dropdown input {
  font-size: 14px !important;
}

/* Ensure relative positioning for dropdown containers */
.relative {
  position: relative !important;
  overflow: visible !important;
}
.my {
    max-height: 80px;
  }
  .my img {
    object-fit: contain;
  }
</style>