<template>
  <WcontSidebar
    v-model:visible="sidebarVisible"
    :collapsed="false"
    title="Редактирование блока"
    @close="closeSidebar"
    @toggle-collapse="() => {}"
  >
    <div class="p-fluid">
      <!-- Базовая информация -->
      <div class="space-y-2">
        <div class="flex gap-2">
          <div class="field w-1/4">
            <InputText
              id="number"
              v-model="localItem.number"
              required
              class="w-full"
              placeholder="Номер блока*"
              style="padding: 6px; font-size: 10px"
            />
          </div>

          <div class="field w-3/4">
            <InputText
              id="title"
              v-model="localItem.title"
              required
              class="w-full"
              placeholder="Название*"
              style="padding: 6px; font-size: 11px"
            />
          </div>
        </div>

        <div class="field">
          <Textarea
            id="description"
            v-model="localItem.description"
            rows="2"
            class="w-full text-xs [>textarea]:text-xs"
            placeholder="Описание"
            style="padding: 4px; font-size: 10px"
          />
        </div>

        <div class="field mb-0" style="margin-top: 0">
          <Textarea
            id="composition"
            v-model="localItem.composition"
            rows="4"
            class="w-full text-xs [>textarea]:text-xs"
            placeholder="Композиция"
            style="padding: 4px; font-size: 10px"
          />
        </div>

        <div class="field mb-2" style="margin-top: 0">
          <MultiSelect
            v-model="localItem.block_type"
            :options="blockTypeOptions"
            placeholder="Выберите типы блока"
            display="chip"
            class="text-xs w-full p-0"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>
        <div class="flex gap-2">
          <div class="field w-1/3">
            <Dropdown
              id="status"
              v-model="localItem.status"
              :options="statusOptions"
              option-label="label"
              option-value="value"
              placeholder="Выберите статус"
              class="w-full text-xs"
              style="font-size: 11px"
            />
          </div>

          <div class="field w-2/3">
            <MultiSelect
              v-model="localItem.concept"
              :options="conceptOptions"
              placeholder="Выберите концепции"
              display="chip"
              class="w-full text-xs"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.collection"
            :options="collectionOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите коллекции"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.layout"
            :options="layoutOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите макеты"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.wpage"
            :options="wpageOptions"
            option-label="label"
            option-value="value"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите страницы"
            panel-class="text-xs"
            filter
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <!-- DataTable для связанных элементов -->
        
        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.elements"
            :options="elementOptions"
            placeholder="Выберите типы элементов"
            display="chip"
            class="w-full text-xs"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.features"
            :options="featuresOptions"
            placeholder="Выберите особенности"
            display="chip"
            class="w-full text-xs"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>
        <div class="flex gap-2">
          <div class="field w-1/2">
            <MultiSelect
              v-model="localItem.style"
              :options="styleOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите стили"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field w-1/2">
            <MultiSelect
              v-model="localItem.graphics"
              :options="graphicsOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите графику"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>

        <div class="field mb-2">
          <div class="flex gap-2">
            <Image
              v-if="localItem.sketch"
              :src="`http://localhost:8055/assets/${localItem.sketch}`"
              alt="Эскиз"
              width="200"
              class="my"
              preview
            />
            <FileUpload
              mode="basic"
              :auto="true"
              accept="image/*"
              :max-file-size="1000000"
              choose-label="Эскиз"
              class="p-button-sm"
              @select="onSketchSelect"
            />
          </div>
        </div>
        <div class="field mb-0">
          <TabView
            class="text-xs"
            :pt="{
              panelcontainer: { style: 'padding:0' },
            }"
          >
            <TabPanel
              header="HTML/CSS/JS"
              value="code"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <div class="space-y-1">
                <PrismEditorWithCopy
                  v-model="localItem.html"
                  editor-class="my-editor text-xs"
                  :highlight="highlightHtml"
                  placeholder="Введите HTML код"
                  field-name="HTML"
                  max-height="120px"
                />
                <div class="p-0 grid grid-cols-2 gap-4">
                  <div class="flex flex-col h-full">
                    <PrismEditorWithCopy
                      v-model="localItem.css"
                      editor-class="my-editor text-xs w-full"
                      :highlight="highlightCss"
                      placeholder="CSS код"
                      field-name="CSS"
                      max-height="60px !important"
                    />
                  </div>
                  <div class="flex flex-col h-full">
                    <PrismEditorWithCopy
                      v-model="localItem.js"
                      editor-class="my-editor text-xs w-full"
                      :highlight="highlightJs"
                      placeholder="JS код"
                      field-name="JavaScript"
                      max-height="60px !important"
                    />
                  </div>
                </div>
              </div>
            </TabPanel>
            <TabPanel
              header="HBS"
              value="hbs"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="localItem.hbs"
                editor-class="my-editor text-xs"
                :highlight="highlightHtml"
                placeholder="Введите HBS код"
                field-name="HBS"
                max-height="200px"
              />
            </TabPanel>
            <TabPanel
              header="JSON"
              value="json"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="localItem.json"
                editor-class="my-editor text-xs"
                :highlight="highlightJson"
                placeholder="Введите JSON код"
                field-name="JSON"
                max-height="200px"
              />
            </TabPanel>

            <TabPanel
              header="Редактор"
              value="editor"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <div>
                <!-- Быстрое добавление переменных -->
                <div class="quick-variables flex flex-wrap gap-1 mb-2">
                  <div
                    v-for="varType in quickVariableTypes"
                    :key="varType"
                    class="relative"
                  >
                    <Button
                      :label="varType"
                      class="p-button-sm p-button-outlined"
                      style="font-size: 9px; padding: 2px 6px"
                      @click="addQuickVariable(varType)"
                    />
                    <!-- Счетчик использованных переменных -->
                    <span
                      v-if="getVariableCount(varType) > 0"
                      class="absolute -top-1 -right-1 bg-blue-500 text-white rounded-full text-xs w-4 h-4 flex items-center justify-center"
                      style="font-size: 8px; min-width: 16px; min-height: 16px"
                    >
                      {{ getVariableCount(varType) }}
                    </span>
                  </div>
                </div>

                <div v-for="(field, index) in parsedJsonFields" :key="index" class="flex gap-1 items-start">
                  <div class="w-1/5">
                    <InputText
                      v-model="field.key"
                      class="w-full text-xs"
                      style="font-size: 10px; padding: 4px"
                      placeholder="Переменная"
                      @input="updateJsonFromFields"
                    />
                  </div>
                  <div class="w-3/5 relative group mb-0">
                    <Textarea
                      v-if="isTextVariable(field.key)"
                      v-model="field.value"
                      class="w-full text-xs"
                      style="font-size: 10px; padding: 4px; padding-right: 5px;"
                      placeholder="Значение"
                      rows="1"
                      @input="updateJsonFromFields"
                    />
                    <InputText
                      v-else
                      v-model="field.value"
                      class="w-full text-xs"
                      style="font-size: 10px; padding: 4px; padding-right: 5px;"
                      placeholder="Значение"
                      @input="updateJsonFromFields"
                    />
                  </div>
                  <div class="w-6">
                    <Button
                      icon="pi pi-copy"
                      class="group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                      style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); right: 4px; top: 4px;"
                      @click="copyToClipboard(field.value)"
                    />
                  </div>
                  <div class="w-1/12 text-xs text-gray-500 text-center pt-1">
                    {{ field.value ? field.value.length : 0 }}
                  </div>
                  <div class="w-1/12 flex gap-1 pt-1">
                    <Button
                      icon="pi pi-clone"
                      class="p-button-text p-button-sm"
                      style="width: 16px; height: 16px; padding: 0"
                      @click="duplicateJsonField(index)"
                    />
                    <Button
                      icon="pi pi-trash"
                      class="p-button-text p-button-sm p-button-danger"
                      style="width: 16px; height: 16px; padding: 0"
                      @click="removeJsonField(index)"
                    />
                  </div>
                </div>

                <div class="flex justify-between items-center mt-2">
                  <Button
                    label="+ Добавить поле"
                    class="p-button-sm p-button-text"
                    @click="addJsonField"
                  />
                  <span class="text-xs text-gray-500">
                    Всего переменных: {{ parsedJsonFields.length }}
                  </span>
                </div>
              </div>
            </TabPanel>

            <TabPanel
              header="Картинки"
              value="images"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <div class="grid grid-cols-2 gap-1">
                <div v-for="(imageData, index) in extractedImagesWithVars" :key="index" class="border rounded p-1">
                  <Image
                    :src="imageData.url"
                    alt="Изображение из JSON"
                    width="200"
                    preview
                    class="w-full mb-2"
                  />
                  <div class="space-y-1">
                    <div class="text-xs font-semibold text-gray-700">
                      Переменная: {{ imageData.variable }}
                    </div>
                    <div class="flex">
                      <InputText
                        v-model="imageData.url"
                        class="w-full text-xs"
                        style="font-size: 10px; padding: 4px"
                        placeholder="URL изображения"
                        @input="updateImageInJson(imageData.variable, imageData.url)"
                      />
                      <div class="w-6">
                        <Button
                          icon="pi pi-copy"
                          class="group-hover:opacity-100 transition-opacity p-button-text p-button-sm"
                          style="width: 16px; height: 16px; padding: 0; z-index: 10; background: rgba(255,255,255,0.9); right: 4px; top: 4px;"
                          @click="copyToClipboard(imageData.url)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <p v-if="!extractedImagesWithVars.length" class="text-xs text-gray-500">
                  Изображения не найдены в JSON (поиск по полям image, imageBackground)
                </p>
              </div>
            </TabPanel>
          </TabView>
        </div>
        <div class="field mb-2">
          <Textarea
            id="notes"
            v-model="localItem.notes"
            rows="1"
            class="w-full text-xs [>textarea]:text-xs"
            placeholder="Заметки"
            style="padding: 4px; font-size: 10px"
          />
        </div>
        <div class="field mb-2">
          <div class="text-xs font-semibold mb-1">Связанные элементы</div>
          <DataTable
            :value="relatedWelements"
            size="small"
            class="text-xs"
            sort-field="number"
            :sort-order="1"
            :pt="{
              table: { style: 'font-size: 10px' },
              header: { style: 'padding: 2px' },
              bodyRow: { style: 'height: 30px' },
              bodyCell: { style: 'padding: 2px' }
            }"
          >
            <Column field="sketch" header="" style="width: 50px">
              <template #body="{ data }">
                <img
                  v-if="data.sketch"
                  :src="`http://localhost:8055/assets/${data.sketch}?width=40&height=40&fit=cover`"
                  alt="Sketch"
                  class="w-10 h-auto rounded"
                >
              </template>
            </Column>
            <Column field="number" header="№" sortable style="width: 80px" />
            <Column field="title" header="Название" sortable />
            <Column field="elem_type" header="Типы" style="width: 120px">
              <template #body="{ data }">
                <div class="flex flex-wrap gap-1">
                  <Tag
                    v-for="type in data.elem_type"
                    :key="type"
                    :value="type"
                    style="padding: 0 3px; font-size: 9px"
                  />
                </div>
              </template>
            </Column>
          </DataTable>
          <span v-if="!relatedWelements.length" class="text-xs text-gray-500">Нет связанных элементов</span>
        </div>
      </div>

      <div class="flex justify-end gap-2 mt-0">
        <Button
          label="Отмена"
          icon="pi pi-times"
          class="p-button-sm"
          @click="closeSidebar"
        />
        <Button
          label="Сохранить"
          icon="pi pi-check"
          class="p-button-sm"
          :loading="saving"
          @click="saveItem"
        />
      </div>
    </div>
  </WcontSidebar>
</template>

<script setup lang="ts">
import 'vue-prism-editor/dist/prismeditor.min.css';
import Prism from 'prismjs';
import 'prismjs/components/prism-clike';
import 'prismjs/components/prism-markup';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-json';
import 'prismjs/themes/prism-tomorrow.css';

import { ref, watch, onMounted, computed } from 'vue';
import { useDirectusItems } from '#imports';
import { useToast } from 'primevue/usetoast';
import Image from 'primevue/image';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import Dropdown from 'primevue/dropdown';
import MultiSelect from 'primevue/multiselect';
import FileUpload from 'primevue/fileupload';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Tag from 'primevue/tag';
import WcontSidebar from '~/components/WcontSidebar.vue';
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue';

interface WBlock {
  id?: string;
  number: string;
  status: string;
  title: string;
  description?: string;
  concept?: string[];
  block_type?: string[];
  layout?: string[];
  wpage?: string[];
  style?: string[];
  elements?: string[];
  element_count?: number;
  composition?: string;
  graphics?: string[];
  collection?: string[];
  features?: string[];
  notes?: string;
  sketch?: string;
  welem_proto?: string[];
  html?: string;
  css?: string;
  js?: string;
  hbs?: string;
  json?: string;
  sort?: number;
  date_created?: string;
  user_created?: string;
  date_updated?: string;
  user_updated?: string;
}

const props = defineProps<{
  visible: boolean;
  editingItem: WBlock | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'saved'): void;
}>();

const { getItems, createItems, updateItem, deleteItems } = useDirectusItems();
const toast = useToast();

const createDefaultBlock = (): WBlock => ({
  id: undefined,
  number: '',
  status: 'idea',
  title: '',
  description: '',
  concept: [],
  block_type: [],
  layout: [],
  wpage: [],
  style: [],
  elements: [],
  composition: '',
  graphics: [],
  collection: [],
  features: [],
  notes: '',
  sketch: '',
  welem_proto: [],
  html: '',
  css: '',
  js: '',
  hbs: '',
  json: '',
});

const sidebarVisible = ref(props.visible);
const localItem = ref<WBlock>(createDefaultBlock());
const saving = ref(false);

// Опции
const statusOptions = [
  { label: 'Идея', value: 'idea' },
  { label: 'В разработке', value: 'in_progress' },
  { label: 'Готово', value: 'done' },
  { label: 'Архив', value: 'archived' },
];

const conceptOptions = ref<string[]>([]);
const blockTypeOptions = ref<string[]>([]);
const elementOptions = ref<string[]>([]);
const layoutOptions = ref<string[]>([]);
const styleOptions = ref<string[]>([]);
const graphicsOptions = ref<string[]>([]);
const collectionOptions = ref<string[]>([]);
const featuresOptions = ref<string[]>([]);
const welemOptions = ref<{ label: string; value: string }[]>([]);
const wpageOptions = ref<{ label: string; value: string }[]>([]);
const relatedWelements = ref([]);

// Состояние для редактора JSON
const parsedJsonFields = ref<Array<{key: string, value: string}>>([]);

// Быстрые переменные для добавления
const quickVariableTypes = ['title', 'image', 'imageBackground', 'url', 'linkText', 'icon', 'text', 'excerpt'];

const loadBlockRelations = async (blockId: string) => {
  try {
    const relations = await getItems({
      collection: 'wblock_proto_welem_proto',
      params: {
        filter: { wblock_proto_id: { _eq: blockId } },
        fields: ['welem_proto_id'],
      },
    });
    return relations.map((r: any) => r.welem_proto_id);
  } catch (error) {
    console.error('Error loading relations:', error);
    return [];
  }
};

const loadWpageRelations = async (blockId: string) => {
  try {
    const relations = await getItems({
      collection: 'wpage_wblock_proto',
      params: {
        filter: { wblock_proto_id: { _eq: blockId } },
        fields: ['wpage_id'],
      },
    });
    return relations.map((r: any) => r.wpage_id);
  } catch (error) {
    console.error('Error loading wpage relations:', error);
    return [];
  }
};

const loadRelatedElements = async (blockId: string) => {
  try {
    const relations = await getItems({
      collection: 'wblock_proto_welem_proto',
      params: {
        filter: { wblock_proto_id: { _eq: blockId } },
        fields: ['welem_proto_id.id', 'welem_proto_id.number', 'welem_proto_id.title', 'welem_proto_id.elem_type', 'welem_proto_id.sketch'],
      },
    });
    relatedWelements.value = relations.map((r: any) => r.welem_proto_id).filter(Boolean);
  } catch (error) {
    console.error('Error loading related elements:', error);
    relatedWelements.value = [];
  }
};

watch(() => props.editingItem, async (newItem) => {
  if (newItem) {
    // Загружаем связанные данные для welem_proto и wpage
    const welemProtoRelations = newItem.id ? await loadBlockRelations(newItem.id) : [];
    const wpageRelations = newItem.id ? await loadWpageRelations(newItem.id) : [];

    // Загружаем связанные элементы для отображения в DataTable
    if (newItem.id) {
      await loadRelatedElements(newItem.id);
    } else {
      relatedWelements.value = [];
    }

    console.log('WBlockProtoSidebarV2 - Loading item:', newItem.id, 'welem_proto relations:', welemProtoRelations, 'wpage relations:', wpageRelations);
    localItem.value = {
      ...createDefaultBlock(),
      ...newItem,
      welem_proto: welemProtoRelations,
      wpage: wpageRelations
    };

    // Парсим JSON для редактора
    parseJsonToFields();

    console.log('WBlockProtoSidebarV2 - localItem after update:', localItem.value);
  } else {
    localItem.value = createDefaultBlock();
    relatedWelements.value = [];
  }
}, { deep: true, immediate: true });

watch(() => props.visible, (newValue) => {
  sidebarVisible.value = newValue;
});

watch(sidebarVisible, (newValue) => {
  if (!newValue) {
    emit('update:visible', false);
  }
});


const saveItem = async () => {
  saving.value = true;
  try {
    const { id, welem_proto, wpage, ...saveData } = localItem.value;

    if (id) {
      await updateItem({
        collection: 'wblock_proto',
        id,
        item: saveData,
      });
      await saveRelations(id, welem_proto || []);
      await saveWpageRelations(id, wpage || []);
    } else {
      const result = await createItems<WBlock>({
        collection: 'wblock_proto',
        items: [saveData],
      });
      const createdItem = Array.isArray(result) ? result[0] : result;
      const newId = createdItem?.id;
      if (newId) {
        if (welem_proto?.length) {
          await saveRelations(newId, welem_proto);
        }
        if (wpage?.length) {
          await saveWpageRelations(newId, wpage);
        }
      }
    }

    emit('saved');
    closeSidebar();
    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: id ? 'Блок обновлен' : 'Блок создан',
      life: 3000,
    });
  } catch (error: any) {
    console.error('Save error:', error);
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: error.message || 'Не удалось сохранить блок',
      life: 5000,
    });
  } finally {
    saving.value = false;
  }
};

const saveRelations = async (blockId: string, welemIds: string[]) => {
  try {
    const currentRelations = await getItems({
      collection: 'wblock_proto_welem_proto',
      params: {
        filter: { wblock_proto_id: { _eq: blockId } },
        fields: ['id'],
      },
    });

    if (currentRelations.length > 0) {
      await deleteItems({
        collection: 'wblock_proto_welem_proto',
        items: currentRelations.map((r: any) => r.id),
      });
    }

    if (welemIds.length > 0) {
      await createItems({
        collection: 'wblock_proto_welem_proto',
        items: welemIds.map((id) => ({
          wblock_proto_id: blockId,
          welem_proto_id: id,
        })),
      });
    }
  } catch (error) {
    console.error('Error saving relations:', error);
    throw error;
  }
};

const saveWpageRelations = async (blockId: string, wpageIds: string[]) => {
  try {
    const currentRelations = await getItems({
      collection: 'wpage_wblock_proto',
      params: {
        filter: { wblock_proto_id: { _eq: blockId } },
        fields: ['id'],
      },
    });

    if (currentRelations.length > 0) {
      await deleteItems({
        collection: 'wpage_wblock_proto',
        items: currentRelations.map((r: any) => r.id),
      });
    }

    if (wpageIds.length > 0) {
      await createItems({
        collection: 'wpage_wblock_proto',
        items: wpageIds.map((id) => ({
          wpage_id: id,
          wblock_proto_id: blockId,
        })),
      });
    }
  } catch (error) {
    console.error('Error saving wpage relations:', error);
    throw error;
  }
};

const onSketchSelect = async (event: any) => {
  const file = event.files[0];
  if (file) {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('http://localhost:8055/files', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Ошибка загрузки файла');
      }

      const data = await response.json();
      localItem.value.sketch = data.data.id;
    } catch (error) {
      console.error('Ошибка при загрузке файла:', error);
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить файл',
      });
    }
  }
};

const closeSidebar = () => {
  emit('update:visible', false);
};

const highlightHtml = (code: string) => Prism.highlight(code, Prism.languages.markup, 'html');
const highlightCss = (code: string) => Prism.highlight(code, Prism.languages.css, 'css');
const highlightJs = (code: string) => Prism.highlight(code, Prism.languages.javascript, 'javascript');
const highlightJson = (code: string) => Prism.highlight(code, Prism.languages.json, 'json');

const loadOptions = async () => {
  try {
    const items = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: ['concept', 'block_type', 'elements', 'layout', 'style', 'graphics', 'collection', 'features'],
      },
    });

    if (Array.isArray(items)) {
      const concepts = new Set<string>();
      const blockTypes = new Set<string>();
      const elements = new Set<string>();
      const layouts = new Set<string>();
      const styles = new Set<string>();
      const graphics = new Set<string>();
      const collections = new Set<string>();
      const features = new Set<string>();

      items.forEach((item: any) => {
        item.concept?.forEach((c: string) => concepts.add(c));
        item.block_type?.forEach((t: string) => blockTypes.add(t));
        item.elements?.forEach((e: string) => elements.add(e));
        item.layout?.forEach((l: string) => layouts.add(l));
        item.style?.forEach((s: string) => styles.add(s));
        item.graphics?.forEach((g: string) => graphics.add(g));
        item.collection?.forEach((c: string) => collections.add(c));
        item.features?.forEach((f: string) => features.add(f));
      });

      conceptOptions.value = Array.from(concepts);
      blockTypeOptions.value = Array.from(blockTypes);
      elementOptions.value = Array.from(elements);
      layoutOptions.value = Array.from(layouts);
      styleOptions.value = Array.from(styles);
      graphicsOptions.value = Array.from(graphics);
      collectionOptions.value = Array.from(collections);
      featuresOptions.value = Array.from(features);

    }
  } catch (error) {
    console.error('Error loading options:', error);
  }
};

const loadWelemOptions = async () => {
  try {
    const elements = await getItems<{ id: string; title: string }>({
      collection: 'welem_proto',
      params: {
        limit: -1,
        fields: ['id', 'title'],
      },
    });
    if (Array.isArray(elements)) {
      welemOptions.value = elements.map((elem) => ({
        value: elem.id,
        label: elem.title || elem.id,
      }));
      console.log('WBlockProtoSidebarV2 - Loaded welemOptions:', welemOptions.value);
    }
  } catch (error) {
    console.error('Ошибка загрузки элементов:', error);
  }
};

const loadWpageOptions = async () => {
  try {
    const pages = await getItems<{ id: string; title: string }>({
      collection: 'wpage',
      params: {
        limit: -1,
        fields: ['id', 'title'],
      },
    });
    if (Array.isArray(pages)) {
      wpageOptions.value = pages.map((page) => ({
        value: page.id,
        label: page.title || page.id,
      }));
    }
  } catch (error) {
    console.error('Ошибка загрузки страниц:', error);
  }
};

// Функции для JSON редактора
const parseJsonToFields = () => {
  if (!localItem.value.json) {
    parsedJsonFields.value = [];
    return;
  }

  try {
    const json = JSON.parse(localItem.value.json);
    parsedJsonFields.value = Object.entries(json).map(([key, value]) => ({
      key,
      value: typeof value === 'string' ? value : JSON.stringify(value)
    }));
  } catch (error) {
    console.error('Error parsing JSON:', error);
    parsedJsonFields.value = [];
  }
};

const updateJsonFromFields = () => {
  try {
    const json = {};
    parsedJsonFields.value.forEach(field => {
      if (field.key.trim()) {
        try {
          json[field.key] = JSON.parse(field.value);
        } catch {
          json[field.key] = field.value;
        }
      }
    });
    localItem.value.json = JSON.stringify(json, null, 2);
  } catch (error) {
    console.error('Error updating JSON:', error);
  }
};

const addJsonField = () => {
  parsedJsonFields.value.push({ key: '', value: '' });
};

const removeJsonField = (index: number) => {
  parsedJsonFields.value.splice(index, 1);
  updateJsonFromFields();
};

const addQuickVariable = (varType: string) => {
  // Логика нумерации: image, image2, image3... (без image1)
  const existingFields = parsedJsonFields.value.filter(field =>
    field.key === varType || field.key.match(new RegExp(`^${varType}\\d+$`))
  );

  let newKey = varType;
  if (existingFields.length > 0) {
    // Если уже есть переменные этого типа, находим следующий номер
    let counter = 2;
    newKey = `${varType}${counter}`;
    while (parsedJsonFields.value.some(field => field.key === newKey)) {
      counter++;
      newKey = `${varType}${counter}`;
    }
  }

  const newField = {
    key: newKey,
    value: ''
  };

  parsedJsonFields.value.push(newField);
  updateJsonFromFields();
};

const getVariableCount = (varType: string) => {
  return parsedJsonFields.value.filter(field =>
    field.key === varType || field.key.startsWith(`${varType}2`) || field.key.startsWith(`${varType}3`)
  ).length;
};

const duplicateJsonField = (index: number) => {
  const originalField = parsedJsonFields.value[index];
  const baseKey = originalField.key.replace(/\d+$/, ''); // убираем цифры в конце

  // Логика нумерации: image, image2, image3... (без image1)
  const existingFields = parsedJsonFields.value.filter(field =>
    field.key === baseKey || field.key.startsWith(baseKey)
  );

  let newKey = baseKey;
  if (existingFields.length > 1) {
    // Если уже есть дубликаты, находим следующий номер
    const numbers = existingFields
      .map(field => {
        const match = field.key.match(/(\d+)$/);
        return match ? parseInt(match[1]) : (field.key === baseKey ? 1 : 0);
      })
      .filter(num => num > 0)
      .sort((a, b) => a - b);

    const nextNumber = numbers.length > 0 ? Math.max(...numbers) + 1 : 2;
    newKey = `${baseKey}${nextNumber}`;
  } else if (existingFields.length === 1) {
    newKey = `${baseKey}2`;
  }

  const duplicatedField = {
    key: newKey,
    value: originalField.value
  };

  parsedJsonFields.value.splice(index + 1, 0, duplicatedField);
  updateJsonFromFields();
};

const isTextVariable = (key: string) => {
  return key.toLowerCase().includes('text') ||
         key.toLowerCase().includes('excerpt') ||
         key.toLowerCase().includes('description');
};

// Вычисляемое свойство для извлечения изображений из JSON
const extractedImagesWithVars = computed(() => {
  if (!localItem.value.json) return [];

  try {
    const json = JSON.parse(localItem.value.json);
    const images = [];

    const extractImages = (obj: any, prefix = '') => {
      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;

        if (typeof value === 'string' && (key.includes('image') || key.includes('Image'))) {
          if (value.startsWith('http') || value.startsWith('/')) {
            images.push({
              variable: fullKey,
              url: value
            });
          }
        } else if (typeof value === 'object' && value !== null) {
          extractImages(value, fullKey);
        }
      }
    };

    extractImages(json);
    return images;
  } catch (error) {
    return [];
  }
});

const updateImageInJson = (variable: string, newUrl: string) => {
  try {
    const json = JSON.parse(localItem.value.json);
    const keys = variable.split('.');
    let current = json;

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }

    const lastKey = keys[keys.length - 1];
    current[lastKey] = newUrl;

    localItem.value.json = JSON.stringify(json, null, 2);
    parseJsonToFields();
  } catch (error) {
    console.error('Error updating image in JSON:', error);
  }
};

const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text);
  toast.add({
    severity: 'success',
    summary: 'Скопировано',
    detail: text.includes('http') ? 'URL скопирован в буфер обмена' : 'Текст скопирован в буфер обмена',
    life: 2000
  });
};

onMounted(() => {
  loadOptions();
  loadWelemOptions();
  loadWpageOptions();
});

</script>

<style scoped>
.my-editor {
  background: #f3f3f3;
  color: #666;
  font-family: 'Fira code', 'Fira Mono', Consolas, Menlo, Courier, monospace;
  font-size: 9px;
  line-height: 1.4;
  padding: 2px;
}
.prism-editor__textarea:focus {
  outline: none;
}
.my {
  max-height: 120px;
}
</style>