<template>
  <WcontSidebar
    v-model:visible="sidebarVisible"
    :collapsed="false"
    title="Редактирование блока"
    @close="closeSidebar"
    @toggle-collapse="() => {}"
  >
    <div class="p-fluid">
      <!-- Базовая информация -->
      <div class="space-y-2">
        <div class="flex gap-2">
          <div class="field w-1/4">
            <InputText
              id="number"
              v-model="localItem.number"
              required
              class="w-full"
              placeholder="Номер блока*"
              style="padding: 6px; font-size: 10px"
            />
          </div>

          <div class="field w-3/4">
            <InputText
              id="title"
              v-model="localItem.title"
              required
              class="w-full"
              placeholder="Название*"
              style="padding: 6px; font-size: 11px"
            />
          </div>
        </div>

        <div class="field">
          <Textarea
            id="description"
            v-model="localItem.description"
            rows="2"
            class="w-full text-xs [>textarea]:text-xs"
            placeholder="Описание"
            style="padding: 4px; font-size: 10px"
          />
        </div>

        <div class="field mb-0" style="margin-top: 0">
          <Textarea
            id="composition"
            v-model="localItem.composition"
            rows="4"
            class="w-full text-xs [>textarea]:text-xs"
            placeholder="Композиция"
            style="padding: 4px; font-size: 10px"
          />
        </div>

        <div class="field mb-2" style="margin-top: 0">
          <MultiSelect
            v-model="localItem.block_type"
            :options="blockTypeOptions"
            placeholder="Выберите типы блока"
            display="chip"
            class="text-xs w-full p-0"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>
        <div class="flex gap-2">
          <div class="field w-1/3">
            <Dropdown
              id="status"
              v-model="localItem.status"
              :options="statusOptions"
              option-label="label"
              option-value="value"
              placeholder="Выберите статус"
              class="w-full text-xs"
              style="font-size: 11px"
            />
          </div>

          <div class="field w-2/3">
            <MultiSelect
              v-model="localItem.concept"
              :options="conceptOptions"
              placeholder="Выберите концепции"
              display="chip"
              class="w-full text-xs"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.collection"
            :options="collectionOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите коллекции"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.layout"
            :options="layoutOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите макеты"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.welem_proto"
            :options="welemOptions"
            option-label="label"
            option-value="value"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите элементы"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>
        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.elements"
            :options="elementOptions"
            placeholder="Выберите типы элементов"
            display="chip"
            class="w-full text-xs"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>

        <div class="field mb-2">
          <MultiSelect
            v-model="localItem.features"
            :options="featuresOptions"
            placeholder="Выберите особенности"
            display="chip"
            class="w-full text-xs"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
        </div>
        <div class="flex gap-2">
          <div class="field w-1/2">
            <MultiSelect
              v-model="localItem.style"
              :options="styleOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите стили"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field w-1/2">
            <MultiSelect
              v-model="localItem.graphics"
              :options="graphicsOptions"
              display="chip"
              class="w-full text-xs"
              placeholder="Выберите графику"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>
        </div>

        <div class="field mb-2">
          <div class="flex gap-2">
            <Image
              v-if="localItem.sketch"
              :src="`http://localhost:8055/assets/${localItem.sketch}`"
              alt="Эскиз"
              width="200"
              class="my"
              preview
            />
            <FileUpload
              mode="basic"
              :auto="true"
              accept="image/*"
              :max-file-size="1000000"
              choose-label="Эскиз"
              class="p-button-sm"
              @select="onSketchSelect"
            />
          </div>
        </div>
        <div class="field mb-0">
          <TabView
            class="text-xs"
            :pt="{
              panelcontainer: { style: 'padding:0' },
            }"
          >
            <TabPanel
              header="HTML/CSS/JS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <div class="space-y-1">
                <PrismEditorWithCopy
                  v-model="localItem.html"
                  editor-class="my-editor text-xs"
                  :highlight="highlightHtml"
                  placeholder="Введите HTML код"
                  field-name="HTML"
                  max-height="120px"
                />
                <div class="p-0 grid grid-cols-2 gap-4">
                  <div class="flex flex-col h-full">
                    <PrismEditorWithCopy
                      v-model="localItem.css"
                      editor-class="my-editor text-xs w-full"
                      :highlight="highlightCss"
                      placeholder="CSS код"
                      field-name="CSS"
                      max-height="60px !important"
                    />
                  </div>
                  <div class="flex flex-col h-full">
                    <PrismEditorWithCopy
                      v-model="localItem.js"
                      editor-class="my-editor text-xs w-full"
                      :highlight="highlightJs"
                      placeholder="JS код"
                      field-name="JavaScript"
                      max-height="60px !important"
                    />
                  </div>
                </div>
              </div>
            </TabPanel>
            <TabPanel
              header="HBS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="localItem.hbs"
                editor-class="my-editor text-xs"
                :highlight="highlightHtml"
                placeholder="Введите HBS код"
                field-name="HBS"
                max-height="200px"
              />
            </TabPanel>
            <TabPanel
              header="JSON"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="localItem.json"
                editor-class="my-editor text-xs"
                :highlight="highlightJson"
                placeholder="Введите JSON код"
                field-name="JSON"
                max-height="200px"
              />
            </TabPanel>
          </TabView>
        </div>
        <div class="field mb-2">
          <Textarea
            id="notes"
            v-model="localItem.notes"
            rows="1"
            class="w-full text-xs [>textarea]:text-xs"
            placeholder="Заметки"
            style="padding: 4px; font-size: 10px"
          />
        </div>
      </div>

      <div class="flex justify-end gap-2 mt-0">
        <Button
          label="Отмена"
          icon="pi pi-times"
          class="p-button-sm"
          @click="closeSidebar"
        />
        <Button
          label="Сохранить"
          icon="pi pi-check"
          class="p-button-sm"
          :loading="saving"
          @click="saveItem"
        />
      </div>
    </div>
  </WcontSidebar>
</template>

<script setup lang="ts">
import 'vue-prism-editor/dist/prismeditor.min.css';
import Prism from 'prismjs';
import 'prismjs/components/prism-clike';
import 'prismjs/components/prism-markup';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-json';
import 'prismjs/themes/prism-tomorrow.css';

import { ref, watch, onMounted } from 'vue';
import { useDirectusItems } from '#imports';
import { useToast } from 'primevue/usetoast';
import Image from 'primevue/image';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import Dropdown from 'primevue/dropdown';
import MultiSelect from 'primevue/multiselect';
import FileUpload from 'primevue/fileupload';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import WcontSidebar from '~/components/WcontSidebar.vue';
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue';

interface WBlock {
  id?: string;
  number: string;
  status: string;
  title: string;
  description?: string;
  concept?: string[];
  block_type?: string[];
  layout?: string[];
  style?: string[];
  elements?: string[];
  element_count?: number;
  composition?: string;
  graphics?: string[];
  collection?: string[];
  features?: string[];
  notes?: string;
  sketch?: string;
  welem_proto?: string[];
  html?: string;
  css?: string;
  js?: string;
  hbs?: string;
  json?: string;
  sort?: number;
  date_created?: string;
  user_created?: string;
  date_updated?: string;
  user_updated?: string;
}

const props = defineProps<{
  visible: boolean;
  editingItem: WBlock | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'saved'): void;
}>();

const { getItems, createItems, updateItem, deleteItems } = useDirectusItems();
const toast = useToast();

const sidebarVisible = ref(props.visible);
const localItem = ref<WBlock>({} as WBlock);
const saving = ref(false);

// Опции
const statusOptions = [
  { label: 'Идея', value: 'idea' },
  { label: 'В разработке', value: 'in_progress' },
  { label: 'Готово', value: 'done' },
  { label: 'Архив', value: 'archived' },
];

const conceptOptions = ref<string[]>([]);
const blockTypeOptions = ref<string[]>([]);
const elementOptions = ref<string[]>([]);
const layoutOptions = ref<string[]>([]);
const styleOptions = ref<string[]>([]);
const graphicsOptions = ref<string[]>([]);
const collectionOptions = ref<string[]>([]);
const featuresOptions = ref<string[]>([]);
const welemOptions = ref<{ label: string; value: string }[]>([]);

watch(() => props.visible, (newValue) => {
  sidebarVisible.value = newValue;
  if (newValue && props.editingItem) {
    loadBlockData(props.editingItem.id!);
  } else {
    // Инициализация для нового блока с значениями по умолчанию
    localItem.value = {
      id: undefined,
      number: '',
      status: 'idea',
      title: '',
      description: '',
      concept: [],
      block_type: [],
      layout: [],
      style: [],
      elements: [],
      composition: '',
      graphics: [],
      collection: [],
      features: [],
      notes: '',
      sketch: '',
      welem_proto: [],
      html: '',
      css: '',
      js: '',
      hbs: '',
      json: '',
    };
  }
});

watch(sidebarVisible, (newValue) => {
  if (!newValue) {
    emit('update:visible', false);
  }
});


const saveItem = async () => {
  saving.value = true;
  try {
    const { id, welem_proto, ...saveData } = localItem.value;

    if (id) {
      await updateItem({
        collection: 'wblock_proto',
        id,
        item: saveData,
      });
      await saveRelations(id, welem_proto || []);
    } else {
      const result = await createItems({
        collection: 'wblock_proto',
        items: [saveData],
      });
      const newId = Array.isArray(result) ? result[0]?.id : (result as any)?.id;
      if (newId && welem_proto?.length) {
        await saveRelations(newId, welem_proto);
      }
    }

    emit('saved');
    closeSidebar();
    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: id ? 'Блок обновлен' : 'Блок создан',
      life: 3000,
    });
  } catch (error: any) {
    console.error('Save error:', error);
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: error.message || 'Не удалось сохранить блок',
      life: 5000,
    });
  } finally {
    saving.value = false;
  }
};

const saveRelations = async (blockId: string, welemIds: string[]) => {
  try {
    const currentRelations = await getItems({
      collection: 'wblock_proto_welem_proto',
      params: {
        filter: { wblock_proto_id: { _eq: blockId } },
        fields: ['id'],
      },
    });

    if (currentRelations.length > 0) {
      await deleteItems({
        collection: 'wblock_proto_welem_proto',
        items: currentRelations.map((r: any) => r.id),
      });
    }

    if (welemIds.length > 0) {
      await createItems({
        collection: 'wblock_proto_welem_proto',
        items: welemIds.map((id) => ({
          wblock_proto_id: blockId,
          welem_proto_id: id,
        })),
      });
    }
  } catch (error) {
    console.error('Error saving relations:', error);
    throw error;
  }
};

const onSketchSelect = async (event: any) => {
  const file = event.files[0];
  if (file) {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('http://localhost:8055/files', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Ошибка загрузки файла');
      }

      const data = await response.json();
      localItem.value.sketch = data.data.id;
    } catch (error) {
      console.error('Ошибка при загрузке файла:', error);
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить файл',
      });
    }
  }
};

const closeSidebar = () => {
  emit('update:visible', false);
};

const highlightHtml = (code: string) => Prism.highlight(code, Prism.languages.markup, 'html');
const highlightCss = (code: string) => Prism.highlight(code, Prism.languages.css, 'css');
const highlightJs = (code: string) => Prism.highlight(code, Prism.languages.javascript, 'javascript');
const highlightJson = (code: string) => Prism.highlight(code, Prism.languages.json, 'json');

const loadOptions = async () => {
  try {
    const items = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: ['concept', 'block_type', 'elements', 'layout', 'style', 'graphics', 'collection', 'features'],
      },
    });

    if (Array.isArray(items)) {
      const concepts = new Set<string>();
      const blockTypes = new Set<string>();
      const elements = new Set<string>();
      const layouts = new Set<string>();
      const styles = new Set<string>();
      const graphics = new Set<string>();
      const collections = new Set<string>();
      const features = new Set<string>();

      items.forEach((item: any) => {
        item.concept?.forEach((c: string) => concepts.add(c));
        item.block_type?.forEach((t: string) => blockTypes.add(t));
        item.elements?.forEach((e: string) => elements.add(e));
        item.layout?.forEach((l: string) => layouts.add(l));
        item.style?.forEach((s: string) => styles.add(s));
        item.graphics?.forEach((g: string) => graphics.add(g));
        item.collection?.forEach((c: string) => collections.add(c));
        item.features?.forEach((f: string) => features.add(f));
      });

      conceptOptions.value = Array.from(concepts);
      blockTypeOptions.value = Array.from(blockTypes);
      elementOptions.value = Array.from(elements);
      layoutOptions.value = Array.from(layouts);
      styleOptions.value = Array.from(styles);
      graphicsOptions.value = Array.from(graphics);
      collectionOptions.value = Array.from(collections);
      featuresOptions.value = Array.from(features);
    }
  } catch (error) {
    console.error('Error loading options:', error);
  }
};

const loadWelemOptions = async () => {
  try {
    const elements = await getItems<{ id: string; title: string }>({
      collection: 'welem_proto',
      params: {
        limit: -1,
        fields: ['id', 'title'],
      },
    });

    if (Array.isArray(elements)) {
      welemOptions.value = elements.map((elem) => ({
        value: elem.id,
        label: elem.title || elem.id,
      }));
    }
  } catch (error) {
    console.error('Ошибка загрузки элементов:', error);
  }
};

onMounted(() => {
  loadOptions();
  loadWelemOptions();
});

</script>

<style scoped>
.my-editor {
  background: #f3f3f3;
  color: #666;
  font-family: 'Fira code', 'Fira Mono', Consolas, Menlo, Courier, monospace;
  font-size: 9px;
  line-height: 1.4;
  padding: 2px;
}
.prism-editor__textarea:focus {
  outline: none;
}
.my {
  max-height: 120px;
}
</style>