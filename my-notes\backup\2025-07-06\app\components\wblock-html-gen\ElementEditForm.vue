<template>
  <div class="h-full p-1 bg-white overflow-auto">


    <div class="flex flex-col gap-2">
      <!-- Номер элемента -->
      <div class="field">

        <InputText id="number" v-model="element.number" class="w-full text-xs [&>input]:text-xs" placeholder="Номер блока*" style="padding:6px; font-size:10px;" />
      </div>

      <!-- Название элемента -->
      <div class="field">

        <InputText id="title" v-model="element.title" class="w-full text-xs [&>input]:text-xs" placeholder="Название*" style="padding:4px; font-size:10px;" />
      </div>

      <!-- Описание элемента -->
      <div class="field mb-0" style="margin-bottom: 0">

        <Textarea id="description" v-model="element.description" rows="2" class="w-full text-xs [&>textarea]:text-xs" auto-resize placeholder="Описание" style="padding:2px; font-size:9px;" />
      </div>
      <div class="field mb-0" style="margin-top: 0;margin-bottom: 0">
          <Textarea
            id="composition"
            v-model="element.composition"
            rows="3"
            class="w-full text-xs [&>textarea]:text-xs"
            placeholder="Композиция"
            style="padding: 2px; font-size: 8px"
          />
        </div>
      <!-- Типы элемента -->
      <div class="field">
        
        <MultiSelect
          id="elem_type"
          v-model="element.elem_type"
          :options="availableTypes"
          placeholder="Выберите типы"
          class="w-full"
          display="chip"
          panel-class="text-xs w-full p-0"
                      style="font-size:11px;"
  :pt="{
    item: { class: 'text-xs' },
    header: { class: 'text-xs' }
  }"
        />
      </div>
      <div class="field mb-0" style="margin-bottom: 0">
            <MultiSelect
            id="collection"
            v-model="element.collection"
            :options="combinedCollectionOptions"
            display="chip"
            class="w-full text-xs"
            placeholder="Выберите коллекции"
            panel-class="text-xs"
            style="font-size: 11px"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
      </div>
      <div class="field mb-0">

          <div class="flex flex-col gap-4">
            <FileUpload
              mode="basic"
              :auto="true"
              accept="image/*"
              :max-file-size="1000000"
              choose-label="Эскиз"
              class="p-button-sm"
              @select="onSketchSelect"
            />
            <Image v-if="element.sketch" :src="`http://localhost:8055/assets/${element.sketch}`" alt="Эскиз" width="200" class="my" preview />
          </div>
        </div>

        <div class="field mb-0">
          <TabView
            class="text-xs"
            :pt="{
            panelcontainer: { style: 'padding:0' },
          }"
          >
            <TabPanel
              header="HTML/CSS/JS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <div class="space-y-1">
                <PrismEditorWithCopy
                  v-model="element.html"
                  class="my-editor h-[220px] overflow-auto text-xs max-h-[220px]"
                  :highlight="highlightHtml"
                  placeholder="Введите HTML код"
                  line-numbers
                />
                <div class="flex gap-2">
                  <PrismEditorWithCopy
                    v-model="element.css"
                    class="my-editor h-[40px] overflow-auto text-xs max-h-[40px]"
                    :highlight="highlightCss"
                    placeholder="CSS код"
                    line-numbers
                  />
                  <PrismEditorWithCopy
                    v-model="element.js"
                    class="my-editor h-[40px] overflow-auto text-xs max-h-[40px]"
                    :highlight="highlightJs"
                    placeholder="JS код"
                    line-numbers
                  />
                </div>
              </div>
            </TabPanel>
            <TabPanel
              header="HBS"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="element.hbs"
                class="my-editor h-[300px] overflow-auto text-xs max-h-[300px]"
                :highlight="highlightHtml"
                placeholder="Введите HBS код"
                line-numbers
              />
            </TabPanel>
            <TabPanel
              header="JSON"
              :pt="{
                header: { class: 'p-0' },
                headerAction: { class: 'text-xs p-0' },
                content: { class: 'p-0' }
              }">
              <PrismEditorWithCopy
                v-model="element.json"
                class="my-editor h-[300px] overflow-auto text-xs max-h-[300px]"
                :highlight="highlightJson"
                placeholder="Введите JSON код"
                line-numbers
              />
            </TabPanel>
          </TabView>
        </div>

      

      <!-- Кнопки управления -->
      <div class="flex justify-end gap-2 mt-2">
        <Button label="Отмена" text class="p-button-sm" @click="cancel" />
        <Button label="Сохранить" class="p-button-sm" @click="save" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, onMounted  } from 'vue'
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import 'vue-prism-editor/dist/prismeditor.min.css'
import Textarea from 'primevue/textarea'
import FileUpload from 'primevue/fileupload'
import Image from 'primevue/image'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import 'primevue/textarea/style'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'
import Toast from 'primevue/toast'


const toast = useToast()
const { getItems } = useDirectusItems()

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  availableTypes: {
    type: Array,
    default: () => []
  },

  availableCollect: {
    type: Array,
    default: () => []
  }

})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

// Создаем локальную копию элемента для редактирования
const element = ref({ ...props.modelValue })

// Определяем, создаем ли мы новый элемент или редактируем существующий
const isNew = computed(() => !props.modelValue.id)

// Локальные опции для collection
const localCollectionOptions = ref([])

// Загрузка опций collection из Directus
const loadCollectionOptions = async () => {
  try {
    const items = await getItems({
      collection: 'welem_proto',
      params: { limit: -1 },
    })

    if (Array.isArray(items)) {
      const collections = new Set()
      items.forEach((item) => {
        item.collection?.forEach((c) => collections.add(c))
      })
      localCollectionOptions.value = Array.from(collections)
    }
  } catch (error) {
    console.error('Ошибка загрузки опций collection:', error)
    // Fallback к статическим опциям
    localCollectionOptions.value = [
      'website',
      'landing',
      'shop',
      'blog',
      'portfolio',
      'corporate',
      'education',
      'medical',
      'restaurant',
      'real-estate',
      'travel',
      'fitness',
      'beauty',
      'technology',
      'finance',
      'legal',
      'nonprofit',
      'event',
      'music',
      'art',
    ]
  }
}

// Объединяем локальные и переданные опции
const combinedCollectionOptions = computed(() => {
  const combined = new Set([
    ...localCollectionOptions.value,
    ...props.availableCollect
  ])
  return Array.from(combined)
})



// Функция для подсветки кода

const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightHbs = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

async function onSketchSelect(event) {
  const file = event.files[0];
  if (!file) return;

  try {
    const uploadFormData = new FormData(); // Изменили имя переменной
    uploadFormData.append('file', file);

    const response = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: uploadFormData // Используем новое имя переменной
    });

    if (!response.ok) throw new Error('Ошибка загрузки файла');

    const { data } = await response.json();
    element.value.sketch = data.id;

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Эскиз загружен',
      life: 3000
    });
  } catch (error) {
    console.error('Ошибка при загрузке эскиза:', error);
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить эскиз',
      life: 3000
    });
  }
}

// Функция сохранения элемента
const save = () => {
  emit('update:modelValue', { ...element.value })
  emit('save')
}

// Функция отмены редактирования
const cancel = () => {
  emit('cancel')
}

// Реактивное обновление формы при изменении props
watch(() => props.modelValue, (newValue) => {
  element.value = { ...newValue }
}, { immediate: true })

// Инициализация при монтировании
onMounted(() => {
  loadCollectionOptions()
})
</script>

<style scoped>
.my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family: Fira code, Fira Mono, Consolas, Menlo, Courier, monospace;
    font-size: 9px;
    line-height: 1.4;
    padding: 2px;
  }

  /* optional class for removing the outline */
  .prism-editor__textarea:focus {
    outline: none;
  }
</style>