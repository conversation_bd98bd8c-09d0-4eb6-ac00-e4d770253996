<template>
  <Dialog 
    v-model:visible="localVisible" 
    :header="title" 
    :style="{ width: '80vw' }"
    modal
  >
    <div class="text-content">
      <div v-if="subtitle" class="subtitle">{{ subtitle }}</div>
      <div class="main-text">{{ text }}</div>
      
      <div v-if="items && items.length" class="nested-items mt-4">
        <h3>Дополнительные материалы:</h3>
        <div 
          v-for="(item, index) in items" 
          :key="index" 
          class="nested-item"
        >
          <h4>{{ item.title }}</h4>
          <p>{{ item.text }}</p>
          <a 
            v-if="item.url" 
            :href="item.url" 
            target="_blank" 
            class="text-blue-500 hover:underline"
          >
            {{ item.linkText || 'Ссылка' }}
          </a>
        </div>
      </div>
    </div>

    <template #footer>
      <Button 
        label="Закрыть" 
        icon="pi pi-times" 
        @click="closeDialog"
      />
      <Button 
        v-if="copyEnabled" 
        label="Копировать" 
        icon="pi pi-copy" 
        severity="secondary" 
        @click="copyText"
      />
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import Dialog from 'primevue/dialog'
import Button from 'primevue/button'
import { useToast } from 'primevue/usetoast'

interface NestedItem {
  title?: string
  text?: string
  url?: string
  linkText?: string
}

const props = withDefaults(defineProps<{
  visible: boolean
  title?: string
  subtitle?: string
  text?: string
  items?: NestedItem[]
  copyEnabled?: boolean
}>(), {
  title: 'Полный текст',
  subtitle: '',
  text: '',
  items: () => [],
  copyEnabled: true
})

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const toast = useToast()
const localVisible = ref(props.visible)

const closeDialog = () => {
  localVisible.value = false
  emit('update:visible', false)
}

const copyText = () => {
  if (!props.text) return

  navigator.clipboard.writeText(props.text)
    .then(() => {
      toast.add({ 
        severity: 'success', 
        summary: 'Копирование', 
        detail: 'Текст скопирован в буфер обмена', 
        life: 2000 
      })
    })
    .catch(err => {
      console.error('Ошибка копирования:', err)
      toast.add({ 
        severity: 'error', 
        summary: 'Ошибка', 
        detail: 'Не удалось скопировать текст', 
        life: 2000 
      })
    })
}

watch(() => props.visible, (newValue) => {
  localVisible.value = newValue
})
</script>

<style scoped>
.text-content {
  line-height: 1.6;
}
.subtitle {
  font-style: italic;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}
.main-text {
  white-space: pre-wrap;
}
.nested-items {
  background-color: var(--surface-50);
  padding: 1rem;
  border-radius: 4px;
}
.nested-item {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-100);
}
.nested-item:last-child {
  border-bottom: none;
}
</style>
