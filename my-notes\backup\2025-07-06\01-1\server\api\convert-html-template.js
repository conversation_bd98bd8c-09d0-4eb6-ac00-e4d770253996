// server/api/convert-html-template.js
import { defineEventHand<PERSON>, readBody, createError } from 'h3';
import { htmlToHandlebarsAndJson, htmlToPugAndJson } from '../utils/htmlToTemplate.js';

/**
 * API-маршрут для конвертации HTML в шаблоны (Handlebars или Pug) и JSON данные
 * Позволяет отдельно отлаживать и запускать конвертацию HTML в шаблоны
 */
export default defineEventHandler(async (event) => {
  try {
    // Получаем данные из запроса
    const { html, format = 'handlebars', blockName = '', blockNumber = '' } = await readBody(event);
    
    console.log(`🔄 Запрос на конвертацию HTML в ${format}...`);
    console.log(`📊 Размер HTML: ${html ? html.length : 0} символов`);
    
    if (!html || html.trim().length === 0) {
      console.warn('⚠️ Получен пустой HTML для конвертации');
      return {
        success: false,
        error: 'Пустой HTML для конвертации',
        template: '',
        jsonData: {}
      };
    }
    
    // Выбираем функцию конвертации в зависимости от запрошенного формата
    let result;
    if (format.toLowerCase() === 'pug') {
      console.log('🔄 Конвертация HTML в Pug...');
      result = htmlToPugAndJson(html, blockName, blockNumber);
    } else {
      console.log('🔄 Конвертация HTML в Handlebars...');
      result = htmlToHandlebarsAndJson(html, blockName, blockNumber);
    }
    
    // Формируем ответ
    const response = {
      success: result.success,
      error: result.error,
      template: format.toLowerCase() === 'pug' ? result.pugTemplate : result.hbsTemplate,
      jsonData: result.jsonData
    };
    
    if (result.success) {
      console.log(`✅ Конвертация HTML в ${format} успешно завершена`);
      console.log(`📊 Сгенерировано ${Object.keys(result.jsonData).length} переменных для шаблона`);
    } else {
      console.error(`❌ Ошибка при конвертации HTML в ${format}: ${result.error}`);
    }
    
    return response;
  } catch (error) {
    console.error('❌ Общая ошибка при конвертации HTML:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message
    });
  }
});