import beautify from 'js-beautify';

export default defineEventHandler(async (event) => {
  const body = await readBody(event); // Читаем тело запроса
  const html = body.html;

  if (!html) {
    return sendError(event, createError({ statusCode: 400, statusMessage: 'HTML код не найден в теле запроса.' }));
  }

  const formattedHtml = beautify.html(html, { indent_size: 2 });
  return { html: formattedHtml };
});

