----01
На странице wpage-gen2 я вижу следующие проблемы
##Левая панель
1. при нажатии на редактирование блока из карточки должен появляться полноценный сайдбар с редактированием блоки из базы в точности как app\components\WBlockProtoSidebarV2.vue (а сейчас появляется какое-то бесполезное модальное окно в котором я вижу только название блока и пару кнопок) - это полностью не функционально и полностью не соответсвует поставленной задаче!
2. при добавлении блоков в области "схема страницы" должны отображаться изначальные title блоков (так проще будет сориентироваться). Сейчас в схеме блоки перименовываются . Также можно напротив заголовка "схема страницы" писать общее количество блоков), а напротив контейнеров блоков слева писать порядковый номер на холсте - 01, 02, 03 и т.д.
(также я вижу что в html код добавляется комментарий в данными блока - лучше пусть в нем также будет отражен окончательный number и title (который присваивается при заполнении number и title страницы - в верхних полях в центральной области)
3. к карточках чекбокс надо сделать в левом верхнем углу и разместить поверх картинки, картинка должна отображаться без искажений (object-fit contain по центру - по ширине и по высоте)
4. в тулбарах неоптимальные фильтры - при нажатии на иконку открывается поле в котором надо снова открывать dropdown (получаются лишние клики)

##Правый сайдбар
Сделать Табы "Редактор" и Картинки (табы должны быть компактные (смоти как сделано в app\components\WBlockProtoSidebarV2.vue
- добавить редактор (интеллектуальный редактор ПОЛНОСТЬЮ! аналогичный табу "Редактор" в app\components\WBlockProtoSidebarV2.vue связанный с полем json
-поля JSON и HBS поместить в соответсвующие табы
- картинки (интеллектуальный редактор аналогичный табу "Картинки" в app\components\WBlockProtoSidebarV2.vue связанный с полем json

----01-1

На странице wpage-gen2 я вижу следующие проблемы и задачи
1. при нажатии на редактирование блока из карточки открывается сайдбар с пустой формой - т.е. я не вижу в форме данные соотвествующие выбранному блоку (глубоко проанализируй причины)
2. к карточках чекбокс надо сделать в левом верхнем углу и разместить поверх картинки, картинка должна отображаться без искажений (object-fit contain по центру - по ширине и по высоте)
3. Область "Схема страницы" можно перенести в правую область, наверх (так - же 30% от общей высоты)
4. Заголовок области "редактирование блока" можно заменить на отображение порядкового номера блоки из схемы + оригинального title добавленного блока из базы (по такому же принципу формируются надписи в контейнерах-блоках в области "схема страницы")
5. в области редактирования данные из таба "редактор" и "Картинки" синхронизируются с полем json только в одну сторону (при редактировании данных из поля json они не меняются в соответсвующих полях в редакторе) необходимо это исправить
6. в области редактирования надо добавить multiselect поле с loadOptions title из коллекции wblock_proto directus/ Также необходимо что в это поле записывался title добавленного оригинального блока (таким образом мы связываем новый экземпляр блока с оригинальным, поэтому при сохранении эти связи должны быть правильно сохранены - m2m связь установлена через поле wblock_proto - узловая коллекция wblock_proto_wblock_proto - wblock_proto_id <-> related_wblock_proto_id)


----01-2

при открытии страницы wpage-gen2 через браузер я вижу сообщение "Вкладки использует много памяти" 2-3гб (даже когда просто только открыл страницу и еще ничего не начал делать - 2гб, а потом когда делаю уже 3гб)
что именно и почему потребляет столько ресурсов? как это можно точно установить, чтобы не предполагать (может доат нужно добавить какое-то логирование или есть какие-то простые быстрые и эффективные программные тесты)? проведи глубокий анализ причин проблемы (учитывай тот файт, что, например на странице wpages использование памяти всего 140-400мб )
я открыл страницу wpages и перешел в chrome dev tools (вкладка memory) нажал take snapshot - вижу snapshot 1 (141mb)
я открыл страницу wpage-gen2 и перешел в chrome dev tools (вкладка memory) нажал take snapshot (анализ делался достаточно долго - 7-10 минут)  - вижу snapshot 1 (705mb)


также я прошу тебя немного доработать интерфейс центральной части - там где расположены табы - я хочу чтобы была кнопка "сворачивания"/collapsible/accordion, уменьшения этой области а ее место тогда занимал iframe

----
подолжаем решение проблемы (вкладка использует много памяти)

в терминале при входе на страницу wpage-gen2 я вижу следующую ошибку
ERROR  [nitro] [unhandledRejection] getComputedStyle is not defined

    at Proxy.setLineNumbersHeight (vue-prism-editor:130:7)
    at Proxy.<anonymous> (vue-prism-editor:98:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)


 WARN  [nuxt] Failed to stringify dev server logs. Received DevalueError: Cannot stringify arbitrary non-POJOs. You can define your own reducer/reviver for rich types following the instructions in https://nuxt.com/docs/api/composables/use-nuxt-app#payload.
 
 может ли она вызывать проблему с чрезмерным использованием памяти?
 chrome пишет что вкладка использует много памяти (2.5гб)
 мне кажется что это связано с использованием iframe
 
 в консоли
Uncaught (in promise) Error: Resource::kQuotaBytesPerItem quota exceededUnderstand this error
prism-editor-global.client.ts:6 [Vue warn]: Component "PrismEditorWithCopy" has already been registered in target app.
warn$1 @ runtime-core.esm-bundler.js?v=767cd15b:50
component @ runtime-core.esm-bundler.js?v=767cd15b:3913
(anonymous) @ prism-editor-global.client.ts:6
(anonymous) @ nuxt.js?v=767cd15b:139
fn @ nuxt.js?v=767cd15b:216
runWithContext @ runtime-core.esm-bundler.js?v=767cd15b:4013
callWithNuxt @ nuxt.js?v=767cd15b:222
(anonymous) @ nuxt.js?v=767cd15b:38
run @ reactivity.esm-bundler.js?v=767cd15b:66
runWithContext @ nuxt.js?v=767cd15b:38
applyPlugin @ nuxt.js?v=767cd15b:139
executePlugin @ nuxt.js?v=767cd15b:158
applyPlugins @ nuxt.js?v=767cd15b:189
await in applyPlugins
initApp @ entry.js:58
(anonymous) @ entry.js:73
setTimeout
(anonymous) @ __uno.css:19
(anonymous) @ __uno.css:19Understand this warning
runtime-core.esm-bundler.js?v=767cd15b:7031 <Suspense> is an experimental feature and its API will likely change.
web-socket.js?v=767cd15b:5 [Content] WS connect to ws://localhost:4001/ws
web-socket.js?v=767cd15b:5 [Content] WS connected!
devtools.client.js?v=767cd15b:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
__uno.css:19 Deprecated since v4. Use Tabs component instead.
mounted @ primevue_tabview.js?v=767cd15b:155
(anonymous) @ runtime-core.esm-bundler.js?v=767cd15b:2841
callWithErrorHandling @ runtime-core.esm-bundler.js?v=767cd15b:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=767cd15b:204
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js?v=767cd15b:2821
flushPostFlushCbs @ runtime-core.esm-bundler.js?v=767cd15b:382
flushJobs @ runtime-core.esm-bundler.js?v=767cd15b:424
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=767cd15b:319
queuePostFlushCb @ runtime-core.esm-bundler.js?v=767cd15b:333
resolve @ runtime-core.esm-bundler.js?v=767cd15b:7135
resolve @ runtime-core.esm-bundler.js?v=767cd15b:7142
(anonymous) @ runtime-core.esm-bundler.js?v=767cd15b:7241
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=767cd15b:7205
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5247
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=767cd15b:7282
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=767cd15b:7282
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrate @ runtime-core.esm-bundler.js?v=767cd15b:1673
mount @ runtime-core.esm-bundler.js?v=767cd15b:3956
app.mount @ runtime-dom.esm-bundler.js?v=767cd15b:1761
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:73
setTimeout
(anonymous) @ __uno.css:19
(anonymous) @ __uno.css:19Understand this warning
wpage-gen2.vue:2290 🧠 Memory [Page mounted]: {used: '556MB', total: '597MB', limit: '4096MB'}
wpage-gen2.vue:2290 🧠 Memory [Before loading blocks]: {used: '556MB', total: '597MB', limit: '4096MB'}
wpage-gen2.vue:912 Загрузка данных блоков...
vue-prism-editor.js?v=767cd15b:133 Uncaught (in promise) TypeError: Failed to execute 'getComputedStyle' on 'Window': parameter 1 is not of type 'Element'.
    at Proxy.setLineNumbersHeight (vue-prism-editor.js?v=767cd15b:133:32)
    at Proxy.<anonymous> (vue-prism-editor.js?v=767cd15b:101:19)
setLineNumbersHeight @ vue-prism-editor.js?v=767cd15b:133
(anonymous) @ vue-prism-editor.js?v=767cd15b:101
Promise.then
nextTick @ runtime-core.esm-bundler.js?v=767cd15b:286
handler2 @ vue-prism-editor.js?v=767cd15b:100
callWithErrorHandling @ runtime-core.esm-bundler.js?v=767cd15b:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=767cd15b:204
baseWatchOptions.call @ runtime-core.esm-bundler.js?v=767cd15b:6215
job @ reactivity.esm-bundler.js?v=767cd15b:1738
watch @ reactivity.esm-bundler.js?v=767cd15b:1774
doWatch @ runtime-core.esm-bundler.js?v=767cd15b:6243
watch @ runtime-core.esm-bundler.js?v=767cd15b:6176
createWatcher @ runtime-core.esm-bundler.js?v=767cd15b:3674
applyOptions @ runtime-core.esm-bundler.js?v=767cd15b:3555
finishComponentSetup @ runtime-core.esm-bundler.js?v=767cd15b:8036
setupStatefulComponent @ runtime-core.esm-bundler.js?v=767cd15b:7959
setupComponent @ runtime-core.esm-bundler.js?v=767cd15b:7884
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5240
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=767cd15b:2045
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1765
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=767cd15b:2045
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=767cd15b:2045
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=767cd15b:7282
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=767cd15b:2045
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
__asyncHydrate @ runtime-core.esm-bundler.js?v=767cd15b:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5328
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
(anonymous) @ runtime-core.esm-bundler.js?v=767cd15b:7219
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=767cd15b:7205
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5247
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=767cd15b:7282
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=767cd15b:7282
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrate @ runtime-core.esm-bundler.js?v=767cd15b:1673
mount @ runtime-core.esm-bundler.js?v=767cd15b:3956
app.mount @ runtime-dom.esm-bundler.js?v=767cd15b:1761
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:73
setTimeout
(anonymous) @ __uno.css:19
(anonymous) @ __uno.css:19Understand this error
2vue-prism-editor.js?v=767cd15b:133 Uncaught (in promise) TypeError: Failed to execute 'getComputedStyle' on 'Window': parameter 1 is not of type 'Element'.
    at Proxy.setLineNumbersHeight (vue-prism-editor.js?v=767cd15b:133:32)
    at Proxy.<anonymous> (vue-prism-editor.js?v=767cd15b:101:19)
setLineNumbersHeight @ vue-prism-editor.js?v=767cd15b:133
(anonymous) @ vue-prism-editor.js?v=767cd15b:101
Promise.then
nextTick @ runtime-core.esm-bundler.js?v=767cd15b:286
handler2 @ vue-prism-editor.js?v=767cd15b:100
callWithErrorHandling @ runtime-core.esm-bundler.js?v=767cd15b:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=767cd15b:204
baseWatchOptions.call @ runtime-core.esm-bundler.js?v=767cd15b:6215
job @ reactivity.esm-bundler.js?v=767cd15b:1738
watch @ reactivity.esm-bundler.js?v=767cd15b:1774
doWatch @ runtime-core.esm-bundler.js?v=767cd15b:6243
watch @ runtime-core.esm-bundler.js?v=767cd15b:6176
createWatcher @ runtime-core.esm-bundler.js?v=767cd15b:3674
applyOptions @ runtime-core.esm-bundler.js?v=767cd15b:3555
finishComponentSetup @ runtime-core.esm-bundler.js?v=767cd15b:8036
setupStatefulComponent @ runtime-core.esm-bundler.js?v=767cd15b:7959
setupComponent @ runtime-core.esm-bundler.js?v=767cd15b:7884
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5240
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=767cd15b:2045
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1765
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=767cd15b:2045
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=767cd15b:2045
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=767cd15b:7282
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateFragment @ runtime-core.esm-bundler.js?v=767cd15b:2045
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1765
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
__asyncHydrate @ runtime-core.esm-bundler.js?v=767cd15b:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5328
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
(anonymous) @ runtime-core.esm-bundler.js?v=767cd15b:7219
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=767cd15b:7205
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5247
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=767cd15b:7282
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=767cd15b:1998
hydrateElement @ runtime-core.esm-bundler.js?v=767cd15b:1879
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=767cd15b:7282
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=767cd15b:5316
componentUpdateFn @ runtime-core.esm-bundler.js?v=767cd15b:5334
run @ reactivity.esm-bundler.js?v=767cd15b:198
setupRenderEffect @ runtime-core.esm-bundler.js?v=767cd15b:5478
mountComponent @ runtime-core.esm-bundler.js?v=767cd15b:5253
hydrateNode @ runtime-core.esm-bundler.js?v=767cd15b:1799
hydrate @ runtime-core.esm-bundler.js?v=767cd15b:1673
mount @ runtime-core.esm-bundler.js?v=767cd15b:3956
app.mount @ runtime-dom.esm-bundler.js?v=767cd15b:1761
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:73
setTimeout
(anonymous) @ __uno.css:19
(anonymous) @ __uno.css:19Understand this error
wpage-gen2.vue:2290 🧠 Memory [Periodic check]: {used: '564MB', total: '601MB', limit: '4096MB'}
wpage-gen2.vue:2300 📊 Data sizes: {blockProtoData: 0, canvasBlocks: 0, parsedJsonFields: 0, extractedImages: 0, history: 0, …}
wpage-gen2.vue:927 Загружено блоков: 2735
wpage-gen2.vue:2290 🧠 Memory [After loading blocks]: {used: '684MB', total: '735MB', limit: '4096MB', blocksCount: 2735}
wpage-gen2.vue:2290 🧠 Memory [Periodic check]: {used: '1008MB', total: '1073MB', limit: '4096MB'}
wpage-gen2.vue:2300 📊 Data sizes: {blockProtoData: 2735, canvasBlocks: 0, parsedJsonFields: 0, extractedImages: 0, history: 0, …}
wpage-gen2.vue:2290 🧠 Memory [After loading filter options]: {used: '1065MB', total: '1122MB', limit: '4096MB'}
wpage-gen2.vue:2290 🧠 Memory [Periodic check]: {used: '1065MB', total: '1122MB', limit: '4096MB'}
wpage-gen2.vue:2300 📊 Data sizes: {blockProtoData: 2735, canvasBlocks: 0, parsedJsonFields: 0, extractedImages: 0, history: 0, …}