---01
На странице wpage-gen2 я вижу следующие проблемы
##Левая панель
1. при нажатии на редактирование блока из карточки должен появляться полноценный сайдбар с редактированием блоки из базы в точности как app\components\WBlockProtoSidebarV2.vue (а сейчас появляется какое-то бесполезное модальное окно в котором я вижу только название блока и пару кнопок) - это полностью не функционально и полностью не соответсвует поставленной задаче!
2. при добавлении блоков в области "схема страницы" должны отображаться изначальные title блоков (так проще будет сориентироваться). Сейчас в схеме блоки перименовываются . Также можно напротив заголовка "схема страницы" писать общее количество блоков), а напротив контейнеров блоков слева писать порядковый номер на холсте - 01, 02, 03 и т.д.
(также я вижу что в html код добавляется комментарий в данными блока - лучше пусть в нем также будет отражен окончательный number и title (который присваивается при заполнении number и title страницы - в верхних полях в центральной области)
3. к карточках чекбокс надо сделать в левом верхнем углу и разместить поверх картинки, картинка должна отображаться без искажений (object-fit contain по центру - по ширине и по высоте)
4. в тулбарах неоптимальные фильтры - при нажатии на иконку открывается поле в котором надо снова открывать dropdown (получаются лишние клики)

##Правый сайдбар
Сделать Табы "Редактор" и Картинки (табы должны быть компактные (смоти как сделано в app\components\WBlockProtoSidebarV2.vue
- добавить редактор (интеллектуальный редактор ПОЛНОСТЬЮ! аналогичный табу "Редактор" в app\components\WBlockProtoSidebarV2.vue связанный с полем json
-поля JSON и HBS поместить в соответсвующие табы
- картинки (интеллектуальный редактор аналогичный табу "Картинки" в app\components\WBlockProtoSidebarV2.vue связанный с полем json

---01-1

На странице wpage-gen2 я вижу следующие проблемы и задачи
1. при нажатии на редактирование блока из карточки открывается сайдбар с пустой формой - т.е. я не вижу в форме данные соотвествующие выбранному блоку (глубоко проанализируй причины)
2. к карточках чекбокс надо сделать в левом верхнем углу и разместить поверх картинки, картинка должна отображаться без искажений (object-fit contain по центру - по ширине и по высоте)
3. Область "Схема страницы" можно перенести в правую область, наверх (так - же 30% от общей высоты)
4. Заголовок области "редактирование блока" можно заменить на отображение порядкового номера блоки из схемы + оригинального title добавленного блока из базы (по такому же принципу формируются надписи в контейнерах-блоках в области "схема страницы")
5. в области редактирования данные из таба "редактор" и "Картинки" синхронизируются с полем json только в одну сторону (при редактировании данных из поля json они не меняются в соответсвующих полях в редакторе) необходимо это исправить
6. в области редактирования надо добавить multiselect поле с loadOptions title из коллекции wblock_proto directus/ Также необходимо что в это поле записывался title добавленного оригинального блока (таким образом мы связываем новый экземпляр блока с оригинальным, поэтому при сохранении эти связи должны быть правильно сохранены - m2m связь установлена через поле wblock_proto - узловая коллекция wblock_proto_wblock_proto - wblock_proto_id <-> related_wblock_proto_id)


