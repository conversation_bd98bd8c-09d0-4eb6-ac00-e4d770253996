<template>
  <DataTable 
    v-model:selection="localSelection"
    v-model:expanded-rows="localExpandedRows"
    :value="items"
    :columns="columns"
    :selection-mode="selectionMode"
    :paginator="paginator"
    :rows="rows"
    :rows-per-page-options="rowsPerPageOptions"
    :sort-field="sortField"
    :sort-order="sortOrder"
    :filter-display="filterDisplay"
    :global-filter-fields="globalFilterFields"
    :loading="loading"
    :data-key="dataKey"
    :striped-rows="stripedRows"
    :responsive-layout="responsiveLayout"
    :class="tableClass"
    @row-select="$emit('row-select', $event)"
    @row-unselect="$emit('row-unselect', $event)"
    @row-expand="$emit('row-expand', $event)"
    @row-collapse="$emit('row-collapse', $event)"
  >
    <template #empty>
      <div class="text-center p-4">{{ emptyMessage }}</div>
    </template>
    
    <template #loading>
      <div class="text-center p-4">{{ loadingMessage }}</div>
    </template>

    <template v-for="(column, index) in columns" :key="index" #[`body.${column.field}`]="slotProps">
      <slot 
        :name="`body.${column.field}`" 
        :data="slotProps.data" 
        :column="column"
      >
        {{ column.body ? column.body(slotProps.data) : slotProps.data[column.field] }}
      </slot>
    </template>
  </DataTable>
</template>

<script setup>
import { ref } from 'vue'
import DataTable from 'primevue/datatable'

const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => []
  },
  selectionMode: {
    type: String,
    default: 'single'
  },
  paginator: {
    type: Boolean,
    default: true
  },
  rows: {
    type: Number,
    default: 10
  },
  rowsPerPageOptions: {
    type: Array,
    default: () => [5, 10, 20, 50]
  },
  sortField: {
    type: String,
    default: 'id'
  },
  sortOrder: {
    type: Number,
    default: 1
  },
  filterDisplay: {
    type: String,
    default: 'menu'
  },
  globalFilterFields: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  dataKey: {
    type: String,
    default: 'id'
  },
  stripedRows: {
    type: Boolean,
    default: false
  },
  responsiveLayout: {
    type: String,
    default: 'stack'
  },
  tableClass: {
    type: String,
    default: ''
  },
  emptyMessage: {
    type: String,
    default: 'Нет данных'
  },
  loadingMessage: {
    type: String,
    default: 'Загрузка...'
  }
})

const emit = defineEmits([
  'row-select', 
  'row-unselect', 
  'row-expand', 
  'row-collapse'
])

const localSelection = ref(null)
const localExpandedRows = ref([])
</script>
