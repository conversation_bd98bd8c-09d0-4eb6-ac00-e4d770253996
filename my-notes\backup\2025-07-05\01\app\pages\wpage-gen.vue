<template>
  <div class="flex h-screen">
    <!-- Левый сайдбар с контейнером для редактирования -->
    <div class="flex" :style="{ width: leftSidebarWidth }">
      <SidebarTemplates 
        class="w-full bg-gray-100" 
        @add-to-canvas="handleAddToCanvas"
        @update:canvas-width="updateCanvasWidth"
      />
    </div>

    <!-- Холст и панель управления -->
    <div class="flex flex-col p-1" :style="{ width: canvasWidth }">
      <input 
        v-model="pageTitle" 
        type="text" 
        placeholder="Введите название страницы" 
        class="w-full p-1 mb-2 border border-gray-300 rounded-lg text-lg" 
      >

      <Canvas 
        ref="canvasRef" 
        :canvas-width="100" 
        class="flex-1 border-2 border-dashed border-gray-300 rounded-lg p-0" 
      />

      <div class="flex gap-2 mt-4">
        <button class="text-sm px-3 py-1 bg-gray-200 text-gray-600 rounded-3xl hover:bg-gray-300" @click="generatePageHtml">
          🔧 HTML
        </button>
        <button class="text-sm px-3 py-1 bg-gray-200 text-gray-600 rounded-3xl hover:bg-gray-300" @click="previewPage">
          👁 Просмотр
        </button>
        <button class="text-sm px-3 py-1 bg-gray-200 text-gray-600 rounded-3xl hover:bg-gray-300" @click="savePage">
          💾 страницу
        </button>
        <button class="text-sm px-3 py-1 bg-gray-200 text-gray-600 rounded-3xl hover:bg-gray-300" @click="saveWblockItems">
          💾 блоки
        </button>
        <button class="text-sm px-3 py-1 bg-gray-200 text-gray-600 rounded-3xl hover:bg-gray-300" @click="savePageAndBlocks">
          💾 страницу и блоки
        </button>
      </div>

      <div class="flex">
        <div class="mt-4 flex-1">
          <label for="cssLinks" class="block text-sm font-medium text-gray-700">CSS ссылки:</label>
          <textarea id="cssLinks" v-model="cssLinks" rows="2" class="mt-1 p-0.5 text-xs block w-full border rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"/>
        </div>
        <div class="mt-4 flex-1">
          <label for="jsScripts" class="block text-sm font-medium text-gray-700">JS скрипты:</label>
          <textarea id="jsScripts" v-model="jsScripts" rows="2" class="mt-1 p-0.5 text-xs block w-full border rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"/>
        </div>
      </div>

      <div class="mt-2 p-2 bg-gray-50 border rounded-lg">
        <h3 class="text-sm font-semibold mb-2">Сгенерированный HTML:</h3>
        <div class="p-1 bg-white border rounded text-xs" style="max-height: 300px; overflow-y: auto;">        
          <pre v-text="renderedHtml"/> 
        </div>
      </div>
    </div>

    <!-- Правый сайдбар с контейнером для редактирования -->
    <div class="flex" :style="{ width: rightSidebarWidth }">
      <SidebarData 
        class="w-full bg-gray-100" 
        @add-to-canvas="handleAddToCanvas"
        @update:canvas-width="updateCanvasWidth"
      />
    </div>

    <!-- Модальное окно предпросмотра -->
    <PreviewModal v-if="showPreview" :html="renderedHtml" @close="showPreview = false" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import SidebarTemplates from '~/components/SidebarTemplates.vue';
import SidebarData from '~/components/SidebarData.vue';
import Canvas from '~/components/Canvas.vue';
import PreviewModal from '~/components/PreviewModal.vue';
import { useNuxtApp } from '#app';
import handlebars from 'handlebars/dist/handlebars.min.js';
import { useDirectusItems } from '#imports';

const canvasRef = ref(null);
const renderedHtml = ref('');
const cssLinks = ref(''); 
const jsScripts = ref('');
const showPreview = ref(false);
const pageTitle = ref('');
const { $fetchJson } = useNuxtApp();

const leftSidebarWidth = ref('30%');
const rightSidebarWidth = ref('30%');
const canvasWidth = ref('40%');

const updateCanvasWidth = (width, direction) => {
  if (direction === 'left') {
    // Левый сайдбар редактирования открыт
    leftSidebarWidth.value = '60%';
    // rightSidebarWidth.value = '20%';
    canvasWidth.value = '40%';
  } else if (direction === 'right') {
    // Правый сайдбар редактирования открыт
    // leftSidebarWidth.value = '20%';
    rightSidebarWidth.value = '60%';
    canvasWidth.value = '40%';
  } else {
    // Возвращаем к исходным размерам
    leftSidebarWidth.value = '30%';
    rightSidebarWidth.value = '30%';
    canvasWidth.value = '40%';
  }

  console.log(`Обновление размеров: левый=${leftSidebarWidth.value}, правый=${rightSidebarWidth.value}, canvas=${canvasWidth.value}, направление=${direction || 'стандарт'}`);
};

const handleAddToCanvas = (payload) => {
  canvasRef.value?.handleAddToCanvas(payload);
};

const generatePageHtml = () => {
console.log('Canvas ref:', canvasRef.value);
console.log('Canvas groups:', canvasRef.value.groups);

// Универсальный способ разыменования
const canvasGroups = canvasRef.value?.groups?.value ?? canvasRef.value?.groups;
console.log('Canvas groups (разыменовано):', canvasGroups);

if (!Array.isArray(canvasGroups) || canvasGroups.length === 0) {
  console.warn('Canvas пустой или не инициализирован', canvasGroups);
  return;
}

let html = '';

canvasGroups.forEach(({ whbs, wjson }) => {
  if (whbs?.hbs && wjson?.json) {
    try {
      const template = handlebars.compile(whbs.hbs);
      html += template(JSON.parse(wjson.json));
    } catch (err) {
      console.error('Ошибка рендеринга Handlebars:', err);
    }
  }
});

renderedHtml.value = html;
};


// Сохранение страницы
const savePage = async () => {
if (!pageTitle.value || !renderedHtml.value) {
  return alert('Введите название и сгенерируйте HTML!');
}

try {
  const { createItems } = useDirectusItems(); 
    const response = await createItems({
      collection: 'wpage',
      items: {
        title: pageTitle.value,        
        html: renderedHtml.value,
        css: cssLinks.value,
        js: jsScripts.value,
      },
    });

    console.log('Страница сохранена:', response);
    alert('Страница успешно сохранена!');
  } catch (error) {
    console.error('Ошибка при сохранении:', error);
    alert('Ошибка при сохранении!');
  }
};


// Предпросмотр страницы
const previewPage = () => {
if (!renderedHtml.value) return alert('Сначала сгенерируйте HTML!');


const previewWindow = window.open('', '_blank');
previewWindow.document.write(`
  <html>
    <head>
      <title>${pageTitle.value}</title>
      ${cssLinks.value}
    </head>
    <body>
      ${renderedHtml.value}
      ${jsScripts.value}
    </body>
  </html>
`);
previewWindow.document.close();
};


// Проверяем, что `Canvas` готов
onMounted(() => {
  if (!canvasRef.value) {
    console.warn('Canvas не подключен!');
  }
});


// Сохранение блоков
const saveWblockItems = async () => {
  if (!pageTitle.value || !canvasRef.value?.groups?.length) {   
    return alert('Введите название страницы и добавьте хотя бы одну группу на холст!');
  }

  try {
    const { createItems } = useDirectusItems();
    const canvasGroups = canvasRef.value.groups;
    console.log("canvasGroups:", canvasGroups); // Добавлен лог

    for (let i = 0; i < canvasGroups.length; i++) {
      const { whbs, wjson } = canvasGroups[i];
      console.log("whbs:", whbs); // Добавлен лог
      console.log("wjson:", wjson); // Добавлен лог

      if (!whbs?.hbs || !wjson?.json) continue;

      try {
        const template = handlebars.compile(whbs.hbs);
        const html = template(JSON.parse(wjson.json));

        // 1. Создаем объект items для wblock
        const wblockItems = { 
          title: `${pageTitle.value} - ${i + 1}`,
          html: html,
          whbs: [{ id: whbs.id }], 
          wjson: [{ id: wjson.id }], 
        };
        console.log("wblockItems:", wblockItems); // Добавлен лог

        // 2. Создаем запись в коллекции 'wblock'
        console.log("Создаем запись в коллекции 'wblock'"); // Добавлен лог
        const wblockResponse = await createItems({
          collection: 'wblock',
          items: wblockItems,
        });
        console.log("wblockResponse:", wblockResponse); // Добавлен лог

        // 3. Получаем ID записи wblock из wblockItems
        const wblockId = wblockResponse.id; 
        console.log("wblockId:", wblockId); // Добавлен лог

        if (wblockId) {
          // 4. Создаем запись в промежуточной коллекции 'wblock_whbs'
          console.log("Создаем запись в коллекции 'wblock_whbs'"); // Добавлен лог
          await createItems({
            collection: 'wblock_whbs',
            items: {
              wblock_id: wblockId,
              whbs_id: whbs.id,
            },
          });

          // 5. Создаем запись в промежуточной коллекции 'wblock_wjson'
          console.log("Создаем запись в коллекции 'wblock_wjson'"); // Добавлен лог
          await createItems({
            collection: 'wblock_wjson',
            items: {
              wblock_id: wblockId,
              wjson_id: wjson.id,
            },
          });

          console.log(`Группа ${i + 1} сохранена в wblock:`, wblockResponse);
        } else {
          console.error(`Ошибка создания записи в wblock для группы ${i + 1}`);
        }

      } catch (err) {
        console.error(`Ошибка обработки группы ${i + 1}:`, err);
      }
    }

    alert('Группы сохранены в wblock!');
  } catch (error) {
    console.error('Ошибка при сохранении групп в wblock:', error);
    alert('Ошибка при сохранении групп в wblock!');
  }
};

// Сохранение страницы и блоков
const savePageAndBlocks = async () => {
  if (!pageTitle.value || !renderedHtml.value || !canvasRef.value?.groups?.length) {
    return alert('Введите название, сгенерируйте HTML и добавьте хотя бы одну группу!');
  }

  try {
    const { createItems } = useDirectusItems();

    // --- 1. Сохранение страницы ---
    console.log('Сохранение страницы...');
    const pageResponse = await createItems({
      collection: 'wpage',
      items: {
        title: pageTitle.value,
        html: renderedHtml.value,
        css: cssLinks.value,
        js: jsScripts.value,
      },
    });

    console.log('Страница сохранена:', pageResponse);
    const pageId = pageResponse.id;

    if (!pageId) throw new Error('Ошибка при сохранении страницы!');

    // --- 2. Сохранение блоков ---
    console.log('Сохранение блоков...');
    const canvasGroups = canvasRef.value.groups;

    for (let i = 0; i < canvasGroups.length; i++) {
      const { whbs, wjson } = canvasGroups[i];

      if (!whbs?.hbs || !wjson?.json) continue;

      try {
        const template = handlebars.compile(whbs.hbs);
        const html = template(JSON.parse(wjson.json));

        // 2.1 Создаём запись в wblock
        console.log(`Сохранение блока ${i + 1}...`);
        const wblockResponse = await createItems({
          collection: 'wblock',
          items: {
            title: `${pageTitle.value} - Группа ${i + 1}`,
            html: html,
            // whbs: [{ id: whbs.id }],
            // wjson: [{ id: wjson.id }],
            // wpage: [{ id: pageId }],
          },
        });

        console.log(`Блок ${i + 1} сохранён:`, wblockResponse);
        const wblockId = wblockResponse.id;

        if (!wblockId) {
          console.error(`Ошибка при сохранении блока ${i + 1}`);
          continue;
        }

        // --- 3. Связываем блок с шаблоном и JSON (как в saveWblockItems) ---
        console.log(`Создание связи wblock_whbs для блока ${i + 1}...`);
        await createItems({
          collection: 'wblock_whbs',
          items: {
            wblock_id: wblockId,
            whbs_id: whbs.id,
          },
        });

        console.log(`Создание связи wblock_wjson для блока ${i + 1}...`);
        await createItems({
          collection: 'wblock_wjson',
          items: {
            wblock_id: wblockId,
            wjson_id: wjson.id,
          },
        });

        // --- 4. Связываем блок с страницей в wblock_wpage ---
        console.log(`Создание связи wpage_wblock для блока ${i + 1}...`);
        await createItems({
          collection: 'wpage_wblock',
          items: {
            wblock_id: wblockId,
            wpage_id: pageId,
          },
        });

        console.log(`Группа ${i + 1} сохранена в wblock и связана со страницей!`);
      } catch (err) {
        console.error(`Ошибка сохранения блока ${i + 1}:`, err);
      }
    }

    alert('Страница и все блоки успешно сохранены!');
  } catch (error) {
    console.error('Ошибка при сохранении страницы и блоков:', error);
    alert('Ошибка при сохранении!');
  }
};
</script>

<style>
/* Стили для сайдбаров и ручного изменения ширины */
.sidebar-wrapper {
  position: relative;
  height: 100%;
  min-width: 200px;
  max-width: 40%;
}

/* Добавляем возможность изменения размера сайдбаров */
.sidebar {
  resize: horizontal;
  overflow: auto;
  min-width: 250px;
  max-width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* Переопределяем стили для container div сайдбаров */
.flex > div:first-child, 
.flex > div:last-child {
  min-width: 250px;
  resize: horizontal;
  overflow: auto;
}

/* Стили для сайдбара редактирования */
.sidebar-edit {
  position: absolute;
  top: 0;
  height: 100%;
  width: 300px;
  background: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 40;
  overflow: auto;
  box-sizing: border-box;
}

/* Стили для контейнеров сайдбаров */
.flex > div {
  transition: width 0.3s ease;
}
</style>