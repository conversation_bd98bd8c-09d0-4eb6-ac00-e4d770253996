<template>
  <div class="variation-selector">
    <h4 class="text-md font-semibold mb-3">Выбор конкретных вариаций</h4>
    
    <!-- Выбор типов вариаций -->
    <div class="variation-types mb-4">
      <div class="flex flex-wrap gap-2 mb-3">
        <Button
          v-for="type in variationTypes"
          :key="type.value"
          :label="type.label"
          :class="{
            'p-button-outlined': !selectedTypes.includes(type.value),
            'p-button': selectedTypes.includes(type.value)
          }"
          class="text-xs"
          @click="toggleVariationType(type.value)"
        />
      </div>
    </div>

    <!-- Детальный выбор для каждого типа -->
    <div v-for="type in selectedTypes" :key="type" class="variation-type-details mb-4">
      <h5 class="text-sm font-semibold mb-2">{{ getTypeLabel(type) }}</h5>
      
      <!-- Цветовые схемы -->
      <div v-if="type === 'colors'" class="color-variations">
        <div class="grid grid-cols-5 gap-2">
          <div
            v-for="variation in colorVariations"
            :key="variation.name"
            class="color-variation-card p-2 border rounded cursor-pointer transition-all"
            :class="{
              'border-blue-500 bg-blue-50': selectedVariations.colors?.includes(variation.name),
              'border-gray-200 hover:border-gray-300': !selectedVariations.colors?.includes(variation.name)
            }"
            @click="toggleVariation('colors', variation.name)"
          >
            <div class="text-xs font-medium mb-1">{{ variation.name }}</div>
            <div class="text-xs text-gray-500 mb-2">{{ variation.description }}</div>
            <div class="flex gap-1">
              <div
                v-for="(replacement, className) in Object.entries(variation.classReplacements).slice(0, 3)"
                :key="className"
                class="w-4 h-4 rounded border"
                :style="{ backgroundColor: getColorFromClass(replacement[1]) }"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Размерные вариации -->
      <div v-if="type === 'sizes'" class="size-variations">
        <div class="grid grid-cols-4 gap-2">
          <div
            v-for="variation in sizeVariations"
            :key="variation.name"
            class="size-variation-card p-2 border rounded cursor-pointer transition-all text-center"
            :class="{
              'border-blue-500 bg-blue-50': selectedVariations.sizes?.includes(variation.name),
              'border-gray-200 hover:border-gray-300': !selectedVariations.sizes?.includes(variation.name)
            }"
            @click="toggleVariation('sizes', variation.name)"
          >
            <div class="text-xs font-medium">{{ variation.name.toUpperCase() }}</div>
            <div class="text-xs text-gray-500">{{ variation.description }}</div>
            <div class="mt-1">
              <div
                class="w-8 h-8 bg-blue-200 rounded mx-auto transition-transform"
                :style="{ transform: `scale(${getSizeScale(variation.name)})` }"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Стили границ -->
      <div v-if="type === 'borders'" class="border-variations">
        <div class="grid grid-cols-4 gap-2">
          <div
            v-for="variation in borderVariations"
            :key="variation.name"
            class="border-variation-card p-2 border rounded cursor-pointer transition-all text-center"
            :class="{
              'border-blue-500 bg-blue-50': selectedVariations.borders?.includes(variation.name),
              'border-gray-200 hover:border-gray-300': !selectedVariations.borders?.includes(variation.name)
            }"
            @click="toggleVariation('borders', variation.name)"
          >
            <div class="text-xs font-medium mb-1">{{ variation.name }}</div>
            <div class="text-xs text-gray-500 mb-2">{{ variation.description }}</div>
            <div
              class="w-12 h-8 bg-gray-100 mx-auto"
              :style="getBorderPreviewStyle(variation)"
            />
          </div>
        </div>
      </div>

      <!-- Теневые эффекты -->
      <div v-if="type === 'shadows'" class="shadow-variations">
        <div class="grid grid-cols-4 gap-2">
          <div
            v-for="variation in shadowVariations"
            :key="variation.name"
            class="shadow-variation-card p-2 border rounded cursor-pointer transition-all text-center"
            :class="{
              'border-blue-500 bg-blue-50': selectedVariations.shadows?.includes(variation.name),
              'border-gray-200 hover:border-gray-300': !selectedVariations.shadows?.includes(variation.name)
            }"
            @click="toggleVariation('shadows', variation.name)"
          >
            <div class="text-xs font-medium mb-1">{{ variation.name }}</div>
            <div class="text-xs text-gray-500 mb-2">{{ variation.description }}</div>
            <div
              class="w-12 h-8 bg-white mx-auto border"
              :style="getShadowPreviewStyle(variation)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Кнопки действий -->
    <div class="flex gap-2 mt-4">
      <Button
        label="Создать выбранные вариации"
        class="text-xs"
        :disabled="!hasSelectedVariations"
        @click="createSelectedVariations"
      />
      <Button
        label="Выбрать все"
        class="text-xs p-button-outlined"
        @click="selectAllVariations"
      />
      <Button
        label="Очистить выбор"
        class="text-xs p-button-outlined"
        @click="clearSelection"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { colorVariations, sizeVariations, borderVariations, shadowVariations } from '~/utils/variation-mappings'

// Props
interface Props {
  selectedElements: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['variationsCreated'])

// Данные
const variationTypes = ref([
  { label: 'Цвета', value: 'colors' },
  { label: 'Размеры', value: 'sizes' },
  { label: 'Границы', value: 'borders' },
  { label: 'Тени', value: 'shadows' }
])

const selectedTypes = ref<string[]>([])
const selectedVariations = ref<Record<string, string[]>>({
  colors: [],
  sizes: [],
  borders: [],
  shadows: []
})

// Вычисляемые свойства
const hasSelectedVariations = computed(() => {
  return Object.values(selectedVariations.value).some(arr => arr.length > 0)
})

// Методы
const toggleVariationType = (type: string) => {
  const index = selectedTypes.value.indexOf(type)
  if (index > -1) {
    selectedTypes.value.splice(index, 1)
    selectedVariations.value[type] = []
  } else {
    selectedTypes.value.push(type)
  }
}

const toggleVariation = (type: string, variationName: string) => {
  if (!selectedVariations.value[type]) {
    selectedVariations.value[type] = []
  }
  
  const index = selectedVariations.value[type].indexOf(variationName)
  if (index > -1) {
    selectedVariations.value[type].splice(index, 1)
  } else {
    selectedVariations.value[type].push(variationName)
  }
}

const getTypeLabel = (type: string): string => {
  const typeObj = variationTypes.value.find(t => t.value === type)
  return typeObj ? typeObj.label : type
}

const getColorFromClass = (className: string): string => {
  const colorMap: Record<string, string> = {
    'btn-primary': '#007bff',
    'btn-success': '#28a745',
    'btn-warning': '#ffc107',
    'btn-danger': '#dc3545',
    'btn-info': '#17a2b8',
    'bg-primary': '#007bff',
    'bg-success': '#28a745',
    'bg-warning': '#ffc107',
    'bg-danger': '#dc3545',
    'bg-info': '#17a2b8'
  }
  return colorMap[className] || '#6c757d'
}

const getSizeScale = (sizeName: string): number => {
  const scaleMap: Record<string, number> = {
    'xs': 0.5,
    'sm': 0.75,
    'lg': 1.25,
    'xl': 1.5
  }
  return scaleMap[sizeName] || 1
}

const getBorderPreviewStyle = (variation: any): string => {
  if (variation.cssOverrides) {
    if (variation.name === 'rounded') return 'border-radius: 8px;'
    if (variation.name === 'pill') return 'border-radius: 50px;'
    if (variation.name === 'sharp') return 'border-radius: 0;'
    if (variation.name === 'thick-border') return 'border: 3px solid #007bff;'
  }
  return ''
}

const getShadowPreviewStyle = (variation: any): string => {
  if (variation.cssOverrides) {
    const match = variation.cssOverrides.match(/box-shadow:\s*([^;]+);/)
    if (match) {
      return `box-shadow: ${match[1]};`
    }
  }
  return ''
}

const selectAllVariations = () => {
  for (const type of selectedTypes.value) {
    switch (type) {
      case 'colors':
        selectedVariations.value.colors = colorVariations.map(v => v.name)
        break
      case 'sizes':
        selectedVariations.value.sizes = sizeVariations.map(v => v.name)
        break
      case 'borders':
        selectedVariations.value.borders = borderVariations.map(v => v.name)
        break
      case 'shadows':
        selectedVariations.value.shadows = shadowVariations.map(v => v.name)
        break
    }
  }
}

const clearSelection = () => {
  selectedVariations.value = {
    colors: [],
    sizes: [],
    borders: [],
    shadows: []
  }
}

const createSelectedVariations = () => {
  emit('variationsCreated', {
    selectedTypes: selectedTypes.value,
    selectedVariations: selectedVariations.value
  })
}
</script>

<style scoped>
.variation-selector {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.color-variation-card,
.size-variation-card,
.border-variation-card,
.shadow-variation-card {
  min-height: 80px;
}

.color-variation-card:hover,
.size-variation-card:hover,
.border-variation-card:hover,
.shadow-variation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
