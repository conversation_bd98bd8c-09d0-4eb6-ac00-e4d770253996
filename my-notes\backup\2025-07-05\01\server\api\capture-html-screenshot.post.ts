import { defineEventHand<PERSON>, readBody, createError } from 'h3'
import puppeteer from 'puppeteer'

interface ScreenshotRequest {
  html: string
  filename?: string
  width?: number
  height?: number
}

interface ScreenshotResponse {
  fileId: string
  filename: string
}

export default defineEventHandler(async (event) => {
  const { html, filename = 'screenshot', width = 1400, height = 800 } = await readBody<ScreenshotRequest>(event)

  if (!html) {
    throw createError({
      statusCode: 400,
      message: 'HTML content is required',
    })
  }

  console.log('🔄 Создание скриншота HTML контента...')

  // Запускаем браузер
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  })

  try {
    const page = await browser.newPage()

    // Устанавливаем размер окна браузера (начальная высота)
    await page.setViewport({ width, height })

    console.log(`📐 Установлен viewport: ${width}x${height}`)

    // Загружаем HTML контент с максимальным ожиданием
    await page.setContent(html, { waitUntil: 'networkidle0', timeout: 60000 })

    console.log('📄 HTML контент загружен')

    // Оптимизированная базовая задержка для полной загрузки контента
    console.log('⏱️ Базовая задержка 3000ms для полной загрузки контента...')
    await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))

    // Ждем загрузки всех изображений с увеличенным timeout
    console.log('🖼️ Ожидание загрузки всех изображений с timeout 10000ms...')
    await page.evaluate(() => {
      return Promise.all(
        Array.from(document.images)
          .filter(img => !img.complete)
          .map(img => new Promise(resolve => {
            img.onload = img.onerror = resolve
            // Увеличенный timeout для изображений
            setTimeout(resolve, 10000)
          }))
      )
    })

    // УМНАЯ прокрутка для активации scroll-анимаций (имитация ручного процесса)
    console.log('📜 УМНАЯ прокрутка для активации scroll-анимаций...')
    await page.evaluate(() => {
      return new Promise((resolve) => {
        // Имитируем ручной процесс: медленная прокрутка вниз, затем быстро вверх
        const scrollToBottom = () => {
          // Медленная прокрутка вниз по частям для активации всех scroll-триггеров
          let currentScroll = 0
          const maxScroll = document.body.scrollHeight
          const scrollStep = Math.max(200, maxScroll / 10) // Прокручиваем по частям

          const scrollInterval = setInterval(() => {
            currentScroll += scrollStep
            window.scrollTo(0, Math.min(currentScroll, maxScroll))

            if (currentScroll >= maxScroll) {
              clearInterval(scrollInterval)
              // Пауза внизу для активации анимаций
              setTimeout(scrollToTop, 2000)
            }
          }, 300) // Медленная прокрутка для активации всех триггеров
        }

        // Быстрая прокрутка вверх
        const scrollToTop = () => {
          window.scrollTo(0, 0)
          setTimeout(resolve, 1500) // Пауза вверху для стабилизации
        }

        // Начинаем процесс
        setTimeout(scrollToBottom, 1000)
      })
    })

    // Анализ анимаций на странице (как в capture-batch-screenshots)
    console.log('🎭 Анализ анимаций на странице...')
    const animationInfo = await page.evaluate(() => {
      const info = {
        hasAnimations: false,
        hasJSAnimations: false,
        hasLibs: false,
        animationCount: 0,
        maxDuration: 0
      }

      // Проверяем CSS анимации
      const allElements = Array.from(document.querySelectorAll('*'))
      allElements.forEach(el => {
        const style = window.getComputedStyle(el)
        if (style.animationDuration !== '0s' || style.transitionDuration !== '0s') {
          info.hasAnimations = true
          info.animationCount++
          const animDuration = parseFloat(style.animationDuration) * 1000
          const transDuration = parseFloat(style.transitionDuration) * 1000
          info.maxDuration = Math.max(info.maxDuration, animDuration, transDuration)
        }
      })

      // Проверяем библиотеки анимаций
      info.hasLibs = !!(window.AOS || window.gsap || window.anime || window.ScrollMagic)

      // Проверяем JS анимации
      info.hasJSAnimations = !!(window.jQuery && window.jQuery.fn.animate) ||
        !!(window.requestAnimationFrame) ||
        document.querySelectorAll('[data-aos], [data-animate], .animate__animated').length > 0

      return info
    })

    // Определяем дополнительное время ожидания на основе анализа
    let additionalWaitTime = 0
    if (animationInfo.hasAnimations || animationInfo.hasJSAnimations) {
      if (animationInfo.maxDuration > 2000) {
        // Для длинных анимаций ждем дольше
        additionalWaitTime = Math.min(animationInfo.maxDuration + 1000, 5000)
      } else if (animationInfo.hasJSAnimations) {
        additionalWaitTime = 1500
      } else if (animationInfo.hasLibs) {
        // Для библиотек анимаций используем стандартную задержку
        additionalWaitTime = 1200
      } else {
        additionalWaitTime = 800
      }

      // Дополнительное время для множественных анимаций
      if (animationInfo.animationCount > 5) {
        additionalWaitTime += 300
      }
    }

    if (additionalWaitTime > 0) {
      console.log(`⏱️ Дополнительное ожидание ${additionalWaitTime}ms для анимаций...`)
      await page.evaluate((delay: number) => new Promise(resolve => setTimeout(resolve, delay)), additionalWaitTime)
    }

    // ПРИНУДИТЕЛЬНАЯ активация всех анимаций
    console.log('🎯 Принудительная активация всех анимаций...')
    await page.evaluate(() => {
      // Принудительно активируем AOS анимации
      if (window.AOS && window.AOS.refresh) {
        window.AOS.refresh()
      }

      // Принудительно активируем WOW анимации
      if (window.WOW) {
        try {
          new window.WOW().init()
        } catch (e) { }
      }

      // Принудительно активируем все элементы с data-aos
      const aosElements = document.querySelectorAll('[data-aos]')
      aosElements.forEach(el => {
        if (el instanceof HTMLElement) {
          el.classList.add('aos-animate')
        }
      })

      // Принудительно активируем все элементы с классами анимаций
      const animatedElements = document.querySelectorAll('.animate__animated, [class*="fade"], [class*="slide"], [class*="zoom"], [class*="bounce"]')
      animatedElements.forEach(el => {
        if (el instanceof HTMLElement) {
          el.style.opacity = '1'
          el.style.visibility = 'visible'
          el.style.transform = 'none'
        }
      })

      // Принудительно показываем скрытые элементы
      const hiddenElements = document.querySelectorAll('[style*="opacity: 0"], [style*="visibility: hidden"]')
      hiddenElements.forEach(el => {
        if (el instanceof HTMLElement) {
          el.style.opacity = '1'
          el.style.visibility = 'visible'
        }
      })
    })

    // Оптимизированная финальная стабилизация после всех операций
    console.log('⏱️ Финальная стабилизация 2000ms...')
    await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 2000)))

    // ДИАГНОСТИКА: Проверяем состояние страницы перед созданием скриншота
    console.log('🔍 ДИАГНОСТИКА: Анализ состояния страницы...')
    const pageAnalysis = await page.evaluate(() => {
      const analysis = {
        totalImages: document.images.length,
        loadedImages: Array.from(document.images).filter(img => img.complete).length,
        failedImages: Array.from(document.images).filter(img => !img.complete).length,
        totalElements: document.querySelectorAll('*').length,
        visibleElements: Array.from(document.querySelectorAll('*')).filter(el => {
          const style = window.getComputedStyle(el)
          return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0'
        }).length,
        documentReadyState: document.readyState,
        bodyHeight: document.body.scrollHeight,
        viewportHeight: window.innerHeight,
        currentScrollPosition: window.pageYOffset
      }

      // Проверяем конкретные библиотеки
      analysis.libraries = {
        jquery: !!window.jQuery,
        aos: !!window.AOS,
        gsap: !!window.gsap,
        bootstrap: !!window.bootstrap || !!window.Bootstrap
      }

      return analysis
    })

    console.log('📊 ДИАГНОСТИКА РЕЗУЛЬТАТЫ:')
    console.log(`   📷 Изображения: ${pageAnalysis.loadedImages}/${pageAnalysis.totalImages} загружено (${pageAnalysis.failedImages} не загружено)`)
    console.log(`   👁️ Элементы: ${pageAnalysis.visibleElements}/${pageAnalysis.totalElements} видимо (${Math.round(pageAnalysis.visibleElements / pageAnalysis.totalElements * 100)}%)`)
    console.log(`   📄 Состояние документа: ${pageAnalysis.documentReadyState}`)
    console.log(`   📏 Высота: ${pageAnalysis.bodyHeight}px, Viewport: ${pageAnalysis.viewportHeight}px, Scroll: ${pageAnalysis.currentScrollPosition}px`)
    console.log(`   📚 Библиотеки:`, pageAnalysis.libraries)
    console.log(`⏱️ Анимации - ${animationInfo.hasAnimations}, JS анимации - ${animationInfo.hasJSAnimations}, библиотеки - ${animationInfo.hasLibs}, количество - ${animationInfo.animationCount}, макс. длительность - ${animationInfo.maxDuration}ms`)

    // Находим body элемент и делаем его скриншот (как в screenshot-tool)
    const bodyElement = await page.$('body')

    if (!bodyElement) {
      throw new Error('Body element not found')
    }

    // Получаем размеры body элемента
    const boundingBox = await bodyElement.boundingBox()

    if (!boundingBox) {
      throw new Error('Could not get body element dimensions')
    }

    console.log(`📏 Реальная высота контента: ${boundingBox.height}px (ширина: ${boundingBox.width}px)`)

    // Делаем скриншот body элемента (автоматически подстраивается под размеры)
    const screenshot = await bodyElement.screenshot({
      type: 'jpeg',
      quality: 90,
      omitBackground: false
    })

    console.log('📸 Скриншот создан')

    // Генерируем имя файла с временной меткой
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
    const finalFilename = `${filename}_${timestamp}.jpg`

    // Создаем файл в Directus через fetch API
    const formData = new FormData()
    formData.append('title', finalFilename)

    // Создаем Blob из буфера
    const blob = new Blob([screenshot], { type: 'image/jpeg' })
    formData.append('file', blob, finalFilename)

    console.log('📤 Загружаем скриншот в Directus...')

    const response = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error(`Failed to upload screenshot to Directus: ${response.statusText}`)
    }

    const data = await response.json()

    console.log('✅ Скриншот успешно загружен в Directus:', data.data.id)

    return {
      fileId: data.data.id,
      filename: finalFilename,
    } as ScreenshotResponse

  } catch (error) {
    console.error('❌ Ошибка при создании скриншота:', error)
    throw createError({
      statusCode: 500,
      message: error.message || 'Failed to capture screenshot',
    })
  } finally {
    // Закрываем браузер
    await browser.close()
    console.log('🔒 Браузер закрыт')
  }
})
