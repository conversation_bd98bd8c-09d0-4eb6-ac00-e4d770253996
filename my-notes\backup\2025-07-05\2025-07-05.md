----
01
----

#Общая задача
- Реализовать удобный конструктор (page builder)
- Создать новую vue страницу "wpage-gen2.vue" в папке pages
- За основу берем wblock-page-gen.vue


##Интерфейс и функционал
###Левая панель (сайдбар) 100%-1440px/2  - 2 области
1 область "Добавление" (высота 70%)
- компактная строка-toolbar иконок с быстрыми действиями (обновить, поиск, фильтр по block_type, фильтр по collection, добавить на холст) - за основу можно взять Toolbar для блоков (строка 8 в wblock-page-gen.vue)
- плитка записей wblock_proto (3 колонки) с сортировкой по полю number по умолчанию (дейсвтия из toolbar связаны с этой плиткой, количество записей - все из базы) - за основу можно взять Стека блоков (строка 1265 в wblock-gen2.vue)
-- каждая запись - это карточка (image+preview (поле sketch), number 8px, title 9.5px), на карточках чекбокс который связан с действием из toolbar "добавить на холст", также мелнькая кнопка "..." с быстрыми действиями (добавить на холст, редактировать, дублировать, удалить), 
-- опция редактирования из карточки открывает сайдбар редактирования (дополнительный) аналогичный сайдбару app\components\WBlockProtoSidebarV2.vue
-- опция добавления одного блока (из карточки) или нескольких (путем выбора чекбоксами в карточках и нажатия на кнопку добавления из toolbar) добавляет выбранные блоки в область "схема" и на центральную часть (холст) и html код (а также css и js) PrismEditorWithCopy
- при добавлении блоков из области добавления по сути создаются новые экземпляры этих блоков, но не сохраняются в базу (для простоты за основу берем поля - number, title, hbs и json (из hbs и json компилируется html "на лету"), css и js)

2 область "Схема" (высота 30%)
- в этой области отображаются добавленные блоки (синхронизируются с холстом и полем html) - частично похожий подход используется в app\pages\wblock-page-gen.vue (кмопонент Canvas) - но он принципиально другой
- можно перемещать вверх-вниз, и удалять из области, а также дублировать
- можно выбрать редактирование и откроется сайдбар для редактирования в правой панели
- перемещение последовательности и редактирование влияет/связано с холстом и полем html

###Центральная часть (1440px) - Логика работы этой строки примерно аналогична wblock-page-gen.vue (со строки 184)
- верхняя строка - undo/redo (функционал отмены-повтора с историей на 30 шагов), поле number, title, tags (multiselect с loadOptions из поля tags коллекции wpage directus), wpage_type (multiselect с loadOptions из поля wpage_type коллекции wpage directus), кнопки переключения viewport 1440px-edsktop, tablet, mobile. кнопки скачивания, просмотра, сохранения страницы, блоков, страницы и блоков.
- холст (iframe) с отображением добавленных блоков (связан с областью схемы и полем html) конструируется из блоков (hbs+json с компляцией через handlebars на лету) и полей css и js в которые интеллектуально добавляются уникальные строчки из добавляемых блоков (поля css и js)
-нижняя часть - табы 
-- таб "html" с полем html в котором собирается общий html код страницы
-- таб CSS+JS с полями CSS и JS в котором собирается уникальные строчки из полей css и js добавляемых блоков (кнопки добавления BS и VJ - аналогично как на wblock-page-gen.vue)
Пояснение - обрати внимание на то что в iframe отображение конструируется путем сборки css в теге head и js внизу тега body (важно что НЕ НУЖНО оборачивать стили и скрипты в теги <style> и <script> - т.к. я использую наборы файлов!


###Правая панель (сайдбар) 100%-1440px/2 - 2 режима/вкладки
1 режим "Редактирование добаленного блока"
- опция редактирования добавленного блока вызывается из области "схема" путем нажатие на редактирование соответсвующего блока
- сайдбар содержит следующие поля - description, block_type (multiselect с loadOptions из поля block_type коллекции wblock_proto directus), notes
- редактор (интеллектуальный редактор аналогичный табу "Редактор" в app\components\WBlockProtoSidebarV2.vue связанный с полем json
- поле HBS (prismEditorWithCopy)
- Поле JSON (prismEditorWithCopy)
- картинки (интеллектуальный редактор аналогичный табу "Картинки" в app\components\WBlockProtoSidebarV2.vue связанный с полем json
- опция редактирования добавленного блока из сайдбара связана с общим полем html и iframe (на них влияют данные из полей HBS и JSON)
- данные из этих полей потом используюстя в качестве основы для новых блоков которые сохраняются в базу при нажатии на кнопки в центральной части

2 режим "Редактирование блока из базы" 
- опция редактирования блока из базы вызывается из карточки левого сайдбара открывает правый сайдбар редактирования (дополнительный) аналогичный сайдбару app\components\WBlockProtoSidebarV2.vue

##Функционал
- добавление блоков из базу на холст (iframe) + схему + html
- редактирование экземпляров добавленных блоков 
- сохранение новых экзмепляров блоков отредактированных через сайдбар с логикой аналогичной wblock-page-gen (сохранение страниц, блоков и страницы+блоков со всеми функциями - компилирование html, получение скриншотов с конструированием html (для поля sketch), установление связей (между wblock_proto и wpage), анализирование html (htmlAnalyzer) для заполнения полей (composition (tree), layout, elements, graphics, features)   
-- логика формирования number, title, collection аналогично wblock-page-gen (за основу берется number, title и tags сохраняемой страницы/полей которые заполнены в верхней центральной части - number, title, tags)
-- для полей HBS, JSON, description, block_type, notes берем данные из сайдбара для сохранения в базу (наверное данные должны кэшироваться или сохраняться где-то в памяти - т.к. у нас один сайдбар для редактирования всех добавленных блоков, но данные будут разные - важно их не потерять, подгружать при переключении между блоками без потери данных и сохранение в базу directus в случае запуска соотвествующего действия)

По сути функционал страницы во многом повторяет wblock-page-gen.vue и wpage-gen2.vue (режим "Прототип") но имеет более удобный/чистый и сфокусированный интерфейс а логика компляции JSON+HBS происходит на основе данных из одного экземпляра/записи а не двух разных. я пытаюсь добиться похожего функционала page builder как у gutenberg (wordpress)

##Существующие страницы для анализа
- @d:\Work\1.1.Code\nuxt\nuxt-primevue/app\pages\wblock-page-gen.vue 
- @d:\Work\1.1.Code\nuxt\nuxt-primevue/app\pages\wblock-gen2.vue (режим "Прототип") 
- @d:\Work\1.1.Code\nuxt\nuxt-primevue/app\pages\wblock-proto2.vue 

##Важно
Перед выполнением задач сначала проанализируй каждый пункт тщательно и детально, 
также проанализируй весь код ПОЛНОСТЬЮ в текущем файле и в файлах на которые я ссылаюсь.
После этого составь план действий и следуй ему внося изменения в код очень аккуратно, точечно и внимательно, чтобы не нарушить целостность кода и текущий рабочий функционал. 
Избегай дублирования функций, ошибок - синтаксических и логических, ничего не удаляй и не меняй за пределами поставленных задач. 
Используй практики, которые уже успешно работают в проекте на основе внимательного и тщательного анализа используемого в проекте подходов, стандартов и паттернов кода (api nuxt-directus composables, серверные api-маршруты, esm модули (nuxt 3 nitro vite) компактный интерфейс primevue, tailwind).
Используй правильные импорты сторонних модулей, библиотек и компонентов так как они уже используются и в той же последовательности (prismjs, cheerio, handlebars и др)
НЕ запускай сервер (он у меня всегда запущен) и браузер (страницы над которыми мы работаем всегда у меня открыты)

Это комплексная задача которая требует интеллектуального анализа и аккуратного подхода, а не бездумного быстрого кодирования. 
Если это необходимо лучше разбить задачу на 2-3 этапа с ручным тестированием, логированием и проверкой результатов, для плавного и эффективного внедрения всех запрошенных задач.
Думай как опытный разработчик, имеющий опыт работы актуальный в 2025 году в стеке nuxt 3 nitro vite, api composables, vue 3, typescript, primevue
Общайся со мной на русском языке. Сейчас и всегда.