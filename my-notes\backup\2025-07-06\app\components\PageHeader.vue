<script setup lang="ts">
	const { name } = defineProps({
		name: {
			type: String,
			default: 'PageHeader',
		},
	})
	import Button from 'primevue/button';

	// const emit = defineEmits(['addToCart'])

	// // Use the addToCart function like this:
	// // Eg-1: addToCart('pizza', 10)
	// // Eg-2: (event) => addToCart('pizza', 10, event)
	// const addToCart = (item, quantity /*, event*/) => {
	// 	emit('addToCart', { item, quantity })
	// }
</script> 
<template> 
    <div class="bg-surface-0 flex px-6 py-6 dark:bg-surface-950 md:px-12 lg:px-20"> 
        <div class="flex-1">
            <ul class="flex font-medium items-center list-none m-0 mb-4 p-0"> 
                <li> <a class="text-surface-500 dark:text-surface-300 no-underline leading-normal cursor-pointer"><NuxtLink to="/">Главная</NuxtLink></a> 
                </li>                 
                <li class="align-middle mt-2 px-2"> 
                    <BaseIcon name="i-material-symbols-chevron-right" height="20px"></BaseIcon>                     
                </li>                 
                <li> <span class="text-surface-900 dark:text-surface-0 leading-normal"><NuxtLink to="/wblocks/">Блоки </NuxtLink></span> 
                </li>                 
            </ul>
        </div>
        <div class="me-auto ms-auto mt-4 lg:mt-0"> 
            <Button label="Add" class="mr-2" outlined icon="pi pi-user-plus"/> 
            <Button label="Save" icon="pi pi-check"/> 
        </div>         
        <div class="flex items-start flex-col lg:justify-between lg:flex-row">                           
</div>         
    </div>     
</template> 
<style scoped></style>
