<template> 
  <DataView :value="items" :sortOrder="sortOrder" :sortField="sortField"> 
      <template #header> 
          <Select v-model="sortKey" :options="sortOptions" optionLabel="label" placeholder="Сортировка" @change="onSortChange($event)"/> 
      </template>         
      <template #list="slotProps"> 
          <div class="flex flex-col"> 
              <div v-for="(item, index) in slotProps.items" :key="index"> 
                  <div class="flex flex-col sm:flex-row sm:items-center p-6 gap-4" :class="{ 'border-t border-surface-200 dark:border-surface-700': index !== 0 }"> 
                      <div class="md:w-40 relative"> 
                          <img class="block xl:block mx-auto rounded w-full" src="https://fm-demo.ru/wp-content/uploads/2025/02/spa-salon-image-052.jpg" :alt="item.title"/> 
                          <div class="absolute bg-black/70 rounded-border" style="left: 4px; top: 4px; margin-top: 20px;"> 
                              <Tag>Тег</Tag>                                 
                          </div>                             
                      </div>                         
                      <div class="flex flex-col md:flex-row justify-between md:items-center flex-1 gap-6"> 
                          <div class="flex flex-row md:flex-col justify-between items-start gap-2"> 
                              <div style="margin-top: 7px;"> <span class="category dark:text-surface-400 font-medium text-sm text-surface-500">Категория</span> 
                                  <div class="text-lg font-medium mt-2">
                                      <NuxtLink :to="`/wblock/${encodeURIComponent(item.title)}`">{{ item.title }}</NuxtLink>
                                  </div>                                     
                              </div>                                 
                              <div class="bg-surface-100 p-1" style="border-radius: 30px"> 
                                  <div class="bg-surface-0 flex items-center gap-2 justify-center py-1 px-2" style="border-radius: 30px; box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.06)"> <span class="text-surface-900 font-medium text-sm">Рейтинг</span> <i class="pi pi-star-fill text-yellow-500"></i> 
                                  </div>                                     
                              </div>                                 
                          </div>                             
                          <div class="flex flex-col md:items-end gap-8"> <span class="text-xl font-semibold">цена</span> 
                              <div class="flex flex-row-reverse md:flex-row gap-2"> 
                                  <Button icon="pi pi-heart" outlined></Button>                                     
                                  <Button icon="pi pi-shopping-cart" label="Купить" class="flex-auto md:flex-initial whitespace-nowrap"></Button>                                     
                              </div>                                 
                          </div>                             
                      </div>                         
                  </div>                     
              </div>                 
          </div>             
      </template>         
  </DataView>     
</template> 

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import DataView from 'primevue/dataview';
import Select from 'primevue/select'; 
import { useDirectusItems } from '#imports'; // Необходимо для работы с Directus

const components = {
DataView,
Select
};

const items = ref([]);
const sortKey = ref();
const sortOrder = ref(1); // 1 по возрастанию, -1 по убыванию
const sortField = ref('title'); // поле для сортировки
const sortOptions = ref([
{ label: 'По возрастанию', value: 'title' },
{ label: 'По убыванию', value: '!title' }, 
]);

onMounted(async () => {
try {
  const { getItems } = useDirectusItems();
  const response = await getItems({
    collection: 'wblock', 
    params: {
      fields: ['*'], 
    },
  });

  if (Array.isArray(response)) {
    items.value = response;
  } else if (response && response.data) {
    items.value = response.data;
  } else {
    console.error('Нет данных в ответе:', response);
  }
} catch (error) {
  console.error('Ошибка при получении данных:', error);
}
});

const onSortChange = (event: { value: { value: string } }) => {
const value = event.value.value;

if (value.indexOf('!') === 0) {
  sortOrder.value = -1;
  sortField.value = value.substring(1, value.length);
} else {
  sortOrder.value = 1;
  sortField.value = value;
}
};

defineExpose({ 
items, 
sortKey, 
sortOrder, 
sortField, 
sortOptions,
onSortChange,
components
});
</script> 