<template>
  <Sidebar 
    v-model:visible="localVisible" 
    :position="position" 
    :class="sidebarClass"
  >
    <template #header>
      <div class="flex justify-between items-center w-full">
        <h2 class="text-xl font-semibold">{{ title }}</h2>
        <Button 
          icon="pi pi-times" 
          class="p-button-text p-button-rounded" 
          @click="localVisible = false" 
        />
      </div>
    </template>

    <form @submit.prevent="submitForm">
      <div class="grid gap-4">
        <template v-for="(field, index) in formFields" :key="index">
          <div class="field">
            <label class="block mb-2 font-medium">{{ field.label }}</label>
            <component 
              :is="getInputComponent(field.type)"
              v-model="formData[field.name]"
              v-bind="getInputProps(field)"
              class="w-full"
            />
            <small v-if="field.hint" class="text-gray-500">{{ field.hint }}</small>
          </div>
        </template>
      </div>

      <div class="flex justify-end gap-2 mt-4">
        <Button 
          label="Отмена" 
          icon="pi pi-times" 
          class="p-button-text" 
          @click="localVisible = false" 
        />
        <Button 
          :label="submitLabel" 
          icon="pi pi-check" 
          type="submit" 
          :loading="submitting"
        />
        
        <!-- Диагностическая кнопка -->
        <Button 
          label="🚨 ДИАГНОСТИКА" 
          @click="diagnosticClick"
        />
      </div>
    </form>

    <Button 
      label="Обновить" 
      @click="simpleClick"
    />
  </Sidebar>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import Dropdown from 'primevue/dropdown'
import InputNumber from 'primevue/inputnumber'
import Checkbox from 'primevue/checkbox'
import Calendar from 'primevue/calendar'
import Button from 'primevue/button'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Форма'
  },
  formFields: {
    type: Array,
    default: () => []
  },
  position: {
    type: String,
    default: 'right'
  },
  sidebarClass: {
    type: String,
    default: 'w-[30rem]'
  },
  submitLabel: {
    type: String,
    default: 'Сохранить'
  }
})

const emit = defineEmits(['submit', 'cancel']);

const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('cancel', value)
})

const formData = ref({})
const submitting = ref(false)

const getInputComponent = (type) => {
  const componentMap = {
    'text': InputText,
    'textarea': Textarea,
    'dropdown': Dropdown,
    'number': InputNumber,
    'checkbox': Checkbox,
    'date': Calendar
  }
  return componentMap[type] || InputText
}

const getInputProps = (field) => {
  const defaultProps = {
    'dropdown': { optionLabel: 'label', optionValue: 'value' },
    'number': { mode: 'decimal' },
    'date': { dateFormat: 'dd.mm.yy' }
  }
  return { ...defaultProps[field.type], ...field.props }
}

const submitForm = () => {
  // Максимально примитивная диагностика
  console.log('🚨 SUBMIT FORM CALLED');
  console.log('Form Data:', formData.value);
  console.log('Local Visible:', localVisible.value);
  
  // Принудительный алерт с максимальной информацией
  alert(`
    SUBMIT DIAGNOSTICS:
    Form Data: ${JSON.stringify(formData.value)}
    Local Visible: ${localVisible.value}
    Submitting: ${submitting.value}
  `);

  // Принудительный эмит события
  try {
    emit('submit', formData.value);
    console.log('🟢 Emit successful');
  } catch (error) {
    console.error('❌ Emit failed:', error);
    alert('EMIT ERROR: ' + error.message);
  }
};

const diagnosticClick = () => {
  console.error('🚨 DIAGNOSTIC BUTTON CLICKED');
  window.alert('🔍 DIAGNOSTIC BUTTON WORKS!');
  console.log('Form Data:', formData.value);
  console.log('Local Visible:', localVisible.value);
};

// Reset form data when sidebar is opened
watch(localVisible, (newValue) => {
  if (newValue) {
    formData.value = props.formFields.reduce((acc, field) => {
      acc[field.name] = field.defaultValue || null
      return acc
    }, {})
  }
})

const testValue = ref(0)

function simpleClick() {
  // Принудительный алерт
  window.alert('CLICKED: ' + testValue.value++)
}
</script>
