<template>
  <div class="flex h-screen">
    <!-- Основной контент -->
    <div
      class="flex-1 overflow-hidden flex flex-col"
      :class="{ 'pr-[30rem]': sidebarVisible }"
    >
      <div class="flex justify-between mb-1 p-1">
        <div class="flex gap-2">
          <Button
            v-tooltip.top="'Обновить данные'"
            icon="pi pi-refresh"
            class="p-button-rounded p-button-text p-button-sm"
            :disabled="loading"
            aria-label="Обновить"
            @click="loadFiles"
          />
          <span class="p-input-icon-left">
            
            <InputText
              v-model="globalFilterValue"
              placeholder="Поиск..."
              class="w-full"
              style="font-size: 12px"
              @input="onGlobalFilterChange"
            />
          </span>
          <MultiSelect
            v-model="selectedTags"
            :options="availableTags"
            placeholder="Фильтр по тегам"
            class="w-64 text-xs"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
            display="chip"
            :filter="true"
            @change="onTagFilterChange"
          />
        </div>
        <div class="flex gap-2">

          <Button
            title="Массовое редактирование"
            icon="pi pi-pencil"
            class="text-xs p-button-warning"
            :disabled="!selectedFiles.length"
            @click="openBulkEdit"
          />
          <Button
  title="Сохранить как страницы"
  icon="pi pi-save"
  class="text-xs p-button-info"
  :disabled="!selectedFiles.length || processingFiles"
  @click="saveSelectedFilesAsPages"
/>
          <Dropdown
            v-model="selectedSplitOption"
            :options="splitOptions"
            option-label="label"
            option-value="value"
            placeholder="Вариант разделения"
            class="text-xs"
          />
          <Button
          v-tooltip.bottom="'Разделить на блоки'"
          icon="pi pi-box"

            class="text-xs"
            :disabled="!selectedFiles.length || processingFiles"
            @click="splitSelectedFiles"
          />
          <Button
          v-tooltip.bottom="'Разделить на блоки + скрины'"
          icon="pi pi-camera"

            class="text-xs p-button-success"
            :disabled="!selectedFiles.length || processingFiles"
            @click="splitSelectedFilesWithScreenshots"
          />
          <Button
          v-tooltip.bottom="'Извлечь элементы'"  
          icon="pi pi-objects-column"
            
            class="text-xs p-button-secondary"
            :disabled="!selectedFiles.length || processingFiles"
            @click="extractSelectedElements"
          />
          <Button
          v-tooltip.bottom="'Извлечь элементы + скрины'"   
          icon="pi pi-images"
            
            class="text-xs p-button-warning"
            :disabled="!selectedFiles.length || processingFiles"
            @click="extractSelectedElementsWithScreenshots"
          />
        </div>
      </div>

      <!-- Область массовых форм -->
      <BulkFormContainer
        :visible="bulkFormVisible"
        :mode="bulkFormMode"
        collection="directus_files"
        :field-config="filesBulkFieldConfig"
        :selected-items="selectedFiles"
        @close="closeBulkForm"
        @saved="onBulkFormSaved"
      />

      <!-- Files DataTable -->
      <DataTable
        v-model:selection="selectedFiles"
        :value="filteredFiles"
        :global-filter-fields="['title', 'type']"
        selection-mode="multiple"
        data-key="id"
        striped-rows
        scrollable
        scroll-height="calc(100vh - 60px)"
        :virtual-scroller-options="{ itemSize: 44 }"
        :loading="loading"
        class="p-datatable-sm text-[13px]"
        style="
          --highlight-bg: var(--primary-50);
          padding: 1px;
          font-size: 11px;
        "
      >
        <Column selection-mode="multiple" style="font-size: 9px; padding: 1px; width: 50px" />

        <Column field="number" header="Номер" :sortable="true" style="font-size: 9px; padding: 1px; width: 80px">
          <template #body="{ data }">
            <div class="truncate">{{ data.number || '-' }}</div>
          </template>
        </Column>
        
        <Column field="title" header="Имя файла" :sortable="true" style="padding: 1px; width: 250px">
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <NuxtLink
                :to="`/file/${encodeURIComponent(data.title)}`"
                class="text-primary hover:underline text-xs"
              >
                {{ data.title }}
              </NuxtLink>
              <Button
                label="⿻"
                class="p-button-text p-button-sm"
                style="width: 30px"
                @click="copyToClipboard(data.content)"
              />
            </div>
          </template>
        </Column>

        <Column field="description" header="Описание" style="padding: 1px; font-size: 9px; width: 150px">
          <template #body="{ data }">
            <div class="truncate">{{ data.description || '-' }}</div>
          </template>
        </Column>

        <Column field="tags" header="Теги" style="padding: 1px; width: 150px">
          <template #body="{ data }">
            <div class="flex flex-wrap gap-1">
              <Tag
                v-for="tag in data.tags"
                :key="tag"
                :value="tag"
                style="padding: 0 3px; font-size: 9px"
              />
            </div>
          </template>
        </Column>

        <Column
          field="type"
          header="Тип файла"
          :sortable="true"
          style="padding: 1px; width: 100px"
        >
          <template #body="{ data }">
            <Tag :value="data.type" style="padding: 0 3px; font-size: 9px" />
          </template>
        </Column>

        

        <Column header="Действия" style="padding: 1px; width: 120px">
          <template #body="{ data }">
            <div class="flex gap-1">
              <Button
                icon="pi pi-eye"
                class="p-button-text p-button-sm"
                @click="previewFile(data)"
              />
              <Button
                icon="pi pi-pencil"
                class="p-button-text p-button-sm p-button-info"
                @click="editFile(data)"
              />
              <Button
                icon="pi pi-pause"
                class="p-button-text p-button-sm p-button-warning"
                :disabled="processingFiles"
                @click="splitFile(data.id)"
              />
            </div>
          </template>
        </Column>
      </DataTable>

      <!-- Results Dialog -->
      <Dialog
        v-model:visible="resultsDialogVisible"
        header="Результаты обработки"
        :style="{ width: '70vw' }"
      >
        <div v-if="processingResults">
          <div class="flex justify-between mb-4">
            <div>
              <div class="text-lg font-semibold mb-2">Итоги обработки</div>
              <p>Обработано файлов: {{ processingResults.processedFiles }}</p>
              <p>Создано записей: {{ createdBlocksCount }}</p>
            </div>
            <Button
              label="Закрыть"
              icon="pi pi-times"
              class="p-button-outlined"
              @click="resultsDialogVisible = false"
            />
          </div>

          <DataTable
            :value="processingResults.results"
            class="p-datatable-sm mt-4"
          >
            <Column field="fileName" header="Имя файла" />
            <Column field="blocksExtracted" header="Извлечено записей">
              <template #body="{ data }">
                {{ data.blocksExtracted || data.elementsExtracted || 0 }}
              </template>
            </Column>
            <Column field="success" header="Статус">
              <template #body="{ data }">
                <Tag
                  :severity="data.success ? 'success' : 'danger'"
                  :value="data.success ? 'Успех' : 'Ошибка'"
                />
              </template>
            </Column>
            <Column field="message" header="Сообщение">
              <template #body="{ data }">
                {{ data.message || data.error || '-' }}
              </template>
            </Column>
          </DataTable>
        </div>
      </Dialog>

      <!-- Preview Dialog -->
      <Dialog
        v-model:visible="previewDialogVisible"
        :header="selectedFile?.title"
        :style="{ width: '80vw' }"
      >
        <div v-if="selectedFile" class="max-h-[70vh] overflow-auto">
          <pre class="whitespace-pre-wrap">{{ selectedFile.content }}</pre>
        </div>
      </Dialog>

      <!-- Toast for notifications -->
      <Toast />
    </div>
    
    <!-- Сайдбар редактирования -->
    <WcontSidebar
      v-model:visible="sidebarVisible"
      :collapsed="false"
      title="Редактирование файла"
      @close="sidebarVisible = false"
      @toggle-collapse="() => {}"
    >
      <div class="p-fluid">
        <!-- Форма редактирования -->
        <div class="space-y-2">
          <div class="field">
             <InputText
              id="number"
              v-model="editingFile.number"
              class="w-full"
              placeholder="Номер файла"
              style="padding: 6px; font-size: 12px"
            />
          </div>

          <div class="field">
             <InputText
              id="title"
              v-model="editingFile.title"
              class="w-full"
              placeholder="Название файла"
              style="padding: 6px; font-size: 11px"
            />
          </div>
          
          <div class="field">
            
            <Textarea
              id="description"
              v-model="editingFile.description"
              rows="2"
              class="w-full text-xs [&>textarea]:text-xs"
              auto-resize
              placeholder="Описание файла"
              style="padding: 4px; font-size: 10px"
            />
          </div>
          
          <div class="field">
            
            <Chips
              id="tags"
              v-model="editingFile.tags"
              class="w-full text-xs"
              placeholder="Теги"
              style="font-size: 11px"
            />
          </div>
          
          <div class="field">
            
            <PrismEditorWithCopy
              v-model="editingFile.content"
              class="my-editor overflow-auto text-xs"
              :highlight="highlightHtml"
              placeholder="HTML содержимое файла"
              line-numbers
              readonly
              style="min-height:600px;"
            />
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-4">
          <Button
            label="Отмена"
            icon="pi pi-times"
            class="p-button-sm"
            @click="closeSidebar"
          />
          <Button
            label="Сохранить"
            icon="pi pi-check"
            class="p-button-sm"
            :loading="saving"
            @click="saveFile"
          />
        </div>
      </div>
    </WcontSidebar>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { useToast } from 'primevue/usetoast'
  import { useDirectusItems, useDirectusFiles  } from '#imports'
  import Textarea from 'primevue/textarea'
  import Chips from 'primevue/chips'
  import MultiSelect from 'primevue/multiselect'
  import Dropdown from 'primevue/dropdown'
  import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'
  import BulkFormContainer from '~/components/BulkFormContainer.vue'
  import 'vue-prism-editor/dist/prismeditor.min.css'
  import Prism from 'prismjs'
  import 'prismjs/components/prism-clike'
  import 'prismjs/components/prism-markup'
  import 'prismjs/components/prism-css'
  import 'prismjs/components/prism-javascript'
  import 'prismjs/themes/prism-tomorrow.css'
  

  // State
  const files = ref([])
  const selectedFiles = ref([])
  const loading = ref(false)
  const processingFiles = ref(false)
  const sortKey = ref('number')
  const sortOrder = ref(1)
  const sortField = ref('number')
  const globalFilterValue = ref('')
  const selectedTags = ref([])
  const availableTags = ref([])
  const previewDialogVisible = ref(false)
  const selectedFile = ref(null)
  const resultsDialogVisible = ref(false)
  const processingResults = ref(null)
  const createdBlocksCount = ref(0)
  
  // Опции для разделения на блоки
  const selectedSplitOption = ref(1) // По умолчанию выбран первый вариант
  const splitOptions = ref([
    { label: 'Вариант 1: header, section, footer', value: 1 },
    { label: 'Вариант 2: header, div.section, footer', value: 2 },
    { label: 'Вариант 3: header, section, div, footer', value: 3 }
  ])
  
  // Сайдбар редактирования
  const sidebarVisible = ref(false)
  const editingFile = ref({
    id: null,
    number: '',
    title: '',
    description: '',
    tags: [],
    content: ''
  })
  const saving = ref(false)

  // Состояние для массовых форм
  const bulkFormVisible = ref(false)
  const bulkFormMode = ref<'create' | 'edit'>('create')

  // Directus integration
  const { createItems, updateItem } = useDirectusItems()
  const { getFiles } = useDirectusFiles()

  // Options
  const sortOptions = ref([
    { label: 'По номеру (возр.)', value: 'number' },
    { label: 'По номеру (убыв.)', value: '!number' },
    { label: 'По имени файла (А-Я)', value: 'title' },
    { label: 'По имени файла (Я-А)', value: '!title' },
    { label: 'По типу файла (А-Я)', value: 'type' },
    { label: 'По типу файла (Я-А)', value: '!type' },
  ])

  // Toast
  const toast = useToast()

  // Конфигурация полей для массовых форм files2
  const filesBulkFieldConfig = computed(() => [
    {
      name: 'number',
      type: 'text' as const,
      placeholder: 'Номер',
      class: 'field-quarter'
    },
    {
      name: 'title',
      type: 'text' as const,
      placeholder: 'Название',
      class: 'field-three-quarters'
    },
    {
      name: 'tags',
      type: 'chips' as const,
      placeholder: 'Теги (через запятую)',
      class: 'field-full'
    },
    {
      name: 'description',
      type: 'textarea' as const,
      placeholder: 'Описание',
      class: 'field-full'
    }
  ])

  // Computed
  const filteredFiles = computed(() => {
    // First filter by globalFilterValue if set
    let result = [...files.value]
    console.log('Начальное количество файлов в filteredFiles:', result.length)

    if (globalFilterValue.value) {
      const searchLower = globalFilterValue.value.toLowerCase()
      result = result.filter(
        (file) =>
          file.title?.toLowerCase().includes(searchLower) ||
          file.type?.toLowerCase().includes(searchLower) ||
          file.tags?.some((tag) => tag.toLowerCase().includes(searchLower)),
      )
      console.log('После фильтрации по поиску:', result.length)
    }
    
    // Filter by selected tags if any
    if (selectedTags.value.length > 0) {
      result = result.filter((file) => {
        if (!file.tags || !Array.isArray(file.tags)) return false
        return selectedTags.value.some(tag => file.tags.includes(tag))
      })
      console.log('После фильтрации по тегам:', result.length)
    }

    // Then apply sorting
    return result.sort((a, b) => {
      const field = sortField.value.replace('!', '')
      const valueA = a[field]
      const valueB = b[field]

      if (typeof valueA === 'string') {
        return sortOrder.value * valueA.localeCompare(valueB)
      } else {
        return sortOrder.value * (valueA - valueB)
      }
    })
  })

  // Methods
  async function loadFiles() {
    loading.value = true
    try {
      // Используем composable для получения файлов вместо прямого fetch
      const folderId = '974200ea-bd51-4320-875f-b49089200404'
      
      // Получаем файлы через composable с указанием лимита -1 для получения всех файлов
      const filesData = await getFiles({
        params: {
          filter: { folder: { _eq: folderId } },
          limit: -1 // Получить все файлы без ограничений
        }
      })
      
      console.log('Получено файлов через composable:', filesData.length)
      console.log('Первые 3 файла:', filesData.slice(0, 3))
      
      // Используем все файлы из указанной папки без фильтрации
      files.value = filesData
      console.log('Установлено файлов в files.value:', files.value.length)
      
      // Extract unique tags for the MultiSelect
      const allTags = new Set()
      files.value.forEach(file => {
        if (file.tags && Array.isArray(file.tags)) {
          file.tags.forEach(tag => allTags.add(tag))
        }
      })
      availableTags.value = Array.from(allTags).sort()
      console.log('Доступные теги:', availableTags.value)
      
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Загружено ${files.value.length} файлов`,
        life: 3000,
      })
    } catch (error) {
      console.error('Ошибка при получении файлов:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить список файлов',
        life: 3000,
      })
    } finally {
      loading.value = false
    }
  }

  function onSortChange(event) {
    const value = event.value.value
    sortField.value = value
    sortKey.value = value.replace('!', '')
    sortOrder.value = value.startsWith('!') ? -1 : 1
  }
  
  function onTagFilterChange() {
    // Фильтрация происходит автоматически через computed свойство
  }

  function onGlobalFilterChange() {
    // The filtering is handled by the computed property
  }

  function copyToClipboard(text) {
    navigator.clipboard.writeText(text)
    toast.add({
      severity: 'info',
      summary: 'Скопировано',
      detail: 'Текст скопирован в буфер обмена',
      life: 3000,
    })
  }

  async function previewFile(file) {
    selectedFile.value = file

    if (!file.content) {
      try {
        // Fetch the file content if it's not already loaded
        const response = await fetch(
          `http://localhost:8055/files/${file.id}/content`,
        )
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const content = await response.text()
        selectedFile.value = { ...file, content }
      } catch (error) {
        console.error('Ошибка при получении содержимого файла:', error)
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Не удалось загрузить содержимое файла',
          life: 3000,
        })
      }
    }

    previewDialogVisible.value = true
  }

  async function processAndSaveBlocks(apiResponse) {
    // Обнуляем счетчик созданных блоков
    createdBlocksCount.value = 0

    // Для каждого файла в результатах
    for (const fileResult of apiResponse.results) {
      if (
        !fileResult.success ||
        !fileResult.blocksToCreate ||
        fileResult.blocksToCreate.length === 0
      ) {
        continue
      }

      try {
        console.log(
          `Сохранение ${fileResult.blocksToCreate.length} блоков в Directus для файла ${fileResult.fileName}...`,
        )

        // Используем useDirectusItems для сохранения блоков в Directus
        const result = await createItems({
          collection: 'wblock_proto',
          items: fileResult.blocksToCreate,
        })

        const createdCount = Array.isArray(result)
          ? result.length
          : result
            ? 1
            : 0
        createdBlocksCount.value += createdCount

        console.log(`Успешно сохранено ${createdCount} блоков`)

        // Обновляем информацию в результатах
        fileResult.blocksAdded = createdCount
      } catch (error) {
        console.error(
          `Ошибка при сохранении блоков в Directus для файла ${fileResult.fileName}:`,
          error,
        )
        fileResult.message = `Ошибка при сохранении блоков: ${error.message}`
      }
    }

    return apiResponse
  }

  // Оптимизированные функции для создания скриншотов (из wblock-page-gen)

  // Функция создания одиночного скриншота (использует batch API для лучшей обработки анимаций)
  async function generateScreenshot(htmlContent: string, filename: string = 'screenshot') {
    try {
      console.log(`📸 Создание скриншота: ${filename}...`)

      // Используем batch API даже для одного скриншота для лучшей обработки анимаций
      const response = await $fetch('/api/capture-batch-screenshots', {
        method: 'POST',
        body: {
          screenshots: [{
            html: htmlContent,
            filename: filename,
            width: 1400,
            height: 800
          }]
        }
      })

      if (response.results && response.results.length > 0 && response.results[0].success) {
        console.log(`✅ Скриншот создан: ${response.results[0].filename}, ID: ${response.results[0].fileId}`)
        return response.results[0].fileId
      } else {
        throw new Error(`Не удалось создать скриншот: ${response.results?.[0]?.error || 'Unknown error'}`)
      }

    } catch (error) {
      console.error(`❌ Ошибка создания скриншота ${filename}:`, error)
      throw error
    }
  }

  // Функция создания множественных скриншотов (batch режим)
  async function generateBatchScreenshots(screenshots: Array<{html: string, filename: string}>) {
    try {
      console.log(`📸 Создание ${screenshots.length} скриншотов в batch режиме...`)

      const response = await $fetch('/api/capture-batch-screenshots', {
        method: 'POST',
        body: {
          screenshots: screenshots.map(item => ({
            html: item.html,
            filename: item.filename,
            width: 1400,
            height: 800
          }))
        }
      })

      console.log(`✅ Batch скриншоты созданы: ${response.successCount}/${screenshots.length} успешно за ${response.totalTime}ms`)

      // Возвращаем массив fileId в том же порядке
      return response.results.map(result => result.success ? result.fileId : null)

    } catch (error) {
      console.error(`❌ Ошибка создания batch скриншотов:`, error)
      throw error
    }
  }

  // Умная функция выбора режима создания скриншотов
  async function generateScreenshotsOptimal(screenshots: Array<{html: string, filename: string}>) {
    // Если скриншот один - используем одиночный режим
    if (screenshots.length === 1) {
      console.log('📸 Используем одиночный режим для 1 скриншота')
      const fileId = await generateScreenshot(screenshots[0].html, screenshots[0].filename)
      return [fileId]
    }

    // Если скриншотов несколько - используем batch режим
    console.log(`📸 Используем batch режим для ${screenshots.length} скриншотов`)
    return await generateBatchScreenshots(screenshots)
  }

  // Функция для обработки результатов API и сохранения блоков со скриншотами в Directus (УНИФИЦИРОВАННЫЙ ПОДХОД)
  async function processAndSaveBlocksWithScreenshots(apiResponse) {
    // Обнуляем счетчик созданных блоков
    createdBlocksCount.value = 0

    // Для каждого файла в результатах
    for (const fileResult of apiResponse.results) {
      if (
        !fileResult.success ||
        !fileResult.blocksToCreate ||
        fileResult.blocksToCreate.length === 0
      ) {
        continue
      }

      try {
        console.log(
          `Сохранение ${fileResult.blocksToCreate.length} блоков со скриншотами в Directus для файла ${fileResult.fileName}...`,
        )

        // 1. Сначала создаем блоки в Directus
        const result = await createItems({
          collection: 'wblock_proto',
          items: fileResult.blocksToCreate,
        })

        const createdBlocks = Array.isArray(result) ? result : [result]
        const createdCount = createdBlocks.length
        createdBlocksCount.value += createdCount

        console.log(`Успешно сохранено ${createdCount} блоков`)

        // 2. Создаем скриншоты УНИФИЦИРОВАННЫМ способом (один файл, все блоки)
        console.log(`📸 Создание унифицированных скриншотов для ${createdCount} блоков...`)

        try {
          // Подготавливаем данные для унифицированного создания скриншотов
          const blocksForScreenshots = createdBlocks.map((block, index) => ({
            html: block.html || '',
            css: block.css || '',
            js: block.js || '',
            filename: `block-${block.number || index + 1}-${block.title || 'screenshot'}`
          }))

          // Создаем все скриншоты унифицированным способом
          const screenshotResponse = await $fetch('/api/capture-blocks-unified', {
            method: 'POST',
            body: {
              blocks: blocksForScreenshots,
              sourceFilename: fileResult.fileName
            }
          })

          console.log(`✅ Унифицированные скриншоты созданы: ${screenshotResponse.successCount}/${createdCount} успешно за ${screenshotResponse.totalTime}ms`)

          // Обновляем блоки с ID скриншотов
          for (let i = 0; i < createdBlocks.length; i++) {
            const block = createdBlocks[i]
            const screenshotResult = screenshotResponse.results[i]

            if (screenshotResult && screenshotResult.success && screenshotResult.fileId) {
              try {
                await updateItem({
                  collection: 'wblock_proto',
                  id: block.id,
                  item: {
                    sketch: screenshotResult.fileId
                  }
                })

                console.log(`✅ Скриншот привязан к блоку ${block.number || i + 1}`)
              } catch (updateError) {
                console.error(`❌ Ошибка обновления блока ${block.number || i + 1} со скриншотом:`, updateError)
              }
            } else {
              console.error(`❌ Скриншот не создан для блока ${block.number || i + 1}`)
            }
          }

          console.log(`✅ Обработка унифицированных скриншотов завершена: ${screenshotResponse.successCount}/${createdBlocks.length} успешно`)
        } catch (screenshotError) {
          console.error(`❌ Ошибка создания унифицированных скриншотов:`, screenshotError)
          // Продолжаем выполнение, даже если скриншоты не удались
        }

        // Обновляем информацию в результатах
        fileResult.blocksAdded = createdCount
      } catch (error) {
        console.error(
          `Ошибка при сохранении блоков со скриншотами в Directus для файла ${fileResult.fileName}:`,
          error,
        )
        fileResult.message = `Ошибка при сохранении блоков со скриншотами: ${error.message}`
      }
    }

    return apiResponse
  }

  async function splitFile(fileId) {
    if (processingFiles.value) return

    try {
      processingFiles.value = true
      loading.value = true

      // 1. Вызываем API для разделения файла
      const response = await fetch('/api/split-html-files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          fileIds: [fileId],
          splitOption: selectedSplitOption.value // Передаем выбранный вариант разделения
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`,
        )
      }

      // 2. Обрабатываем результат и сохраняем блоки
      const apiResult = await response.json()
      const processedResult = await processAndSaveBlocks(apiResult)

      // 3. Сохраняем результаты для отображения
      processingResults.value = processedResult

      // 4. Показываем диалог с результатами
      resultsDialogVisible.value = true

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Файл разделен на ${createdBlocksCount.value} блоков`,
        life: 5000,
      })
    } catch (error) {
      console.error('Ошибка при разделении файла:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось разделить файл на блоки: ' + error.message,
        life: 5000,
      })
    } finally {
      processingFiles.value = false
      loading.value = false
    }
  }

  // Функция для загрузки содержимого файла
  async function loadFileContent(fileId) {
    try {
      const response = await fetch(`http://localhost:8055/assets/${fileId}`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.text()
    } catch (error) {
      console.error('Ошибка при загрузке содержимого файла:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить содержимое файла',
        life: 3000,
      })
      return ''
    }
  }

  // Функция для открытия сайдбара редактирования файла
  async function editFile(file) {
    // Копируем данные файла в объект редактирования
    editingFile.value = {
      id: file.id,
      number: file.number || '',
      title: file.title || '',
      description: file.description || '',
      tags: file.tags || [],
      content: ''
    }
    
    // Загружаем содержимое файла
    if (file.type === 'text/html' || file.filename_download?.endsWith('.html')) {
      editingFile.value.content = await loadFileContent(file.id)
    }
    
    // Открываем сайдбар
    sidebarVisible.value = true
  }
  
  // Функция для закрытия сайдбара
  function closeSidebar() {
    sidebarVisible.value = false
    // Сбрасываем данные редактирования
    editingFile.value = {
      id: null,
      number: '',
      title: '',
      description: '',
      tags: [],
      content: ''
    }
  }
  
  // Функция для сохранения изменений файла
  async function saveFile() {
    if (!editingFile.value.id) return
    
    saving.value = true
    
    try {
      // Обновляем метаданные файла в Directus через специальный API для системной коллекции files
      const response = await fetch(`http://localhost:8055/files/${editingFile.value.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          number: editingFile.value.number,
          title: editingFile.value.title,
          description: editingFile.value.description,
          tags: editingFile.value.tags
        })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.errors?.[0]?.message || `HTTP error! status: ${response.status}`)
      }
      
      // Обновляем данные в локальном массиве
      const fileIndex = files.value.findIndex(f => f.id === editingFile.value.id)
      if (fileIndex !== -1) {
        files.value[fileIndex] = {
          ...files.value[fileIndex],
          number: editingFile.value.number,
          title: editingFile.value.title,
          description: editingFile.value.description,
          tags: editingFile.value.tags
        }
      }
      
      // Показываем уведомление об успехе
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Файл успешно обновлен',
        life: 3000
      })
      
      // Закрываем сайдбар
      closeSidebar()
    } catch (error) {
      console.error('Ошибка при обновлении файла:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось обновить файл: ' + error.message,
        life: 5000
      })
    } finally {
      saving.value = false
    }
  }
  
  async function splitSelectedFiles() {
    if (selectedFiles.value.length === 0 || processingFiles.value) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Не выбрано ни одного файла или обработка уже запущена',
        life: 3000,
      })
      return
    }

    try {
      processingFiles.value = true
      loading.value = true

      const fileIds = selectedFiles.value.map((file) => file.id)

      // 1. Вызываем API для разделения файлов
      const response = await fetch('/api/split-html-files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileIds,
          splitOption: selectedSplitOption.value // Передаем выбранный вариант разделения
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`,
        )
      }

      // 2. Обрабатываем результат и сохраняем блоки
      const apiResult = await response.json()
      const processedResult = await processAndSaveBlocks(apiResult)

      // 3. Сохраняем результаты для отображения
      processingResults.value = processedResult

      // 4. Показываем диалог с результатами
      resultsDialogVisible.value = true

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Разделено ${fileIds.length} файлов на ${createdBlocksCount.value} блоков`,
        life: 5000,
      })

      // Clear selection after processing
      selectedFiles.value = []
    } catch (error) {
      console.error('Ошибка при разделении файлов:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось разделить файлы на блоки: ' + error.message,
        life: 5000,
      })
    } finally {
      processingFiles.value = false
      loading.value = false
    }
  }

  // Функция для разделения файлов на блоки с созданием скриншотов
  async function splitSelectedFilesWithScreenshots() {
    if (selectedFiles.value.length === 0 || processingFiles.value) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Не выбрано ни одного файла или обработка уже запущена',
        life: 3000,
      })
      return
    }

    try {
      processingFiles.value = true
      loading.value = true

      const fileIds = selectedFiles.value.map((file) => file.id)

      // 1. Вызываем API для разделения файлов
      const response = await fetch('/api/split-html-files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileIds,
          splitOption: selectedSplitOption.value
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`,
        )
      }

      // 2. Обрабатываем результат и сохраняем блоки с скриншотами
      const apiResult = await response.json()
      const processedResult = await processAndSaveBlocksWithScreenshots(apiResult)

      // 3. Сохраняем результаты для отображения
      processingResults.value = processedResult

      // 4. Показываем диалог с результатами
      resultsDialogVisible.value = true

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Разделено ${fileIds.length} файлов на ${createdBlocksCount.value} блоков со скриншотами`,
        life: 5000,
      })

      // Clear selection after processing
      selectedFiles.value = []
    } catch (error) {
      console.error('Ошибка при разделении файлов со скриншотами:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось разделить файлы на блоки со скриншотами: ' + error.message,
        life: 5000,
      })
    } finally {
      processingFiles.value = false
      loading.value = false
    }
  }

  // Функция для сохранения выбранных файлов как страниц
async function saveSelectedFilesAsPages() {
  if (selectedFiles.value.length === 0) return
  
  try {
    processingFiles.value = true
    
    for (const file of selectedFiles.value) {
      // 1. Загружаем содержимое файла
      const htmlContent = await loadFileContent(file.id)
      
      // 2. Извлекаем контент с учетом варианта разделения
      const { bodyHtml, cssContent, jsContent } = await $fetch('/api/extract-page-content', {
        method: 'POST',
        body: {
          html: htmlContent,
          fileInfo: file,
          splitOption: selectedSplitOption.value // Передаем выбранный вариант для CSS/JS префиксов
        }
      })
      
      // 3. Создаем интеллектуальный скриншот с правильной структурой HTML
      console.log(`📸 Создание скриншота для страницы: ${file.title}`)
      let screenshotId = null

      try {
        // Конструируем полный HTML документ с правильными стилями и скриптами
        const fullHtmlForScreenshot = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${file.title || 'Generated Page'}</title>
  ${cssContent || ''}
</head>
<body>
  ${bodyHtml}
  ${jsContent || ''}
</body>
</html>`

        const filename = `page_${file.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`

        // Используем интеллектуальный API для скриншотов (как в wblock-page-gen.vue)
        const screenshotResponse = await $fetch('/api/capture-html-screenshot', {
          method: 'POST',
          body: {
            html: fullHtmlForScreenshot,
            filename: filename,
            width: 1400,
            height: 800
          }
        })

        // API возвращает объект { fileId, filename }, извлекаем fileId
        screenshotId = screenshotResponse.fileId
        console.log(`✅ Скриншот страницы создан: ${screenshotId}`)
      } catch (screenshotError) {
        console.error(`⚠️ Ошибка создания скриншота для ${file.title}:`, screenshotError)
        // Продолжаем сохранение без скриншота
      }
      
      // 4. Сохраняем в wpage
      const savedPage = await createItems({
        collection: 'wpage',
        items: [{
          number: file.number || '',
          title: file.title,
          tags: file.tags || [],
          html: bodyHtml,
          css: cssContent,
          js: jsContent,
          sketch: screenshotId
        }]
      })
      
      const pageId = Array.isArray(savedPage) ? savedPage[0].id : savedPage.id
      
      // 5. Создаем связь m2m
      await createItems({
        collection: 'wpage_directus_files',
        items: [{
          wpage_id: pageId,
          directus_files_id: file.id
        }]
      })
    }
    
    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранено ${selectedFiles.value.length} файлов как страниц`
    })
    
  } catch (error) {
    console.error('Ошибка:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить файлы как страницы'
    })
  } finally {
    processingFiles.value = false
  }
}

  // Подсветка HTML кода
  function highlightHtml(code) {
    return Prism.highlight(code, Prism.languages.markup, 'markup')
  }

  // Функция для извлечения элементов из выбранных файлов
  async function extractSelectedElements() {
    if (selectedFiles.value.length === 0 || processingFiles.value) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Не выбрано ни одного файла или обработка уже запущена',
        life: 3000,
      })
      return
    }

    try {
      processingFiles.value = true
      loading.value = true

      const fileIds = selectedFiles.value.map((file) => file.id)

      // 1. Вызываем API для извлечения элементов
      const response = await fetch('/api/extract-html-elements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileIds,
          splitOption: selectedSplitOption.value // Передаем выбранный вариант для CSS/JS префиксов
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`,
        )
      }

      // 2. Обрабатываем результат и сохраняем элементы
      const apiResult = await response.json()
      const processedResult = await processAndSaveElements(apiResult)

      // 3. Сохраняем результаты для отображения
      processingResults.value = processedResult

      // 4. Показываем диалог с результатами
      resultsDialogVisible.value = true

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Извлечено ${fileIds.length} файлов на ${createdBlocksCount.value} элементов`,
        life: 5000,
      })

      // Clear selection after processing
      selectedFiles.value = []
    } catch (error) {
      console.error('Ошибка при извлечении элементов:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось извлечь элементы: ' + error.message,
        life: 5000,
      })
    } finally {
      processingFiles.value = false
      loading.value = false
    }
  }

  // Функция для обработки и сохранения элементов
  async function processAndSaveElements(apiResponse) {
    // Обнуляем счетчик созданных элементов
    createdBlocksCount.value = 0

    // Для каждого файла в результатах
    for (const fileResult of apiResponse.results) {
      if (
        !fileResult.success ||
        !fileResult.elementsToCreate ||
        fileResult.elementsToCreate.length === 0
      ) {
        continue
      }

      try {
        console.log(
          `Сохранение ${fileResult.elementsToCreate.length} элементов в Directus для файла ${fileResult.fileName}...`,
        )

        // Используем useDirectusItems для сохранения элементов в Directus
        const result = await createItems({
          collection: 'welem_proto',
          items: fileResult.elementsToCreate,
        })

        const createdCount = Array.isArray(result)
          ? result.length
          : result
            ? 1
            : 0
        createdBlocksCount.value += createdCount

        console.log(`Успешно сохранено ${createdCount} элементов`)

        // Обновляем информацию в результатах
        fileResult.elementsAdded = createdCount
      } catch (error) {
        console.error(
          `Ошибка при сохранении элементов в Directus для файла ${fileResult.fileName}:`,
          error,
        )
        fileResult.message = `Ошибка при сохранении элементов: ${error.message}`
      }
    }

    return apiResponse
  }

  // Функция для извлечения элементов из выбранных файлов с генерацией скриншотов
  async function extractSelectedElementsWithScreenshots() {
    if (selectedFiles.value.length === 0 || processingFiles.value) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Не выбрано ни одного файла или обработка уже запущена',
        life: 3000,
      })
      return
    }

    try {
      processingFiles.value = true
      loading.value = true

      const fileIds = selectedFiles.value.map((file) => file.id)

      // 1. Вызываем API для извлечения элементов с данными для скриншотов
      const response = await $fetch('/api/extract-html-elements-with-screenshots', {
        method: 'POST',
        body: {
          fileIds,
          splitOption: selectedSplitOption.value
        }
      })

      // 2. Обрабатываем результат и сохраняем элементы с скриншотами
      const processedResult = await processAndSaveElementsWithScreenshots(response)

      // 3. Сохраняем результаты для отображения
      processingResults.value = processedResult

      // 4. Показываем диалог с результатами
      resultsDialogVisible.value = true

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Извлечено ${fileIds.length} файлов на ${createdBlocksCount.value} элементов с скриншотами`,
        life: 5000,
      })

      // Clear selection after processing
      selectedFiles.value = []
    } catch (error) {
      console.error('Ошибка при извлечении элементов с скриншотами:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось извлечь элементы с скриншотами: ' + error.message,
        life: 5000,
      })
    } finally {
      processingFiles.value = false
      loading.value = false
    }
  }

  // Функция для обработки и сохранения элементов с скриншотами (ОПТИМИЗИРОВАННАЯ)
  async function processAndSaveElementsWithScreenshots(apiResponse) {
    // Обнуляем счетчик созданных элементов
    createdBlocksCount.value = 0
    let totalScreenshots = 0

    // ЭТАП 1: Создаем все элементы без скриншотов
    const allElementsForScreenshots = []
    const elementToCreatedElementMap = new Map()

    for (const fileResult of apiResponse.results) {
      if (
        !fileResult.success ||
        !fileResult.elementsToCreate ||
        fileResult.elementsToCreate.length === 0
      ) {
        continue
      }

      try {
        console.log(
          `Сохранение ${fileResult.elementsToCreate.length} элементов в Directus для файла ${fileResult.fileName}...`,
        )

        // Создаем элементы без скриншотов сначала
        const elementsToCreateWithoutScreenshots = fileResult.elementsToCreate.map(element => {
          const { _screenshotData, ...elementData } = element
          return elementData
        })

        // Разбиваем на батчи по 50 элементов для избежания ошибки "request entity too large"
        const BATCH_SIZE = 50
        const createdElements = []

        for (let i = 0; i < elementsToCreateWithoutScreenshots.length; i += BATCH_SIZE) {
          const batch = elementsToCreateWithoutScreenshots.slice(i, i + BATCH_SIZE)
          console.log(`Создание батча ${Math.floor(i / BATCH_SIZE) + 1}: ${batch.length} элементов (${i + 1}-${Math.min(i + BATCH_SIZE, elementsToCreateWithoutScreenshots.length)} из ${elementsToCreateWithoutScreenshots.length})`)

          try {
            const batchResult = await createItems({
              collection: 'welem_proto',
              items: batch,
            })

            const batchElements = Array.isArray(batchResult) ? batchResult : [batchResult]
            createdElements.push(...batchElements)

            console.log(`✅ Батч ${Math.floor(i / BATCH_SIZE) + 1} создан: ${batchElements.length} элементов`)
          } catch (batchError) {
            console.error(`❌ Ошибка создания батча ${Math.floor(i / BATCH_SIZE) + 1}:`, batchError)
            throw batchError
          }
        }

        const createdCount = createdElements.length
        createdBlocksCount.value += createdCount

        console.log(`Успешно сохранено ${createdCount} элементов в ${Math.ceil(elementsToCreateWithoutScreenshots.length / BATCH_SIZE)} батчах`)

        // Собираем данные для batch создания скриншотов
        for (let i = 0; i < createdElements.length; i++) {
          const createdElement = createdElements[i]
          const originalElementData = fileResult.elementsToCreate[i]

          if (originalElementData._screenshotData) {
            const elementForScreenshot = {
              html: originalElementData._screenshotData.fullHtmlDocument,
              selector: originalElementData._screenshotData.selector,
              elementTitle: originalElementData._screenshotData.elementTitle,
              elementNumber: originalElementData._screenshotData.elementNumber,
              elementHtml: originalElementData.html // Точный HTML код элемента
            }

            allElementsForScreenshots.push(elementForScreenshot)
            elementToCreatedElementMap.set(elementForScreenshot, createdElement)
          }
        }

        // Обновляем информацию в результатах
        fileResult.elementsAdded = createdCount
      } catch (error) {
        console.error(
          `Ошибка при сохранении элементов в Directus для файла ${fileResult.fileName}:`,
          error,
        )
        fileResult.message = `Ошибка при сохранении элементов: ${error.message}`
      }
    }

    // ЭТАП 2: Создаем все скриншоты в batch режиме
    if (allElementsForScreenshots.length > 0) {
      try {
        console.log(`📸 Создание ${allElementsForScreenshots.length} скриншотов элементов в batch режиме...`)

        const batchResponse = await $fetch('/api/capture-batch-element-screenshots', {
          method: 'POST',
          body: {
            elements: allElementsForScreenshots,
            width: 1400,
            height: 800
          }
        })

        console.log(`✅ Batch скриншоты созданы: ${batchResponse.successCount}/${allElementsForScreenshots.length} успешно, ${batchResponse.skippedCount} пропущено за ${batchResponse.totalTime}ms`)

        // ЭТАП 3: Обновляем элементы с ID скриншотов
        for (let i = 0; i < batchResponse.results.length; i++) {
          const screenshotResult = batchResponse.results[i]
          const elementForScreenshot = allElementsForScreenshots[i]
          const createdElement = elementToCreatedElementMap.get(elementForScreenshot)

          if (screenshotResult.success && screenshotResult.fileId && createdElement) {
            try {
              await updateItem({
                collection: 'welem_proto',
                id: createdElement.id,
                item: {
                  sketch: screenshotResult.fileId
                },
              })

              totalScreenshots++
              console.log(`✅ Скриншот привязан к элементу ${screenshotResult.elementTitle}`)
            } catch (updateError) {
              console.error(`❌ Ошибка обновления элемента ${screenshotResult.elementTitle}:`, updateError)
            }
          } else if (!screenshotResult.success) {
            console.warn(`⚠️ Скриншот не создан для элемента ${screenshotResult.elementTitle}: ${screenshotResult.error}`)
          }
        }

      } catch (batchError) {
        console.error('❌ Ошибка batch создания скриншотов:', batchError)
      }
    }

    console.log(`🏁 Создано ${totalScreenshots} скриншотов для элементов`)
    return apiResponse
  }

  // Функции для массовых форм

  function openBulkEdit() {
    if (selectedFiles.value.length === 0) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Выберите файлы для редактирования',
        life: 3000
      })
      return
    }
    bulkFormMode.value = 'edit'
    bulkFormVisible.value = true
  }

  function closeBulkForm() {
    bulkFormVisible.value = false
  }

  function onBulkFormSaved(savedItems) {
    // Обновляем список файлов
    loadFiles()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Обработано ${savedItems.length} файлов`,
      life: 3000
    })
  }

  // Удаляем тестовую функцию, так как основная функция теперь использует composable
  onMounted(async () => {
    await loadFiles()
  })
</script>

<style scoped>
  .truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 9px;
    line-height: 1.4;
    padding: 2px;
  }

  /* optional class for removing the outline */
  .prism-editor__textarea:focus {
    outline: none;
  }
</style>

