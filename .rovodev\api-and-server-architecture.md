# API и серверная архитектура

## Обзор серверной части

Проект использует Nuxt Server API (Nitro) для обработки серверных операций. Серверная часть отвечает за:
- Анализ и обработку HTML
- Генерацию скриншотов
- Конвертацию HTML в шаблоны
- Извлечение элементов из HTML
- Работу с файловой системой

## Структура серверных API

### Основные директории
```
server/
├── api/                    # API endpoints
│   ├── analyze-*.post.ts   # Анализ контента
│   ├── capture-*.post.ts   # Генерация скриншотов
│   ├── extract-*.post.ts   # Извлечение элементов
│   ├── html-to-*.post.ts   # Конвертация HTML
│   └── split-*.js          # Разделение файлов
├── utils/                  # Серверные утилиты
│   ├── htmlToTemplate.js   # Основная логика конвертации
│   ├── htmlAnalyzer.ts     # Анализ HTML
│   └── elementTypeMapping.ts # Маппинг типов
└── middleware/             # Middleware
    └── parse-json-body.js  # Парсинг JSON
```

## Ключевые API endpoints

### 1. Анализ контента

#### `analyze-html.post.ts`
**Назначение**: Анализ HTML структуры и извлечение метаданных

**Входные параметры**:
```typescript
{
  html: string,           // HTML контент
  options?: {
    extractText?: boolean,
    analyzeStructure?: boolean,
    detectComponents?: boolean
  }
}
```

**Возвращает**:
```typescript
{
  success: boolean,
  structure: {
    elements: ElementInfo[],
    hierarchy: TreeNode[],
    statistics: AnalysisStats
  },
  metadata: {
    title?: string,
    description?: string,
    keywords?: string[]
  }
}
```

#### `analyze-html-element.post.ts`
**Назначение**: Детальный анализ отдельного HTML элемента

**Входные параметры**:
```typescript
{
  html: string,
  selector?: string,
  elementIndex?: number
}
```

#### `batch-analyze-html.post.ts`
**Назначение**: Массовый анализ множественных HTML документов

### 2. Генерация скриншотов

#### `capture-html-screenshot.post.ts`
**Назначение**: Создание скриншота HTML контента

**Входные параметры**:
```typescript
{
  html: string,
  options: {
    width?: number,         // Ширина viewport (по умолчанию 1200)
    height?: number,        // Высота viewport (по умолчанию 800)
    fullPage?: boolean,     // Полная страница или viewport
    quality?: number,       // Качество изображения (1-100)
    format?: 'png' | 'jpeg' | 'webp',
    delay?: number          // Задержка перед скриншотом (мс)
  }
}
```

**Возвращает**:
```typescript
{
  success: boolean,
  screenshot: string,     // Base64 encoded изображение
  metadata: {
    width: number,
    height: number,
    format: string,
    size: number          // Размер в байтах
  }
}
```

#### `capture-element-screenshot.post.ts`
**Назначение**: Скриншот конкретного элемента

#### `capture-batch-screenshots.post.ts`
**Назначение**: Массовая генерация скриншотов

**Особенности**:
- Использует Puppeteer для рендеринга
- Поддержка различных viewport размеров
- Оптимизация для batch обработки
- Автоматическое управление ресурсами браузера

### 3. Конвертация HTML

#### `html-to-template.post.ts`
**Назначение**: Конвертация HTML в Handlebars шаблон + JSON данные

**Входные параметры**:
```typescript
{
  html: string,
  options?: {
    preserveStructure?: boolean,
    extractVariables?: boolean,
    generateHelpers?: boolean
  }
}
```

**Возвращает**:
```typescript
{
  success: boolean,
  hbs: string,           // Handlebars шаблон
  variables: object,     // JSON переменные
  helpers?: string[],    // Список необходимых helpers
  error?: string
}
```

**Алгоритм работы**:
1. Парсинг HTML с помощью Cheerio
2. Анализ повторяющихся паттернов
3. Извлечение динамических данных
4. Генерация Handlebars синтаксиса
5. Создание JSON структуры данных

### 4. Извлечение элементов

#### `extract-html-elements.post.ts`
**Назначение**: Извлечение отдельных элементов из HTML

**Входные параметры**:
```typescript
{
  html: string,
  criteria: {
    selectors?: string[],     // CSS селекторы
    tags?: string[],          // HTML теги
    classes?: string[],       // CSS классы
    minSize?: number,         // Минимальный размер элемента
    maxDepth?: number         // Максимальная глубина вложенности
  }
}
```

**Возвращает**:
```typescript
{
  success: boolean,
  elements: ExtractedElement[]
}

interface ExtractedElement {
  html: string,
  selector: string,
  type: string,
  metadata: {
    tag: string,
    classes: string[],
    attributes: Record<string, string>,
    textContent: string,
    size: { width: number, height: number }
  }
}
```

#### `extract-elements-from-blocks.post.ts`
**Назначение**: Извлечение элементов из блоков с дополнительной обработкой

### 5. Разделение файлов

#### `split-html-files.js`
**Назначение**: Разделение HTML файлов на логические блоки

**Варианты разделения**:
- **simple**: По основным тегам (header, main, footer, section)
- **advanced**: По семантическим блокам с анализом структуры
- **semantic**: По смысловым разделам с AI-анализом

**Входные параметры**:
```javascript
{
  html: string,
  splitType: 'simple' | 'advanced' | 'semantic',
  options: {
    minBlockSize?: number,
    preserveStructure?: boolean,
    generateTitles?: boolean
  }
}
```

## Серверные утилиты

### htmlToTemplate.js
**Основная утилита для конвертации HTML в шаблоны**

**Ключевые функции**:
```javascript
// Основная функция конвертации
export function htmlToHandlebarsAndJson(html, options = {}) {
  const $ = cheerio.load(html)
  
  // 1. Анализ структуры
  const structure = analyzeStructure($)
  
  // 2. Извлечение переменных
  const variables = extractVariables($, structure)
  
  // 3. Генерация шаблона
  const template = generateTemplate($, variables)
  
  return {
    success: true,
    hbsTemplate: template,
    jsonData: variables
  }
}

// Анализ повторяющихся элементов
function analyzeRepeatingElements($) {
  // Логика поиска паттернов
}

// Извлечение динамических данных
function extractDynamicContent($, element) {
  // Логика извлечения контента
}
```

### htmlAnalyzer.ts
**Анализатор HTML структуры**

**Основные возможности**:
```typescript
export class HtmlAnalyzer {
  // Анализ семантической структуры
  analyzeSemanticStructure(html: string): SemanticStructure

  // Определение типов элементов
  classifyElements(elements: Element[]): ElementClassification[]

  // Анализ доступности
  analyzeAccessibility(html: string): AccessibilityReport

  // Извлечение метаданных
  extractMetadata(html: string): PageMetadata
}
```

### elementTypeMapping.ts
**Маппинг типов элементов**

```typescript
export const ELEMENT_TYPE_MAPPING = {
  // Навигационные элементы
  navigation: ['nav', '.navbar', '.menu', '.navigation'],
  
  // Контентные блоки
  hero: ['.hero', '.banner', '.jumbotron'],
  card: ['.card', '.item', '.product'],
  
  // Формы
  form: ['form', '.form', '.contact-form'],
  
  // Медиа
  gallery: ['.gallery', '.carousel', '.slider'],
  
  // Структурные элементы
  header: ['header', '.header'],
  footer: ['footer', '.footer'],
  sidebar: ['.sidebar', '.aside', 'aside']
}

export function detectElementType(element: Element): string {
  // Логика определения типа
}
```

## Паттерны серверной разработки

### 1. Стандартный API endpoint

```typescript
// server/api/example.post.ts
export default defineEventHandler(async (event) => {
  try {
    // 1. Валидация входных данных
    const body = await readBody(event)
    const { requiredParam, optionalParam } = body

    if (!requiredParam) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Required parameter is missing'
      })
    }

    // 2. Основная логика
    const result = await processData(requiredParam, optionalParam)

    // 3. Возврат результата
    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    }

  } catch (error) {
    console.error('API Error:', error)
    
    // 4. Обработка ошибок
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || 'Internal server error'
    })
  }
})
```

### 2. Batch обработка

```typescript
// Паттерн для массовой обработки
export default defineEventHandler(async (event) => {
  const { items, options } = await readBody(event)
  
  const results = []
  const errors = []
  
  // Обработка с ограничением параллельности
  const BATCH_SIZE = 5
  
  for (let i = 0; i < items.length; i += BATCH_SIZE) {
    const batch = items.slice(i, i + BATCH_SIZE)
    
    const batchPromises = batch.map(async (item, index) => {
      try {
        const result = await processItem(item, options)
        return { index: i + index, success: true, data: result }
      } catch (error) {
        return { index: i + index, success: false, error: error.message }
      }
    })
    
    const batchResults = await Promise.allSettled(batchPromises)
    
    batchResults.forEach(result => {
      if (result.status === 'fulfilled') {
        if (result.value.success) {
          results.push(result.value)
        } else {
          errors.push(result.value)
        }
      }
    })
  }
  
  return {
    success: errors.length === 0,
    processed: results.length,
    errors: errors.length,
    results,
    errors
  }
})
```

### 3. Puppeteer интеграция

```typescript
// Паттерн для работы с Puppeteer
import puppeteer from 'puppeteer'

let browser: any = null

async function getBrowser() {
  if (!browser) {
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage'
      ]
    })
  }
  return browser
}

export async function captureScreenshot(html: string, options: ScreenshotOptions) {
  const browser = await getBrowser()
  const page = await browser.newPage()
  
  try {
    // Настройка viewport
    await page.setViewport({
      width: options.width || 1200,
      height: options.height || 800
    })
    
    // Загрузка контента
    await page.setContent(html, { waitUntil: 'networkidle0' })
    
    // Ожидание рендеринга
    if (options.delay) {
      await page.waitForTimeout(options.delay)
    }
    
    // Создание скриншота
    const screenshot = await page.screenshot({
      type: options.format || 'png',
      quality: options.quality || 90,
      fullPage: options.fullPage || false,
      encoding: 'base64'
    })
    
    return screenshot
    
  } finally {
    await page.close()
  }
}

// Очистка ресурсов при завершении процесса
process.on('exit', async () => {
  if (browser) {
    await browser.close()
  }
})
```

## Оптимизация и производительность

### 1. Кеширование
```typescript
// Простое in-memory кеширование
const cache = new Map()

export function withCache<T>(key: string, fn: () => Promise<T>, ttl = 300000): Promise<T> {
  const cached = cache.get(key)
  
  if (cached && Date.now() - cached.timestamp < ttl) {
    return Promise.resolve(cached.data)
  }
  
  return fn().then(data => {
    cache.set(key, { data, timestamp: Date.now() })
    return data
  })
}
```

### 2. Ограничение ресурсов
```typescript
// Семафор для ограничения параллельных операций
class Semaphore {
  private permits: number
  private waiting: Array<() => void> = []

  constructor(permits: number) {
    this.permits = permits
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--
      return Promise.resolve()
    }

    return new Promise(resolve => {
      this.waiting.push(resolve)
    })
  }

  release(): void {
    this.permits++
    if (this.waiting.length > 0) {
      const next = this.waiting.shift()!
      this.permits--
      next()
    }
  }
}

const screenshotSemaphore = new Semaphore(3) // Максимум 3 параллельных скриншота
```

### 3. Обработка ошибок
```typescript
// Централизованная обработка ошибок
export function handleApiError(error: any, context: string) {
  console.error(`[${context}] Error:`, error)
  
  if (error.code === 'ENOENT') {
    throw createError({
      statusCode: 404,
      statusMessage: 'File not found'
    })
  }
  
  if (error.code === 'EACCES') {
    throw createError({
      statusCode: 403,
      statusMessage: 'Permission denied'
    })
  }
  
  throw createError({
    statusCode: 500,
    statusMessage: 'Internal server error'
  })
}
```

## Интеграция с фронтендом

### Клиентские вызовы API
```typescript
// Универсальная функция для вызова API
export async function callServerApi<T>(
  endpoint: string, 
  data: any, 
  options: { timeout?: number } = {}
): Promise<T> {
  const controller = new AbortController()
  
  if (options.timeout) {
    setTimeout(() => controller.abort(), options.timeout)
  }
  
  try {
    const response = await $fetch<T>(`/api/${endpoint}`, {
      method: 'POST',
      body: data,
      signal: controller.signal
    })
    
    return response
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('Request timeout')
    }
    throw error
  }
}

// Специализированные функции
export const apiService = {
  analyzeHtml: (html: string) => 
    callServerApi('analyze-html', { html }),
    
  captureScreenshot: (html: string, options: any) => 
    callServerApi('capture-html-screenshot', { html, options }),
    
  convertToTemplate: (html: string) => 
    callServerApi('html-to-template', { html })
}
```

Эта серверная архитектура обеспечивает мощные возможности обработки контента с высокой производительностью и надежностью.