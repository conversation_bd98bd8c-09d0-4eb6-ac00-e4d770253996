<template>
    <div v-if="file">
      <h1>{{ file.title }}</h1>
      <div v-if="fileContent">
        <pre v-if="isTextFile">{{ fileContent }}</pre>
        <img v-else-if="isImageFile" :src="fileContent" :alt="file.title" />
        <p v-else>Невозможно отобразить содержимое файла данного типа.</p>
      </div>
      <p v-else>Загрузка содержимого файла...</p>
      <button @click="createWblockItem">Сохранить в Wblock</button>
    </div>
  </template>

  <script setup lang="ts">
  import { ref, onMounted, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { useDirectusItems } from '#imports';

  const route = useRoute();
  const { createItems } = useDirectusItems();
  const file = ref(null);
  const fileContent = ref(null);

  const isTextFile = computed(() => {
    return ['text/plain', 'text/html', 'application/json', 'text/markdown'].includes(file.value?.type);
  });

  const isImageFile = computed(() => {
    return ['image/png', 'image/jpeg', 'image/webp'].includes(file.value?.type);
  });

  onMounted(async () => {
    try {
      const title = decodeURIComponent(route.params.title);
      const filesResponse = await fetch('http://localhost:8055/files');
      const filesData = await filesResponse.json();
      file.value = filesData.data.find(f => f.title === title);

      if (file.value) {
        const fileResponse = await fetch(`http://localhost:8055/assets/${file.value.id}`);
        if (isTextFile.value) {
          fileContent.value = await fileResponse.text();
        } else if (isImageFile.value) {
          fileContent.value = fileResponse.url;
        } else {
          console.warn('Невозможно отобразить содержимое файла данного типа:', file.value.type);
        }
      }
    } catch (error) {
      console.error('Ошибка при получении файла:', error);
    }
  });






  const createWblockItem = async () => {
    try {
      const response = await createItems({
        collection: 'wblock',
        items: {
          title: file.value.title, // Используем file.value.title
          html: fileContent.value  // Используем fileContent.value
        }
      });

      console.log('Запись успешно создана:', response);
    } catch (error) {
      console.error('Ошибка при создании записи:', error);
    }
  };
  </script>




