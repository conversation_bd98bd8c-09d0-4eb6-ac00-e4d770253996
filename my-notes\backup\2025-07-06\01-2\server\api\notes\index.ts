import { readdir, stat, readFile } from 'fs/promises'
import { resolve, extname } from 'path'
import { existsSync } from 'fs'
import yaml from 'js-yaml' //  Импортируем js-yaml 

export default defineEventHandler(async (event) => {
  const notesDir = resolve(process.cwd(), 'my-notes')

  if (!existsSync(notesDir)) {
    return []
  }

  try {
    const files = await readdir(notesDir)
    const filePromises = files.map(async (file) => {
      const filePath = resolve(notesDir, file)
      const stats = await stat(filePath)

      // Пропускаем директории и файлы, не являющиеся markdown или txt
      if (stats.isDirectory() || !['.md', '.txt'].includes(extname(file).toLowerCase())) {
        return null
      }

      const content = await readFile(filePath, 'utf-8')
      const yamlData = extractYamlData(content) // Извлекаем YAML данные

      return {
        name: file,
        path: file,
        size: stats.size,
        modified: stats.mtime,
        yamlData // Добавляем YAML данные к информации о файле
      }
    })

    const fileInfos = await Promise.all(filePromises)
    return fileInfos.filter(Boolean)
  } catch (error) {
    throw createError({
      statusCode: 500,
      message: 'Error reading notes directory'
    })
  }
})

// Функция для извлечения данных YAML из файла
function extractYamlData(content) {
  const yamlBlockMatch = content.match(/^---\n([\s\S]+?)\n---\n/)
  if (yamlBlockMatch) {
    try {
      return yaml.load(yamlBlockMatch[1])
    } catch (error) {
      console.error('Error parsing YAML:', error)
    }
  }
  return null
} 
