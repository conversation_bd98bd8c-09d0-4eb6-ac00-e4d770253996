<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useToast } from 'primevue/usetoast'

definePageMeta({
  title: 'Инструмент скриншотов',
  description: 'Создание скриншотов секций веб-страниц',
  navOrder: 10,
  type: 'primary',
  icon: 'i-mdi-camera',
})

const toast = useToast()
const url = ref('')
const loading = ref(false)
const screenshots = ref([])
const progress = ref(0)
const totalSections = ref(0)

const captureScreenshots = async () => {
  if (!url.value) {
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Пожалуйста, введите URL страницы',
      life: 3000,
    })
    return
  }

  try {
    loading.value = true
    screenshots.value = []
    progress.value = 0
    totalSections.value = 0

    // Отправляем запрос на сервер для создания скриншотов
    const response = await $fetch('/api/capture-screenshots', {
      method: 'POST',
      body: { url: url.value },
    })

    screenshots.value = response.screenshots
    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Создано ${screenshots.value.length} скриншотов`,
      life: 3000,
    })
  } catch (error) {
    console.error('Ошибка при создании скриншотов:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: error.message || 'Не удалось создать скриншоты',
      life: 5000,
    })
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="container mx-auto p-4">
    

    <Card class="mb-4">
      <template #title>Создание скриншотов</template>
      <template #content>
        <div class="flex flex-col gap-4">
          <div class="flex gap-2">
            <span class="p-input-icon-left flex-1">
              <i class="pi pi-link" />
              <InputText 
                v-model="url" 
                placeholder="Введите URL страницы" 
                class="w-full" 
                :disabled="loading"
              />
            </span>
            <Button 
              label="Создать скриншоты" 
              icon="pi pi-camera" 
              :loading="loading" 
              :disabled="!url"
              @click="captureScreenshots"
            />
          </div>

          <ProgressBar 
            v-if="loading" 
            :value="progress" 
            class="h-2"
          />
          <small v-if="loading && totalSections > 0">
            Обработано {{ progress }}% ({{ Math.round(totalSections * progress / 100) }} из {{ totalSections }} секций)
          </small>
        </div>
      </template>
    </Card>

    <div v-if="screenshots.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <Card v-for="(screenshot, index) in screenshots" :key="index" class="overflow-hidden">
        <template #header>
          <div class="relative">
            <Image 
              :src="`http://localhost:8055/assets/${screenshot.fileId}`" 
              alt="Скриншот секции" 
              width="100%" 
              preview 
            />
          </div>
        </template>
        <template #title>Секция {{ index + 1 }}</template>
        <template #subtitle>{{ screenshot.filename }}</template>
        <template #content>
          <div class="flex justify-between">
            <Button 
              icon="pi pi-download" 
              label="Скачать" 
              class="p-button-outlined p-button-sm" 
              @click="() => window.open(`http://localhost:8055/assets/${screenshot.fileId}`, '_blank')"
            />
            <Button 
              icon="pi pi-external-link" 
              label="Открыть в Directus" 
              class="p-button-outlined p-button-sm" 
              @click="() => window.open(`http://localhost:8055/admin/content/files/${screenshot.fileId}`, '_blank')"
            />
          </div>
        </template>
      </Card>
    </div>

    <div v-else-if="!loading" class="text-center p-4 text-gray-500">
      <i class="pi pi-camera text-5xl mb-3"/>
      <p>Введите URL страницы и нажмите "Создать скриншоты"</p>
    </div>
  </div>
</template>

<style scoped>
.container {
  max-width: 1200px;
}
</style>