import { load } from 'cheerio';

/**
 * Упрощенная версия функции для конвертации HTML в шаблон Handlebars и JSON данные
 * Фокусируется только на корректной обработке полей html и hbs
 * Сохраняет оригинальное форматирование атрибутов, особенно data-атрибутов с JSON
 * 
 * @param {string} html - HTML-код для конвертации
 * @returns {Object} Объект с полями hbsTemplate и jsonData
 */
export function htmlToHandlebarsAndJson(html) {
  try {
    if (!html || html.trim().length === 0) {
      return { 
        html: '', 
        hbsTemplate: '',
        jsonData: {},
        success: false,
        error: 'Пустой HTML для конвертации'
      };
    }
    
    // Сохраняем оригинальный HTML
    const originalHtml = html;
    
    // Создаем объект для JSON данных
    let jsonData = {};
    let variableCounters = {};
    
    // Создаем копию HTML для обработки
    let processedHtml = originalHtml;
    
    // Функция для безопасной замены текста в HTML
    function safeReplace(html, searchText, replacement) {
      // Экранируем специальные символы в регулярном выражении
      const escapedText = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`(>\\s*)(${escapedText})(\\s*<)`, 'g');
      return html.replace(regex, `$1${replacement}$3`);
    }
    
    // Обрабатываем текстовые узлы с помощью регулярных выражений
    const textNodeRegex = />\s*([^<>]+?)\s*</g;
    let textMatch;
    let lastIndex = 0;
    
    while ((textMatch = textNodeRegex.exec(originalHtml)) !== null) {
      // Предотвращаем бесконечный цикл
      if (textMatch.index === lastIndex) {
        lastIndex++;
        continue;
      }
      lastIndex = textMatch.index;
      
      const fullMatch = textMatch[0];
      const text = textMatch[1].trim();
      
      if (text && text.length > 1) { // Игнорируем пробелы и очень короткие тексты
        // Определяем имя переменной
        let varName = 'text';
        
        // Добавляем счетчик для уникальности
        if (!variableCounters[varName]) {
          variableCounters[varName] = 1;
        } else {
          variableCounters[varName]++;
        }
        
        // Формируем имя переменной с учетом счетчика
        const fullVarName = variableCounters[varName] > 1 ? 
          `${varName}${variableCounters[varName]}` : varName;
        
        // Добавляем в JSON
        jsonData[fullVarName] = text;
        
        // Заменяем текст на переменную в HTML
        const replacement = fullMatch.replace(text, `{{${fullVarName}}}`);
        processedHtml = processedHtml.replace(fullMatch, replacement);
      }
    }
    
    // Обрабатываем изображения
    const imgRegex = /<img[^>]+src=(['"])([^'"]+)\1[^>]*>/g;
    let imgMatch;
    
    while ((imgMatch = imgRegex.exec(originalHtml)) !== null) {
      const fullMatch = imgMatch[0];
      const src = imgMatch[2];
      
      if (src) {
        // Определяем имя переменной
        const varName = 'image';
        
        // Добавляем счетчик для уникальности
        if (!variableCounters[varName]) {
          variableCounters[varName] = 1;
        } else {
          variableCounters[varName]++;
        }
        
        // Формируем имя переменной с учетом счетчика
        const fullVarName = variableCounters[varName] > 1 ? 
          `${varName}${variableCounters[varName]}` : varName;
        
        // Добавляем в JSON
        jsonData[fullVarName] = src;
        
        // Заменяем src на переменную в HTML, сохраняя оригинальные кавычки
        const quote = imgMatch[1]; // Оригинальные кавычки (одинарные или двойные)
        const replacement = fullMatch.replace(`src=${quote}${src}${quote}`, `src=${quote}{{${fullVarName}}}${quote}`);
        processedHtml = processedHtml.replace(fullMatch, replacement);
      }
    }
    
    // Обрабатываем ссылки
    const linkRegex = /<a[^>]+href=(['"])([^'"#][^'"]*?)\1[^>]*>/g;
    let linkMatch;
    
    while ((linkMatch = linkRegex.exec(originalHtml)) !== null) {
      const fullMatch = linkMatch[0];
      const href = linkMatch[2];
      
      if (href && !href.startsWith('javascript:')) {
        // Определяем имя переменной
        const varName = 'url';
        
        // Добавляем счетчик для уникальности
        if (!variableCounters[varName]) {
          variableCounters[varName] = 1;
        } else {
          variableCounters[varName]++;
        }
        
        // Формируем имя переменной с учетом счетчика
        const fullVarName = variableCounters[varName] > 1 ? 
          `${varName}${variableCounters[varName]}` : varName;
        
        // Добавляем в JSON
        jsonData[fullVarName] = href;
        
        // Заменяем href на переменную в HTML, сохраняя оригинальные кавычки
        const quote = linkMatch[1]; // Оригинальные кавычки (одинарные или двойные)
        const replacement = fullMatch.replace(`href=${quote}${href}${quote}`, `href=${quote}{{${fullVarName}}}${quote}`);
        processedHtml = processedHtml.replace(fullMatch, replacement);
      }
    }
    
    // Обрабатываем теги link для CSS
    const linkCssRegex = /<link([^>]+)href=(['"])([^'"]+)\2([^>]*?)>/g;
    let linkCssMatch;
    
    while ((linkCssMatch = linkCssRegex.exec(originalHtml)) !== null) {
      const fullMatch = linkCssMatch[0];
      const beforeHref = linkCssMatch[1];
      const quote = linkCssMatch[2];
      const href = linkCssMatch[3];
      const afterHref = linkCssMatch[4];
      
      if (href) {
        // Определяем имя переменной
        const varName = 'cssUrl';
        
        // Добавляем счетчик для уникальности
        if (!variableCounters[varName]) {
          variableCounters[varName] = 1;
        } else {
          variableCounters[varName]++;
        }
        
        // Формируем имя переменной с учетом счетчика
        const fullVarName = variableCounters[varName] > 1 ? 
          `${varName}${variableCounters[varName]}` : varName;
        
        // Добавляем в JSON
        jsonData[fullVarName] = href;
        
        // Заменяем href на переменную в HTML, сохраняя оригинальные кавычки и формат тега
        const replacement = `<link${beforeHref}href=${quote}{{${fullVarName}}}${quote}${afterHref}${!afterHref.trim().endsWith('/') ? ' /' : ''}>`;        processedHtml = processedHtml.replace(fullMatch, replacement);
      }
    }
    
    // Применяем декодирование HTML-сущностей к обработанному HTML
    const hbsTemplate = decodeHtmlEntities(processedHtml);
    
    // Декодируем HTML-сущности в оригинальном HTML перед возвратом
    const decodedHtml = decodeHtmlEntities(originalHtml);
    
    return { 
      html: decodedHtml,  // Возвращаем декодированный HTML
      hbsTemplate,        // Возвращаем шаблон с восстановленными атрибутами
      jsonData,
      success: true,
      error: null
    };
  } catch (error) {
    console.error('Ошибка при конвертации HTML в шаблон:', error);
    return {
      html: html || '',
      hbsTemplate: '',
      jsonData: {},
      success: false,
      error: error.message
    };
  }
}

/**
 * Функция для декодирования HTML-сущностей в строке
 * @param {string} html - HTML-строка для декодирования
 * @returns {string} Декодированная HTML-строка
 */
export function decodeHtmlEntities(html) {
  if (!html) return '';
  
  // Функция для декодирования HTML-сущностей в строке
  function decodeEntities(str) {
    return str
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&#39;/g, "'");
  }
  
  // Специальная обработка для data-атрибутов с JSON
  function preserveDataAttributes(html) {
    // Находим все data-атрибуты, которые могут содержать JSON или экранированные кавычки
    const dataAttrRegex = /(data-[a-zA-Z0-9_-]+)=(['"])(.*?)\2/g;
    
    // Сначала обрабатываем data-атрибуты с JSON, не декодируя весь HTML сразу
    let processedHtml = html.replace(dataAttrRegex, (match, attrName, quote, attrValue) => {
      // Проверяем, содержит ли атрибут JSON структуру или экранированные кавычки
      const hasJsonStructure = attrValue.includes('{') && attrValue.includes('}');
      const hasEscapedQuotes = attrValue.includes('&quot;') || attrValue.includes('&#39;');
      
      // Если это JSON-атрибут или содержит экранированные кавычки, обрабатываем специально
      if (hasJsonStructure || hasEscapedQuotes) {
        // Декодируем экранированные кавычки внутри JSON
        const decodedValue = attrValue
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>');
        
        // Используем одинарные кавычки для атрибута, чтобы избежать конфликтов с двойными кавычками в JSON
        return `${attrName}='${decodedValue}'`;
      }
      
      // Для обычных атрибутов сохраняем оригинальные кавычки
      return match;
    });
    
    // Обрабатываем также другие атрибуты, которые могут содержать JSON (например, style, onclick, data-*)
    const otherJsonAttrRegex = /([a-zA-Z0-9_-]+)=(['"])(.*?\{.*?\}.*?)\2/g;
    processedHtml = processedHtml.replace(otherJsonAttrRegex, (match, attrName, quote, attrValue) => {
      // Проверяем, содержит ли атрибут экранированные кавычки
      const hasEscapedQuotes = attrValue.includes('&quot;') || attrValue.includes('&#39;');
      
      if (hasEscapedQuotes) {
        // Декодируем экранированные кавычки внутри JSON
        const decodedValue = attrValue
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>');
        
        // Используем одинарные кавычки для атрибута
        return `${attrName}='${decodedValue}'`;
      }
      
      return match;
    });
    
    
    // Затем декодируем оставшиеся HTML-сущности
    processedHtml = decodeEntities(processedHtml);
    
    return processedHtml;
  }
  
  // Специальная обработка для самозакрывающихся тегов link
  function preserveSelfClosingTags(html) {
    // Находим все самозакрывающиеся теги link
    return html.replace(/<link([^>]*?)(\s*\/?)\s*>/g, (match, attributes, endSlash) => {
      // Убеждаемся, что тег правильно закрыт
      const properEndSlash = endSlash.trim() ? endSlash : ' /';
      return `<link${attributes}${properEndSlash}>`;
    });
  }
  
  // Применяем все обработки последовательно
  let result = preserveDataAttributes(html);
  result = preserveSelfClosingTags(result);
  
  return result;
}