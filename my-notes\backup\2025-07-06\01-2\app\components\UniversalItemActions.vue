<template>
  <div class="flex flex-col gap-2">
    <Button 
      label="Редактировать" 
      icon="pi pi-pencil" 
      class="p-button-sm p-button-outlined" 
      @click="editItem"
    />
    <Button 
      label="Дублировать" 
      icon="pi pi-copy" 
      class="p-button-sm p-button-outlined p-button-secondary" 
      @click="duplicateItem"
    />
    <Button 
      label="Конвертировать" 
      icon="pi pi-refresh" 
      class="p-button-sm p-button-outlined p-button-info" 
      @click="convertItem"
    />
    <Button 
      label="Удалить" 
      icon="pi pi-trash" 
      class="p-button-sm p-button-outlined p-button-danger" 
      @click="deleteItem"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'

interface ItemActionProps {
  item: Record<string, any>
  allowEdit?: boolean
  allowDuplicate?: boolean
  allowConvert?: boolean
  allowDelete?: boolean
}

const props = withDefaults(defineProps<ItemActionProps>(), {
  allowEdit: true,
  allowDuplicate: true,
  allowConvert: true,
  allowDelete: true
})

const emit = defineEmits<{
  (e: 'edit', item: Record<string, any>): void
  (e: 'duplicate', item: Record<string, any>): void
  (e: 'convert', item: Record<string, any>): void
  (e: 'delete', item: Record<string, any>): void
}>()

const toast = useToast()

function editItem() {
  if (!props.allowEdit) return
  emit('edit', props.item)
}

function duplicateItem() {
  if (!props.allowDuplicate) return
  emit('duplicate', props.item)
  toast.add({ 
    severity: 'info', 
    summary: 'Дублирование', 
    detail: 'Элемент будет продублирован', 
    life: 2000 
  })
}

function convertItem() {
  if (!props.allowConvert) return
  emit('convert', props.item)
  toast.add({ 
    severity: 'info', 
    summary: 'Конвертация', 
    detail: 'Элемент будет конвертирован', 
    life: 2000 
  })
}

function deleteItem() {
  if (!props.allowDelete) return
  emit('delete', props.item)
  toast.add({ 
    severity: 'warn', 
    summary: 'Удаление', 
    detail: 'Элемент будет удален', 
    life: 2000 
  })
}
</script>

<style scoped>
.p-button.p-button-sm {
  width: 100%;
}
</style>
