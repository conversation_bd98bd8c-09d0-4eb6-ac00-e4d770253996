import { defineEventHandler, readBody } from 'h3'
import { load } from 'cheerio'
import { generateTreeStructure } from '../utils/htmlAnalyzer'
import { getElementTypes } from '../utils/elementTypeMapping'

// Функция анализа HTML элемента (используем существующие утилиты)
const analyzeHtmlElement = (html: string) => {
  try {
    const $ = load(html || '', {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    })

    const elementTypes = new Set<string>()

    // Анализируем ТОЛЬКО корневой элемент (не вложенные)
    // Проверяем, является ли HTML фрагментом или полным документом
    const isFragment = !html.includes('<html') && !html.includes('<!DOCTYPE')

    if (isFragment) {
      // Для HTML фрагментов анализируем первый элемент верхнего уровня
      const rootElements = $($.root().children())
      if (rootElements.length > 0) {
        const rootElement = rootElements.first()
        const types = getElementTypes(rootElement, $)
        types.forEach(type => elementTypes.add(type))
      }
    } else {
      // Для полных HTML документов анализируем первый элемент в body
      const bodyElements = $('body > *')
      if (bodyElements.length > 0) {
        const rootElement = bodyElements.first()
        const types = getElementTypes(rootElement, $)
        types.forEach(type => elementTypes.add(type))
      } else {
        // Если body не найден, анализируем первый корневой элемент
        const rootElement = $('*').first()
        if (rootElement.length > 0) {
          const types = getElementTypes(rootElement, $)
          types.forEach(type => elementTypes.add(type))
        }
      }
    }

    return {
      elementTypes: Array.from(elementTypes),
      treeStructure: generateTreeStructure(html)
    }
  } catch (error) {
    console.error('Ошибка анализа HTML элемента:', error)
    return {
      elementTypes: [],
      treeStructure: html
    }
  }
}

export default defineEventHandler(async (event) => {
  try {
    const { records } = await readBody(event)

    if (!records || !Array.isArray(records)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Records array is required'
      })
    }

    console.log(`🔍 Начинаем batch анализ ${records.length} HTML элементов...`)

    const results = []

    for (const record of records) {
      try {
        if (!record.html) {
          console.warn(`⚠️ Пропускаем запись ${record.id} - нет HTML`)
          continue
        }

        const analysis = analyzeHtmlElement(record.html)

        results.push({
          id: record.id,
          elementTypes: analysis.elementTypes,
          treeStructure: analysis.treeStructure,
          success: true
        })

        console.log(`✅ Анализ завершен для записи ${record.id}: найдено ${analysis.elementTypes.length} типов элементов`)

      } catch (recordError) {
        console.error(`❌ Ошибка анализа записи ${record.id}:`, recordError)
        results.push({
          id: record.id,
          elementTypes: [],
          treeStructure: '',
          success: false,
          error: recordError.message
        })
      }
    }

    console.log(`🎉 Batch анализ завершен: ${results.length} записей обработано`)

    return {
      success: true,
      results: results,
      total: records.length,
      processed: results.length
    }

  } catch (error) {
    console.error('❌ Ошибка batch анализа HTML элементов:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to analyze HTML elements'
    })
  }
})
