import { load } from 'cheerio';

/**
 * Преобразует HTML в handlebars-шаблон и JSON-данные.
 * Динамические части (например, текст, src, href, значения атрибутов) заменяются на {{ключ}}, а значения собираются в JSON.
 * @param html исходный HTML-код
 * @returns { template: string, data: object }
 */
export function htmlToHbsJson(html: string): { template: string, data: any } {
  const $ = load(html);
  const data: any = {};
  let counter = 1;

  function processNode(node: any, parentKey: string = 'root'): string {
    if (node.type === 'text') {
      const text = node.data.trim();
      if (text) {
        const key = `${parentKey}_text${counter++}`;
        data[key] = text;
        return `{{${key}}}`;
      }
      return '';
    }
    if (node.type === 'tag') {
      const tag = node.name;
      let attrs = '';
      const attrData: any = {};
      Object.entries(node.attribs || {}).forEach(([attr, value]) => {
        // Если значение динамическое (например, src, href, alt, title, value, style)
        if (/^(src|href|alt|title|value|placeholder|style|data-[\w-]+)$/i.test(attr)) {
          const key = `${parentKey}_${attr}${counter++}`;
          data[key] = value;
          attrData[attr] = `{{${key}}}`;
        } else {
          attrData[attr] = value;
        }
      });
      // Формируем строку атрибутов
      attrs = Object.entries(attrData).map(([k, v]) => `${k}="${v}"`).join(' ');
      if (attrs) attrs = ' ' + attrs;
      // Обрабатываем детей
      const children = (node.children || []).map((child: any) => processNode(child, `${parentKey}_${tag}`)).join('');
      return `<${tag}${attrs}>${children}</${tag}>`;
    }
    return '';
  }

  // Обрабатываем все корневые элементы
  const template = $.root().children().map((i, el) => processNode(el, 'root')).get().join('');
  return { template, data };
}

/**
 * Собирает HTML из handlebars-шаблона и JSON-данных
 * @param template handlebars-шаблон
 * @param data JSON-данные
 * @returns HTML-строка
 */
export function hbsJsonToHtml(template: string, data: any): string {
  // Простейшая замена {{ключ}} на значение
  return template.replace(/{{(.*?)}}/g, (_, key) => data[key] || '');
}