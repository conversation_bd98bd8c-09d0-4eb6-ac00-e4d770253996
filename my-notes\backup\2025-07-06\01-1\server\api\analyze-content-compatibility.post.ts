export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { elementVariables, contentSets } = body

    if (!elementVariables || !contentSets) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Element variables and content sets are required'
      })
    }

    // Анализируем совместимость каждого набора контента
    const compatibilityResults = contentSets.map((contentSet: any) => {
      const contentVariables = Object.keys(contentSet.json || {})
      
      // Точные совпадения
      const exactMatches: { [key: string]: string[] } = {}
      elementVariables.forEach((elemVar: string) => {
        exactMatches[elemVar] = contentVariables.filter(contentVar => 
          contentVar === elemVar
        )
      })

      // Предложения по маппингу на основе семантического анализа
      const suggestions: { [key: string]: any[] } = {}
      elementVariables.forEach((elemVar: string) => {
        suggestions[elemVar] = []

        contentVariables.forEach(contentVar => {
          let confidence = 0
          let reason = ''

          // Анализ по типам переменных
          if (elemVar.includes('title') && contentVar.includes('title')) {
            confidence = 0.9
            reason = 'Оба содержат "title"'
          } else if (elemVar.includes('text') && contentVar.includes('text')) {
            confidence = 0.8
            reason = 'Оба содержат "text"'
          } else if (elemVar.includes('image') && contentVar.includes('image')) {
            confidence = 0.9
            reason = 'Оба содержат "image"'
          } else if (elemVar.includes('url') && contentVar.includes('url')) {
            confidence = 0.9
            reason = 'Оба содержат "url"'
          } else if (elemVar.includes('excerpt') && contentVar.includes('text')) {
            confidence = 0.6
            reason = 'excerpt может использовать text'
          } else if (elemVar.includes('linkText') && contentVar.includes('text')) {
            confidence = 0.7
            reason = 'linkText может использовать text'
          }

          // Анализ по длине контента
          const contentLength = String(contentSet.json[contentVar] || '').length
          if (elemVar.includes('title') && contentLength < 100) {
            confidence += 0.1
            reason += ', подходящая длина для заголовка'
          } else if (elemVar.includes('text') && contentLength > 50) {
            confidence += 0.1
            reason += ', подходящая длина для текста'
          }

          if (confidence > 0.5) {
            suggestions[elemVar].push({
              contentVar,
              confidence: Math.min(confidence, 1),
              reason,
              contentLength,
              contentPreview: String(contentSet.json[contentVar] || '').substring(0, 50) + '...'
            })
          }
        })

        // Сортируем предложения по уверенности
        suggestions[elemVar].sort((a, b) => b.confidence - a.confidence)
      })

      // Вычисляем общий процент покрытия
      const coveredVariables = elementVariables.filter((elemVar: string) => 
        exactMatches[elemVar].length > 0 || suggestions[elemVar].length > 0
      )
      const coverage = coveredVariables.length / elementVariables.length

      return {
        contentSetId: contentSet.id,
        contentSetTitle: contentSet.title,
        exactMatches,
        suggestions,
        coverage,
        contentVariables,
        variableCount: contentVariables.length,
        score: coverage * 100 // Общий балл совместимости
      }
    })

    // Сортируем по убыванию совместимости
    compatibilityResults.sort((a, b) => b.score - a.score)

    return {
      success: true,
      results: compatibilityResults,
      bestMatch: compatibilityResults[0] || null
    }

  } catch (error: any) {
    console.error('Ошибка анализа совместимости контента:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Internal server error'
    })
  }
})
