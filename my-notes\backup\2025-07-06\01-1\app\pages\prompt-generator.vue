<template>
  <div class="flex h-screen">
    <!-- Основной контент -->
    <div class="flex-1 overflow-hidden flex flex-col">
      <div class="flex justify-between mb-4 p-4">
        <div class="flex gap-2">
          <Button 
            label="Новый шаблон" 
            icon="pi pi-plus" 
            class="p-button-outlined" 
            @click="openNewTemplateDialog"
          />
          <Dropdown 
            v-model="selectedCategory" 
            :options="categories" 
            option-label="name" 
            placeholder="Категория"
            class="w-48"
          />
        </div>
        
        <div class="flex gap-2">
          <MultiSelect
            v-model="selectedTags"
            :options="availableTags"
            placeholder="Фильтр по тегам"
            display="chip"
            class="w-64"
          />
        </div>
      </div>

      <!-- Список шаблонов -->
      <div class="flex-1 overflow-auto p-4">
        <DataTable :value="filteredTemplates" selection-mode="single" @row-select="onTemplateSelect">
          <Column field="name" header="Название"/>
          <Column field="category" header="Категория">
            <template #body="{ data }">
              <Tag :value="data.category" :severity="getCategorySeverity(data.category)" />
            </template>
          </Column>
          <Column field="description" header="Описание"/>
          <Column field="tags" header="Теги">
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag v-for="tag in data.tags" :key="tag" :value="tag" class="mr-1" />
              </div>
            </template>
          </Column>
          <Column header="Действия" style="width: 120px">
            <template #body="{ data }">
              <Button v-tooltip="'Дублировать'" icon="pi pi-copy" class="p-button-text" @click="duplicateTemplate(data)" />
              <Button v-tooltip="'Редактировать'" icon="pi pi-pencil" class="p-button-text p-button-success" @click="editTemplate(data)" />
              <Button v-tooltip="'Удалить'" icon="pi pi-trash" class="p-button-text p-button-danger" @click="confirmDelete(data)" />
            </template>
          </Column>
        </DataTable>
      </div>
    </div>

    <!-- Редактор промптов -->
    <div v-if="selectedTemplate" class="w-1/2 border-l flex flex-col">
      <div class="p-4 border-b flex justify-between items-center">
        <div>
          <h3 class="text-lg font-semibold">{{ selectedTemplate.name }}</h3>
          <p class="text-sm text-gray-500">{{ selectedTemplate.description }}</p>
        </div>
        <div class="flex gap-2">
          <Button 
            label="Сгенерировать" 
            icon="pi pi-bolt" 
            :loading="generating" 
            @click="generatePrompt"
          />
        </div>
      </div>

      <div class="flex-1 flex flex-col">
        <!-- Вкладки -->
        <TabView class="flex-1 flex flex-col">
          <TabPanel header="Конструктор">
            <div class="p-4 h-full flex flex-col">
              <div class="mb-4">
                <label class="block mb-2 font-medium">Системный промпт</label>
                <Textarea v-model="selectedTemplate.systemPrompt" rows="3" class="w-full" />
                <small class="text-gray-500">Определяет роль и контекст для ИИ</small>
              </div>
              
              <div class="mb-4">
                <label class="block mb-2 font-medium">Шаблон промпта</label>
                <Textarea v-model="selectedTemplate.template" class="w-full flex-1" />
                <div class="mt-2 flex flex-wrap gap-2">
                  <Button 
                    v-for="varName in templateVariables" 
                    :key="varName" 
                    :label="'{{' + varName + '}}'" 
                    class="p-button-sm p-button-outlined"
                    @click="insertVariable(varName)"
                  />
                </div>
              </div>
            </div>
          </TabPanel>

          <TabPanel header="Параметры">
            <div class="p-4">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label class="block mb-2 font-medium">Модель</label>
                  <Dropdown 
                    v-model="selectedTemplate.model" 
                    :options="availableModels" 
                    option-label="name"
                    class="w-full"
                  />
                </div>
                <div>
                  <label class="block mb-2 font-medium">Температура</label>
                  <Slider v-model="selectedTemplate.temperature" :min="0" :max="1" :step="0.1" />
                  <small class="text-gray-500">{{ temperatureDescription }}</small>
                </div>
              </div>

              <div class="mb-4">
                <label class="block mb-2 font-medium">Примеры</label>
                <DataTable :value="selectedTemplate.examples" class="p-datatable-sm">
                  <Column field="input" header="Ввод"/>
                  <Column field="output" header="Вывод"/>
                  <Column header="Действия">
                    <template #body="{ data, index }">
                      <Button icon="pi pi-trash" class="p-button-text p-button-danger" @click="removeExample(index)" />
                    </template>
                  </Column>
                </DataTable>
                <div class="mt-2 flex gap-2">
                  <InputText v-model="newExample.input" placeholder="Ввод" class="flex-1" />
                  <InputText v-model="newExample.output" placeholder="Вывод" class="flex-1" />
                  <Button icon="pi pi-plus" @click="addExample" />
                </div>
              </div>
            </div>
          </TabPanel>

          <TabPanel header="Результат">
            <div class="p-4 h-full">
              <div v-if="generatedResult" class="bg-gray-50 p-4 rounded h-full overflow-auto">
                <pre class="whitespace-pre-wrap">{{ generatedResult }}</pre>
              </div>
              <div v-else class="flex items-center justify-center h-full text-gray-400">
                Нажмите "Сгенерировать" для получения результата
              </div>
            </div>
          </TabPanel>
        </TabView>
      </div>
    </div>

    <!-- Диалог нового шаблона -->
    <Dialog v-model:visible="newTemplateDialog" header="Новый шаблон" :modal="true" style="width: 50vw">
      <div class="grid gap-4">
        <div class="field">
          <label class="block mb-2 font-medium">Категория</label>
          <Dropdown 
            v-model="newTemplate.category" 
            :options="categories" 
            option-label="name"
            class="w-full"
          />
        </div>

        <div class="field">
          <label class="block mb-2 font-medium">Название</label>
          <InputText v-model="newTemplate.name" class="w-full" />
        </div>

        <div class="field">
          <label class="block mb-2 font-medium">Описание</label>
          <InputText v-model="newTemplate.description" class="w-full" />
        </div>

        <div class="field">
          <label class="block mb-2 font-medium">Теги</label>
          <InputChips v-model="newTemplate.tags" class="w-full" />
        </div>

        <div class="field">
          <label class="block mb-2 font-medium">Системный промпт</label>
          <Textarea v-model="newTemplate.systemPrompt" rows="3" class="w-full" />
        </div>

        <div class="field">
          <label class="block mb-2 font-medium">Шаблон промпта</label>
          <Textarea v-model="newTemplate.template" rows="10" class="w-full" />
        </div>
      </div>

      <template #footer>
        <Button label="Отмена" icon="pi pi-times" class="p-button-text" @click="newTemplateDialog = false" />
        <Button label="Сохранить" icon="pi pi-check" autofocus @click="saveTemplate" />
      </template>
    </Dialog>

    <!-- Диалог подтверждения удаления -->
    <Dialog v-model:visible="deleteDialog" header="Подтверждение" :modal="true" style="width: 400px">
      <div class="confirmation-content flex items-center">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
        <span>Вы уверены, что хотите удалить этот шаблон?</span>
      </div>
      <template #footer>
        <Button label="Нет" icon="pi pi-times" class="p-button-text" @click="deleteDialog = false" />
        <Button label="Да" icon="pi pi-check" class="p-button-danger" @click="deleteTemplate" />
      </template>
    </Dialog>

    <Toast />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Button from 'primevue/button'
import Dropdown from 'primevue/dropdown'
import MultiSelect from 'primevue/multiselect'
import Tag from 'primevue/tag'
import Textarea from 'primevue/textarea'
import InputText from 'primevue/inputtext'
import InputChips from 'primevue/inputchips'
import Slider from 'primevue/slider'
import Dialog from 'primevue/dialog'
import Tabs from 'primevue/tabs'
import Tab from 'primevue/tab'
import Toast from 'primevue/toast'

const toast = useToast()

// Категории шаблонов
const categories = ref([
  { name: 'Nuxt/Nitro', value: 'nuxt' },
  { name: 'Верстка', value: 'markup' },
  { name: 'Компоненты', value: 'components' },
  { name: 'Данные', value: 'data' }
])

// Модели ИИ
const availableModels = ref([
  { name: 'GPT-4', value: 'gpt-4' },
  { name: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
  { name: 'Claude 3', value: 'claude-3' }
])

// Данные
const templates = ref([])
const selectedTemplate = ref(null)
const selectedCategory = ref(null)
const selectedTags = ref([])
const availableTags = ref([])
const generatedResult = ref('')
const generating = ref(false)
const newTemplateDialog = ref(false)
const deleteDialog = ref(false)
const templateToDelete = ref(null)
const newExample = ref({ input: '', output: '' })

const newTemplate = ref({
  category: 'nuxt',
  name: '',
  description: '',
  tags: [],
  systemPrompt: '',
  template: '',
  model: 'gpt-4',
  temperature: 0.7,
  examples: []
})

// Загрузка данных
onMounted(() => {
  loadTemplates()
})

function loadTemplates() {
  // Примеры шаблонов для Nuxt/Nitro
  const nuxtTemplates = [
    {
      id: 1,
      category: 'nuxt',
      name: 'Генерация API эндпоинта',
      description: 'Создание Nitro API эндпоинта для CRUD операций',
      tags: ['api', 'nitro', 'crud'],
      systemPrompt: `Ты опытный разработчик Nuxt.js приложений. Сгенерируй код API эндпоинта на основе предоставленных требований. Используй современные практики и обеспечивай безопасность.`,
      template: `Сгенерируй Nitro API эндпоинт для {{entityName}} со следующими функциями:
- Получение списка (GET)
- Создание новой записи (POST)
- Обновление записи (PUT)
- Удаление записи (DELETE)

Дополнительные требования:
{{requirements}}

Используй TypeScript и добавь JSDoc комментарии.`,
      model: 'gpt-4',
      temperature: 0.5,
      examples: [
        {
          input: "entityName: 'products', requirements: 'Валидация полей, обработка ошибок'",
          output: "// Пример сгенерированного кода..."
        }
      ]
    },
    {
      id: 2,
      category: 'nuxt',
      name: 'Генерация компонента',
      description: 'Создание Vue компонента с TypeScript',
      tags: ['component', 'vue', 'typescript'],
      systemPrompt: `Ты эксперт по Vue.js и TypeScript. Создавай качественные компоненты с четкой типизацией и документацией.`,
      template: `Создай Vue компонент {{componentName}} со следующими характеристиками:
- Пропсы: {{props}}
- Слоты: {{slots}}
- Функционал: {{functionality}}

Дополнительные требования:
{{requirements}}

Используй Composition API и TypeScript. Добавь документацию.`,
      model: 'gpt-4',
      temperature: 0.6
    }
  ]

  // Примеры шаблонов для верстки
  const markupTemplates = [
    {
      id: 3,
      category: 'markup',
      name: 'Генерация секции',
      description: 'Создание адаптивной секции с Bootstrap 5',
      tags: ['bootstrap', 'section', 'responsive'],
      systemPrompt: `Ты профессиональный верстальщик. Создавай чистый, семантичный и адаптивный HTML/CSS код с использованием Bootstrap 5. Учитывай современные тенденции дизайна.`,
      template: `Создай секцию {{sectionName}} с Bootstrap 5 со следующими характеристиками:
- Тип: {{sectionType}} (hero, features, testimonials и т.д.)
- Структура: {{structure}}
- Стиль: {{style}} (минимализм, корпоративный, креативный и т.д.)
- Особенности: {{features}}

Дополнительные требования:
{{requirements}}

Используй современные практики верстки. Добавь комментарии.`,
      model: 'gpt-4',
      temperature: 0.7,
      examples: [
        {
          input: "sectionName: 'Hero', sectionType: 'hero', style: 'modern', features: 'анимация, кнопка CTA'",
          output: "<!-- Пример сгенерированной секции -->..."
        }
      ]
    }
  ]

  templates.value = [...nuxtTemplates, ...markupTemplates]
  
  // Собираем все теги
  const tags = new Set()
  templates.value.forEach(template => {
    template.tags?.forEach(tag => tags.add(tag))
  })
  availableTags.value = Array.from(tags)
}

// Вычисляемые свойства
const filteredTemplates = computed(() => {
  let filtered = [...templates.value]
  
  if (selectedCategory.value) {
    filtered = filtered.filter(t => t.category === selectedCategory.value.value)
  }
  
  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(template => 
      template.tags?.some(tag => selectedTags.value.includes(tag))
    )
  }
  
  return filtered
})

const templateVariables = computed(() => {
  if (!selectedTemplate.value?.template) return []
  const matches = selectedTemplate.value.template.match(/\{\{\s*(\w+)\s*\}\}/g) || []
  return [...new Set(matches.map(m => m.replace(/\{\{\s*|\s*\}\}/g, '')))]
})

const temperatureDescription = computed(() => {
  const temp = selectedTemplate.value?.temperature || 0.7
  if (temp < 0.3) return 'Высокая точность, низкая креативность'
  if (temp < 0.7) return 'Баланс точности и креативности'
  return 'Высокая креативность, возможны неточности'
})

// Методы
function getCategorySeverity(category) {
  switch (category) {
    case 'nuxt': return 'info'
    case 'markup': return 'success'
    case 'components': return 'warning'
    case 'data': return 'danger'
    default: return null
  }
}

function openNewTemplateDialog() {
  newTemplate.value = {
    category: 'nuxt',
    name: '',
    description: '',
    tags: [],
    systemPrompt: '',
    template: '',
    model: 'gpt-4',
    temperature: 0.7,
    examples: []
  }
  newTemplateDialog.value = true
}

function onTemplateSelect({ data }) {
  selectedTemplate.value = { ...data }
}

function saveTemplate() {
  if (!newTemplate.value.name) {
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Название обязательно', life: 3000 })
    return
  }

  const newId = Math.max(...templates.value.map(t => t.id), 0) + 1
  templates.value.push({
    id: newId,
    ...newTemplate.value
  })
  
  loadTemplates()
  newTemplateDialog.value = false
  toast.add({ severity: 'success', summary: 'Успех', detail: 'Шаблон сохранен', life: 3000 })
}

function editTemplate(template) {
  selectedTemplate.value = template
}

function duplicateTemplate(template) {
  const newId = Math.max(...templates.value.map(t => t.id), 0) + 1
  templates.value.push({
    id: newId,
    ...JSON.parse(JSON.stringify(template)),
    name: `${template.name} (копия)`
  })
  toast.add({ severity: 'success', summary: 'Успех', detail: 'Шаблон дублирован', life: 3000 })
}

function confirmDelete(template) {
  templateToDelete.value = template
  deleteDialog.value = true
}

function deleteTemplate() {
  templates.value = templates.value.filter(t => t.id !== templateToDelete.value.id)
  if (selectedTemplate.value?.id === templateToDelete.value.id) {
    selectedTemplate.value = null
  }
  deleteDialog.value = false
  templateToDelete.value = null
  toast.add({ severity: 'success', summary: 'Успех', detail: 'Шаблон удален', life: 3000 })
}

function addExample() {
  if (!newExample.value.input || !newExample.value.output) return
  
  if (!selectedTemplate.value.examples) {
    selectedTemplate.value.examples = []
  }
  
  selectedTemplate.value.examples.push({ ...newExample.value })
  newExample.value = { input: '', output: '' }
  toast.add({ severity: 'success', summary: 'Успех', detail: 'Пример добавлен', life: 3000 })
}

function removeExample(index) {
  selectedTemplate.value.examples.splice(index, 1)
}

function insertVariable(varName) {
  if (!selectedTemplate.value.template) {
    selectedTemplate.value.template = ''
  }
  selectedTemplate.value.template += ` {{${varName}}}`
}

async function generatePrompt() {
  if (!selectedTemplate.value?.template) {
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Шаблон пуст', life: 3000 })
    return
  }

  generating.value = true
  
  try {
    // TODO: Реальная интеграция с OpenAI API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    generatedResult.value = `Пример сгенерированного промпта для "${selectedTemplate.value.name}":
    
Системный промпт:
${selectedTemplate.value.systemPrompt}

Промпт:
${selectedTemplate.value.template.replace(/\{\{\w+\}\}/g, match => {
  const varName = match.replace(/\{\{|\}\}/g, '')
  return `[${varName}]`
})}

Параметры:
- Модель: ${selectedTemplate.value.model}
- Температура: ${selectedTemplate.value.temperature}`
    
    toast.add({ severity: 'success', summary: 'Успех', detail: 'Промпт сгенерирован', life: 3000 })
  } catch (error) {
    console.error('Ошибка генерации:', error)
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось сгенерировать промпт', life: 3000 })
  } finally {
    generating.value = false
  }
}
</script>

<style scoped>
.field {
  @apply mb-4;
}

.confirmation-content {
  @apply flex items-center;
}

.dark .dialog-content {
  @apply bg-gray-800;
}

.template-variable {
  @apply bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm cursor-pointer;
  @apply dark:bg-blue-900 dark:text-blue-200;
}

.template-variable:hover {
  @apply bg-blue-200 dark:bg-blue-800;
}
</style>