import { load } from 'cheerio'

/**
 * Функция для определения типов элемента на основе его HTML
 * Может возвращать несколько типов для одного элемента
 * @param $element - jQuery-объект элемента
 * @param $ - cheerio instance
 * @returns массив типов элемента
 */
export function getElementTypes($element: any, $: ReturnType<typeof load>): string[] {
  const element = $element[0];
  if (!element) return [];
  
  const tagName = element.name.toLowerCase();
  const classes = $element.attr('class') || '';
  const classArray = classes.split(/\s+/).filter(c => c.length > 0);
  
  const types: string[] = [];
  
  // Проверяем заголовки
  if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
    types.push('Заголовок');
  }
  
  // Проверяем текстовые элементы
  if (tagName === 'p') types.push('Текст');
  if (tagName === 'span') types.push('Краткий-текст');
  if (tagName === 'blockquote') types.push('Цитата');
  
  // Проверяем интерактивные элементы
  if (tagName === 'button' || 
      classArray.some(c => ['btn', 'button', 'mfn-link', 'content_link', 'icon_bar'].includes(c))) {
    types.push('Кнопка');
  }
  
  // Проверяем навигацию
  if (tagName === 'nav' || 
      classArray.some(c => ['navbar', 'nav'].includes(c))) {
    types.push('Навигация');
  }
  if (classArray.some(c => c.includes('breadcrumb'))) {
    types.push('Хлебные-крошки');
  }
  
  // Проверяем контейнеры
  if (classArray.some(c => c.includes('card'))) types.push('Карточка');
  if (classArray.some(c => c.includes('accordion'))) types.push('Аккордеон');
  if (classArray.some(c => c.includes('modal') || c.includes('popup'))) types.push('Модальное-окно');
  
  // Проверяем медиа
  if (['img', 'picture', 'figure'].includes(tagName)) types.push('Картинка');
  if (tagName === 'video') types.push('Видео');
  if (tagName === 'iframe') types.push('iframe');
  if (tagName === 'svg') types.push('svg');
  
  // Проверяем формы и таблицы
  if (tagName === 'form') types.push('Форма');
  if (tagName === 'table') types.push('Таблица');
  
  // Проверяем специальные элементы
  if (tagName === 'header') types.push('Шапка');
  if (tagName === 'footer') types.push('Подвал');
  if (tagName === 'i') types.push('Иконка');
  
  // Проверяем сложные компоненты по классам
  if (classArray.some(c => c.includes('image_frame'))) types.push('Image-frame');
  if (classArray.some(c => ['article_box', 'promo_box', 'call_to_action'].includes(c))) types.push('Cta-box');
  if (classArray.some(c => ['feature_list', 'column_list', 'get_in_touch', 'opening_hours'].includes(c))) types.push('Инфо-лист');
  if (classArray.some(c => c.includes('timeline_items'))) types.push('Таймлайн');
  if (classArray.some(c => c.includes('fancy-divider'))) types.push('Fancy-divider');
  if (classArray.some(c => c.includes('portfolio_grid'))) types.push('Плитка');
  if (classArray.some(c => c.includes('ws_images'))) types.push('Wow-slider');
  if (classArray.some(c => ['nav-tabs', 'nav-pills'].includes(c))) types.push('Табы');
  if (classArray.some(c => c.includes('offcanvas'))) types.push('Offcanvas');
  if (classArray.some(c => c.includes('pagination'))) types.push('Пагинация');
  if (classArray.some(c => c.includes('progress'))) types.push('Прогресс-бар');
  if (classArray.some(c => c.includes('toast'))) types.push('Toast');
  if (classArray.some(c => c.includes('alert'))) types.push('Оповещение');
  if (classArray.some(c => c.includes('badge'))) types.push('Бейдж');
  if (classArray.some(c => c.includes('btn-group'))) types.push('Группа-кнопок');
  if (classArray.some(c => c.includes('dropdown'))) types.push('Dropdown');
  if (classArray.some(c => c.includes('highlight'))) types.push('Выделение');
  if (classArray.some(c => c.includes('flip-box'))) types.push('Флип-бокс');
  if (classArray.some(c => c.includes('atropos'))) types.push('Atropos');
  if (classArray.some(c => c.includes('interactive-banner'))) types.push('iBanner');
  if (classArray.some(c => c.includes('swiper'))) types.push('Swiper');
  if (classArray.some(c => c.includes('customers-style'))) types.push('Логотипы');
  if (classArray.some(c => c.includes('countdown-style'))) types.push('Countdown');
  if (classArray.some(c => c.includes('feature-box') || c.includes('fancy-text-box-style'))) types.push('Инфо-бокс');
  if (classArray.some(c => c.includes('counter'))) types.push('Счетчик');
  if (classArray.some(c => c.includes('divider-style'))) types.push('Разделитель');
  if (classArray.some(c => c.includes('first-letter'))) types.push('Буквица');
  if (classArray.some(c => c.includes('image-gallery-style') || c.includes('instafeed-grid'))) types.push('Галерея');
  if (classArray.some(c => c.includes('pricing-table-style'))) types.push('Прайс');
  if (classArray.some(c => c.includes('process-step-style'))) types.push('Процесс');
  if (classArray.some(c => c.includes('carousel'))) types.push('Карусель');
  if (classArray.some(c => c.includes('list-group') || c.includes('list-style'))) types.push('Чек-лист');
  if (classArray.some(c => c.includes('chart-percent') || c.includes('chart_box'))) types.push('Диаграмма');
  if (classArray.some(c => c.includes('review-style') || c.includes('testimonials-style'))) types.push('Отзывы');
  if (classArray.some(c => c.includes('rotate-box-style'))) types.push('Rotate-box');
  if (classArray.some(c => c.includes('services-box-style'))) types.push('Services-box');
  if (classArray.some(c => c.includes('sliding-box-item'))) types.push('Sliding-box');
  if (classArray.some(c => c.includes('team-style'))) types.push('Команда');
  if (classArray.some(c => c.includes('rev_slider'))) types.push('Rev-Slider');
  if (classArray.some(c => c.includes('portfolio-filter'))) types.push('Фильтр-портфолио');
  if (classArray.some(c => c.includes('portfolio'))) types.push('Портфолио');
  if (classArray.some(c => c.includes('blog'))) types.push('Блог');
  
  // Возвращаем только найденные типы (без типа по умолчанию)
  return types;
}
