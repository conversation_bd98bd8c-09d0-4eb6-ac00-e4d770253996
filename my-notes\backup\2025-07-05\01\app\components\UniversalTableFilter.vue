<template>
  <div class="flex flex-col gap-2">
    <div class="flex gap-2">
      <InputText 
        v-model="localGlobalFilterValue" 
        placeholder="Поиск" 
        class="w-full"
        @input="emitFilterChange"
      />
      <MultiSelect 
        v-model="localSelectedTags" 
        :options="availableTags" 
        placeholder="Теги" 
        class="w-full"
        @change="emitFilterChange"
      />
    </div>
    <div class="flex gap-2 items-center">
      <label>Режим отображения:</label>
      <div class="flex gap-2">
        <Button 
          :class="{ 'bg-primary': viewMode === 'compact', 'bg-secondary': viewMode !== 'compact' }"
          @click="updateViewMode('compact')"
        >
          Компактный
        </Button>
        <Button 
          :class="{ 'bg-primary': viewMode === 'full', 'bg-secondary': viewMode !== 'full' }"
          @click="updateViewMode('full')"
        >
          Полный
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import InputText from 'primevue/inputtext'
import MultiSelect from 'primevue/multiselect'
import Button from 'primevue/button'

const props = defineProps({
  globalFilterValue: {
    type: String,
    default: ''
  },
  selectedTags: {
    type: Array,
    default: () => []
  },
  availableTags: {
    type: Array,
    default: () => []
  },
  viewMode: {
    type: String,
    default: 'compact',
    validator: (value) => ['compact', 'full'].includes(value)
  }
})

const emit = defineEmits([
  'update:globalFilterValue', 
  'update:selectedTags', 
  'update:viewMode'
])

const localGlobalFilterValue = ref(props.globalFilterValue)
const localSelectedTags = ref(props.selectedTags)

const emitFilterChange = () => {
  emit('update:globalFilterValue', localGlobalFilterValue.value)
  emit('update:selectedTags', localSelectedTags.value)
}

const updateViewMode = (mode) => {
  emit('update:viewMode', mode)
}

watch(() => props.globalFilterValue, (newValue) => {
  localGlobalFilterValue.value = newValue
})

watch(() => props.selectedTags, (newValue) => {
  localSelectedTags.value = newValue
})
</script>

<style scoped>
.bg-primary {
  background-color: var(--primary-color);
  color: white;
}
.bg-secondary {
  background-color: var(--surface-100);
  color: var(--text-color);
}
</style>
