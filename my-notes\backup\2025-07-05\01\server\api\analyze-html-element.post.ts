// server/api/analyze-html-element.post.ts
import { defineEventHandler, readBody, createError } from 'h3';
import { load } from 'cheerio';
import { generateTreeStructure } from '../utils/htmlAnalyzer';
import { getElementTypes } from '../utils/elementTypeMapping';

export default defineEventHandler(async (event) => {
  try {
    const { html } = await readBody(event);

    if (!html || html.trim().length === 0) {
      return {
        success: false,
        error: 'HTML не предоставлен или пустой'
      };
    }

    console.log('🔍 Анализ HTML элемента...');

    // Настраиваем cheerio для сохранения оригинальных кавычек в атрибутах
    const $ = load(html, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    });

    // Генерируем структуру DOM дерева
    const treeStructure = generateTreeStructure(html);

    // Определяем типы элемента - ТОЛЬКО корневого элемента
    const elementTypes = [];

    // Проверяем, является ли HTML фрагментом или полным документом
    const isFragment = !html.includes('<html') && !html.includes('<!DOCTYPE')

    if (isFragment) {
      // Для HTML фрагментов анализируем первый элемент верхнего уровня
      const rootElements = $($.root().children())
      if (rootElements.length > 0) {
        const rootElement = rootElements.first()
        const types = getElementTypes(rootElement, $)
        elementTypes.push(...types)
      }
    } else {
      // Для полных HTML документов анализируем первый элемент в body
      const bodyElements = $('body > *')
      if (bodyElements.length > 0) {
        const rootElement = bodyElements.first()
        const types = getElementTypes(rootElement, $)
        elementTypes.push(...types)
      } else {
        // Если body не найден, анализируем первый корневой элемент
        const rootElement = $('*').first()
        if (rootElement.length > 0) {
          const types = getElementTypes(rootElement, $)
          elementTypes.push(...types)
        }
      }
    }

    // Удаляем дубликаты
    const uniqueElementTypes = [...new Set(elementTypes)];

    console.log(`✅ Анализ завершен. Найдено типов: ${uniqueElementTypes.length}`);

    return {
      success: true,
      elementTypes: uniqueElementTypes,
      treeStructure: treeStructure || '',
      htmlLength: html.length
    };

  } catch (error) {
    console.error('❌ Ошибка при анализе HTML элемента:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message
    });
  }
});
