import { defineEventHand<PERSON>, readBody, createError } from 'h3'
import { load } from 'cheerio'
import { decodeHtmlEntities } from '../utils/htmlToTemplate.js'

interface ExtractPageContentRequest {
  html: string
  fileInfo: {
    title: string
    number?: string
    tags?: string[]
  }
  splitOption?: number
}

export default defineEventHandler(async (event) => {
  const { html, fileInfo, splitOption = 1 } = await readBody<ExtractPageContentRequest>(event)

  console.log(`🔍 Извлечение контента страницы: ${fileInfo.title}`)
  console.log(`🔧 Используется вариант разделения: ${splitOption}`)

  if (!html || html.length < 10) {
    throw createError({
      statusCode: 400,
      statusMessage: 'HTML content is required and must not be empty'
    })
  }

  // Определяем префикс пути в зависимости от splitOption (аналогично split-html-files.js)
  let pathPrefix: string
  if (splitOption === 2) {
    pathPrefix = 'https://fm-demo.ru/html2/'
  } else if (splitOption === 3) {
    pathPrefix = 'https://fm-demo.ru/html2/'
  } else {
    pathPrefix = 'https://fm-demo.ru/html/'
  }

  console.log(`🔗 Используется префикс для CSS/JS: ${pathPrefix}`)

  // Сначала извлечем CSS и JavaScript код перед удалением тегов (аналогично split-html-files.js)
  let cssContent = ''
  let jsContent = ''

  try {
    // Создаем отдельный экземпляр cheerio для извлечения CSS и JS
    const $extract = load(html, {
      decodeEntities: false,
      xmlMode: false,
      _useHtmlParser2: true
    })

    console.log('🔍 Извлечение CSS из тегов link и style...')

    // Извлекаем CSS из тегов link и обрабатываем относительные пути
    $extract('link[rel="stylesheet"], link[rel="preconnect"]').each((i, el) => {
      const href = $extract(el).attr('href')

      if (href && !href.startsWith('http://') && !href.startsWith('https://') && !href.startsWith('//')) {
        $extract(el).attr('href', `${pathPrefix}${href}`)
        console.log(`🔗 Добавлен префикс к CSS пути: ${href} -> ${pathPrefix}${href}`)
      }

      cssContent += $extract.html(el) + '\n'
    })

    // Извлекаем CSS из тегов style
    $extract('style').each((i, el) => {
      cssContent += $extract.html(el) + '\n'
    })

    console.log(`✅ Извлечено ${cssContent.length} символов CSS`)

    console.log('🔍 Извлечение JavaScript из тегов script...')

    // Извлекаем JavaScript из тегов script и обрабатываем относительные пути
    $extract('script').each((i, el) => {
      const src = $extract(el).attr('src')

      if (src && !src.startsWith('http://') && !src.startsWith('https://') && !src.startsWith('//')) {
        $extract(el).attr('src', `${pathPrefix}${src}`)
        console.log(`🔗 Добавлен префикс к JS пути: ${src} -> ${pathPrefix}${src}`)
      }

      jsContent += $extract.html(el) + '\n'
    })

    console.log(`✅ Извлечено ${jsContent.length} символов JavaScript`)
  } catch (extractError) {
    console.error('❌ Ошибка при извлечении CSS и JavaScript:', extractError)
  }

  // Теперь извлекаем HTML контент
  try {
    // Настраиваем cheerio для сохранения оригинальных кавычек в атрибутах
    const $ = load(html, {
      decodeEntities: false, // Отключаем декодирование HTML-сущностей
      xmlMode: false,       // Не используем XML режим
      _useHtmlParser2: true // Используем htmlparser2 для лучшей совместимости
    })

    // Удаляем теги script и link
    console.log('🗑️ Удаление тегов script и link...')
    $('script').remove()
    $('link').remove()

    // Извлекаем HTML без head и script
    let bodyHtml = ''
    const bodyElement = $('body')
    if (bodyElement.length > 0) {
      bodyHtml = bodyElement.html() || ''
    } else {
      $('head').remove()
      bodyHtml = $.html()
    }

    // Декодируем HTML-сущности в атрибутах (аналогично split-html-files.js)
    const decodedBodyHtml = decodeHtmlEntities(bodyHtml)

    console.log(`✅ Извлечен HTML контент, длина: ${decodedBodyHtml.length} символов`)

    return {
      bodyHtml: decodedBodyHtml,
      cssContent: cssContent,
      jsContent: jsContent
    }

  } catch (error) {
    console.error('❌ Ошибка при извлечении HTML контента:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to extract page content'
    })
  }
})