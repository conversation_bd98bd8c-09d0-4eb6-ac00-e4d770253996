<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useDirectusItems } from '#imports';

const route = useRoute();
const router = useRouter();
const { getItems, createItems } = useDirectusItems();

const item = ref(null);
const loading = ref(true);
const error = ref(null);

onMounted(async () => {
  try {
    console.log('Запрос к записи с title:', route.params.name);
    const response = await getItems({
      collection: 'wcont',
      params: {
        filter: { name: { _eq: route.params.name } },
        fields: ['*']
      }
    });

    if (Array.isArray(response) && response.length > 0) {
      item.value = response[0];
    } else {
      console.warn('Запись не найдена:', route.params.name);
    }
  } catch (err) {
    console.error('Ошибка при загрузке записи:', err);
    error.value = 'Ошибка загрузки данных';
  } finally {
    loading.value = false;
  }
});

const createJsonRecord = async () => {
  if (!item.value) return;
  
  const jsonData = {
    title: item.value.title,
    subtitle: item.value.subtitle,
    excerpt: item.value.excerpt,
    image: item.value.image,
    linkText: item.value.linkText,
    text: item.value.text,
    items: item.value.items || []
  };

  try {
    const newRecord = await createItems({
      collection: 'wjson',
      items: [{ title: item.value.name, json: jsonData }]
    });
    console.log('Запись в wjson создана:', newRecord);    
  } catch (err) {
    console.error('Ошибка при создании записи:', err);
  }
};
</script>

<template>
  <div v-if="loading">Загрузка...</div>
  <div v-else-if="error">{{ error }}</div>
  <div v-else-if="item">
    <h1>{{ item.name }}</h1>
    <p>{{ item.title }}</p>
    <p>{{ item.subtitle }}</p>
    <p>{{ item.excerpt }}</p>
    <img :src="item.image" alt="Image" v-if="item.image" />
    <p v-text="item.text"></p>
    
    <button @click="createJsonRecord">Создать запись в wjson</button>
  </div>
</template>
