<template>
  <DataTable
    :value="filteredItems"
    :paginator="true"
    :rows="10"
    :rows-per-page-options="[5,10,20,50]"
    :loading="loading"
    data-key="id"
    striped-rows
    responsive-layout="scroll"
    class="p-datatable-sm"
  >
    <Column field="title" header="Название" :sortable="true" />
    <Column field="type" header="Тип" :sortable="true">
      <template #body="{ data }">
        <Tag :value="data.type" :severity="getTypeSeverity(data.type)" />
      </template>
    </Column>
    <Column header="Теги">
      <template #body="{ data }">
        <div class="flex gap-1 flex-wrap">
          <Tag v-for="tag in data.tags" :key="tag" :value="tag" class="mb-1" />
        </div>
      </template>
    </Column>
    <Column header="Действия" class="text-right">
      <template #body="{ data }">
        <div class="flex gap-2 justify-end">
          <Button icon="pi pi-pencil" class="p-button-rounded p-button-text" @click="$emit('edit', data)" />
          <Button icon="pi pi-trash" class="p-button-rounded p-button-text p-button-danger" @click="$emit('delete', data.id)" />
        </div>
      </template>
    </Column>
  </DataTable>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Tag from 'primevue/tag'
import Button from 'primevue/button'

interface TableItem {
  id?: string
  title: string
  type: string
  tags?: string[]
  content?: string
}

const emit = defineEmits<{
  (e: 'edit', value: TableItem): void
  (e: 'delete', id: string): void
}>()

const props = defineProps({
  filteredItems: {
    type: Array as PropType<TableItem[]>,
    required: true
  },
  loading: {
    type: Boolean,
    required: true
  }
})

const getTypeSeverity = (type: string): string => {
  switch(type) {
    case 'header': return 'primary'
    case 'content': return 'success'
    case 'footer': return 'warning'
    default: return 'info'
  }
}
