<template>
  <div class="flex h-screen">
    <!-- Основной контент -->
    <div
      class="flex-1 overflow-hidden flex flex-col"
      :class="{ 'pr-[30rem]': sidebarVisible }"
    >
      <div class="flex justify-between mb-1 p-1">
        <div class="flex gap-2">
          <Button
            v-tooltip.top="'Обновить данные'"
            icon="pi pi-refresh"
            class="p-button-rounded p-button-text p-button-sm"
            :disabled="loading"
            aria-label="Обновить"
            @click="loadData"
          />
          <span class="p-input-icon-left">
            <InputText
              v-model="globalFilterValue"
              placeholder="Поиск..."
              icon="pi pi-search"
              class="w-full"
              style="font-size: 12px"
              @input="onGlobalFilterChange"
            />
          </span>
          <MultiSelect
            v-model="selectedTags"
            :options="availableTags"
            placeholder="Фильтр по тегам"
            display="chip"
            class="w-64 text-xs"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
            @change="applyTagsFilter"
          />
          <MultiSelect
            v-model="selectedWpageTypes"
            :options="availableWpageTypes"
            placeholder="Фильтр по типу страницы"
            display="chip"
            class="w-64 text-xs"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
            @change="applyWpageTypesFilter"
          />
        </div>
        <div class="flex gap-2">
          <Button
            v-tooltip.bottom="'Массовое добавление'"
            icon="pi pi-plus-circle"
            class="text-xs p-button-success"
            @click="openBulkCreate"
          />
          <Button
            v-tooltip.bottom="'Массовое редактирование'"
            icon="pi pi-pencil"
            class="text-xs p-button-primary"
            :disabled="!selectedItems.length"
            @click="openBulkEdit"
          />
          <Button
            v-tooltip.bottom="'Создать'"
            icon="pi pi-plus"
            class="p-button-success text-xs"
            @click="openCreateDialog"
          />
          <Button
            v-tooltip.bottom="'Добавление по URL'"
            icon="pi pi-link"
            class="p-button-warning text-xs"
            @click="openUrlCreateDialog"
          />
          <Button
            v-tooltip.bottom="'Эскизы'"
            icon="pi pi-images"
            class="p-button-info text-xs"
            :disabled="!selectedItems.length"
            @click="bulkUploadSketches"
          />
          <Button
            v-tooltip.bottom="'Скрины'"
            icon="pi pi-camera"
            class="p-button-secondary text-xs"
            :disabled="!selectedItems.length"
            @click="bulkGenerateScreenshots"
          />
          <Dropdown
            v-model="selectedSplitOption"
            :options="splitOptions"
            option-label="label"
            option-value="value"
            placeholder="Вариант разделения"
            class="text-xs"
          />
          <Button
            v-tooltip.bottom="'Разделить на блоки'"
            icon="pi pi-box"
            class="text-xs"
            :disabled="!selectedItems.length || processingItems"
            @click="splitSelectedPages"
          />
          <Button
            v-tooltip.bottom="'Разделить на блоки + скрины'"
            icon="pi pi-camera"
            class="text-xs p-button-success"
            :disabled="!selectedItems.length || processingItems"
            @click="splitSelectedPagesWithScreenshots"
          />
          <Button
            v-tooltip.bottom="'Извлечь элементы'"
            icon="pi pi-objects-column"
            class="text-xs p-button-secondary"
            :disabled="!selectedItems.length || processingItems"
            @click="extractSelectedElements"
          />
          <Button
            v-tooltip.bottom="'Извлечь элементы + скрины'"
            icon="pi pi-images"
            class="text-xs p-button-warning"
            :disabled="!selectedItems.length || processingItems"
            @click="extractSelectedElementsWithScreenshots"
          />
          <Button
            v-tooltip.bottom="'Дублировать выбранные'"
            icon="pi pi-copy"
            class="p-button-primary text-xs"
            :disabled="!selectedItems.length"
            @click="bulkDuplicateItems"
          />
          <Button
            v-tooltip.bottom="'Удалить выбранные'"
            icon="pi pi-trash"
            class="p-button-danger text-xs"
            :disabled="!selectedItems.length"
            @click="bulkDeleteItems"
          />
          <ProgressBar
            v-if="loading || processingItems"
            mode="indeterminate"
            style="height: 6px"
            class="mt-2"
          />
        </div>
      </div>

      <!-- Область массовых форм -->
      <BulkFormContainer
        :visible="bulkFormVisible"
        :mode="bulkFormMode"
        collection="wpage"
        :field-config="wpagesBulkFieldConfig"
        :selected-items="selectedItems"
        @close="closeBulkForm"
        @saved="onBulkFormSaved"
      />

      <div class="flex-1 overflow-auto">
        <DataTable
          v-model:selection="selectedItems"
          v-model:sort-field="sortField"
          v-model:sort-order="sortOrder"
          v-model:expanded-rows="expandedRows"
          :value="filteredItems"
          selection-mode="multiple"
          scrollable
          scroll-height="calc(100vh - 70px)"
          :virtual-scroller-options="{ itemSize: 44 }"
          filter-display="menu"
          :global-filter-fields="globalFilterFields"
          :loading="loading"
          data-key="id"
          striped-rows
          responsive-layout="scroll"
          class="p-datatable-sm text-[13px]"
          style="
            --highlight-bg: var(--primary-50);
            padding: 1px;
            font-size: 11px;
          "
          @row-select="onRowSelect"
        >
          <Column :expander="true" style="width: 3rem" />
          <Column
            selection-mode="multiple"
            style="font-size: 9px; padding: 1px; width: 50px"
          />
          <Column
            field="number"
            header="№"
            :sortable="true"
            style="font-size: 9px; padding: 1px; width: 50px"
          >
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.number }}</span>
              </div>
            </template>
          </Column>

          <Column field="sketch" header="Эскиз" style="padding: 1px; width: 120px">
            <template #body="{ data }">
              <Image
                v-if="data.sketch"
                :src="`http://localhost:8055/assets/${data.sketch}`"
                alt="Эскиз"
                width="100"
                class="my"
                preview
                :pt="{
                  image: { class: 'my', style:'object-fit:contain' },
                }"
              />
              <span v-else>-</span>
            </template>
          </Column>

          <Column field="title" header="Название" :sortable="true" style="padding: 1px; width: 200px">
            <template #body="{ data }">
              <span>{{ data.title }}</span>
            </template>
          </Column>

          <Column
            field="description"
            header="Описание"
            style="padding: 1px; font-size: 9px; width: 250px"
          >
            <template #body="{ data }">
              <span class="description-cell">{{ data.description }}</span>
            </template>
          </Column>

          <Column
            field="html"
            header="HTML"
            style="padding: 1px; font-size: 9px; width: 80px"
          >
            <template #body="{ data }">
              <Button
                label="⿻ HTML"
                class="p-button-text p-button-sm"
                style="width: 70px; font-size: 10px; padding: 1px"
                @click="copyToClipboard(data.html)"
              />
            </template>
          </Column>

          <Column
            field="wpage_type"
            header="Тип страницы"
            :sortable="true"
            style="padding: 1px; width: 150px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="type in data.wpage_type"
                  :key="type"
                  :value="type"
                  style="padding: 0 3px; font-size: 11px"
                />
              </div>
            </template>
          </Column>

          <Column
            field="tags"
            header="Теги"
            :sortable="true"
            style="padding: 1px; width: 200px"
          >
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="tag in data.tags"
                  :key="tag"
                  :value="tag"
                  style="padding: 0 3px; font-size: 11px"
                  @click="addTagToFilter(tag)"
                />
              </div>
            </template>
          </Column>

          <Column header="Действия" :exportable="false" style="width: 180px">
            <template #body="{ data }">
              <div class="flex gap-1">
                <Button
                  icon="pi pi-pencil"
                  class="p-button-text p-button-sm"
                  @click="editItem(data)"
                />
                <Button
                  icon="pi pi-copy"
                  class="p-button-text p-button-sm"
                  @click="duplicateItem(data)"
                />
                <Button
                  icon="pi pi-trash"
                  class="p-button-text p-button-sm p-button-danger"
                  @click="confirmDelete(data)"
                />
              </div>
            </template>
          </Column>
          <template #expansion="slotProps">
            <WPageBlocksTable
              :blocks="slotProps.data.wblock_proto?.map(r => r.wblock_proto_id).filter(Boolean)"
              @edit="handleEditBlock"
              @duplicate="handleDuplicateBlock"
              @delete="handleDeleteBlock"
            />
          </template>
        </DataTable>

        <!-- Прогресс разделения на блоки -->
        <div v-if="processingItems && splitProcessingStatus" class="mt-4 p-4 bg-blue-50 rounded">
          <div class="text-sm font-semibold mb-2">Разделение на блоки</div>
          <ProgressBar :value="splitProcessingProgress" class="h-2 mb-2" />
          <div class="text-xs text-gray-600">{{ splitProcessingStatus }}</div>
        </div>
      </div>
    </div>

    <!-- Сайдбар редактирования -->
    <WcontSidebar
      v-model:visible="sidebarVisible"
      :collapsed="false"
      title="Редактирование страницы"
      @close="sidebarVisible = false"
      @toggle-collapse="() => {}"
    >
      <div class="p-fluid">
        <!-- Базовая информация -->
        <div class="space-y-2">
          <div class="flex gap-2">
            <div class="field w-1/4">
              <InputText
                id="number"
                v-model="editingItem.number"
                required
                class="w-full"
                placeholder="Номер страницы*"
                style="padding: 6px; font-size: 10px"
              />
            </div>

            <div class="field w-3/4">
              <InputText
                id="title"
                v-model="editingItem.title"
                required
                class="w-full"
                placeholder="Название*"
                style="padding: 6px; font-size: 11px"
              />
            </div>
          </div>

          <div class="field">
            <Textarea
              id="description"
              v-model="editingItem.description"
              rows="2"
              class="w-full text-xs [&>textarea]:text-xs"
              placeholder="Описание"
              style="padding: 4px; font-size: 10px"
            />
          </div>

          <div class="field">
              <InputText
                id="url"
                v-model="editingItem.url"                
                class="w-full"
                placeholder="url"
                style="padding: 4px; font-size: 10px"
              />
            
          </div>

          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.wpage_type"
              :options="availableWpageTypes"
              placeholder="Выберите типы страницы"
              display="chip"
              class="text-xs w-full p-0"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-2">
            <MultiSelect
              v-model="editingItem.tags"
              :options="availableTags"
              placeholder="Выберите теги"
              display="chip"
              class="text-xs w-full p-0"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
          </div>

          <div class="field mb-2">
            <div class="flex gap-2">
              <Image
                v-if="editingItem.sketch"
                :src="`http://localhost:8055/assets/${editingItem.sketch}`"
                alt="Эскиз"
                width="200"
                class="my"
                :pt="{
                  image: { class: 'my', style:'object-fit:contain' },
                }"
                preview
              />
              <FileUpload
                mode="basic"
                :auto="true"
                accept="image/*"
                :max-file-size="1000000"
                choose-label="Эскиз"
                class="p-button-sm"
                @select="onSketchSelect"
              />
            </div>
          </div>

          <div class="field mb-0">
            <TabView
              class="text-xs"
              :pt="{
              panelcontainer: { style: 'padding:0' },
            }"
            >
              <TabPanel
                header="HTML/CSS/JS"
                :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
                <div class="space-y-1">
                  <PrismEditorWithCopy
                    v-model="editingItem.html"
                    editor-class="my-editor text-xs"
                    :highlight="highlightHtml"
                    placeholder="Введите HTML код"
                    field-name="HTML"
                    max-height="120px"
                  />
                  <div class="p-0 grid grid-cols-2 gap-4">
                    <div class="flex flex-col h-full">
                    <PrismEditorWithCopy
                      v-model="editingItem.css"
                      editor-class="my-editor text-xs w-full"
                      :highlight="highlightCss"
                      placeholder="CSS код"
                      field-name="CSS"
                      max-height="60px !important"
                    />
                  </div>
                  <div class="flex flex-col h-full">
                    <PrismEditorWithCopy
                      v-model="editingItem.js"
                      editor-class="my-editor text-xs w-full"
                      :highlight="highlightJs"
                      placeholder="JS код"
                      field-name="JavaScript"
                      max-height="60px !important"
                    />
                  </div>
                  </div>
                </div>
              </TabPanel>
            </TabView>
          </div>

          <!-- Связанные записи -->
          <div class="field mb-2">
            <h5 class="text-xs font-semibold mb-2">Связанные блоки (wblock_proto)</h5>
            <DataTable
              v-if="relatedWblockProto.length"
              :value="relatedWblockProto"
              class="p-datatable-sm text-xs"
              style="font-size: 10px"
              sort-field="number"
              :sort-order="1"
            >
              <Column field="number" header="№" sortable style="width: 80px; font-size: 8.5px;" />
              <Column field="sketch" header="" style="width: 50px; padding: 2px">
                <template #body="{ data }">
                  <Image
                    v-if="data.sketch"
                    :src="`http://localhost:8055/assets/${data.sketch}`"
                    alt="Sketch"
                    width="40"
            class="my"
            preview
                  />
                </template>
              </Column>
              <Column field="title" header="Название" sortable />
              <Column field="block_type" header="Типы" style="width: 120px">
                <template #body="{ data }">
                  <div class="flex flex-wrap gap-1">
                    <Tag
                      v-for="type in data.block_type"
                      :key="type"
                      :value="type"
                      style="padding: 0 3px; font-size: 9px"
                    />
                  </div>
                </template>
              </Column>
            </DataTable>
            <span v-else class="text-xs text-gray-500">Нет связанных блоков</span>
          </div>

          <div class="field mb-2">
            <h5 class="text-xs font-semibold mb-2">Связанные файлы (files)</h5>
            <DataTable
              v-if="relatedFiles.length"
              :value="relatedFiles"
              class="p-datatable-sm text-xs"
              style="font-size: 10px"
            >
              <Column field="number" header="№" style="width: 60px" />
              <Column field="title" header="Название" />
            </DataTable>
            <span v-else class="text-xs text-gray-500">Нет связанных файлов</span>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-0">
          <Button
            label="Отмена"
            icon="pi pi-times"
            class="p-button-sm"
            @click="closeSidebar"
          />
          <Button
            label="Сохранить"
            icon="pi pi-check"
            class="p-button-sm"
            :loading="saving"
            @click="saveItem"
          />
        </div>
      </div>
    </WcontSidebar>

    <!-- Сайдбар для редактирования блока -->
    <WBlockProtoSidebarV2
       v-if="blockSidebarVisible"
       :visible="blockSidebarVisible"
       :editing-item="editingBlock"
       @update:visible="blockSidebarVisible = $event"
       @saved="handleBlockSaved"
     />

    <!-- Диалог подтверждения удаления -->
    <ConfirmDialog />

    <!-- Диалог результатов обработки -->
    <Dialog
  v-model:visible="resultsDialogVisible"
      header="Результаты обработки"
      :style="{ width: '50vw' }"
      :modal="true"
    >
      <div v-if="processingResults">
        <p class="mb-4">
          <strong>Обработано:</strong> {{ processingResults.results?.length || 0 }} страниц
        </p>
        <p class="mb-4">
          <strong>Создано записей:</strong> {{ createdItemsCount }}
        </p>

        <DataTable
          :value="processingResults.results"
          class="p-datatable-sm mt-4"
        >
          <Column field="fileName" header="Страница" style="width: 30%" />
          <Column header="Блоки" style="width: 15%">
            <template #body="{ data }">
              {{ data.blocksToCreate?.length || data.elementsToCreate?.length || 0 }}
            </template>
          </Column>
          <Column header="Обработано" style="width: 15%">
            <template #body="{ data }">
              {{ data.blocksAdded || data.elementsAdded || 0 }}
            </template>
          </Column>
          <Column header="Прогресс" style="width: 25%">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <ProgressBar
                  :value="getProgressPercentage(data)"
                  class="flex-1 h-2"
                  :class="{
                    'p-progressbar-success': data.status === 'completed',
                    'p-progressbar-danger': data.status === 'error'
                  }"
                />
                <span class="text-xs">{{ getProgressPercentage(data) }}%</span>
              </div>
            </template>
          </Column>
          <Column header="Статус" style="width: 15%">
            <template #body="{ data }">
              <Tag
                :value="getStatusText(data)"
                :severity="getStatusSeverity(data)"
                class="text-xs"
              />
            </template>
          </Column>
        </DataTable>
      </div>
    </Dialog>

    <!-- Диалог создания страниц по URL -->
    <Dialog
      v-model:visible="urlCreateDialogVisible"
      header="Создание страниц по URL"
      :style="{ width: '50vw' }"
      :modal="true"
    >
      <div class="space-y-4">
        <!-- Верхние поля -->
        <div class="grid grid-cols-4 gap-2">
          <div class="field">
            <label for="urlNumber" class="text-xs font-semibold">Номер</label>
            <InputText
              id="urlNumber"
              v-model="urlFormData.number"
              placeholder="Номер"
              class="w-full text-xs"
              style="font-size: 11px"
            />
          </div>
          <div class="field">
            <label for="urlPrefix" class="text-xs font-semibold">Префикс</label>
            <InputText
              id="urlPrefix"
              v-model="urlFormData.prefix"
              placeholder="Префикс"
              class="w-full text-xs"
              style="font-size: 11px"
            />
          </div>
          <div class="field">
            <label for="urlTags" class="text-xs font-semibold">Теги</label>
            <InputText
              id="urlTags"
              v-model="urlFormData.tags"
              placeholder="Теги (через запятую)"
              class="w-full text-xs"
              style="font-size: 11px"
            />
          </div>
          <div class="field">
            <label for="urlSplitOption" class="text-xs font-semibold">Разделение</label>
            <Dropdown
              id="urlSplitOption"
              v-model="urlFormData.splitOption"
              :options="splitOptions"
              option-label="label"
              option-value="value"
              placeholder="Вариант"
              class="w-full text-xs"
              style="font-size: 11px"
            />
          </div>
        </div>

        <!-- Поля URL с типами -->
        <div class="space-y-2">
          <div class="text-sm font-semibold">URL и типы страниц:</div>
          <div
            v-for="(url, index) in urlFormData.urls"
            :key="index"
            class="flex gap-2 items-center"
          >
            <InputText
              v-model="urlFormData.urls[index]"
              placeholder="https://example.com/page.html"
              class="flex-1 text-xs"
              style="font-size: 11px"
            />
            <MultiSelect
              v-model="urlFormData.urlSpecificTypes[index]"
              :options="availableWpageTypes"
              placeholder="Типы"
              display="chip"
              class="w-48 text-xs"
              panel-class="text-xs"
              style="font-size: 11px"
              :pt="{
                item: { class: 'text-xs' },
                header: { class: 'text-xs' },
              }"
            />
            <Button
              icon="pi pi-trash"
              class="p-button-danger p-button-sm"
              :disabled="urlFormData.urls.length === 1"
              @click="removeUrlField(index)"
            />
          </div>
          <Button
            label="Добавить URL"
            icon="pi pi-plus"
            class="p-button-outlined p-button-sm"
            @click="addUrlField"
          />
        </div>


        <!-- Индикация прогресса -->
        <div v-if="processingUrls" class="space-y-2">
          <ProgressBar :value="urlProcessingProgress" class="h-2" />
          <div class="text-sm text-gray-600">
            {{ urlProcessingStatus }}
          </div>
        </div>

        <!-- Кнопки действий -->
        <div class="flex justify-end gap-2">
          <Button
            label="Отмена"
            icon="pi pi-times"
            class="p-button-text"
            :disabled="processingUrls"
            @click="closeUrlCreateDialog"
          />
          <Button
            label="Сохранить"
            icon="pi pi-check"
            class="p-button-success"
            :loading="processingUrls"
            :disabled="!urlFormData.urls.length || !urlFormData.urls[0] || processingUrls"
            @click="createPagesFromUrls"
          />
        </div>
      </div>
    </Dialog>

    <!-- Уведомления -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
  import 'vue-prism-editor/dist/prismeditor.min.css'
  import Prism from 'prismjs'
  import 'prismjs/components/prism-clike'
  import 'prismjs/components/prism-markup'
  import 'prismjs/components/prism-css'
  import 'prismjs/components/prism-javascript'
  import 'prismjs/themes/prism-tomorrow.css'
  import { ref, computed, onMounted } from 'vue'
  import { useDirectusItems } from '#imports'
  import { useConfirm } from 'primevue/useconfirm'
  import { useToast } from 'primevue/usetoast'
  import Image from 'primevue/image'
  import Button from 'primevue/button'
  import InputText from 'primevue/inputtext'
  import Textarea from 'primevue/textarea'
  import Dropdown from 'primevue/dropdown'
  import MultiSelect from 'primevue/multiselect'
  import FileUpload from 'primevue/fileupload'
  import Dialog from 'primevue/dialog'
  import ProgressBar from 'primevue/progressbar'
  import TabView from 'primevue/tabview'
  import TabPanel from 'primevue/tabpanel'
  import BulkFormContainer from '~/components/BulkFormContainer.vue'
  import WBlockProtoSidebarV2 from '~/components/WBlockProtoSidebarV2.vue'




  interface WBlockProto {
    id?: string;
    number: string;
    status: string;
    title: string;
    description?: string;
    concept?: string[];
    block_type?: string[];
    layout?: string[];
    style?: string[];
    elements?: string[];
    element_count?: number;
    composition?: string;
    graphics?: string[];
    collection?: string[];
    features?: string[];
    notes?: string;
    sketch?: string;
    welem_proto?: string[];
    html?: string;
    css?: string;
    js?: string;
    hbs?: string;
    json?: string;
    sort?: number;
    date_created?: string;
    user_created?: string;
    date_updated?: string;
    user_updated?: string;
  }
  
  interface WPage {
    id?: string
    number: string
    title: string
    url: string
    description?: string
    wpage_type?: string[]
    tags?: string[]
    sketch?: string
    html?: string
    css?: string
    js?: string
    sort?: number
    date_created?: string
    user_created?: string
    date_updated?: string
    user_updated?: string
    wblock_proto?: {
      id: number;
      wpage_id: string;
      wblock_proto_id: WBlockProto;
    }[];
  }
  
  // API и утилиты
  const { getItems, createItems, updateItem, deleteItems } = useDirectusItems()
  const confirm = useConfirm()
  const toast = useToast()
  
  const expandedRows = ref([])

  // Состояние
  const items = ref<WPage[]>([])
  const loading = ref(false)
  const saving = ref(false)
  const processingItems = ref(false)
  const sidebarVisible = ref(false)
  
  const selectedItems = ref<WPage[]>([])
  const globalFilterValue = ref('')
  const selectedTags = ref<string[]>([])
  const selectedWpageTypes = ref<string[]>([])
  const resultsDialogVisible = ref(false)
  const processingResults = ref(null)
  const createdItemsCount = ref(0)

  // Состояние для массовых форм
  const bulkFormVisible = ref(false)
const blockSidebarVisible = ref(false)
  const bulkFormMode = ref<'create' | 'edit'>('create')

  // Состояние для диалога создания по URL
  const urlCreateDialogVisible = ref(false)
  const processingUrls = ref(false)
  const urlProcessingStatus = ref('')
  const urlProcessingProgress = ref(0)

  // Состояние для прогресса разделения на блоки
  const splitProcessingStatus = ref('')
  const splitProcessingProgress = ref(0)

  // Функции для диалога результатов
  const getProgressPercentage = (data: any) => {
    const total = data.blocksToCreate?.length || data.elementsToCreate?.length || 0
    const processed = data.blocksAdded || data.elementsAdded || 0
    if (total === 0) return 0
    return Math.round((processed / total) * 100)
  }

  const getStatusText = (data: any) => {
    if (data.status === 'completed') return 'Завершено'
    if (data.status === 'error') return 'Ошибка'
    if (data.status === 'processing') return 'Обработка'
    return 'Ожидание'
  }

  const getStatusSeverity = (data: any) => {
    if (data.status === 'completed') return 'success'
    if (data.status === 'error') return 'danger'
    if (data.status === 'processing') return 'info'
    return 'secondary'
  }
  const urlFormData = ref({
    number: '',
    prefix: '',
    tags: '',
    splitOption: 1,
    urls: [''],
    urlSpecificTypes: {} as Record<number, string[]>
  })

  // Опции для разделения на блоки (аналогично files2.vue)
  const selectedSplitOption = ref(1)
  const splitOptions = ref([
    { label: 'Вариант 1: header, section, footer', value: 1 },
    { label: 'Вариант 2: header, div.section, footer', value: 2 },
    { label: 'Вариант 3: header, section, div, footer', value: 3 }
  ])

  // Редактирование
  const editingItem = ref<WPage>({
    number: '',
    title: '',
    url: '',
    description: '',
    wpage_type: [],
    tags: [],
    sketch: '',
    html: '',
    css: '',
    js: '',
  })
const editingBlock = ref<WBlockProto | null>(null)

  // Связанные записи
  const relatedWblockProto = ref([])
  const relatedFiles = ref([])

  // Сортировка
  const sortField = ref('number')
  const sortOrder = ref(1)

  // Вычисляемые свойства
  const availableTags = computed(() => {
    const tags = new Set<string>()
    items.value.forEach((item) => {
      item.tags?.forEach((tag) => tags.add(tag))
    })
    return Array.from(tags)
  })

  const availableWpageTypes = computed(() => {
    const types = new Set<string>()
    items.value.forEach((item) => {
      item.wpage_type?.forEach((type) => types.add(type))
    })
    return Array.from(types)
  })

  const filteredItems = computed(() => {
    let result = [...items.value]

    // Фильтр по тегам
    if (selectedTags.value.length > 0) {
      result = result.filter((item) => {
        const itemTags = item.tags || []
        return selectedTags.value.some((tag) => itemTags.includes(tag))
      })
    }

    // Фильтр по типам страниц
    if (selectedWpageTypes.value.length > 0) {
      result = result.filter((item) => {
        const itemTypes = item.wpage_type || []
        return selectedWpageTypes.value.some((type) => itemTypes.includes(type))
      })
    }

    // Глобальный поиск
    if (globalFilterValue.value) {
      const searchValue = globalFilterValue.value.toLowerCase()
      result = result.filter(
        (item) =>
          (item.title?.toLowerCase() || '').includes(searchValue) ||
          (item.number?.toLowerCase() || '').includes(searchValue) ||
          (item.description?.toLowerCase() || '').includes(searchValue),
      )
    }

    return result
  })

  const globalFilterFields = ['title', 'number', 'description']

  // Конфигурация полей для массовых форм wpages
  const wpagesBulkFieldConfig = computed(() => [
    {
      name: 'wpage_type',
      type: 'multiselect' as const,
      placeholder: 'Типы страниц',
      options: availableWpageTypes.value,
      class: 'field-full'
    },
    {
      name: 'tags',
      type: 'multiselect' as const,
      placeholder: 'Теги',
      options: availableTags.value,
      class: 'field-full'
    },
    {
      name: 'html',
      type: 'prism' as const,
      placeholder: 'HTML код',
      class: 'field-full'
    },
    {
      name: 'css',
      type: 'prism' as const,
      placeholder: 'CSS код',
      class: 'field-full'
    },
    {
      name: 'js',
      type: 'prism' as const,
      placeholder: 'JavaScript код',
      class: 'field-full'
    },
    {
      name: 'sketch',
      type: 'file' as const,
      placeholder: 'Эскиз (изображение)',
      class: 'field-full'
    }
  ])

  // Подсветка синтаксиса
  const highlightHtml = (code: string) => {
    return Prism.highlight(code, Prism.languages.markup, 'html')
  }

  const highlightCss = (code: string) => {
    return Prism.highlight(code, Prism.languages.css, 'css')
  }

  const highlightJs = (code: string) => {
    return Prism.highlight(code, Prism.languages.javascript, 'javascript')
  }

  // Методы
  const onRowSelect = (event: { data: WPage }) => {
    // Удалено автоматическое открытие сайдбара при выборе строки
  }

  const loadData = async () => {
    loading.value = true
    try {
      const pages = await getItems<WPage>({
        collection: 'wpage',
        params: {
          limit: -1,
          sort: [sortField.value],
          fields: ['*', 'wblock_proto.wblock_proto_id.*'],
        },
      })

      console.log('Loaded pages:', pages)
      items.value = pages
    } catch (error) {
      console.error('Error loading data:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить данные страниц',
        life: 3000,
      })
    } finally {
      loading.value = false
    }
  }

  const handleEditBlock = async (block: WBlockProto) => {
    console.log('handleEditBlock called with block:', block); // Новый лог
    try {
      const [blockData] = await getItems<WBlockProto>({
        collection: 'wblock_proto',
        params: {
          filter: { id: { _eq: block.id } },
          fields: ['*', 'welem_proto.welem_proto_id'], // Загружаем все поля и связанные welem_proto
          limit: 1,
        },
      });

      if (blockData) {
        const welemProtoIds = blockData.welem_proto?.map(r => r.welem_proto_id.id);
        editingBlock.value = {
          ...blockData,
          welem_proto: welemProtoIds
        };
        console.log('editingBlock.value before sidebar open:', editingBlock.value); // Новый лог
        blockSidebarVisible.value = true;
        console.log('blockSidebarVisible.value set to true:', blockSidebarVisible.value); // Новый лог
      } else {
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Блок не найден',
          life: 3000,
        });
      }
    } catch (error) {
      console.error('Error loading block data for editing:', error);
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить данные блока для редактирования',
        life: 3000,
      });
    }
  };

  const handleDuplicateBlock = async (block: WBlockProto) => {
    const { id, ...duplicateData } = block
    const duplicate = {
      ...duplicateData,
      number: `${block.number}-copy`,
      title: `${block.title} (копия)`,
    }

    try {
      await createItems({
        collection: 'wblock_proto',
        items: [duplicate],
      })
      await loadData() // Refresh data
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Блок скопирован',
        life: 3000,
      })
    } catch (error: any) {
      console.error('Error duplicating block:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось скопировать блок: ' + error.message,
        life: 5000,
      })
    }
  }

  const handleDeleteBlock = (block: WBlockProto) => {
    confirm.require({
      message: `Вы уверены, что хотите удалить блок "${block.title}"?`,
      header: 'Подтверждение удаления',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          await deleteItems({
            collection: 'wblock_proto',
            items: [block.id!],
          })
          await loadData() // Refresh data
          toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: 'Блок удален',
            life: 3000,
          })
        } catch (error: any) {
          console.error('Error deleting block:', error)
          toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось удалить блок: ' + error.message,
            life: 5000,
          })
        }
      },
    })
  }

  const handleBlockSaved = () => {
    blockSidebarVisible.value = false
    editingBlock.value = null
    loadData()
  }

  const saveItem = async () => {
    saving.value = true
    try {
      const { id, ...saveData } = editingItem.value

      // Очищаем пустые поля и подготавливаем данные
      const cleanedData = {
        ...saveData,
        // Убираем пустые массивы и null значения
        wpage_type: saveData.wpage_type?.length ? saveData.wpage_type : null,
        tags: saveData.tags?.length ? saveData.tags : null,
        sketch: saveData.sketch || null,
        html: saveData.html || '',
        css: saveData.css || '',
        js: saveData.js || '',
        description: saveData.description || null,
        url: saveData.url || null
      }

      if (id) {
        // Обновление существующей страницы
        await updateItem({
          collection: 'wpage',
          id,
          item: cleanedData,
        })
      } else {
        // Создание новой страницы
        const result = await createItems({
          collection: 'wpage',
          items: [cleanedData],
        })
        console.log('Created page result:', result)
      }

      await loadData()
      closeSidebar()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: id ? 'Страница обновлена' : 'Страница создана',
        life: 3000,
      })
    } catch (error) {
      console.error('Save error:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось сохранить страницу',
        life: 5000,
      })
    } finally {
      saving.value = false
    }
  }

  const editItem = async (item: WPage) => {
    try {
      const [pageData] = await getItems({
        collection: 'wpage',
        params: {
          filter: { id: { _eq: item.id } },
          fields: ['*'],
          limit: 1,
        },
      })

      // Загружаем связанные записи
      await loadRelatedRecords(item.id!)

      editingItem.value = { ...pageData }
      sidebarVisible.value = true
    } catch (error) {
      console.error('Error loading page:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось загрузить данные страницы',
        life: 3000,
      })
    }
  }

  const loadRelatedRecords = async (pageId: string) => {
    try {
      // Загружаем связанные wblock_proto
      const wblockRelations = await getItems({
        collection: 'wpage_wblock_proto',
        params: {
          filter: { wpage_id: { _eq: pageId } },
          fields: ['wblock_proto_id.id', 'wblock_proto_id.number', 'wblock_proto_id.title', 'wblock_proto_id.sketch', 'wblock_proto_id.block_type'],
        },
      })
      relatedWblockProto.value = wblockRelations.map(r => r.wblock_proto_id)

      // Загружаем связанные files
      const filesRelations = await getItems({
        collection: 'wpage_directus_files',
        params: {
          filter: { wpage_id: { _eq: pageId } },
          fields: ['directus_files_id.id', 'directus_files_id.number', 'directus_files_id.title', 'directus_files_id.filename_download'],
        },
      })
      relatedFiles.value = filesRelations.map(r => ({
        id: r.directus_files_id.id,
        number: r.directus_files_id.number,
        title: r.directus_files_id.title || r.directus_files_id.filename_download
      }))
    } catch (error) {
      console.error('Error loading related records:', error)
      relatedWblockProto.value = []
      relatedFiles.value = []
    }
  }

  const duplicateItem = async (item: WPage) => {
    const duplicate = { ...item }
    delete duplicate.id
    duplicate.number = `${duplicate.number}-copy`
    duplicate.title = `${duplicate.title} (копия)`

    try {
      await createItems({
        collection: 'wpage',
        items: [duplicate],
      })
      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Страница скопирована',
      })
    } catch (error) {
      console.error('Error duplicating item:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось скопировать страницу',
      })
    }
  }

  const confirmDelete = (item: WPage) => {
    confirm.require({
      message: 'Вы уверены, что хотите удалить эту страницу?',
      header: 'Подтверждение удаления',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteItem(item),
    })
  }

  const deleteItem = async (item: WPage) => {
    try {
      await deleteItems({
        collection: 'wpage',
        items: [item.id!],
      })
      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Страница удалена',
      })
    } catch (error) {
      console.error('Error deleting item:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось удалить страницу',
      })
    }
  }

  const openCreateDialog = () => {
    editingItem.value = {
      number: '',
      title: '',
      url: '',
      description: '',
      wpage_type: [],
      tags: [],
      sketch: '',
      html: '',
      css: '',
      js: '',
    }
    relatedWblockProto.value = []
    relatedFiles.value = []
    sidebarVisible.value = true
  }

  const closeSidebar = () => {
    sidebarVisible.value = false
    editingItem.value = {
      number: '',
      title: '',
      url: '',
      description: '',
      wpage_type: [],
      tags: [],
      sketch: '',
      html: '',
      css: '',
      js: '',
    }
    relatedWblockProto.value = []
    relatedFiles.value = []
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.add({
      severity: 'info',
      summary: 'Скопировано',
      detail: 'Текст скопирован в буфер обмена',
      life: 3000,
    })
  }

  const addTagToFilter = (tag: string) => {
    if (!selectedTags.value.includes(tag)) {
      selectedTags.value.push(tag)
    }
  }

  const onGlobalFilterChange = () => {
    // Обработка изменения глобального фильтра
  }

  const applyTagsFilter = () => {
    // Обработка изменения фильтра по тегам
  }

  const applyWpageTypesFilter = () => {
    // Обработка изменения фильтра по типам страниц
  }

  const onSketchSelect = async (event: any) => {
    const file = event.files[0]
    if (file) {
      try {
        const formData = new FormData()
        formData.append('file', file)

        const response = await fetch('http://localhost:8055/files', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          throw new Error('Ошибка загрузки файла')
        }

        const data = await response.json()
        editingItem.value.sketch = data.data.id
      } catch (error) {
        console.error('Ошибка при загрузке файла:', error)
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Не удалось загрузить файл',
        })
      }
    }
  }

  // Массовое дублирование выбранных элементов
  const bulkDuplicateItems = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true

      // Получаем полные данные выбранных записей
      const selectedIds = selectedItems.value.map(item => item.id)
      const fullRecords = await getItems({
        collection: 'wpage',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: ['*'],
        },
      })

      // Подготавливаем данные для дублирования
      const duplicates = fullRecords.map(item => {
        const duplicate = { ...item }
        delete duplicate.id
        duplicate.number = `${duplicate.number}-1c`
        duplicate.title = `${duplicate.title}-1c`
        return duplicate
      })

      // Создаем дубликаты
      await createItems({
        collection: 'wpage',
        items: duplicates,
      })

      await loadData()
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Дублировано ${duplicates.length} страниц`,
      })
    } catch (error) {
      console.error('Error duplicating items:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось дублировать страницы',
      })
    } finally {
      loading.value = false
    }
  }

  // Массовое удаление выбранных элементов
  const bulkDeleteItems = () => {
    if (!selectedItems.value.length) return

    confirm.require({
      message: `Вы уверены, что хотите удалить ${selectedItems.value.length} выбранных страниц?`,
      header: 'Подтверждение удаления',
      icon: 'pi pi-exclamation-triangle',
      accept: async () => {
        try {
          loading.value = true
          const selectedIds = selectedItems.value.map(item => item.id)

          await deleteItems({
            collection: 'wpage',
            items: selectedIds,
          })

          await loadData()
          selectedItems.value = []

          toast.add({
            severity: 'success',
            summary: 'Успешно',
            detail: `Удалено ${selectedIds.length} страниц`,
          })
        } catch (error) {
          console.error('Error deleting items:', error)
          toast.add({
            severity: 'error',
            summary: 'Ошибка',
            detail: 'Не удалось удалить страницы',
          })
        } finally {
          loading.value = false
        }
      }
    })
  }

  // Массовая загрузка эскизов
  const bulkUploadSketches = async () => {
    if (!selectedItems.value.length) return

    // Создаем невидимый input для выбора файлов
    const fileInput = document.createElement('input')
    fileInput.type = 'file'
    fileInput.multiple = true
    fileInput.accept = 'image/*'

    fileInput.onchange = async (event) => {
      const files = event.target.files
      if (!files || files.length === 0) return

      try {
        loading.value = true

        // Получаем ID выбранных элементов
        const selectedIds = selectedItems.value.map(item => item.id)

        // Загружаем файлы и обновляем записи
        let successCount = 0
        let errorCount = 0

        for (let i = 0; i < Math.min(files.length, selectedIds.length); i++) {
          const file = files[i]
          const itemId = selectedIds[i]

          try {
            // Загружаем файл в Directus
            const formData = new FormData()
            formData.append('file', file)

            const response = await fetch('http://localhost:8055/files', {
              method: 'POST',
              body: formData,
            })

            if (!response.ok) {
              throw new Error('Ошибка загрузки файла')
            }

            const data = await response.json()

            // Обновляем запись с новым эскизом
            await updateItem({
              collection: 'wpage',
              id: itemId,
              item: {
                sketch: data.data.id
              }
            })

            successCount++
          } catch (error) {
            console.error(`Ошибка загрузки эскиза для записи ${itemId}:`, error)
            errorCount++
          }
        }

        await loadData()
        selectedItems.value = []

        toast.add({
          severity: successCount > 0 ? 'success' : 'error',
          summary: 'Результат загрузки',
          detail: `Успешно: ${successCount}, Ошибок: ${errorCount}`,
          life: 5000,
        })
      } catch (error) {
        console.error('Ошибка массовой загрузки эскизов:', error)
        toast.add({
          severity: 'error',
          summary: 'Ошибка',
          detail: 'Не удалось загрузить эскизы',
        })
      } finally {
        loading.value = false
      }
    }

    fileInput.click()
  }



  // Функция для разделения страниц на блоки (адаптированная из files2.vue)
  const splitSelectedPages = async () => {
    if (!selectedItems.value.length || processingItems.value) return

    try {
      processingItems.value = true
      loading.value = true
      createdItemsCount.value = 0
      splitProcessingProgress.value = 0
      splitProcessingStatus.value = 'Подготовка к разделению...'

      const pageIds = selectedItems.value.map(page => page.id)
      splitProcessingStatus.value = `Разделение ${pageIds.length} страниц на блоки...`

      // Вызываем API для разделения страниц на блоки
      const response = await fetch('/api/split-html-files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileIds: pageIds,
          splitOption: selectedSplitOption.value,
          sourceCollection: 'wpage', // Указываем что работаем с коллекцией wpage
          includeSourceIds: true // Просим включить ID источников в ответ
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`,
        )
      }

      // Обрабатываем результат и сохраняем блоки
      const apiResult = await response.json()

      // Показываем диалог с результатами СРАЗУ для отображения прогресса
      // Инициализируем статусы для всех страниц
      apiResult.results.forEach(result => {
        result.status = 'waiting'
        result.blocksAdded = 0
      })
      processingResults.value = apiResult
      resultsDialogVisible.value = true

      const processedResult = await processAndSaveBlocks(apiResult)

      // Обновляем результаты после обработки
      processingResults.value = processedResult

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Разделено ${pageIds.length} страниц на ${createdItemsCount.value} блоков`,
        life: 5000,
      })

      // Очищаем выбор после обработки
      selectedItems.value = []

      // Завершаем прогресс
      splitProcessingProgress.value = 100
      splitProcessingStatus.value = 'Разделение завершено'
    } catch (error) {
      console.error('Ошибка при разделении страниц:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось разделить страницы на блоки: ' + error.message,
        life: 5000,
      })
    } finally {
      processingItems.value = false
      loading.value = false
      splitProcessingProgress.value = 0
      splitProcessingStatus.value = ''
    }
  }

  // Функция для разделения страниц на блоки с созданием скриншотов
  const splitSelectedPagesWithScreenshots = async () => {
    if (!selectedItems.value.length || processingItems.value) return

    try {
      processingItems.value = true
      loading.value = true
      createdItemsCount.value = 0

      const pageIds = selectedItems.value.map(page => page.id)

      // Вызываем API для разделения страниц на блоки с скриншотами
      const response = await fetch('/api/split-html-files', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileIds: pageIds,
          splitOption: selectedSplitOption.value,
          withScreenshots: true,
          sourceCollection: 'wpage'
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`,
        )
      }

      // Обрабатываем результат и сохраняем блоки с скриншотами
      const apiResult = await response.json()

      // Показываем диалог с результатами СРАЗУ для отображения прогресса
      // Инициализируем статусы для всех страниц
      apiResult.results.forEach(result => {
        result.status = 'waiting'
        result.blocksAdded = 0
      })
      processingResults.value = apiResult
      resultsDialogVisible.value = true

      const processedResult = await processAndSaveBlocksWithScreenshots(apiResult)

      // Обновляем результаты после обработки
      processingResults.value = processedResult

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Разделено ${pageIds.length} страниц на ${createdItemsCount.value} блоков со скриншотами`,
        life: 5000,
      })

      // Очищаем выбор после обработки
      selectedItems.value = []
    } catch (error) {
      console.error('Ошибка при разделении страниц со скриншотами:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось разделить страницы на блоки со скриншотами: ' + error.message,
        life: 5000,
      })
    } finally {
      processingItems.value = false
      loading.value = false
    }
  }

  // Функция для извлечения элементов из выбранных страниц
  const extractSelectedElements = async () => {
    if (!selectedItems.value.length || processingItems.value) return

    try {
      processingItems.value = true
      loading.value = true
      createdItemsCount.value = 0

      const pageIds = selectedItems.value.map(page => page.id)

      // Вызываем API для извлечения элементов
      const response = await fetch('/api/extract-html-elements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileIds: pageIds,
          splitOption: selectedSplitOption.value,
          sourceCollection: 'wpage'
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(
          errorData.error || `HTTP error! status: ${response.status}`,
        )
      }

      // Обрабатываем результат и сохраняем элементы
      const apiResult = await response.json()
      const processedResult = await processAndSaveElements(apiResult)

      // Сохраняем результаты для отображения
      processingResults.value = processedResult

      // Показываем диалог с результатами
      resultsDialogVisible.value = true

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Извлечено ${createdItemsCount.value} элементов из ${pageIds.length} страниц`,
        life: 5000,
      })

      // Очищаем выбор после обработки
      selectedItems.value = []
    } catch (error) {
      console.error('Ошибка при извлечении элементов:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось извлечь элементы: ' + error.message,
        life: 5000,
      })
    } finally {
      processingItems.value = false
      loading.value = false
    }
  }

  // Функция для извлечения элементов из выбранных страниц с генерацией скриншотов
  const extractSelectedElementsWithScreenshots = async () => {
    if (!selectedItems.value.length || processingItems.value) return

    try {
      processingItems.value = true
      loading.value = true
      createdItemsCount.value = 0

      const pageIds = selectedItems.value.map(page => page.id)

      // Вызываем API для извлечения элементов с данными для скриншотов
      const response = await $fetch('/api/extract-html-elements-with-screenshots', {
        method: 'POST',
        body: {
          fileIds: pageIds,
          splitOption: selectedSplitOption.value,
          sourceCollection: 'wpage'
        }
      })

      // Обрабатываем результат и сохраняем элементы с скриншотами
      const processedResult = await processAndSaveElementsWithScreenshots(response)

      // Сохраняем результаты для отображения
      processingResults.value = processedResult

      // Показываем диалог с результатами
      resultsDialogVisible.value = true

      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: `Извлечено ${createdItemsCount.value} элементов со скриншотами из ${pageIds.length} страниц`,
        life: 5000,
      })

      // Очищаем выбор после обработки
      selectedItems.value = []
    } catch (error) {
      console.error('Ошибка при извлечении элементов со скриншотами:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось извлечь элементы со скриншотами: ' + error.message,
        life: 5000,
      })
    } finally {
      processingItems.value = false
      loading.value = false
    }
  }

  // Функция для обработки результатов API и сохранения блоков в Directus (адаптированная из files2.vue)
  async function processAndSaveBlocks(apiResponse) {
    // Обнуляем счетчик созданных блоков
    createdItemsCount.value = 0

    // Для каждой страницы в результатах
    for (let i = 0; i < apiResponse.results.length; i++) {
      const pageResult = apiResponse.results[i]

      // Обновляем статус страницы на "processing"
      pageResult.status = 'processing'
      pageResult.blocksAdded = 0

      // Принудительно обновляем реактивность
      processingResults.value = {
        ...processingResults.value,
        results: [...processingResults.value.results]
      }

      // Обновляем прогресс
      splitProcessingProgress.value = Math.round((i / apiResponse.results.length) * 100)
      splitProcessingStatus.value = `Обработка страницы ${i + 1} из ${apiResponse.results.length}: ${pageResult.fileName}`

      // Устанавливаем статус обработки для этой страницы
      pageResult.status = 'processing'
      if (
        !pageResult.success ||
        !pageResult.blocksToCreate ||
        pageResult.blocksToCreate.length === 0
      ) {
        continue
      }

      try {
        console.log(
          `Сохранение ${pageResult.blocksToCreate.length} блоков в Directus для страницы ${pageResult.fileName}...`,
        )

        // Адаптируем блоки для wpage - HTML не требует декодирования, CSS и JS берем напрямую
        const adaptedBlocks = pageResult.blocksToCreate.map(block => ({
          ...block,
          html: block.html, // HTML уже готов к использованию
          css: block.css || '', // CSS напрямую из поля
          js: block.js || '' // JS напрямую из поля
        }))

        // Используем useDirectusItems для сохранения блоков в Directus
        const result = await createItems({
          collection: 'wblock_proto',
          items: adaptedBlocks,
        })

        const createdBlocks = Array.isArray(result) ? result : [result]
        const createdCount = createdBlocks.length
        createdItemsCount.value += createdCount

        console.log(`Успешно сохранено ${createdCount} блоков`)

        // Обновляем прогресс после создания блоков
        pageResult.blocksAdded = createdCount
        pageResult.status = 'processing'

        // Принудительно обновляем реактивность
        processingResults.value = {
          ...processingResults.value,
          results: [...processingResults.value.results]
        }

        // ВАЖНО: Создаем связи между страницей и созданными блоками
        if (createdBlocks.length > 0) {
          // Получаем ID страницы из имени файла или из pageResult
          const pageId = pageResult.sourcePageId || pageResult.fileId || pageResult.fileName

          if (pageId) {
            console.log(`Создание связей между страницей ${pageId} и ${createdBlocks.length} блоками...`)

            const relations = createdBlocks.map(block => ({
              wpage_id: pageId,
              wblock_proto_id: block.id
            }))

            await createItems({
              collection: 'wpage_wblock_proto',
              items: relations,
            })

            console.log(`Успешно создано ${relations.length} связей`)
          } else {
            console.warn('Не удалось определить ID страницы для создания связей')
          }
        }

        // Обновляем информацию в результатах
        pageResult.blocksAdded = createdCount
        pageResult.status = 'completed'
        pageResult.progress = 100
        pageResult.message = `Успешно создано ${createdCount} блоков и связей`

        // Принудительно обновляем реактивность
        processingResults.value = {
          ...processingResults.value,
          results: [...processingResults.value.results]
        }
      } catch (error) {
        console.error(
          `Ошибка при сохранении блоков в Directus для страницы ${pageResult.fileName}:`,
          error,
        )
        pageResult.status = 'error'
        pageResult.message = `Ошибка при сохранении блоков: ${error.message}`
      }
    }

    return apiResponse
  }

  // Функция для обработки результатов API и сохранения блоков со скриншотами в Directus
  async function processAndSaveBlocksWithScreenshots(apiResponse) {
    // Обнуляем счетчик созданных блоков
    createdItemsCount.value = 0

    // Для каждой страницы в результатах
    for (const pageResult of apiResponse.results) {
      // Обновляем статус страницы на "processing"
      pageResult.status = 'processing'
      pageResult.blocksAdded = 0

      // Принудительно обновляем реактивность
      processingResults.value = {
        ...processingResults.value,
        results: [...processingResults.value.results]
      }
      if (
        !pageResult.success ||
        !pageResult.blocksToCreate ||
        pageResult.blocksToCreate.length === 0
      ) {
        continue
      }

      try {
        console.log(
          `Сохранение ${pageResult.blocksToCreate.length} блоков со скриншотами в Directus для страницы ${pageResult.fileName}...`,
        )

        // Адаптируем блоки для wpage
        const adaptedBlocks = pageResult.blocksToCreate.map(block => ({
          ...block,
          html: block.html,
          css: block.css || '',
          js: block.js || ''
        }))

        // Сначала создаем блоки в Directus
        const result = await createItems({
          collection: 'wblock_proto',
          items: adaptedBlocks,
        })

        const createdBlocks = Array.isArray(result) ? result : [result]
        const createdCount = createdBlocks.length
        createdItemsCount.value += createdCount

        console.log(`Успешно сохранено ${createdCount} блоков`)

        // Обновляем прогресс после создания блоков
        pageResult.blocksAdded = createdCount
        pageResult.status = 'processing'

        // Принудительно обновляем реактивность
        processingResults.value = {
          ...processingResults.value,
          results: [...processingResults.value.results]
        }

        // Создаем скриншоты для блоков
        console.log(`📸 Создание скриншотов для ${createdCount} блоков...`)

        try {
          // Подготавливаем данные для создания скриншотов в правильном формате
          const screenshotsData = createdBlocks.map((block, index) => {
            // Создаем полный HTML документ для скриншота
            const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${block.title || `Block ${index + 1}`}</title>
  
    ${block.css || ''}
  
</head>
<body>
  ${block.html || ''}
  
    ${block.js || ''}
  
</body>
</html>`

            return {
              html: fullHtml,
              filename: `${block.title || `Block-${index + 1}`}`.replace(/[^a-zA-Z0-9-_]/g, '_')
            }
          })

          const screenshotResponse = await $fetch('/api/capture-batch-screenshots', {
            method: 'POST',
            body: {
              screenshots: screenshotsData
            }
          })

          console.log(`✅ Скриншоты созданы: ${screenshotResponse.successCount}/${createdCount} успешно`)

          // Обновляем блоки с ID скриншотов
          for (let i = 0; i < createdBlocks.length; i++) {
            const block = createdBlocks[i]
            const screenshotResult = screenshotResponse.results[i]

            if (screenshotResult && screenshotResult.success && screenshotResult.fileId) {
              try {
                await updateItem({
                  collection: 'wblock_proto',
                  id: block.id,
                  item: {
                    sketch: screenshotResult.fileId
                  }
                })

                console.log(`✅ Скриншот привязан к блоку ${block.number || i + 1}`)
              } catch (updateError) {
                console.error(`❌ Ошибка обновления блока ${block.number || i + 1} со скриншотом:`, updateError)
              }
            } else {
              console.error(`❌ Скриншот не создан для блока ${block.number || i + 1}`)
            }
          }

          console.log(`✅ Обработка скриншотов завершена: ${screenshotResponse.successCount}/${createdBlocks.length} успешно`)

          // Обновляем прогресс после создания скриншотов
          pageResult.status = 'processing'

          // Принудительно обновляем реактивность
          processingResults.value = {
            ...processingResults.value,
            results: [...processingResults.value.results]
          }
        } catch (screenshotError) {
          console.error('❌ Ошибка создания скриншотов:', screenshotError)
        }

        // ВАЖНО: Создаем связи между страницей и созданными блоками
        if (createdBlocks.length > 0) {
          // Получаем ID страницы из имени файла или из pageResult
          const pageId = pageResult.sourcePageId || pageResult.fileId || pageResult.fileName

          if (pageId) {
            console.log(`Создание связей между страницей ${pageId} и ${createdBlocks.length} блоками...`)

            const relations = createdBlocks.map(block => ({
              wpage_id: pageId,
              wblock_proto_id: block.id
            }))

            await createItems({
              collection: 'wpage_wblock_proto',
              items: relations,
            })

            console.log(`Успешно создано ${relations.length} связей`)
          } else {
            console.warn('Не удалось определить ID страницы для создания связей')
          }
        }

        // Обновляем информацию в результатах
        pageResult.blocksAdded = createdCount
        pageResult.status = 'completed'
        pageResult.progress = 100
        pageResult.message = `Успешно создано ${createdCount} блоков, связей и скриншотов`

        // Принудительно обновляем реактивность
        processingResults.value = {
          ...processingResults.value,
          results: [...processingResults.value.results]
        }
      } catch (error) {
        console.error(
          `Ошибка при сохранении блоков со скриншотами в Directus для страницы ${pageResult.fileName}:`,
          error,
        )
        pageResult.message = `Ошибка при сохранении блоков со скриншотами: ${error.message}`
      }
    }

    return apiResponse
  }

  // Функция для обработки и сохранения элементов
  async function processAndSaveElements(apiResponse) {
    // Обнуляем счетчик созданных элементов
    createdItemsCount.value = 0

    // Для каждой страницы в результатах
    for (const pageResult of apiResponse.results) {
      if (
        !pageResult.success ||
        !pageResult.elementsToCreate ||
        pageResult.elementsToCreate.length === 0
      ) {
        continue
      }

      try {
        console.log(
          `Сохранение ${pageResult.elementsToCreate.length} элементов в Directus для страницы ${pageResult.fileName}...`,
        )

        // Адаптируем элементы для wpage
        const adaptedElements = pageResult.elementsToCreate.map(element => ({
          ...element,
          html: element.html,
          css: element.css || '',
          js: element.js || ''
        }))

        // Используем useDirectusItems для сохранения элементов в Directus
        const result = await createItems({
          collection: 'welem_proto',
          items: adaptedElements,
        })

        const createdCount = Array.isArray(result)
          ? result.length
          : result
            ? 1
            : 0
        createdItemsCount.value += createdCount

        console.log(`Успешно сохранено ${createdCount} элементов`)

        // Обновляем информацию в результатах
        pageResult.elementsAdded = createdCount
      } catch (error) {
        console.error(
          `Ошибка при сохранении элементов в Directus для страницы ${pageResult.fileName}:`,
          error,
        )
        pageResult.message = `Ошибка при сохранении элементов: ${error.message}`
      }
    }

    return apiResponse
  }

  // Функция для обработки и сохранения элементов с скриншотами
  async function processAndSaveElementsWithScreenshots(apiResponse) {
    // Обнуляем счетчик созданных элементов
    createdItemsCount.value = 0
    let totalScreenshots = 0

    // ЭТАП 1: Создаем все элементы без скриншотов
    const allElementsForScreenshots = []
    const elementToCreatedElementMap = new Map()

    for (const pageResult of apiResponse.results) {
      if (
        !pageResult.success ||
        !pageResult.elementsToCreate ||
        pageResult.elementsToCreate.length === 0
      ) {
        continue
      }

      try {
        console.log(
          `Сохранение ${pageResult.elementsToCreate.length} элементов в Directus для страницы ${pageResult.fileName}...`,
        )

        // Создаем элементы без скриншотов сначала
        const elementsToCreateWithoutScreenshots = pageResult.elementsToCreate.map(element => {
          const { _screenshotData, ...elementData } = element
          return {
            ...elementData,
            html: elementData.html,
            css: elementData.css || '',
            js: elementData.js || ''
          }
        })

        // Разбиваем на батчи по 50 элементов для избежания ошибки "request entity too large"
        const BATCH_SIZE = 50
        const createdElements = []

        for (let i = 0; i < elementsToCreateWithoutScreenshots.length; i += BATCH_SIZE) {
          const batch = elementsToCreateWithoutScreenshots.slice(i, i + BATCH_SIZE)
          console.log(`Создание батча ${Math.floor(i / BATCH_SIZE) + 1}: ${batch.length} элементов`)

          try {
            const batchResult = await createItems({
              collection: 'welem_proto',
              items: batch,
            })

            const batchElements = Array.isArray(batchResult) ? batchResult : [batchResult]
            createdElements.push(...batchElements)

            console.log(`✅ Батч ${Math.floor(i / BATCH_SIZE) + 1} создан: ${batchElements.length} элементов`)
          } catch (batchError) {
            console.error(`❌ Ошибка создания батча ${Math.floor(i / BATCH_SIZE) + 1}:`, batchError)
            throw batchError
          }
        }

        const createdCount = createdElements.length
        createdItemsCount.value += createdCount

        console.log(`Успешно сохранено ${createdCount} элементов в ${Math.ceil(elementsToCreateWithoutScreenshots.length / BATCH_SIZE)} батчах`)

        // Собираем данные для batch создания скриншотов
        for (let i = 0; i < createdElements.length; i++) {
          const createdElement = createdElements[i]
          const originalElementData = pageResult.elementsToCreate[i]

          if (originalElementData._screenshotData) {
            const elementForScreenshot = {
              html: originalElementData._screenshotData.fullHtmlDocument,
              selector: originalElementData._screenshotData.selector,
              elementTitle: originalElementData._screenshotData.elementTitle,
              elementNumber: originalElementData._screenshotData.elementNumber,
              elementHtml: originalElementData.html
            }

            allElementsForScreenshots.push(elementForScreenshot)
            elementToCreatedElementMap.set(elementForScreenshot, createdElement)
          }
        }

        // Обновляем информацию в результатах
        pageResult.elementsAdded = createdCount
      } catch (error) {
        console.error(
          `Ошибка при сохранении элементов в Directus для страницы ${pageResult.fileName}:`,
          error,
        )
        pageResult.message = `Ошибка при сохранении элементов: ${error.message}`
      }
    }

    // ЭТАП 2: Создаем все скриншоты в batch режиме
    if (allElementsForScreenshots.length > 0) {
      try {
        console.log(`📸 Создание ${allElementsForScreenshots.length} скриншотов элементов в batch режиме...`)

        const batchResponse = await $fetch('/api/capture-batch-element-screenshots', {
          method: 'POST',
          body: {
            elements: allElementsForScreenshots,
            width: 1400,
            height: 800
          }
        })

        console.log(`✅ Batch скриншоты созданы: ${batchResponse.successCount}/${allElementsForScreenshots.length} успешно`)

        // ЭТАП 3: Обновляем элементы с ID скриншотов
        for (let i = 0; i < batchResponse.results.length; i++) {
          const screenshotResult = batchResponse.results[i]
          const elementForScreenshot = allElementsForScreenshots[i]
          const createdElement = elementToCreatedElementMap.get(elementForScreenshot)

          if (screenshotResult.success && screenshotResult.fileId && createdElement) {
            try {
              await updateItem({
                collection: 'welem_proto',
                id: createdElement.id,
                item: {
                  sketch: screenshotResult.fileId
                },
              })

              totalScreenshots++
              console.log(`✅ Скриншот привязан к элементу ${screenshotResult.elementTitle}`)
            } catch (updateError) {
              console.error(`❌ Ошибка обновления элемента ${screenshotResult.elementTitle}:`, updateError)
            }
          } else if (!screenshotResult.success) {
            console.warn(`⚠️ Скриншот не создан для элемента ${screenshotResult.elementTitle}: ${screenshotResult.error}`)
          }
        }

      } catch (batchError) {
        console.error('❌ Ошибка batch создания скриншотов:', batchError)
      }
    }

    console.log(`✅ Обработка завершена: создано ${createdItemsCount.value} элементов, ${totalScreenshots} скриншотов`)

    return apiResponse
  }

  // Функции для массовых форм
  function openBulkCreate() {
    bulkFormMode.value = 'create'
    bulkFormVisible.value = true
  }

  function openBulkEdit() {
    if (selectedItems.value.length === 0) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'Выберите страницы для редактирования',
        life: 3000
      })
      return
    }
    bulkFormMode.value = 'edit'
    bulkFormVisible.value = true
  }

  function closeBulkForm() {
    bulkFormVisible.value = false
  }

  function onBulkFormSaved(savedItems: any[]) {
    // Обновляем список страниц
    loadData()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Обработано ${savedItems.length} страниц`,
      life: 3000
    })
  }

  // Функции для диалога создания по URL
  const openUrlCreateDialog = () => {
    urlFormData.value = {
      number: '',
      prefix: '',
      tags: '',
      splitOption: 1,
      urls: [''],
      urlSpecificTypes: { 0: [] }
    }
    urlProcessingProgress.value = 0
    urlProcessingStatus.value = ''
    urlCreateDialogVisible.value = true
  }

  const closeUrlCreateDialog = () => {
    urlCreateDialogVisible.value = false
    urlFormData.value = {
      number: '',
      prefix: '',
      tags: '',
      splitOption: 1,
      urls: [''],
      urlSpecificTypes: { 0: [] }
    }
    urlProcessingProgress.value = 0
    urlProcessingStatus.value = ''
  }

  const addUrlField = () => {
    const newIndex = urlFormData.value.urls.length
    urlFormData.value.urls.push('')
    urlFormData.value.urlSpecificTypes[newIndex] = []
  }

  const removeUrlField = (index: number) => {
    if (urlFormData.value.urls.length > 1) {
      urlFormData.value.urls.splice(index, 1)
      delete urlFormData.value.urlSpecificTypes[index]
      // Переиндексируем urlSpecificTypes
      const newTypes: Record<number, string[]> = {}
      Object.keys(urlFormData.value.urlSpecificTypes).forEach((key, i) => {
        const numKey = parseInt(key)
        if (numKey > index) {
          newTypes[numKey - 1] = urlFormData.value.urlSpecificTypes[numKey]
        } else if (numKey < index) {
          newTypes[numKey] = urlFormData.value.urlSpecificTypes[numKey]
        }
      })
      urlFormData.value.urlSpecificTypes = newTypes
    }
  }

  // Функция создания страниц из URL (исправленная архитектура)
  const createPagesFromUrls = async () => {
    if (!urlFormData.value.urls.length || !urlFormData.value.urls[0]) return

    try {
      processingUrls.value = true
      urlProcessingProgress.value = 0
      urlProcessingStatus.value = 'Подготовка...'

      // Фильтруем пустые URL
      const validUrls = urlFormData.value.urls.filter(url => url.trim())

      if (validUrls.length === 0) {
        toast.add({
          severity: 'warn',
          summary: 'Предупреждение',
          detail: 'Введите хотя бы один URL',
          life: 3000,
        })
        return
      }

      let successCount = 0
      let errorCount = 0

      // Обрабатываем каждый URL
      for (let i = 0; i < validUrls.length; i++) {
        const url = validUrls[i].trim()

        try {
          // Обновляем прогресс
          urlProcessingProgress.value = Math.round((i / validUrls.length) * 100)
          urlProcessingStatus.value = `Обработка ${i + 1} из ${validUrls.length}: ${url}`

          console.log(`🔄 Обработка URL ${i + 1}/${validUrls.length}: ${url}`)

          // Генерируем номер с инкрементом
          const incrementNumber = String(i + 1).padStart(2, '0')
          const pageNumber = urlFormData.value.number ? `${urlFormData.value.number}-${incrementNumber}` : incrementNumber

          // Извлекаем title из URL
          const urlObj = new URL(url)
          let pageTitle = urlObj.pathname.split('/').pop() || 'page'

          // Убираем расширение .html
          pageTitle = pageTitle.replace(/\.html?$/i, '')

          // Заменяем дефисы на пробелы и применяем capitalize
          pageTitle = pageTitle.replace(/-/g, ' ')
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ')

          // Добавляем префикс если указан
          if (urlFormData.value.prefix) {
            pageTitle = `${urlFormData.value.prefix} ${pageTitle}`
          }

          // Парсим теги
          const tags = urlFormData.value.tags ?
            urlFormData.value.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : []

          // Определяем типы страниц для этого URL
          const wpageTypes = urlFormData.value.urlSpecificTypes[i] || []

          // 1. Извлекаем контент через API
          urlProcessingStatus.value = `Извлечение контента: ${pageTitle}`
          console.log(`📥 Извлечение контента из ${url}...`)
          const extractResponse = await $fetch('/api/extract-page-from-url', {
            method: 'POST',
            body: {
              url: url,
              splitOption: urlFormData.value.splitOption
            }
          })

          if (!extractResponse.success || !extractResponse.data) {
            throw new Error(extractResponse.error || 'Не удалось извлечь контент')
          }

          const { bodyHtml, cssContent, jsContent, fullHtml, bodyTag } = extractResponse.data

          // 2. Создаем скриншот напрямую с URL (не с HTML)
          urlProcessingStatus.value = `Создание скриншота: ${pageTitle}`
          console.log(`📸 Создание скриншота с URL ${url}...`)
          let screenshotId = null

          try {
            const screenshotResponse = await $fetch('/api/capture-url-screenshot', {
              method: 'POST',
              body: {
                url: url,
                filename: `page_${pageNumber}_${pageTitle.replace(/[^a-zA-Z0-9\-_]/g, '_')}`,
                width: 1400,
                height: 800
              }
            })
            screenshotId = screenshotResponse.fileId
            console.log(`✅ Скриншот создан: ${screenshotId}`)
          } catch (screenshotError) {
            console.error('⚠️ Ошибка создания скриншота:', screenshotError)
            // Продолжаем без скриншота
          }

          // 3. Сохраняем страницу через composables
          urlProcessingStatus.value = `Сохранение страницы: ${pageTitle}`
          console.log(`💾 Сохранение страницы ${pageTitle}...`)
          const pageData = {
            number: pageNumber,
            title: pageTitle,
            description: bodyTag, // Сохраняем тег body в description
            tags: tags.length > 0 ? tags : null,
            wpage_type: wpageTypes.length > 0 ? wpageTypes : null,
            html: bodyHtml,
            css: cssContent,
            js: jsContent,
            sketch: screenshotId,
            url: url
          }

          await createItems({
            collection: 'wpage',
            items: [pageData]
          })

          console.log(`✅ Страница "${pageTitle}" сохранена успешно`)
          successCount++

        } catch (error) {
          console.error(`❌ Ошибка при обработке URL ${url}:`, error)
          errorCount++
        }
      }

      // Завершение
      urlProcessingProgress.value = 100
      urlProcessingStatus.value = 'Завершение...'

      // Обновляем данные
      await loadData()
      closeUrlCreateDialog()

      // Показываем результат
      toast.add({
        severity: successCount > 0 ? 'success' : 'error',
        summary: 'Создание страниц завершено',
        detail: `Успешно: ${successCount}, Ошибок: ${errorCount}`,
        life: 5000,
      })

    } catch (error) {
      console.error('❌ Общая ошибка создания страниц из URL:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось создать страницы из URL',
        life: 5000,
      })
    } finally {
      processingUrls.value = false
      urlProcessingProgress.value = 0
      urlProcessingStatus.value = ''
    }
  }

  // Массовая генерация скриншотов для страниц
  const bulkGenerateScreenshots = async () => {
    if (!selectedItems.value.length) return

    try {
      loading.value = true

      // Получаем полные данные выбранных записей
      const selectedIds = selectedItems.value.map(item => item.id)

      const fullRecords = await getItems({
        collection: 'wpage',
        params: {
          filter: { id: { _in: selectedIds } },
          fields: ['id', 'number', 'title', 'html', 'css', 'js'],
        },
      })

      // Фильтруем записи с HTML
      const recordsWithHtml = fullRecords.filter(item => item.html?.trim())

      if (recordsWithHtml.length === 0) {
        toast.add({
          severity: 'warn',
          summary: 'Предупреждение',
          detail: 'У выбранных записей отсутствует HTML для генерации скриншотов',
          life: 3000,
        })
        return
      }

      let successCount = 0
      let errorCount = 0

      // Генерируем скриншоты для каждой записи
      for (const record of recordsWithHtml) {
        try {
          const pageTitle = record.title || 'Untitled Page'
          const pageNumber = record.number || 'page'

          // Создаем полный HTML для скриншота
          const pageFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${pageTitle}</title>
  ${record.css || ''}
</head>
<body>
  ${record.html}
  ${record.js || ''}
</body>
</html>`

          // Создаем скриншот используя оптимизированный API для страниц
          const response = await $fetch('/api/capture-html-screenshot', {
            method: 'POST',
            body: {
              html: pageFullHtml,
              filename: `page_${pageNumber}_${pageTitle.replace(/[^a-zA-Z0-9\-_]/g, '_')}`,
              width: 1400,
              height: 800
            }
          })

          // Обновляем запись с новым sketch ID
          await updateItem({
            collection: 'wpage',
            id: record.id,
            item: {
              sketch: response.fileId
            },
          })

          successCount++
        } catch (error) {
          console.error(`Ошибка при генерации скриншота для записи ${record.id}:`, error)
          errorCount++
        }
      }

      // Обновляем данные в таблице
      await loadData()
      selectedItems.value = []

      // Показываем уведомление о результате
      toast.add({
        severity: 'success',
        summary: 'Генерация скриншотов завершена',
        detail: `Успешно: ${successCount}, Ошибок: ${errorCount}`,
        life: 3000,
      })
    } catch (error) {
      console.error('Ошибка массовой генерации скриншотов:', error)
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: error.message || 'Не удалось выполнить генерацию скриншотов',
        life: 5000,
      })
    } finally {
      loading.value = false
    }
  }

  // Инициализация
  onMounted(async () => {
    await loadData()
  })
</script>
<style scoped>
  .my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 9px;
    line-height: 1.4;
    padding: 2px;
  }

  /* optional class for removing the outline */
  .prism-editor__textarea:focus {
    outline: none;
  }
  
  .description-cell, .composition-cell {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 100px;
  }
  
  .my {
    max-height: 120px;
  }
  
  img.my  {
    object-fit: contain !important;
  }
  .p-tabview-panels {

    padding: 0 !important;
}

  .my2 {
    max-height: 40px;
  }
  
  .my2 img  {
    object-fit: contain !important;
    width:auto;
    height:45px;
  }
</style>
