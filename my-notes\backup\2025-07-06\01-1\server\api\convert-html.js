import { defineEventHandler, readBody, createError } from 'h3';
import html2pug from 'html2pug';
import { htmlToHandlebarsAndJson, decodeHtmlEntities } from '../utils/htmlToTemplate';

export default defineEventHandler(async (event) => {
  try {
    const { html, format } = await readBody(event);
    
    if (!html) {
      throw createError({
        statusCode: 400,
        statusMessage: 'HTML код не найден в теле запроса.'
      });
    }
    
    // Результат конвертации
    let template = '';
    let data = {};
    
    // Конвертируем в зависимости от выбранного формата
    if (format === 'pug') {
      try {
        // Конвертация HTML в Pug
        const pugOptions = {
          fragment: true,      // Обрабатываем как фрагмент HTML
          doctype: 'html',    // Тип документа
          commas: false,      // Без запятых
          doubleQuotes: true, // Используем двойные кавычки
          tabs: false,        // Используем пробелы вместо табуляции
          spaces: 2           // Количество пробелов для отступа
        };
        
        template = html2pug(html, pugOptions);
        
        // Для Pug пока не реализуем извлечение данных
        data = { message: 'Извлечение данных для Pug пока не реализовано' };
      } catch (pugError) {
        console.error('Ошибка при конвертации HTML в Pug:', pugError);
        throw createError({
          statusCode: 500,
          statusMessage: `Ошибка при конвертации в Pug: ${pugError.message}`
        });
      }
    } else {
      // По умолчанию конвертируем в Handlebars
      try {
        // Используем существующую функцию для конвертации в Handlebars
        const result = htmlToHandlebarsAndJson(html, 'template', '01');
        
        if (result.success) {
          template = result.hbsTemplate;
          data = result.jsonData;
        } else {
          throw new Error(result.error || 'Неизвестная ошибка при конвертации в Handlebars');
        }
      } catch (hbsError) {
        console.error('Ошибка при конвертации HTML в Handlebars:', hbsError);
        throw createError({
          statusCode: 500,
          statusMessage: `Ошибка при конвертации в Handlebars: ${hbsError.message}`
        });
      }
    }
    
    // Возвращаем результат
    return {
      success: true,
      template,
      data
    };
    
  } catch (error) {
    console.error('Ошибка при конвертации HTML:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || error.message
    });
  }
});

