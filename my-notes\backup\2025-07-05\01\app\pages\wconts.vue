<template>
  <div class="flex h-screen">
    <!-- Основной контент -->
    <div class="flex-1 overflow-hidden flex flex-col" :class="{ 'pr-[30rem]': sidebarVisible }">
      <div class="flex justify-between mb-4 p-4">
        <div class="flex gap-2">
          <span class="p-input-icon-left">
            <i class="pi pi-search" />
            <InputText v-model="globalFilterValue" placeholder="Поиск..." @input="onGlobalFilterChange" />
          </span>
          <MultiSelect 
            v-model="selectedTags" 
            :options="availableTags" 
            placeholder="Фильтр по тегам" 
            display="chip" 
            class="w-64"
            @change="applyTagsFilter"
          />
        </div>
        <div class="flex gap-2">
          <Button 
            :icon="viewMode === 'compact' ? 'pi pi-list' : 'pi pi-table'" 
            :label="viewMode === 'compact' ? 'Полный вид' : 'Краткий вид'" 
            class="p-button-outlined" 
            @click="toggleViewMode"
          />
        </div>
      </div>
      
      <div class="flex-1 overflow-auto">
        <DataTable 
          v-model:selection="selectedItem" 
          v-model:expanded-rows="expandedRows"
          :value="filteredItems" 
          selection-mode="single" 
          :paginator="true" 
          :rows="10"
          :rows-per-page-options="[5, 10, 20, 50]" 
          :sort-field="sortField"
          :sort-order="sortOrder"
          filter-display="menu"
          :global-filter-fields="globalFilterFields"
          :loading="loading"
          data-key="id"
          striped-rows
          responsive-layout="scroll"
          class="p-datatable-sm text-[13px]"
          :expanded-rows="expandedRows"
          @row-select="onRowSelect"
          @row-unselect="onRowUnselect"
          @row-expand="onRowExpand"
          @row-collapse="onRowCollapse"
        >
          <template #empty>
            <div class="text-center p-4">Нет данных для отображения</div>
          </template>
          
          <template #loading>
            <div class="text-center p-4">Загрузка данных...</div>
          </template>
          
          <template #expansion="slotProps">
            <div class="p-4 bg-surface-50">
              <DataTable :value="slotProps.data.items" class="p-datatable-sm text-[13px]">
                <Column field="title" header="Title">
                  <template #body="{ data }">
                    <div class="truncate-text cursor-pointer" @click="showFullTextFromCell(data.title, $event)">
                      {{ truncateText(data.title) }}
                      <Button
                        v-if="data.title?.length > 255" 
                        label="👁️" 
                        class="p-button-text p-button-sm" 
                        @click.stop="showFullText(data.title)" 
                      />
                    </div>
                  </template>
                </Column>
                
                <Column field="excerpt" header="Excerpt">
                  <template #body="{ data }">
                    <div class="truncate-text cursor-pointer" @click="showFullTextFromCell(data.excerpt, $event)">
                      {{ truncateText(data.excerpt) }}
                      <Button
                        v-if="data.excerpt?.length > 255" 
                        label="👁️" 
                        class="p-button-text p-button-sm" 
                        @click.stop="showFullText(data.excerpt)" 
                      />
                    </div>
                  </template>
                </Column>
                
                <Column field="text" header="Text">
                  <template #body="{ data }">
                    <div class="truncate-text cursor-pointer" @click="showFullTextFromCell(data.text, $event)">
                      {{ truncateText(data.text) }}
                      <Button
                        v-if="data.text?.length > 255" 
                        label="👁️" 
                        class="p-button-text p-button-sm" 
                        @click.stop="showFullText(data.text)" 
                      />
                    </div>
                  </template>
                </Column>
                
                <Column field="url" header="URL" />
                <Column field="linkText" header="Link Text" />
                <Column field="image" header="Image">
                  <template #body="{ data }">
                    <img v-if="data.image" :src="data.image" class="w-12 h-12 object-cover rounded" >
                    <span v-else>-</span>
                  </template>
                </Column>
              </DataTable>
            </div>
          </template>
          
          <!-- Всегда отображаемые колонки -->
          <Column :expander="true" header-style="width: 3rem" />
          
          <Column field="art" header="Art" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.art }}</span>
                <Button label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.art)" />
              </div>
            </template>
          </Column>
          
          <Column field="name" header="Name" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <NuxtLink :to="`/wcont/${encodeURIComponent(data.name)}`" class="text-primary hover:underline">
                  {{ truncateText(data.name) }}
                </NuxtLink>
                <Button label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.name)" />
                <Button
                  v-if="data.name?.length > 255" 
                  label="👁️" 
                  class="p-button-text p-button-sm" 
                  @click="showFullText(data.name)" 
                />
              </div>
            </template>
          </Column>
          
          <Column field="description" header="Description" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.description || '-' }}</span>
                <Button v-if="data.description" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.description)" />
              </div>
            </template>
          </Column>
          
          <Column field="tags" header="Tags" :sortable="false">
            <template #body="{ data }">
              <div class="flex flex-wrap gap-1">
                <Tag
                  v-for="tag in data.tags" :key="tag" :value="tag" 
                  class="mr-1 cursor-pointer" @click="addTagToFilter(tag)" />
                <Button v-if="data.tags?.length" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.tags?.join(', '))" />
              </div>
            </template>
          </Column>
          
          <!-- Дополнительные колонки, отображаемые только в полном режиме -->
          <Column v-if="viewMode === 'full'" field="title" header="Title" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.title || '-' }}</span>
                <Button v-if="data.title" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.title)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="subtitle" header="Subtitle" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.subtitle || '-' }}</span>
                <Button v-if="data.subtitle" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.subtitle)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="excerpt" header="Excerpt" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.excerpt || '-' }}</span>
                <Button v-if="data.excerpt" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.excerpt)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="text" header="Text" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span class="truncate max-w-xs">{{ data.text ? (data.text.length > 100 ? data.text.substring(0, 100) + '...' : data.text) : '-' }}</span>
                <Button v-if="data.text" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.text)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="url" header="URL" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.url || '-' }}</span>
                <Button v-if="data.url" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.url)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="linkText" header="Link Text" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.linkText || '-' }}</span>
                <Button v-if="data.linkText" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.linkText)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="number" header="Number" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.number || '-' }}</span>
                <Button v-if="data.number" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.number)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="inscription" header="Inscription" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.inscription || '-' }}</span>
                <Button v-if="data.inscription" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.inscription)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="icon" header="Icon" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.icon || '-' }}</span>
                <Button v-if="data.icon" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.icon)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="product" header="Product" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.product || '-' }}</span>
                <Button v-if="data.product" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.product)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="category" header="Category" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.category || '-' }}</span>
                <Button v-if="data.category" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.category)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="additional" header="Additional" :sortable="true">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <span>{{ data.additional || '-' }}</span>
                <Button v-if="data.additional" label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.additional)" />
              </div>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="items" header="Items" :sortable="false">
            <template #body="{ data }">
              <Button 
                v-if="data.items && data.items.length" 
                
                label="Показать" 
                class="p-button-sm p-button-outlined" 
                @click="showItemsDialog(data.items)"
              />
              <span v-else>-</span>
            </template>
          </Column>
          
          <!-- Колонки с изображениями -->
          <Column v-if="viewMode === 'full'" field="image" header="Image" :sortable="false">
            <template #body="{ data }">
              <div v-if="data.image" class="flex items-center gap-2">
                <img :src="data.image" class="w-12 h-12 object-cover rounded" >
                <Button label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.image)" />
              </div>
              <span v-else>-</span>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="imageBackground" header="Bg Image" :sortable="false">
            <template #body="{ data }">
              <div v-if="data.imageBackground" class="flex items-center gap-2">
                <img :src="data.imageBackground" class="w-12 h-12 object-cover rounded" >
                <Button label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.imageBackground)" />
              </div>
              <span v-else>-</span>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="imageLogo" header="Logo" :sortable="false">
            <template #body="{ data }">
              <div v-if="data.imageLogo" class="flex items-center gap-2">
                <img :src="data.imageLogo" class="w-12 h-12 object-cover rounded" >
                <Button label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.imageLogo)" />
              </div>
              <span v-else>-</span>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="imageIcon" header="Icon Image" :sortable="false">
            <template #body="{ data }">
              <div v-if="data.imageIcon" class="flex items-center gap-2">
                <img :src="data.imageIcon" class="w-12 h-12 object-cover rounded" >
                <Button label="⿻" class="p-button-text p-button-sm" @click="copyToClipboard(data.imageIcon)" />
              </div>
              <span v-else>-</span>
            </template>
          </Column>
          
          <Column v-if="viewMode === 'full'" field="imageGallery" header="Gallery" :sortable="false">
            <template #body="{ data }">
              <Button 
                v-if="data.imageGallery && data.imageGallery.length" 
                 
                label="Галерея" 
                class="p-button-sm p-button-outlined" 
                @click="showGalleryDialog(data.imageGallery)"
              />
              <span v-else>-</span>
            </template>
          </Column>
          
          <!-- Колонка с действиями -->
          <Column field="actions" header="Действия" :exportable="false" style="min-width: 8rem">
            <template #body="{ data }">
              <div class="flex gap-1">
                <Button label="✏️" class="p-button-rounded p-button-warning p-button-sm" @click="openEditSidebar(data)" />
                <Button label="📋" class="p-button-rounded p-button-info p-button-sm" @click="duplicateItem(data)" />
                <Button label="📤" class="p-button-rounded p-button-success p-button-sm" @click="convertToWJson(data)" />
              </div>
            </template>
          </Column>
        </DataTable>
      </div>
    </div>
    
    <!-- Сайдбар для редактирования -->
    <div 
      v-if="sidebarVisible" 
      class="fixed right-0 top-0 h-screen bg-white border-l border-gray-200 shadow-lg overflow-hidden flex flex-col transition-all duration-300"
      :class="{ 
        'w-[30rem]': !sidebarCollapsed, 
        'w-16': sidebarCollapsed 
      }"
    >
      <div class="flex justify-between items-center p-4 border-b">
        <h2 v-if="!sidebarCollapsed" class="text-xl font-semibold">Редактирование записи</h2>
        <div class="flex gap-2">
          <Button 
            :label="sidebarCollapsed ? '◀️' : '▶️'" 
            class="p-button-text" 
            @click="toggleSidebar" 
          />
          <Button label="❌" class="p-button-text" @click="closeSidebar" />
        </div>
      </div>
      
      <div v-if="editingItem && !sidebarCollapsed" class="flex-1 overflow-y-auto p-4 text-[13px]">
        <div class="grid grid-cols-1 gap-4">
          <div class="field">
            <label for="art" class="block mb-1">Art</label>
            <InputText id="art" v-model="editingItem.art" class="w-full" />
          </div>
          
          <div class="field">
            <label for="name" class="block mb-1">Name</label>
            <InputText id="name" v-model="editingItem.name" class="w-full" />
          </div>
          
          <div class="field">
            <label for="description" class="block mb-1">Description</label>
            <InputText id="description" v-model="editingItem.description" class="w-full" />
          </div>
          
          <div class="field">
            <label for="tags" class="block mb-1">Tags</label>
            <InputChips v-model="editingItem.tags" class="w-full" />
          </div>
          
          <div class="field">
            <label for="title" class="block mb-1">Title</label>
            <InputText id="title" v-model="editingItem.title" class="w-full" />
          </div>
          
          <div class="field">
            <label for="subtitle" class="block mb-1">Subtitle</label>
            <InputText id="subtitle" v-model="editingItem.subtitle" class="w-full" />
          </div>
          
          <div class="field">
            <label for="excerpt" class="block mb-1">Excerpt</label>
            <Textarea id="excerpt" v-model="editingItem.excerpt" rows="3" class="w-full" />
          </div>
          
          <div class="field">
            <label for="text" class="block mb-1">Text</label>
            <Textarea id="text" v-model="editingItem.text" rows="5" class="w-full" />
          </div>
          
          <div class="field">
            <label for="url" class="block mb-1">URL</label>
            <InputText id="url" v-model="editingItem.url" class="w-full" />
          </div>
          
          <div class="field">
            <label for="linkText" class="block mb-1">Link Text</label>
            <InputText id="linkText" v-model="editingItem.linkText" class="w-full" />
          </div>
          
          <div class="field">
            <label for="number" class="block mb-1">Number</label>
            <InputNumber id="number" v-model="editingItem.number" class="w-full" />
          </div>
          
          <div class="field">
            <label for="inscription" class="block mb-1">Inscription</label>
            <InputText id="inscription" v-model="editingItem.inscription" class="w-full" />
          </div>
          
          <div class="field">
            <label for="icon" class="block mb-1">Icon</label>
            <InputText id="icon" v-model="editingItem.icon" class="w-full" />
          </div>
          
          <div class="field">
            <label for="product" class="block mb-1">Product</label>
            <InputText id="product" v-model="editingItem.product" class="w-full" />
          </div>
          
          <div class="field">
            <label for="category" class="block mb-1">Category</label>
            <InputText id="category" v-model="editingItem.category" class="w-full" />
          </div>
          
          <div class="field">
            <label for="additional" class="block mb-1">Additional</label>
            <InputText id="additional" v-model="editingItem.additional" class="w-full" />
          </div>
          
          <div class="field">
            <label for="image" class="block mb-1">Image URL</label>
            <InputText id="image" v-model="editingItem.image" class="w-full" />
            <img v-if="editingItem.image" :src="editingItem.image" class="mt-2 w-full h-32 object-cover rounded" >
          </div>
          
          <div class="field">
            <label for="imageBackground" class="block mb-1">Background Image URL</label>
            <InputText id="imageBackground" v-model="editingItem.imageBackground" class="w-full" />
            <img v-if="editingItem.imageBackground" :src="editingItem.imageBackground" class="mt-2 w-full h-32 object-cover rounded" >
          </div>
          
          <div class="field">
            <label for="imageLogo" class="block mb-1">Logo Image URL</label>
            <InputText id="imageLogo" v-model="editingItem.imageLogo" class="w-full" />
            <img v-if="editingItem.imageLogo" :src="editingItem.imageLogo" class="mt-2 w-full h-32 object-cover rounded" >
          </div>
          
          <div class="field">
            <label for="imageIcon" class="block mb-1">Icon Image URL</label>
            <InputText id="imageIcon" v-model="editingItem.imageIcon" class="w-full" />
            <img v-if="editingItem.imageIcon" :src="editingItem.imageIcon" class="mt-2 w-full h-32 object-cover rounded" >
          </div>
          
          <!-- Добавляем редактор items -->
          <div class="field mt-4">
            <label for="items" class="block mb-1">Items</label>
            <ItemsEditor v-model="editingItem.items" />
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-8">
          <Button label="Отмена" icon="❌" class="p-button-outlined" @click="closeSidebar" />
          <Button label="Сохранить" icon="✅" @click="saveItem" />
        </div>
      </div>
    </div>
    
    <!-- Диалог для показа полного текста -->
    <Dialog 
      v-model:visible="fullTextDialogVisible" 
      header="Полный текст" 
      :modal="true" 
      :style="{ width: '50vw' }"
    >
      <p style="white-space: pre-wrap;">{{ fullText }}</p>
    </Dialog>
    
    <!-- Диалог для показа вложенных items -->
    <Dialog 
      v-model:visible="itemsDialogVisible" 
      header="Вложенные элементы" 
      :modal="true" 
      :breakpoints="{ '960px': '75vw', '640px': '90vw' }" 
      :style="{ width: '80vw' }"
    >
      <DataTable 
        :value="currentItems" 
        striped-rows 
        class="p-datatable-sm"
      >
        <Column field="title" header="Title"/>
        <Column field="excerpt" header="Excerpt"/>
        <Column field="text" header="Text">
          <template #body="{ data }">
            <div class="max-w-xs truncate">
              {{ data.text ? (data.text.length > 100 ? data.text.substring(0, 100) + '...' : data.text) : '-' }}
            </div>
          </template>
        </Column>
        <Column field="url" header="URL"/>
        <Column field="linkText" header="Link Text"/>
        <Column field="image" header="Image">
          <template #body="{ data }">
            <img v-if="data.image" :src="data.image" class="w-12 h-12 object-cover rounded" >
            <span v-else>-</span>
          </template>
        </Column>
      </DataTable>
    </Dialog>
    
    <!-- Диалог для показа галереи -->
    <Dialog 
      v-model:visible="galleryDialogVisible" 
      header="Галерея изображений" 
      :modal="true" 
      :breakpoints="{ '960px': '75vw', '640px': '90vw' }" 
      :style="{ width: '80vw' }"
    >
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div v-for="(image, index) in currentGallery" :key="index" class="relative">
          <img :src="image" class="w-full h-48 object-cover rounded" >
          <Button 
            label="⿻" 
            class="p-button-rounded p-button-sm absolute top-2 right-2 bg-white" 
            @click="copyToClipboard(image)" 
          />
        </div>
      </div>
    </Dialog>
    
    <!-- Toast для уведомлений -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useDirectusItems } from '#imports';
import { useToast } from 'primevue/usetoast';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import InputNumber from 'primevue/inputnumber';
import Textarea from 'primevue/textarea';
import Sidebar from 'primevue/sidebar';
import Dialog from 'primevue/dialog';
import Chips from 'primevue/chips';
import Select from 'primevue/select';
import MultiSelect from 'primevue/multiselect';
import Tag from 'primevue/tag';
import Toast from 'primevue/toast';
import InputChips from 'primevue/inputchips';
import ItemsEditor from '../components/ItemsEditor.vue';

// Состояние
const items = ref([]);
const loading = ref(true);
const viewMode = ref('compact'); // 'compact' или 'full'
const sortField = ref('art');
const sortOrder = ref(1); // 1 - asc, -1 - desc
const globalFilterValue = ref('');
const selectedTags = ref([]);
const availableTags = ref([]);
const selectedItem = ref(null);
const sidebarVisible = ref(false);
const sidebarCollapsed = ref(false);
const editingItem = ref(null);
const expandedRows = ref({});
const fullTextDialogVisible = ref(false);
const fullText = ref('');
const itemsDialogVisible = ref(false);
const galleryDialogVisible = ref(false);
const currentItems = ref([]);
const currentGallery = ref([]);

const toast = useToast();

// Поля для глобального поиска
const globalFilterFields = [
  'id', 'art', 'name', 'description', 'title', 'subtitle', 'excerpt', 'text', 'url', 
  'linkText', 'number', 'inscription', 'icon', 'product', 'category', 'additional'
];

// Фильтрованные элементы
const filteredItems = computed(() => {
  let filtered = [...items.value];
  
  // Фильтрация по тегам, если выбраны
  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(item => {
      if (!item.tags) return false;
      return selectedTags.value.every(tag => item.tags.includes(tag));
    });
  }
  
  return filtered;
});

// Загрузка данных
onMounted(async () => {
  try {
    const { getItems, createItems, updateItem } = useDirectusItems();
    const response = await getItems({
      collection: 'wcont', 
      params: {
        fields: ['*'], 
        sort: ['art']
      },
    });
    
    if (Array.isArray(response)) {
      items.value = response;
    } else if (response && response.data) {
      items.value = response.data;
    } else {
      console.error('Нет данных в ответе:', response);
    }
    
    // Собираем все уникальные теги
    const tags = new Set();
    items.value.forEach(item => {
      if (item.tags && Array.isArray(item.tags)) {
        item.tags.forEach(tag => tags.add(tag));
      }
    });
    availableTags.value = Array.from(tags);
    
  } catch (error) {
    console.error('Ошибка при получении данных:', error);
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось загрузить данные', life: 3000 });
  } finally {
    loading.value = false;
  }
});

// Методы
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'compact' ? 'full' : 'compact';
};

const onGlobalFilterChange = (event) => {
  // Обработка глобального фильтра
  globalFilterValue.value = event.target.value;
};

const applyTagsFilter = () => {
  // Применение фильтра по тегам
  console.log('Фильтр по тегам:', selectedTags.value);
};

const addTagToFilter = (tag) => {
  if (!selectedTags.value.includes(tag)) {
    selectedTags.value = [...selectedTags.value, tag];
  }
};

const copyToClipboard = (text) => {
  if (!text) return;
  
  navigator.clipboard.writeText(text).then(() => {
    toast.add({ severity: 'success', summary: 'Скопировано', detail: 'Текст скопирован в буфер обмена', life: 2000 });
  }).catch(err => {
    console.error('Ошибка при копировании в буфер обмена:', err);
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось скопировать в буфер обмена', life: 3000 });
  });
};

const onRowSelect = (event) => {
  console.log('Выбрана запись:', event.data);
};

const onRowUnselect = () => {
  console.log('Отменен выбор записи');
};

const openEditSidebar = (item) => {
  // Клонируем объект, чтобы не изменять оригинал напрямую
  editingItem.value = JSON.parse(JSON.stringify(item));
  sidebarVisible.value = true;
};

const saveItem = async () => {
  if (!editingItem.value) return;
  
  try {
    loading.value = true;
    const { updateItem } = useDirectusItems();
    
    await updateItem({
      collection: 'wcont',
      id: editingItem.value.id,
      item: editingItem.value
    });
    
    // Обновляем запись в локальном массиве
    const index = items.value.findIndex(item => item.id === editingItem.value.id);
    if (index !== -1) {
      items.value[index] = { ...editingItem.value };
    }
    
    toast.add({ severity: 'success', summary: 'Успех', detail: 'Запись успешно обновлена', life: 3000 });
    sidebarVisible.value = false;
  } catch (error) {
    console.error('Ошибка при сохранении записи:', error);
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось сохранить запись', life: 3000 });
  } finally {
    loading.value = false;
  }
};

const duplicateItem = async (item) => {
  try {
    loading.value = true;
    
    const newItem = { ...item };
    delete newItem.id;
    newItem.art = `${newItem.art}-copy`;
    newItem.name = `${newItem.name} (копия)`;
    
    const result = await createItems({
      collection: 'wcont',
      items: [newItem]
    });
    
    if (result && Array.isArray(result) && result.length > 0) {
      items.value.push(result[0]);
      toast.add({ severity: 'success', summary: 'Успех', detail: 'Запись успешно дублирована', life: 3000 });
    }
  } catch (error) {
    console.error('Ошибка при дублировании записи:', error);
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось дублировать запись', life: 3000 });
  } finally {
    loading.value = false;
  }
};

const convertToWJson = async (item) => {
  try {
    loading.value = true;
    
    const jsonData = {
      title: item.title,
      subtitle: item.subtitle,
      excerpt: item.excerpt,
      image: item.image,
      linkText: item.linkText,
      text: item.text,
      items: item.items || []
    };

    const result = await createItems({
      collection: 'wjson',
      items: [{ title: item.name, json: jsonData }]
    });
    
    if (result && Array.isArray(result) && result.length > 0) {
      toast.add({ severity: 'success', summary: 'Успех', detail: 'Запись успешно конвертирована в wjson', life: 3000 });
    }
  } catch (error) {
    console.error('Ошибка при конвертировании записи:', error);
    toast.add({ severity: 'error', summary: 'Ошибка', detail: 'Не удалось конвертировать запись', life: 3000 });
  } finally {
    loading.value = false;
  }
};

const showItemsDialog = (items) => {
  if (!items || !Array.isArray(items)) {
    toast.add({ severity: 'info', summary: 'Информация', detail: 'Нет вложенных элементов', life: 3000 });
    return;
  }
  
  currentItems.value = items;
  itemsDialogVisible.value = true;
};

const showGalleryDialog = (gallery) => {
  if (!gallery || !Array.isArray(gallery) || gallery.length === 0) {
    toast.add({ severity: 'info', summary: 'Информация', detail: 'Галерея пуста', life: 3000 });
    return;
  }
  
  currentGallery.value = gallery;
  galleryDialogVisible.value = true;
};

// Функции для работы с текстом
const truncateText = (text: string) => {
  if (!text) return '-';
  return text.length > 255 ? text.substring(0, 255) + '...' : text;
};

const showFullText = (text: string) => {
  fullText.value = text;
  fullTextDialogVisible.value = true;
};

// Функции для работы с сайдбаром
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
};

const closeSidebar = () => {
  sidebarVisible.value = false;
  editingItem.value = null;
};

// Функции для работы с Directus
const { getItems, createItems, updateItem } = useDirectusItems();

const onRowExpand = (event) => {
  // Implementation of onRowExpand
};

const onRowCollapse = (event) => {
  // Implementation of onRowCollapse
};

// Добавляем компонент в список компонентов
const components = {
  ItemsEditor,
  DataTable,
  Column,
  Button,
  InputText,
  InputNumber,
  Textarea,
  Dialog,
  InputChips,
  Select,
  MultiSelect,
  Tag,
  Toast
};

// Добавляем функцию для просмотра полного текста по клику на ячейку
const showFullTextFromCell = (text, event) => {
  // Проверяем, что клик не был на кнопке
  if (event?.target?.closest('.p-button')) return;
  
  if (text && text.length > 255) {
    fullText.value = text;
    fullTextDialogVisible.value = true;
  }
};
</script>

<style scoped>
.truncate-text {
  max-width: 255px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.truncate-text:hover {
  color: var(--primary-color);
}

/* Стили для сайдбара */
.sidebar-transition {
  transition: width 0.3s ease;
}

/* Стили для таблицы */
:deep(.p-datatable) {
  font-size: 13px;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  padding: 0.5rem;
}

/* Оптимизации для мобильных устройств */
@media (max-width: 640px) {
  .sidebar {
    width: 100vw !important;
  }
}

/* Добавляем стили для вложенных элементов */
:deep(.p-datatable-expanded-row-content) {
  background-color: var(--surface-ground);
  padding: 1rem;
}

:deep(.p-datatable-expanded-row) {
  background-color: var(--surface-hover);
}
</style> 