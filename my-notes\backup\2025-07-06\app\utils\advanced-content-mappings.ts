// Расширенная система маппинга контента с интеллектуальным применением

export interface ContentVariable {
  name: string
  type: 'text' | 'html' | 'image' | 'url' | 'number' | 'boolean' | 'array' | 'object'
  description: string
  defaultValue: any
  required: boolean
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
    allowedValues?: any[]
  }
}

export interface ContentSet {
  id: string
  name: string
  description: string
  category: string
  variables: Record<string, any>
  metadata: {
    author: string
    version: string
    tags: string[]
    language: string
    created: string
    updated: string
  }
  preview?: {
    title: string
    description: string
    image?: string
  }
}

export interface ContentTemplate {
  id: string
  name: string
  description: string
  category: string
  targetElements: {
    types?: string[]
    hasVariables?: string[]
    scope: 'any' | 'specific'
  }
  variableSchema: ContentVariable[]
  contentSets: ContentSet[]
  autoImageGeneration?: {
    enabled: boolean
    service: 'unsplash' | 'placeholder' | 'ai'
    keywords: string[]
    dimensions: { width: number, height: number }[]
  }
  enabled: boolean
}

// Предустановленные шаблоны контента
export const contentTemplates: ContentTemplate[] = [
  {
    id: 'business-cards',
    name: 'Бизнес карточки',
    description: 'Контент для бизнес карточек и услуг',
    category: 'business',
    targetElements: {
      types: ['card', 'service', 'feature'],
      hasVariables: ['title', 'description', 'image'],
      scope: 'any'
    },
    variableSchema: [
      {
        name: 'title',
        type: 'text',
        description: 'Заголовок услуги',
        defaultValue: 'Наша услуга',
        required: true,
        validation: { minLength: 3, maxLength: 100 }
      },
      {
        name: 'description',
        type: 'text',
        description: 'Описание услуги',
        defaultValue: 'Описание нашей замечательной услуги',
        required: true,
        validation: { minLength: 10, maxLength: 500 }
      },
      {
        name: 'image',
        type: 'image',
        description: 'Изображение услуги',
        defaultValue: 'https://via.placeholder.com/300x200',
        required: false
      },
      {
        name: 'price',
        type: 'text',
        description: 'Цена услуги',
        defaultValue: 'От 1000 руб.',
        required: false
      },
      {
        name: 'features',
        type: 'array',
        description: 'Список особенностей',
        defaultValue: ['Качественно', 'Быстро', 'Недорого'],
        required: false
      }
    ],
    contentSets: [
      {
        id: 'web-development',
        name: 'Веб-разработка',
        description: 'Услуги веб-разработки',
        category: 'it',
        variables: {
          title: 'Разработка веб-сайтов',
          description: 'Создаем современные, быстрые и адаптивные веб-сайты с использованием последних технологий',
          image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=300&h=200&fit=crop',
          price: 'От 50 000 руб.',
          features: ['Адаптивный дизайн', 'SEO оптимизация', 'Быстрая загрузка', 'Современные технологии']
        },
        metadata: {
          author: 'System',
          version: '1.0.0',
          tags: ['веб-разработка', 'сайты', 'it'],
          language: 'ru',
          created: '2024-01-01',
          updated: '2024-01-01'
        }
      },
      {
        id: 'mobile-apps',
        name: 'Мобильные приложения',
        description: 'Разработка мобильных приложений',
        category: 'it',
        variables: {
          title: 'Мобильные приложения',
          description: 'Разрабатываем нативные и кроссплатформенные мобильные приложения для iOS и Android',
          image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=300&h=200&fit=crop',
          price: 'От 100 000 руб.',
          features: ['iOS и Android', 'Нативная производительность', 'Интуитивный интерфейс', 'Интеграция с API']
        },
        metadata: {
          author: 'System',
          version: '1.0.0',
          tags: ['мобильные приложения', 'ios', 'android'],
          language: 'ru',
          created: '2024-01-01',
          updated: '2024-01-01'
        }
      }
    ],
    autoImageGeneration: {
      enabled: true,
      service: 'unsplash',
      keywords: ['business', 'technology', 'office', 'computer'],
      dimensions: [
        { width: 300, height: 200 },
        { width: 400, height: 300 },
        { width: 600, height: 400 }
      ]
    },
    enabled: true
  },
  {
    id: 'team-members',
    name: 'Команда',
    description: 'Карточки членов команды',
    category: 'team',
    targetElements: {
      types: ['team-card', 'person', 'member'],
      hasVariables: ['name', 'position', 'photo'],
      scope: 'any'
    },
    variableSchema: [
      {
        name: 'name',
        type: 'text',
        description: 'Имя сотрудника',
        defaultValue: 'Иван Иванов',
        required: true,
        validation: { minLength: 2, maxLength: 100 }
      },
      {
        name: 'position',
        type: 'text',
        description: 'Должность',
        defaultValue: 'Специалист',
        required: true,
        validation: { minLength: 2, maxLength: 100 }
      },
      {
        name: 'photo',
        type: 'image',
        description: 'Фотография сотрудника',
        defaultValue: 'https://via.placeholder.com/150x150',
        required: false
      },
      {
        name: 'bio',
        type: 'text',
        description: 'Краткая биография',
        defaultValue: 'Опытный специалист с многолетним стажем работы',
        required: false
      },
      {
        name: 'skills',
        type: 'array',
        description: 'Навыки',
        defaultValue: ['JavaScript', 'React', 'Node.js'],
        required: false
      }
    ],
    contentSets: [
      {
        id: 'frontend-team',
        name: 'Frontend команда',
        description: 'Команда frontend разработчиков',
        category: 'development',
        variables: {
          name: 'Анна Петрова',
          position: 'Senior Frontend Developer',
          photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
          bio: 'Опытный frontend разработчик с 5+ годами опыта в создании современных веб-приложений',
          skills: ['React', 'Vue.js', 'TypeScript', 'CSS3', 'Webpack']
        },
        metadata: {
          author: 'System',
          version: '1.0.0',
          tags: ['команда', 'frontend', 'разработка'],
          language: 'ru',
          created: '2024-01-01',
          updated: '2024-01-01'
        }
      }
    ],
    autoImageGeneration: {
      enabled: true,
      service: 'unsplash',
      keywords: ['professional', 'portrait', 'business', 'person'],
      dimensions: [
        { width: 150, height: 150 },
        { width: 200, height: 200 },
        { width: 300, height: 300 }
      ]
    },
    enabled: true
  },
  {
    id: 'testimonials',
    name: 'Отзывы',
    description: 'Отзывы клиентов',
    category: 'social-proof',
    targetElements: {
      types: ['testimonial', 'review', 'feedback'],
      hasVariables: ['quote', 'author', 'company'],
      scope: 'any'
    },
    variableSchema: [
      {
        name: 'quote',
        type: 'text',
        description: 'Текст отзыва',
        defaultValue: 'Отличная работа! Рекомендую всем.',
        required: true,
        validation: { minLength: 10, maxLength: 1000 }
      },
      {
        name: 'author',
        type: 'text',
        description: 'Автор отзыва',
        defaultValue: 'Иван Петров',
        required: true,
        validation: { minLength: 2, maxLength: 100 }
      },
      {
        name: 'company',
        type: 'text',
        description: 'Компания автора',
        defaultValue: 'ООО "Компания"',
        required: false
      },
      {
        name: 'rating',
        type: 'number',
        description: 'Рейтинг (1-5)',
        defaultValue: 5,
        required: false,
        validation: { allowedValues: [1, 2, 3, 4, 5] }
      },
      {
        name: 'avatar',
        type: 'image',
        description: 'Аватар автора',
        defaultValue: 'https://via.placeholder.com/80x80',
        required: false
      }
    ],
    contentSets: [
      {
        id: 'satisfied-clients',
        name: 'Довольные клиенты',
        description: 'Положительные отзывы клиентов',
        category: 'positive',
        variables: {
          quote: 'Команда превзошла все наши ожидания. Проект был выполнен в срок и с высочайшим качеством.',
          author: 'Мария Сидорова',
          company: 'ООО "Инновации"',
          rating: 5,
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face'
        },
        metadata: {
          author: 'System',
          version: '1.0.0',
          tags: ['отзывы', 'клиенты', 'положительные'],
          language: 'ru',
          created: '2024-01-01',
          updated: '2024-01-01'
        }
      }
    ],
    autoImageGeneration: {
      enabled: true,
      service: 'unsplash',
      keywords: ['portrait', 'professional', 'happy', 'client'],
      dimensions: [
        { width: 80, height: 80 },
        { width: 100, height: 100 }
      ]
    },
    enabled: true
  }
]

// Функции для работы с контентом
export function getContentTemplateById(id: string): ContentTemplate | undefined {
  return contentTemplates.find(template => template.id === id)
}

export function getContentSetById(templateId: string, setId: string): ContentSet | undefined {
  const template = getContentTemplateById(templateId)
  return template?.contentSets.find(set => set.id === setId)
}

export function getAllEnabledTemplates(): ContentTemplate[] {
  return contentTemplates.filter(template => template.enabled)
}

export function getTemplatesByCategory(category: string): ContentTemplate[] {
  return contentTemplates.filter(template => template.category === category && template.enabled)
}

// Автоматическая генерация изображений
export async function generateImageUrl(keywords: string[], dimensions: { width: number, height: number }, service: 'unsplash' | 'placeholder' = 'unsplash'): Promise<string> {
  if (service === 'unsplash') {
    const query = keywords.join(',')
    return `https://source.unsplash.com/${dimensions.width}x${dimensions.height}/?${query}`
  } else {
    return `https://via.placeholder.com/${dimensions.width}x${dimensions.height}`
  }
}

// Применение контента к HBS шаблону
export function applyContentToTemplate(hbs: string, contentData: Record<string, any>): string {
  // Используем существующую логику из htmlToTemplate
  let result = hbs

  for (const [key, value] of Object.entries(contentData)) {
    const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
    result = result.replace(regex, String(value))
  }

  return result
}

// Сохранение и загрузка пользовательских шаблонов
export function saveCustomContentTemplates(templates: ContentTemplate[]): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('customContentTemplates', JSON.stringify(templates))
  }
}

export function loadCustomContentTemplates(): ContentTemplate[] {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('customContentTemplates')
    return stored ? JSON.parse(stored) : []
  }
  return []
}

export function getAllContentTemplates(): ContentTemplate[] {
  const custom = loadCustomContentTemplates()
  return [...contentTemplates, ...custom]
}
