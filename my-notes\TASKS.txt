npx nuxt dev --port 3010

kilocode.ai

--Важный промпт
Перед выполнением задач сначала проанализируй каждый пункт тщательно и детально, 
также проанализируй весь код ПОЛНОСТЬЮ в текущем файле и в файлах на которые я ссылаюсь.
После этого составь план действий и следуй ему внося изменения в код очень аккуратно, точечно и внимательно, чтобы не нарушить целостность кода и текущий рабочий функционал. 
Избегай дублирования функций, ошибок - синтаксических и логических, ничего не удаляй и не меняй за пределами поставленных задач. 
Используй практики, которые уже успешно работают в проекте на основе внимательного и тщательного анализа используемого в проекте подходов, стандартов и паттернов кода (api nuxt-directus composables, серверные api-маршруты, esm модули (nuxt 3 nitro vite) компактный интерфейс primevue, tailwind).
Используй правильные импорты сторонних модулей, библиотек и компонентов так как они уже используются и в той же последовательности (prismjs, cheerio, handlebars и др)
НЕ запускай сервер (он у меня всегда запущен) и браузер (страницы над которыми мы работаем всегда у меня открыты)

Это комплексная задача которая требует интеллектуального анализа и аккуратного подхода, а не бездумного быстрого кодирования. 
Если это необходимо лучше разбить задачу на 2-3 этапа с ручным тестированием, логированием и проверкой результатов, для плавного и эффективного внедрения всех запрошенных задач.
Думай как опытный разработчик, имеющий опыт работы актуальный в 2025 году в стеке nuxt 3 nitro vite, api composables, vue 3, typescript, primevue
Общайся со мной на русском языке. Сейчас и всегда.
---

Общая база страниц
	http://localhost:3010/files2           (html файлы, разделитель на блоки, извлекатель элементов, конвертор в wpage)
	http://localhost:3010/wpages           (база wpage - быстрое созд/доб/ред/дубл/удал блоков, масс-доб/ред, разделитель на блоки, извлекатель элементов)
	http://localhost:3010/wblock-proto2    (база wblock_proto - быстрое созд/доб/ред/дубл/удал блоков, масс-доб/ред, анализ+шаблониз, загрузка картинок, скрины)
	http://localhost:3010/welem-proto 	   (база welem_proto - быстрое созд/доб/ред/дубл/удал блоков, масс-доб/ред, анализ+шаблониз, загрузка картинок, скрины)
	http://localhost:3010/wjson 	       (2в1 база wjson + конструктор - быстрое созд/доб/ред/дубл/удал wjson, масс-доб/ред, конструктор, сайдбар с анализом и редактором)
		- 📄база примитивов - public\data\content-primitives.json
	http://localhost:3010/graphics         (база wjson-графики - быстрое созд/доб/ред/дубл/удал, масс-доб/ред, сборщик наборов, сайдбар с редактором и галереей)

	http://localhost:3010/wpage-gen2
	http://localhost:3010/wblock-html-gen  (генератор из html - элементы, блоки, страницы)
	http://localhost:3010/wblock-page-gen  (генератор из hbs+json - блоки, страницы)
	http://localhost:3010/wblock-gen2      (генератор блоков 4в1 - стандарт, комбинации (доски-страницы), контент, прототип)
			-📁контент наборы storage\user-settings\content-sets (json файлы)
	http://localhost:3010/welem-gen2       (генератор блоков 5в1 - стандарт, комбинации (блоки/шаблоны), вариации, темы, контент)
			-📑комбинации app\data\grid-templates.ts
			-📁вариации (json) storage\user-settings\variation-groups
				-📑классы app\data\css-classes.ts
			-📑темы app\utils\advanced-theme-mappings.ts
			-📑контент app\utils\advanced-content-mappings.ts

Дополнительно
	http://localhost:3010/screenshot-tool
	http://localhost:3010/html-converter
	http://localhost:3010/notes
	http://localhost:3010/wconts2	
	http://localhost:3010/wblock-gen       
	http://localhost:3010/welem-gen 
	http://localhost:3010/wpage-gen
----


-----> 🛠️я тут🛠️


-----------
Базы
-----------
	✅-База файлов (files2.vue - база html файлов из папки)
		-✅Разборка файлов на элементы (split to elements) - crafto и betheme
		-✅Разборка на элементы со скринами
		-✅ разборка на блоки со скринами (еще проблема с качеством скринов)
		-✅сохранить как страницу со скрином, (не добавляются префиксы в js и css)
		-?сохранить как страницу со скриномблоками и связями
		-?множественная загрузка файлов с быстрыми параметрами для всех
		-?множественная загрузка html по url 
		- база картинок на сервере - directus (файлы из базы)
	✅-База страниц - wpages.vue с подчиненными wblock_proto (аналогично wconts) 
		-✅разделение на блоки и элементы
		-✅Массовая загрузка и редакт
		-✅отображение подчиненных wblock_proto
		-✅добавить массовый скриншотинг
		-✅множественная загрузка загрузка по url
	✅-База блоков - wblock-proto2
		-✅Разборка блоков на элементы
		-✅Разборка блоков на элементы со скринами
		-✅Массовая загрузка новых блоков
		-✅Доработать связывания и сайдбары для показа связанных записей /multiselect (везде во всех сайдбарах)
		-✅массовое дублирование и удаление
		-✅Шаблонизация (html>hbs+json)
		-✅Скрины
		-✅ умный редактор
		- data_structure
		-✅ добавить hbs и json в быстрое редактирование + улучшенный редактор json (как интеллектуальные формы)
		-⚠️(+быстрая и массовая конвертация в wjson)
		- добавить сборку/разборку
		- Создайте систему рейтинга блоков для выделения лучших образцов
		- интеллектуальный анализ сложности
	✅База элементов - welem-proto.vue 
	    -✅общее создание и провер функций
		-✅Массовая загрузка и редакт
		- добавить hbs и json в быстрое редактирование + улучшенный редактор json  (как интеллектуальные формы)
		(свой uiverse) 
	✅-База контента - wjson.vue 		
		-✅умный редактор в сайдбаре
	    -✅Массовая загрузка и редакт
		-✅улучшенный конструктор контента
		-✅быстрое управление примитивами/единицами контента
		-сделать быстрое добавление новых примитивов в набор (как в graphics)
		-улучшить подчиненную таблицу
		-добавить улучшенный редактор контента в массовые формы (как интеллектуальные формы)
	   
	✅--База url-графики (наборы) - graphics.vue (грид-генератор наборов/коллекций графики behance-like)
		✅- Массовая загрузка		
	 	- режим комбинаций
			-верх область - грид плитку justified masonry карточек/примитивов отдельных картинок с image/preview из поля json всех записей wjson (без повторов url)
			-панель - фильтр по тегам, коллекции, сбрось, только отмеченные, снять отметки и кнопка применить 
			-карточки - url, копи-буфер, размеры картинки, чек-боксы (выбор карточек)

	Общее 
		-?yaml вместо json
		-(вручную)надо подкорректировать сопоставление элементов по типам (есть не все)	
		-(вручную)оптимизация извлечения элементов - не нужны все	
		-(50%)в multiselect (сортировки, поле поиска и эскизы-превью)

		-кнопки обновления и удаления в toolbar (+коррект дубля) - в wblock-page-gen и в wblock-html-gen
		-быстрые готовые фильтры
		-система баллов/рейтингов (интеллектуальный анализ)

-----------
Мастеры-конструкторы массовой генерации
-----------

	✅ wblock-html-gen 
		-✅доработать чтобы можно было сохранять страницу+блоки
		-✅доработать чтобы был hbs и json
		-✅анализатор не заполняет особенности
		-✅при добавлении элементов также прокидывать css и js
		-поддержка иконочных шрифтов (для iframe, blob - предпросмотра и скриншотинга) можно с внешнего севера
		-быстрая очистка кода, авто-разделитель block с вариативностью
		-улучшить качество сопоставления строк при добавлении css/JS (избегать дублей)
		-undo/redo
		-умный редактор контента в html (отдельная вкладка)
		-интеграция с режимом контента/библиотеками
	✅ wblock-page-gen
		-размер групп в canvas (попробовать разделить логику/управление и сделать доп.управление внутренними половинами), размер иконок
		-показ оригинального от из блоков с сопоставление совместимости (все ли переменные и совпадение их длины (разброс/анализ отклонения)
		-интеллектуальный редактор JSON
		-undo/redo
		-интеграция с режимом контента/библиотеками
	✅ wblock-gen - мастер блоков по типу / набора блоков
		✅- JSON X HBS
	✅ wblock-gen2 - мастер блоков по типу / набора блоков	
		✅- полный сайдбар редактирования
		✅отображение типов блоков прямо в карточке
		✅-режим "контент"
		✅-режим массовых комбинаций блоков (сборка досок-страниц)
		✅-режим "прототип" (для быстрой переработки шаблонов)
		-настроить сортировку по полю number по умолчанию
		-добавить редактор json в группы редакт
		-⚠️ добавить редактор JSON в сайдбары (2 сайдбара - стандарт, комбинации, )
		-⚠️ добавить массовое сохранение досок-страниц
		-режим контент "тормозит" (вкладка используем много памяти)
		-улучшить функцию создания наборов контента (более компактный редактор)
		-(вручную)Перепроверить скриншотинг в wblock-gen 
		-вариации стилей и вариации блоков (как в гутенберг)
		-?в карточке - отображение списка переменных в шаблоне с интеллектуальной детекцией используемых в json на входе и наоборот

	✅ welem-gen - мастер элементов по типу / набора элементов (аналог wblock-gen) JSON+HBS
	✅ welem-gen2 (5в1) - улучшенный мульти-мастер элементов
		--улучшить генерацию скриншотов во всех режимах чтобы с учетом обертки
		-- ✅Стандарт - мастер элементов по типу / набора элементов (wblock-gen)
		- проверить применение обертки (не обновляется скрин)
		-добавить редактор json в группы редакт
		-- ✅Комбинации - мастер генерации блоков из рядов и колонок - конструктор bootstrap сетки + превью (аналог wpbakery) 
		-- +-Вариации (60%) - мастер генерации вариаций стилей + превью + предросмотр скринов (подмена классов - цветовые схемы, размеры, границы и рамки, тени)
		-- +-Темы (60%) - мастер генерации тем (палитра, типографика, отступы, UIkit-превью)
				-темы сохраняются пока в localStorage				
		-- ✅Контент - мастер подстановки контента (наборы wjson+wblock+из файлов, анализ элементов, сопоставление, быстрый конструктор, новые наборы, превью, комбинатор)
		- не загружается контент из файлов и других источников  (нету load-user-setting)
		⚠️-- Атомарный (мастер сборных элементов (атомарный) - создание новых и пересборка существующих)
		 --- Репитер (each items)
		 --- ручной "вариатор"/мастер пресетов плитка 3x3 с превью вариаций с быстрым пробросом стилей и/или замены классов индивидуально в каждый
		 ✅- полный сайдбар редактирования как в welem-proto

	✅wjson-конструктор (библиотека примтивов с редактором, сборка конструктор и массовые формы)
		-режим комбинаций
		-проблема - дублирование переменных в конструкторе (необходима обработка с добавлением уникального инкремента при добавлении в формы)
		-проблема - интеллектуальных формах - измнения порядка должно влиять на инкремент (подумать)
	wpage-просмотрщик (быстрый смотр просматривать и редактировать страницы)
-----------
Далее
-----------
	
	Умная архитектура слоев используя возможности HBS, JSON, cheerio, linkedom, pupeeteer, CSS умного анализа, pinegrow
	
	⚠️- wpage-gen мастер массовых страниц по типу / набора страниц
		-?стандартный режим
		-?режим массовых комбинаций блоков и страниц
		-flexible режим (умный мастер сборки по типам с интеллектуальным анализом совместимости по коду и классам)
		-режим контента (умное дерево контента с префиксами от блоков + специальный HBS)
	⚠️-graphic-gen
		- генератор (мастер графики/canva)
		-генератор иллюстраций (комбинации из картинок)
	⚠️-uikit-gen - мастер концепций типа colormind
		-улучшенный вариант темизации - темы + наборы элементов	
		-CSSgen система 
	⚠️⚠️- website-gen - мастер полного html шаблона (пошаговый типа бриф/logo-maker)
	⚠️⚠️- HTML-to-WP-gen
	
	- мастер мудбордов
	- мастер простых и сложных версток (базовые наборы)
		
	-подготовка страниц к конвертации через pinegrow (html-to-wp)
	-свой плагин (аддон) для вариаций блоков, вариаций стилей блоков и паттернов
	

	ДАШБОРДЫ 
	общие умные цепочки генерации/комбинаций
	
---------
Общая ERP
--------

	Продумать важные цепочки для автоматизации маркетинга
	Монетизация действий
	Автоматизация бизнес-процессов (гибкая система типа n8n)

	--Модули
	-Продукт
	-Продажи
	-Производство
	-Маркетинг
	-Финансы

-------
Проработка процессов
----

Процесс - загрузка в базу
1. Добавление страниц по URL / 
1v2. Загрузка файлов в directus + сохранение как страниц
2. Разделение добавленных страниц на блоки со скринами


Анализ моего процесса "Демо-шаблоны"

- Выбор страницы из базы
- быстрая замена данных в блоках
- ручные корректировки
- загрузка на сайт

мысли
переработка режима "прототип" в более удобный конструктор (типа гутена)
лучшее из прототипа, wblock-page-gen/wblock-html-gen
боковой сайдбар добавления слева
боковой сайдбар редактирования справа + iframe
нижняя панель - библиотека контента


-----------
Задачи ч2
---------

-подготовка страниц к конвертации через pinegrow (html-to-wp)
-база CSS и JS

переработка отобранных шаблонов crafto, porto, betheme
	

авто-связывание с файлом (связка-коллекция directus_files.wblock_proto) htmlfile	
		
быстрая заливка файлов с необходимой разметкой (crafto, porto) - блоки и элементы
	
Массовые авто-конвертации html-hbs


----
дополнительно для graphics.vue
----
		meltiselect по связанном страницами блокам
		кнопка меняющую количество карточек в строке (4,5,6) 		
		5.(50%)в карточке оптимизировать галерею еще более интеллектуально (картинки должны заполнять все пространство (сделай варианты на 1, на 2, на 4 с авто-адаптацией)
		6. чекбоксы в карточках не работают (смотри как сделано в wblock-gen2 - там еще у нас тоже будут еще чек-боксы для комбинаций)
		runtime-core.esm-bun…r.js?v=6ed52193:266 Uncaught TypeError: Invalid attempt to spread non-iterable instance.
		In order to be iterable, non-array objects must have a [Symbol.iterator]() method.
		7.(50%) область плитки карточек надо сделать на всю высоту - смотри как сделано wblock-gen2, добавляемая сверху область должна быть во внутреннем контейнере чтобы экран не делился пополам по вертикали), 
		фильтрам можно сделать sticky функцию чтобы при прокуртке вниз они оставались в поле видимости



----------
ROVO DEV
----------

----<EMAIL>
acli rovodev auth login --email "<EMAIL>" --token < token.txt
acli rovodev run

************************************************************************************************************************************************************************************************
************************************************************************************************************************************************************************************************
************************************************************************************************************************************************************************************************

---<EMAIL>
************************************************************************************************************************************************************************************************

https://rovodevagents-beta.atlassian.net/wiki/external/Yzc2NzI4MTk3YTBhNDdiYjkzZDhhZTc3MjE0ZmE4Y2Q
https://www.atlassian.com/blog/announcements/rovo-dev-command-line-interface

--

ssh u9338676@*************
MG5M73no6mky0Rd6



https://dobromash-stanki.ru/
Логин admin_dobr
zSDVi)7gJ@LsID@OAew%x1gv
---
Логин: u3001522
Пароль: Tkz3D45m6txd0UK3
Адрес панели управления хостингом: https://server215.hosting.reg.ru:1500/
