import { defineEventHandler, readBody } from 'h3'
import { analyzeHtml } from '../utils/htmlAnalyzer'

export default defineEventHandler(async (event) => {
  const { records } = await readBody(event)
  const results = []

  for (const record of records) {
    const analysis = analyzeHtml(record.html)
    results.push({
      id: record.id,
      layout: Array.from(analysis.layout),
      elements: Array.from(analysis.elements),
      graphics: Array.from(analysis.graphics),
      features: Array.from(analysis.features),
      treeStructure: analysis.treeStructure
    })
  }

  return { results }
})
