<template>
  <div class="html-template-converter">
    <div class="grid">
      <!-- Левая колонка - ввод HTML -->
      <div class="col-12 md:col-4">
        <h3>HTML</h3>
        <Textarea v-model="htmlInput" rows="20" class="w-full" placeholder="Вставьте HTML код сюда" />
        <div class="mt-3 flex justify-content-between">
          <Button label="Конвертировать" icon="pi pi-refresh" @click="convertHtml" />
          <Button label="Очистить" icon="pi pi-trash" class="p-button-secondary" @click="clearAll" />
        </div>
      </div>
      
      <!-- Правая колонка - результаты -->
      <div class="col-12 md:col-8">
        <TabView>
          <!-- Вкладка с HTML результатом -->
          <TabPanel header="HTML">
            <div class="result-container">
              <div class="flex justify-content-between mb-2">
                <h3>Оригинальный HTML</h3>
                <Button icon="pi pi-copy" class="p-button-text" @click="copyToClipboard(result.html)" />
              </div>
              <Textarea v-model="result.html" rows="20" class="w-full" readonly />
            </div>
          </TabPanel>
          
          <!-- Вкладка с Handlebars шаблоном -->
          <TabPanel header="Handlebars">
            <div class="result-container">
              <div class="flex justify-content-between mb-2">
                <h3>Handlebars шаблон</h3>
                <Button icon="pi pi-copy" class="p-button-text" @click="copyToClipboard(result.hbs)" />
              </div>
              <Textarea v-model="result.hbs" rows="20" class="w-full" readonly />
            </div>
          </TabPanel>
          
          <!-- Вкладка с JSON данными -->
          <TabPanel header="JSON">
            <div class="result-container">
              <div class="flex justify-content-between mb-2">
                <h3>JSON данные</h3>
                <Button icon="pi pi-copy" class="p-button-text" @click="copyToClipboard(JSON.stringify(result.json, null, 2))" />
              </div>
              <Textarea v-model="jsonString" rows="20" class="w-full" readonly />
            </div>
          </TabPanel>
        </TabView>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useToast } from 'primevue/usetoast';

const toast = useToast();

// Состояние компонента
const htmlInput = ref('');
const result = ref({
  html: '',
  hbs: '',
  json: {}
});

// Форматированный JSON для отображения
const jsonString = computed(() => {
  try {
    return JSON.stringify(result.value.json, null, 2);
  } catch (e) {
    return '{}';
  }
});

// Конвертация HTML в шаблон
async function convertHtml() {
  if (!htmlInput.value.trim()) {
    toast.add({
      severity: 'warn',
      summary: 'Пустой ввод',
      detail: 'Пожалуйста, введите HTML код для конвертации',
      life: 3000
    });
    return;
  }
  
  try {
    // Используем упрощенную версию API для конвертации
    const response = await fetch('/api/split-html-files-simple', {
      method: 'POST',
      body: JSON.stringify({ html: htmlInput.value }),
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (data.success && data.results && data.results.length > 0) {
      // Берем первый результат
      const firstResult = data.results[0];
      result.value = {
        html: firstResult.html || '',
        hbs: firstResult.hbs || '',
        json: firstResult.json || {}
      };
      
      toast.add({
        severity: 'success',
        summary: 'Конвертация выполнена',
        detail: `Обработано блоков: ${data.blocksProcessed}`,
        life: 3000
      });
    } else {
      toast.add({
        severity: 'error',
        summary: 'Ошибка конвертации',
        detail: data.error || 'Не удалось обработать HTML',
        life: 3000
      });
    }
  } catch (error) {
    console.error('Ошибка при конвертации:', error);
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Произошла ошибка при обработке запроса',
      life: 3000
    });
  }
}

// Копирование в буфер обмена
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(() => {
    toast.add({
      severity: 'info',
      summary: 'Скопировано',
      detail: 'Текст скопирован в буфер обмена',
      life: 2000
    });
  });
}

// Очистка всех полей
function clearAll() {
  htmlInput.value = '';
  result.value = {
    html: '',
    hbs: '',
    json: {}
  };
}
</script>

<style scoped>
.html-template-converter {
  padding: 1rem;
}

.result-container {
  height: 100%;
}
</style>