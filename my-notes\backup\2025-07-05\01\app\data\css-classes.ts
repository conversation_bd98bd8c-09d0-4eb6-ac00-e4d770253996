// Коллекция CSS классов для использования в конструкторах

export interface CSSClass {
  name: string
  category: string
  description: string
  example?: string
}

// Bootstrap классы
export const bootstrapClasses: CSSClass[] = [
  // Цвета текста
  { name: 'text-primary', category: 'colors', description: 'Основной цвет текста' },
  { name: 'text-secondary', category: 'colors', description: 'Вторичный цвет текста' },
  { name: 'text-success', category: 'colors', description: 'Зеленый цвет текста' },
  { name: 'text-danger', category: 'colors', description: 'Красный цвет текста' },
  { name: 'text-warning', category: 'colors', description: 'Желтый цвет текста' },
  { name: 'text-info', category: 'colors', description: 'Синий цвет текста' },
  { name: 'text-light', category: 'colors', description: 'Светлый цвет текста' },
  { name: 'text-dark', category: 'colors', description: 'Темный цвет текста' },
  { name: 'text-muted', category: 'colors', description: 'Приглушенный цвет текста' },
  { name: 'text-white', category: 'colors', description: 'Белый цвет текста' },

  // Цвета фона
  { name: 'bg-primary', category: 'backgrounds', description: 'Основной цвет фона' },
  { name: 'bg-secondary', category: 'backgrounds', description: 'Вторичный цвет фона' },
  { name: 'bg-success', category: 'backgrounds', description: 'Зеленый цвет фона' },
  { name: 'bg-danger', category: 'backgrounds', description: 'Красный цвет фона' },
  { name: 'bg-warning', category: 'backgrounds', description: 'Желтый цвет фона' },
  { name: 'bg-info', category: 'backgrounds', description: 'Синий цвет фона' },
  { name: 'bg-light', category: 'backgrounds', description: 'Светлый цвет фона' },
  { name: 'bg-dark', category: 'backgrounds', description: 'Темный цвет фона' },
  { name: 'bg-white', category: 'backgrounds', description: 'Белый цвет фона' },
  { name: 'bg-transparent', category: 'backgrounds', description: 'Прозрачный фон' },

  // Размеры текста
  { name: 'fs-1', category: 'typography', description: 'Очень большой текст' },
  { name: 'fs-2', category: 'typography', description: 'Большой текст' },
  { name: 'fs-3', category: 'typography', description: 'Средний большой текст' },
  { name: 'fs-4', category: 'typography', description: 'Обычный текст' },
  { name: 'fs-5', category: 'typography', description: 'Маленький текст' },
  { name: 'fs-6', category: 'typography', description: 'Очень маленький текст' },

  // Вес шрифта
  { name: 'fw-light', category: 'typography', description: 'Тонкий шрифт' },
  { name: 'fw-normal', category: 'typography', description: 'Обычный шрифт' },
  { name: 'fw-bold', category: 'typography', description: 'Жирный шрифт' },
  { name: 'fw-bolder', category: 'typography', description: 'Очень жирный шрифт' },

  // Отступы
  { name: 'm-0', category: 'spacing', description: 'Без внешних отступов' },
  { name: 'm-1', category: 'spacing', description: 'Маленькие внешние отступы' },
  { name: 'm-2', category: 'spacing', description: 'Средние внешние отступы' },
  { name: 'm-3', category: 'spacing', description: 'Большие внешние отступы' },
  { name: 'm-4', category: 'spacing', description: 'Очень большие внешние отступы' },
  { name: 'm-5', category: 'spacing', description: 'Максимальные внешние отступы' },

  { name: 'p-0', category: 'spacing', description: 'Без внутренних отступов' },
  { name: 'p-1', category: 'spacing', description: 'Маленькие внутренние отступы' },
  { name: 'p-2', category: 'spacing', description: 'Средние внутренние отступы' },
  { name: 'p-3', category: 'spacing', description: 'Большие внутренние отступы' },
  { name: 'p-4', category: 'spacing', description: 'Очень большие внутренние отступы' },
  { name: 'p-5', category: 'spacing', description: 'Максимальные внутренние отступы' },

  // Границы
  { name: 'border', category: 'borders', description: 'Граница со всех сторон' },
  { name: 'border-top', category: 'borders', description: 'Граница сверху' },
  { name: 'border-end', category: 'borders', description: 'Граница справа' },
  { name: 'border-bottom', category: 'borders', description: 'Граница снизу' },
  { name: 'border-start', category: 'borders', description: 'Граница слева' },
  { name: 'border-0', category: 'borders', description: 'Без границ' },

  // Скругления
  { name: 'rounded', category: 'borders', description: 'Скругленные углы' },
  { name: 'rounded-top', category: 'borders', description: 'Скругление сверху' },
  { name: 'rounded-end', category: 'borders', description: 'Скругление справа' },
  { name: 'rounded-bottom', category: 'borders', description: 'Скругление снизу' },
  { name: 'rounded-start', category: 'borders', description: 'Скругление слева' },
  { name: 'rounded-circle', category: 'borders', description: 'Круглая форма' },
  { name: 'rounded-pill', category: 'borders', description: 'Форма таблетки' },

  // Тени
  { name: 'shadow-none', category: 'effects', description: 'Без тени' },
  { name: 'shadow-sm', category: 'effects', description: 'Маленькая тень' },
  { name: 'shadow', category: 'effects', description: 'Обычная тень' },
  { name: 'shadow-lg', category: 'effects', description: 'Большая тень' },

  // Flexbox
  { name: 'd-flex', category: 'layout', description: 'Flex контейнер' },
  { name: 'flex-row', category: 'layout', description: 'Горизонтальное направление' },
  { name: 'flex-column', category: 'layout', description: 'Вертикальное направление' },
  { name: 'justify-content-start', category: 'layout', description: 'Выравнивание по началу' },
  { name: 'justify-content-center', category: 'layout', description: 'Выравнивание по центру' },
  { name: 'justify-content-end', category: 'layout', description: 'Выравнивание по концу' },
  { name: 'justify-content-between', category: 'layout', description: 'Распределение между' },
  { name: 'align-items-start', category: 'layout', description: 'Выравнивание элементов по началу' },
  { name: 'align-items-center', category: 'layout', description: 'Выравнивание элементов по центру' },
  { name: 'align-items-end', category: 'layout', description: 'Выравнивание элементов по концу' },

  // Позиционирование
  { name: 'position-static', category: 'position', description: 'Статичное позиционирование' },
  { name: 'position-relative', category: 'position', description: 'Относительное позиционирование' },
  { name: 'position-absolute', category: 'position', description: 'Абсолютное позиционирование' },
  { name: 'position-fixed', category: 'position', description: 'Фиксированное позиционирование' },
  { name: 'position-sticky', category: 'position', description: 'Липкое позиционирование' },

  // Видимость
  { name: 'd-none', category: 'display', description: 'Скрыть элемент' },
  { name: 'd-block', category: 'display', description: 'Блочный элемент' },
  { name: 'd-inline', category: 'display', description: 'Строчный элемент' },
  { name: 'd-inline-block', category: 'display', description: 'Строчно-блочный элемент' },
  { name: 'invisible', category: 'display', description: 'Невидимый элемент' },
  { name: 'visible', category: 'display', description: 'Видимый элемент' }
]

// Tailwind CSS классы
export const tailwindClasses: CSSClass[] = [
  // Цвета текста
  { name: 'text-blue-500', category: 'colors', description: 'Синий цвет текста' },
  { name: 'text-red-500', category: 'colors', description: 'Красный цвет текста' },
  { name: 'text-green-500', category: 'colors', description: 'Зеленый цвет текста' },
  { name: 'text-yellow-500', category: 'colors', description: 'Желтый цвет текста' },
  { name: 'text-purple-500', category: 'colors', description: 'Фиолетовый цвет текста' },
  { name: 'text-gray-500', category: 'colors', description: 'Серый цвет текста' },
  { name: 'text-white', category: 'colors', description: 'Белый цвет текста' },
  { name: 'text-black', category: 'colors', description: 'Черный цвет текста' },

  // Цвета фона
  { name: 'bg-blue-500', category: 'backgrounds', description: 'Синий цвет фона' },
  { name: 'bg-red-500', category: 'backgrounds', description: 'Красный цвет фона' },
  { name: 'bg-green-500', category: 'backgrounds', description: 'Зеленый цвет фона' },
  { name: 'bg-yellow-500', category: 'backgrounds', description: 'Желтый цвет фона' },
  { name: 'bg-purple-500', category: 'backgrounds', description: 'Фиолетовый цвет фона' },
  { name: 'bg-gray-500', category: 'backgrounds', description: 'Серый цвет фона' },
  { name: 'bg-white', category: 'backgrounds', description: 'Белый цвет фона' },
  { name: 'bg-transparent', category: 'backgrounds', description: 'Прозрачный фон' },

  // Размеры текста
  { name: 'text-xs', category: 'typography', description: 'Очень маленький текст' },
  { name: 'text-sm', category: 'typography', description: 'Маленький текст' },
  { name: 'text-base', category: 'typography', description: 'Обычный текст' },
  { name: 'text-lg', category: 'typography', description: 'Большой текст' },
  { name: 'text-xl', category: 'typography', description: 'Очень большой текст' },
  { name: 'text-2xl', category: 'typography', description: 'Огромный текст' },

  // Вес шрифта
  { name: 'font-light', category: 'typography', description: 'Тонкий шрифт' },
  { name: 'font-normal', category: 'typography', description: 'Обычный шрифт' },
  { name: 'font-medium', category: 'typography', description: 'Средний шрифт' },
  { name: 'font-semibold', category: 'typography', description: 'Полужирный шрифт' },
  { name: 'font-bold', category: 'typography', description: 'Жирный шрифт' },

  // Отступы
  { name: 'm-0', category: 'spacing', description: 'Без внешних отступов' },
  { name: 'm-1', category: 'spacing', description: 'Маленькие внешние отступы' },
  { name: 'm-2', category: 'spacing', description: 'Средние внешние отступы' },
  { name: 'm-4', category: 'spacing', description: 'Большие внешние отступы' },
  { name: 'm-8', category: 'spacing', description: 'Очень большие внешние отступы' },

  { name: 'p-0', category: 'spacing', description: 'Без внутренних отступов' },
  { name: 'p-1', category: 'spacing', description: 'Маленькие внутренние отступы' },
  { name: 'p-2', category: 'spacing', description: 'Средние внутренние отступы' },
  { name: 'p-4', category: 'spacing', description: 'Большие внутренние отступы' },
  { name: 'p-8', category: 'spacing', description: 'Очень большие внутренние отступы' },

  // Границы
  { name: 'border', category: 'borders', description: 'Граница со всех сторон' },
  { name: 'border-t', category: 'borders', description: 'Граница сверху' },
  { name: 'border-r', category: 'borders', description: 'Граница справа' },
  { name: 'border-b', category: 'borders', description: 'Граница снизу' },
  { name: 'border-l', category: 'borders', description: 'Граница слева' },
  { name: 'border-0', category: 'borders', description: 'Без границ' },

  // Скругления
  { name: 'rounded', category: 'borders', description: 'Скругленные углы' },
  { name: 'rounded-sm', category: 'borders', description: 'Маленькое скругление' },
  { name: 'rounded-md', category: 'borders', description: 'Среднее скругление' },
  { name: 'rounded-lg', category: 'borders', description: 'Большое скругление' },
  { name: 'rounded-full', category: 'borders', description: 'Круглая форма' },

  // Тени
  { name: 'shadow-none', category: 'effects', description: 'Без тени' },
  { name: 'shadow-sm', category: 'effects', description: 'Маленькая тень' },
  { name: 'shadow', category: 'effects', description: 'Обычная тень' },
  { name: 'shadow-md', category: 'effects', description: 'Средняя тень' },
  { name: 'shadow-lg', category: 'effects', description: 'Большая тень' },
  { name: 'shadow-xl', category: 'effects', description: 'Очень большая тень' },

  { name: 'text-dark-gray', category: 'colors', description: 'Темно-серый' }
]

// Объединенный список всех классов
export const allClasses: CSSClass[] = [...bootstrapClasses, ...tailwindClasses]

// Функции для работы с классами
export function getClassesByCategory(category: string): CSSClass[] {
  return allClasses.filter(cls => cls.category === category)
}

export function searchClasses(query: string): CSSClass[] {
  const lowerQuery = query.toLowerCase()
  return allClasses.filter(cls =>
    cls.name.toLowerCase().includes(lowerQuery) ||
    cls.description.toLowerCase().includes(lowerQuery)
  )
}

export function getCategories(): string[] {
  const categories = new Set(allClasses.map(cls => cls.category))
  return Array.from(categories).sort()
}

export function getClassNames(): string[] {
  return allClasses.map(cls => cls.name)
}

// Сохранение и загрузка пользовательских классов
export function saveCustomClasses(classes: CSSClass[]): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('customCSSClasses', JSON.stringify(classes))
  }
}

export function loadCustomClasses(): CSSClass[] {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('customCSSClasses')
    return stored ? JSON.parse(stored) : []
  }
  return []
}

export function getAllClasses(): CSSClass[] {
  const custom = loadCustomClasses()
  return [...allClasses, ...custom]
}

export function addCustomClass(cssClass: CSSClass): void {
  const customClasses = loadCustomClasses()
  customClasses.push(cssClass)
  saveCustomClasses(customClasses)
}

export function removeCustomClass(className: string): void {
  const customClasses = loadCustomClasses()
  const filtered = customClasses.filter(cls => cls.name !== className)
  saveCustomClasses(filtered)
}
