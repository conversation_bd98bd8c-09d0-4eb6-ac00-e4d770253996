<template>
  <div class="flex flex-col gap-4 p-1">
    <div class="flex h-[calc(100vh-1rem)]">
      <!-- Левый сайдбар -->
      <div class="flex" :style="{ width: leftSidebarWidth }">
        <div class="flex flex-col w-full bg-surface-50 dark:bg-surface-800 border-r border-surface-200 dark:border-surface-700">
          
          <!-- Область добавления (70% высоты) -->
          <div class="flex-none" style="height: 70%;">
            <!-- Toolbar для блоков -->
            <div class="structure-toolbar toolbar-container flex items-center p-1 border-b border-surface-200 dark:border-surface-700 bg-white">
              <div class="flex items-center gap-1">
                <!-- Кнопка обновления -->
                <Button
                  v-tooltip.top="'Обновить данные'"
                  icon="pi pi-refresh"
                  class="p-button-sm p-button-text"
                  @click="loadBlockProtoData"
                />

                <!-- Поиск -->
                <div class="relative">
                  <Button
                    icon="pi pi-search"
                    class="p-button-sm p-button-text"
                    @click="toggleBlockSearch"
                  />
                  <div v-if="showBlockSearch" class="search-dropdown">
                    <InputText
                      v-model="blockProtoSearch"
                      placeholder="Поиск блоков..."
                      class="w-full text-xs"
                      style="font-size: 12px"
                    />
                  </div>
                </div>

                <!-- Фильтры -->
                <div class="relative">
                  <Button
                    icon="pi pi-filter"
                    class="p-button-sm p-button-text"
                    @click="toggleBlockFilters"
                  />
                  <div v-if="showBlockFilters" class="filters-dropdown">
                    <MultiSelect
                      v-model="selectedBlockTypes"
                      :options="blockTypeOptions"
                      filter
                      placeholder="Типы блоков"
                      class="w-48 text-xs"
                      display="chip"
                      panel-class="text-xs"
                    />
                  </div>
                </div>

                <!-- Добавить выбранные на холст -->
                <Button
                  v-tooltip.top="'Добавить выбранные на холст'"
                  icon="pi pi-plus"
                  class="p-button-sm p-button-text"
                  :disabled="selectedBlocks.length === 0"
                  @click="addSelectedBlocksToCanvas"
                />
              </div>
            </div>

            <!-- Плитка блоков (3 колонки) -->
            <div class="flex-1 overflow-auto p-2">
              <div class="grid grid-cols-3 gap-2">
                <div
                  v-for="block in filteredBlocks"
                  :key="block.id"
                  class="block-card relative border rounded-lg p-2 cursor-pointer hover:bg-surface-100 dark:hover:bg-surface-700"
                  :class="{ 'bg-blue-50 border-blue-300': selectedBlocks.includes(block.id) }"
                >
                  <!-- Чекбокс -->
                  <Checkbox
                    v-model="selectedBlocks"
                    :value="block.id"
                    class="absolute top-1 left-1"
                  />
                  
                  <!-- Превью изображения -->
                  <div class="w-full h-16 mb-1 bg-surface-200 rounded flex items-center justify-center">
                    <img
                      v-if="block.sketch"
                      :src="'http://localhost:8055/assets/' + block.sketch"
                      :alt="block.title"
                      class="w-full h-full object-cover rounded"
                    >
                    <i v-else class="pi pi-image text-surface-400"/>
                  </div>
                  
                  <!-- Номер и название -->
                  <div class="text-xs text-surface-600 mb-1" style="font-size: 8px;">{{ block.number }}</div>
                  <div class="text-xs font-medium truncate" style="font-size: 9.5px;">{{ block.title }}</div>
                  
                  <!-- Кнопка действий -->
                  <Button
                    icon="pi pi-ellipsis-h"
                    class="absolute top-1 right-1 p-button-sm p-button-text"
                    @click="showBlockActions(block, $event)"
                  />

                  <!-- Быстрое добавление -->
                  <Button
                    icon="pi pi-plus"
                    class="absolute bottom-1 right-1 p-button-sm p-button-text"
                    @click="addSingleBlockToCanvas(block)"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Область схемы (30% высоты) -->
          <div class="flex-none border-t border-surface-200 dark:border-surface-700" style="height: 30%;">
            <div class="p-2 bg-surface-100 dark:bg-surface-900 text-xs font-medium border-b">
              Схема страницы
            </div>
            <div class="flex-1 overflow-auto p-2">
              <div
                v-for="(block, index) in canvasBlocks"
                :key="block.tempId"
                class="schema-block flex items-center justify-between p-2 mb-1 bg-white dark:bg-surface-800 border rounded cursor-pointer hover:bg-surface-50"
                @click="selectSchemaBlock(block)"
              >
                <span class="text-xs truncate flex-1">{{ block.title }}</span>
                <div class="flex gap-1">
                  <Button
                    v-if="index > 0"
                    icon="pi pi-arrow-up"
                    class="p-button-sm p-button-text"
                    @click.stop="moveBlockUp(index)"
                  />
                  <Button
                    v-if="index < canvasBlocks.length - 1"
                    icon="pi pi-arrow-down"
                    class="p-button-sm p-button-text"
                    @click.stop="moveBlockDown(index)"
                  />
                  <Button
                    icon="pi pi-copy"
                    class="p-button-sm p-button-text"
                    @click.stop="duplicateBlock(index)"
                  />
                  <Button
                    icon="pi pi-pencil"
                    class="p-button-sm p-button-text"
                    @click.stop="editSchemaBlock(block)"
                  />
                  <Button
                    icon="pi pi-trash"
                    class="p-button-sm p-button-text p-button-danger"
                    @click.stop="removeBlock(index)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Центральная часть -->
      <div class="flex flex-col" :style="{ width: centerWidth }">
        <!-- Верхняя строка управления -->
        <div class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border-b border-surface-200 dark:border-surface-700">
          <!-- Undo/Redo -->
          <div class="flex gap-1">
            <Button
              v-tooltip.top="'Отменить'"
              icon="pi pi-undo"
              class="p-button-sm p-button-text"
              :disabled="!canUndo"
              @click="undo"
            />
            <Button
              v-tooltip.top="'Повторить'"
              icon="pi pi-redo"
              class="p-button-sm p-button-text"
              :disabled="!canRedo"
              @click="redo"
            />
          </div>

          <!-- Поля страницы -->
          <InputText
            v-model="pageData.number"
            placeholder="Номер"
            class="w-20 text-xs"
            style="font-size: 11px"
          />
          <InputText
            v-model="pageData.title"
            placeholder="Название страницы"
            class="flex-1 text-xs"
            style="font-size: 12px"
          />
          <MultiSelect
            v-model="pageData.tags"
            :options="pageTagsOptions"
            display="chip"
            class="text-xs max-w-sm"
            filter
            placeholder="Теги"
            panel-class="text-xs"
          />
          <MultiSelect
            v-model="pageData.wpage_type"
            :options="pageTypeOptions"
            display="chip"
            class="text-xs max-w-sm"
            filter
            placeholder="Тип страницы"
            panel-class="text-xs"
          />

          <!-- Viewport переключатели -->
          <div class="flex gap-1">
            <Button
              v-for="device in devices"
              :key="device.value"
              :icon="device.icon"
              :class="[
                'p-button-sm',
                activeDevice === device.value ? 'p-button-info' : 'p-button-outlined'
              ]"
              @click="setActiveDevice(device.value)"
            />
          </div>

          <!-- Действия -->
          <div class="flex gap-1">
            <Button
              v-tooltip.top="'Скачать'"
              icon="pi pi-download"
              class="p-button-sm p-button-text"
              @click="downloadPage"
            />
            <Button
              v-tooltip.top="'Просмотр'"
              icon="pi pi-eye"
              class="p-button-sm p-button-text"
              @click="previewPage"
            />
            <Button
              v-tooltip.top="'Сохранить страницу'"
              icon="pi pi-save"
              class="p-button-sm p-button-success"
              @click="savePage"
            />
            <Button
              v-tooltip.top="'Сохранить блоки'"
              icon="pi pi-save"
              class="p-button-sm p-button-info"
              @click="saveBlocks"
            />
            <Button
              v-tooltip.top="'Сохранить страницу и блоки'"
              icon="pi pi-save"
              class="p-button-sm p-button-warning"
              @click="savePageAndBlocks"
            />
          </div>
        </div>

        <!-- Холст (iframe) -->
        <div class="flex-1 flex justify-center p-2 bg-surface-100 dark:bg-surface-900">
          <div
            :class="{
              'mx-auto max-w-[375px] border rounded-lg': activeDevice === 'mobile',
              'mx-auto max-w-[768px] border rounded-lg': activeDevice === 'tablet',
              'mx-auto max-w-[1440px] border rounded-lg': activeDevice === 'desktop',
            }"
            style="width: 100%; height: 100%;"
          >
            <iframe
              v-if="compiledHtml"
              :srcdoc="getFullHtml()"
              class="w-full h-full border-0 rounded-lg"
            />
            <div v-else class="flex items-center justify-center h-full text-surface-500">
              <div class="text-center">
                <i class="pi pi-file-o text-4xl mb-2"/>
                <p>Добавьте блоки для создания страницы</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Нижние табы -->
        <div class="flex-none h-64 border-t border-surface-200 dark:border-surface-700">
          <TabView v-model:active-index="activeTabIndex">
            <TabPanel header="HTML">
              <PrismEditorWithCopy
                v-model="compiledHtml"
                :highlight="highlightHtml"
                placeholder="HTML код страницы"
                field-name="HTML"
                class="text-xs"
                style="height: 200px;"
              />
            </TabPanel>
            <TabPanel header="CSS + JS">
              <div class="flex gap-2 h-full">
                <div class="flex-1">
                  <div class="text-xs mb-1 flex items-center gap-2">
                    CSS
                    <Button
                      label="BS"
                      class="p-button-sm p-button-outlined text-xs"
                      @click="addBootstrap"
                    />
                  </div>
                  <PrismEditorWithCopy
                    v-model="pageData.css"
                    :highlight="highlightCss"
                    placeholder="CSS код"
                    field-name="CSS"
                    class="text-xs"
                    style="height: 160px;"
                  />
                </div>
                <div class="flex-1">
                  <div class="text-xs mb-1 flex items-center gap-2">
                    JavaScript
                    <Button
                      label="VJ"
                      class="p-button-sm p-button-outlined text-xs"
                      @click="addVueJs"
                    />
                  </div>
                  <PrismEditorWithCopy
                    v-model="pageData.js"
                    :highlight="highlightJs"
                    placeholder="JavaScript код"
                    field-name="JavaScript"
                    class="text-xs"
                    style="height: 160px;"
                  />
                </div>
              </div>
            </TabPanel>
          </TabView>
        </div>
      </div>

      <!-- Правый сайдбар -->
      <div class="flex" :style="{ width: rightSidebarWidth }">
        <div class="flex flex-col w-full bg-surface-50 dark:bg-surface-800 border-l border-surface-200 dark:border-surface-700">
          <div class="p-2 bg-surface-100 dark:bg-surface-900 text-xs font-medium border-b">
            {{ sidebarMode === 'edit-canvas-block' ? 'Редактирование блока' : 'Редактирование прототипа' }}
          </div>
          <div class="flex-1 overflow-auto p-2">
            <!-- Режим редактирования добавленного блока -->
            <div v-if="sidebarMode === 'edit-canvas-block' && selectedBlock">
              <div class="space-y-3">
                <!-- Базовая информация -->
                <div class="space-y-2">
                  <div class="flex gap-2">
                    <div class="field w-1/4">
                      <InputText
                        v-model="selectedBlock.number"
                        placeholder="Номер*"
                        class="w-full text-xs"
                        style="font-size: 10px"
                      />
                    </div>
                    <div class="field w-3/4">
                      <InputText
                        v-model="selectedBlock.title"
                        placeholder="Название*"
                        class="w-full text-xs"
                        style="font-size: 11px"
                      />
                    </div>
                  </div>

                  <div class="field">
                    <Textarea
                      v-model="selectedBlock.description"
                      rows="2"
                      class="w-full text-xs"
                      placeholder="Описание"
                      style="font-size: 10px"
                    />
                  </div>

                  <div class="field">
                    <MultiSelect
                      v-model="selectedBlock.block_type"
                      :options="blockTypeOptions"
                      placeholder="Типы блока"
                      display="chip"
                      class="text-xs w-full"
                      panel-class="text-xs"
                      style="font-size: 11px"
                    />
                  </div>

                  <div class="field">
                    <Textarea
                      v-model="selectedBlock.notes"
                      rows="2"
                      class="w-full text-xs"
                      placeholder="Заметки"
                      style="font-size: 10px"
                    />
                  </div>
                </div>

                <!-- Редактор JSON -->
                <div class="field">
                  <label class="text-xs font-medium mb-1 block">JSON данные</label>
                  <PrismEditorWithCopy
                    v-model="selectedBlock.json"
                    :highlight="highlightJson"
                    placeholder="JSON данные блока"
                    field-name="JSON"
                    class="text-xs"
                    style="height: 150px;"
                  />
                </div>

                <!-- Редактор HBS -->
                <div class="field">
                  <label class="text-xs font-medium mb-1 block">HBS шаблон</label>
                  <PrismEditorWithCopy
                    v-model="selectedBlock.hbs"
                    :highlight="highlightHtml"
                    placeholder="HBS шаблон блока"
                    field-name="HBS"
                    class="text-xs"
                    style="height: 150px;"
                  />
                </div>

                <!-- Кнопки действий -->
                <div class="flex gap-2 pt-2">
                  <Button
                    label="Применить"
                    icon="pi pi-check"
                    class="p-button-sm p-button-success flex-1"
                    @click="applyBlockChanges"
                  />
                  <Button
                    label="Сброс"
                    icon="pi pi-refresh"
                    class="p-button-sm p-button-outlined flex-1"
                    @click="resetBlockChanges"
                  />
                </div>
              </div>
            </div>

            <!-- Заглушка когда блок не выбран -->
            <div v-else-if="sidebarMode === 'edit-canvas-block'" class="text-center text-surface-500 mt-8">
              <i class="pi pi-info-circle text-2xl mb-2"/>
              <p class="text-xs">Выберите блок из схемы для редактирования</p>
            </div>

            <!-- Режим редактирования блока из базы -->
            <div v-else-if="sidebarMode === 'edit-proto-block'" class="text-center text-surface-500 mt-8">
              <i class="pi pi-pencil text-2xl mb-2"/>
              <p class="text-xs">Редактирование блока из базы</p>
              <p class="text-xs text-surface-400 mt-2">Используйте боковую панель</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Диалоги и всплывающие окна -->
    <Toast />
    <Dialog v-model:visible="showPreview" modal header="Предпросмотр страницы" :style="{ width: '90vw', height: '90vh' }">
      <iframe :srcdoc="getFullHtml()" class="w-full h-full border-0" />
    </Dialog>

    <!-- Контекстное меню для блоков -->
    <ContextMenu ref="blockContextMenu" :model="blockContextMenuItems" />

    <!-- Диалог редактирования блока из базы -->
    <WBlockProtoSidebarV2
      v-if="editingProtoBlock"
      v-model:visible="showProtoBlockSidebar"
      :item="editingProtoBlock"
      @save="handleProtoBlockSave"
      @cancel="closeProtoBlockSidebar"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useDirectusItems } from '#imports'
import handlebars from 'handlebars'
import Prism from 'prismjs'

// Определяем название страницы
definePageMeta({
  title: 'Page Builder 2.0'
})

// Composables
const toast = useToast()
const { getItems: getBlockProtoItems, createItems, updateItem, deleteItem } = useDirectusItems()
const { getItems: getPageItems } = useDirectusItems()

// Reactive data
const leftSidebarWidth = ref('400px')
const rightSidebarWidth = ref('400px')
const centerWidth = computed(() => 'calc(100vw - 800px)')

// Состояние поиска и фильтров
const showBlockSearch = ref(false)
const showBlockFilters = ref(false)
const blockProtoSearch = ref('')
const selectedBlockTypes = ref([])
const blockTypeOptions = ref([])

// Данные блоков
const blockProtoData = ref([])
const selectedBlocks = ref([])
const canvasBlocks = ref([])

// Данные страницы
const pageData = ref({
  number: '',
  title: '',
  tags: [],
  wpage_type: [],
  css: '',
  js: ''
})

// Опции для селектов
const pageTagsOptions = ref([])
const pageTypeOptions = ref([])

// Состояние устройств
const activeDevice = ref('desktop')
const devices = [
  { value: 'mobile', icon: 'pi pi-mobile' },
  { value: 'tablet', icon: 'pi pi-tablet' },
  { value: 'desktop', icon: 'pi pi-desktop' }
]

// Состояние табов
const activeTabIndex = ref(0)

// HTML компиляция
const compiledHtml = ref('')

// История для undo/redo
const history = ref([])
const historyIndex = ref(-1)
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// Правый сайдбар
const sidebarMode = ref('edit-canvas-block') // 'edit-canvas-block' | 'edit-proto-block'
const selectedBlock = ref(null)

// Диалоги
const showPreview = ref(false)
const showProtoBlockSidebar = ref(false)
const editingProtoBlock = ref(null)

// Контекстное меню
const blockContextMenu = ref(null)
const blockContextMenuItems = ref([
  {
    label: 'Добавить на холст',
    icon: 'pi pi-plus',
    command: () => addSingleBlockToCanvas(selectedContextBlock.value)
  },
  {
    label: 'Редактировать',
    icon: 'pi pi-pencil',
    command: () => editProtoBlock(selectedContextBlock.value)
  },
  {
    label: 'Дублировать',
    icon: 'pi pi-copy',
    command: () => duplicateProtoBlock(selectedContextBlock.value)
  },
  {
    separator: true
  },
  {
    label: 'Удалить',
    icon: 'pi pi-trash',
    command: () => deleteProtoBlock(selectedContextBlock.value)
  }
])
const selectedContextBlock = ref(null)

// Computed
const filteredBlocks = computed(() => {
  let filtered = blockProtoData.value

  // Поиск
  if (blockProtoSearch.value) {
    const search = blockProtoSearch.value.toLowerCase()
    filtered = filtered.filter(block => 
      block.title?.toLowerCase().includes(search) ||
      block.number?.toLowerCase().includes(search)
    )
  }

  // Фильтр по типам
  if (selectedBlockTypes.value.length > 0) {
    filtered = filtered.filter(block => 
      block.block_type?.some(type => selectedBlockTypes.value.includes(type))
    )
  }

  // Сортировка по number
  return filtered.sort((a, b) => {
    const numA = a.number || ''
    const numB = b.number || ''
    return numA.localeCompare(numB, undefined, { numeric: true })
  })
})

// Methods для работы с данными
const loadBlockProtoData = async () => {
  try {
    console.log('Загрузка данных блоков...')
    const blocks = await getBlockProtoItems({
      collection: 'wblock_proto',
      params: {
        fields: ['*'],
        sort: ['number', 'title'],
        limit: -1
      }
    })

    blockProtoData.value = blocks || []
    console.log('Загружено блоков:', blockProtoData.value.length)

    // Загружаем опции для фильтров
    await loadFilterOptions()

  } catch (error) {
    console.error('Ошибка загрузки блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить блоки',
      life: 3000
    })
  }
}

const loadFilterOptions = async () => {
  try {
    // Загружаем уникальные типы блоков
    const uniqueBlockTypes = new Set()
    blockProtoData.value.forEach(block => {
      if (block.block_type && Array.isArray(block.block_type)) {
        block.block_type.forEach(type => uniqueBlockTypes.add(type))
      }
    })
    blockTypeOptions.value = Array.from(uniqueBlockTypes).sort()

    // Загружаем опции для страниц
    const pages = await getPageItems({
      collection: 'wpage',
      params: {
        fields: ['tags', 'wpage_type'],
        limit: -1
      }
    })

    const uniqueTags = new Set()
    const uniquePageTypes = new Set()

    pages?.forEach(page => {
      if (page.tags && Array.isArray(page.tags)) {
        page.tags.forEach(tag => uniqueTags.add(tag))
      }
      if (page.wpage_type && Array.isArray(page.wpage_type)) {
        page.wpage_type.forEach(type => uniquePageTypes.add(type))
      }
    })

    pageTagsOptions.value = Array.from(uniqueTags).sort()
    pageTypeOptions.value = Array.from(uniquePageTypes).sort()

  } catch (error) {
    console.error('Ошибка загрузки опций:', error)
  }
}

const toggleBlockSearch = () => {
  showBlockSearch.value = !showBlockSearch.value
  if (!showBlockSearch.value) {
    blockProtoSearch.value = ''
  }
}

const toggleBlockFilters = () => {
  showBlockFilters.value = !showBlockFilters.value
}

const addSelectedBlocksToCanvas = () => {
  if (selectedBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите блоки для добавления',
      life: 3000
    })
    return
  }

  const blocksToAdd = blockProtoData.value.filter(block =>
    selectedBlocks.value.includes(block.id)
  )

  blocksToAdd.forEach(block => {
    const canvasBlock = {
      tempId: Date.now() + Math.random(), // Уникальный временный ID
      id: block.id,
      number: block.number,
      title: block.title,
      description: block.description,
      hbs: block.hbs,
      json: block.json,
      css: block.css,
      js: block.js,
      block_type: block.block_type,
      collection: block.collection,
      sketch: block.sketch
    }

    canvasBlocks.value.push(canvasBlock)
  })

  // Очищаем выбор
  selectedBlocks.value = []

  // Компилируем HTML
  compileHtml()

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: `Добавлено блоков: ${blocksToAdd.length}`,
    life: 3000
  })
}

const addSingleBlockToCanvas = (block) => {
  const canvasBlock = {
    tempId: Date.now() + Math.random(),
    id: block.id,
    number: block.number,
    title: block.title,
    description: block.description,
    hbs: block.hbs,
    json: block.json,
    css: block.css,
    js: block.js,
    block_type: block.block_type,
    collection: block.collection,
    sketch: block.sketch
  }

  canvasBlocks.value.push(canvasBlock)
  compileHtml()

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: `Блок "${block.title}" добавлен`,
    life: 3000
  })
}

const showBlockActions = (block, event) => {
  selectedContextBlock.value = block
  blockContextMenu.value.show(event)
}

const editProtoBlock = (block) => {
  editingProtoBlock.value = { ...block }
  sidebarMode.value = 'edit-proto-block'
  showProtoBlockSidebar.value = true
}

const duplicateProtoBlock = async (block) => {
  try {
    const duplicatedBlock = {
      ...block,
      id: undefined,
      number: block.number + '_copy',
      title: block.title + ' (копия)',
      date_created: undefined,
      date_updated: undefined
    }

    await createItems({
      collection: 'wblock_proto',
      items: [duplicatedBlock]
    })

    await loadBlockProtoData()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Блок продублирован',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка дублирования блока:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось продублировать блок',
      life: 3000
    })
  }
}

const deleteProtoBlock = async (block) => {
  if (!confirm(`Удалить блок "${block.title}"?`)) {
    return
  }

  try {
    await deleteItem({
      collection: 'wblock_proto',
      id: block.id
    })

    await loadBlockProtoData()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Блок удален',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка удаления блока:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось удалить блок',
      life: 3000
    })
  }
}

const handleProtoBlockSave = async (updatedBlock) => {
  try {
    await updateItem({
      collection: 'wblock_proto',
      id: updatedBlock.id,
      item: updatedBlock
    })

    await loadBlockProtoData()
    closeProtoBlockSidebar()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Блок сохранен',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка сохранения блока:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блок',
      life: 3000
    })
  }
}

const closeProtoBlockSidebar = () => {
  showProtoBlockSidebar.value = false
  editingProtoBlock.value = null
  sidebarMode.value = 'edit-canvas-block'
}

// Функции для правого сайдбара
const applyBlockChanges = () => {
  if (!selectedBlock.value) return

  // Применяем изменения и перекомпилируем
  compileHtml()

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Изменения применены',
    life: 3000
  })
}

const resetBlockChanges = () => {
  if (!selectedBlock.value) return

  // Находим оригинальный блок и восстанавливаем его данные
  const originalBlock = blockProtoData.value.find(block => block.id === selectedBlock.value.id)
  if (originalBlock) {
    Object.assign(selectedBlock.value, {
      number: originalBlock.number,
      title: originalBlock.title,
      description: originalBlock.description,
      hbs: originalBlock.hbs,
      json: originalBlock.json,
      block_type: originalBlock.block_type,
      notes: originalBlock.notes
    })

    compileHtml()

    toast.add({
      severity: 'info',
      summary: 'Сброшено',
      detail: 'Изменения отменены',
      life: 3000
    })
  }
}

// Функция компиляции HTML из блоков
const compileHtml = () => {
  let html = ''
  let css = ''
  let js = ''

  canvasBlocks.value.forEach((block, index) => {
    if (block.hbs) {
      try {
        let blockHtml = block.hbs

        // Если есть JSON данные, компилируем через handlebars
        if (block.json) {
          const template = handlebars.compile(block.hbs)
          const jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
          blockHtml = template(jsonData)
        }

        html += `<!-- Block ${index + 1}: ${block.title} -->\n`
        html += blockHtml
        html += '\n\n'

        // Добавляем CSS и JS без дублирования
        if (block.css && !css.includes(block.css)) {
          css += (css ? '\n' : '') + block.css
        }

        if (block.js && !js.includes(block.js)) {
          js += (js ? '\n' : '') + block.js
        }

      } catch (error) {
        console.error('Ошибка компиляции блока:', block.title, error)
        html += `<div style="color:red; padding:10px; border:1px solid red;">Ошибка компиляции блока "${block.title}": ${error.message}</div>\n\n`
      }
    }
  })

  compiledHtml.value = html

  // Обновляем CSS и JS страницы
  if (css && !pageData.value.css.includes(css)) {
    pageData.value.css += (pageData.value.css ? '\n' : '') + css
  }

  if (js && !pageData.value.js.includes(js)) {
    pageData.value.js += (pageData.value.js ? '\n' : '') + js
  }
}

const selectSchemaBlock = (block) => {
  selectedBlock.value = block
  console.log('Выбран блок схемы:', block)
}

const moveBlockUp = (index) => {
  if (index > 0) {
    [canvasBlocks.value[index - 1], canvasBlocks.value[index]] = 
    [canvasBlocks.value[index], canvasBlocks.value[index - 1]]
  }
}

const moveBlockDown = (index) => {
  if (index < canvasBlocks.value.length - 1) {
    [canvasBlocks.value[index], canvasBlocks.value[index + 1]] = 
    [canvasBlocks.value[index + 1], canvasBlocks.value[index]]
  }
}

const duplicateBlock = (index) => {
  const block = { ...canvasBlocks.value[index], tempId: Date.now() }
  canvasBlocks.value.splice(index + 1, 0, block)
}

const editSchemaBlock = (block) => {
  selectedBlock.value = block
  sidebarMode.value = 'edit-canvas-block'
}

const removeBlock = (index) => {
  canvasBlocks.value.splice(index, 1)
}

const setActiveDevice = (device) => {
  activeDevice.value = device
}

const undo = () => {
  if (canUndo.value && historyIndex.value > 0) {
    historyIndex.value--
    const state = history.value[historyIndex.value]
    canvasBlocks.value = JSON.parse(JSON.stringify(state.canvasBlocks))
    pageData.value = JSON.parse(JSON.stringify(state.pageData))
    compileHtml()
  }
}

const redo = () => {
  if (canRedo.value && historyIndex.value < history.value.length - 1) {
    historyIndex.value++
    const state = history.value[historyIndex.value]
    canvasBlocks.value = JSON.parse(JSON.stringify(state.canvasBlocks))
    pageData.value = JSON.parse(JSON.stringify(state.pageData))
    compileHtml()
  }
}

const saveToHistory = () => {
  const state = {
    canvasBlocks: JSON.parse(JSON.stringify(canvasBlocks.value)),
    pageData: JSON.parse(JSON.stringify(pageData.value))
  }

  // Удаляем все состояния после текущего индекса
  history.value = history.value.slice(0, historyIndex.value + 1)

  // Добавляем новое состояние
  history.value.push(state)

  // Ограничиваем историю 30 шагами
  if (history.value.length > 30) {
    history.value.shift()
  } else {
    historyIndex.value++
  }
}

const downloadPage = () => {
  const html = getFullHtml()
  const blob = new Blob([html], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${pageData.value.title || 'page'}.html`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Страница скачана',
    life: 3000
  })
}

const previewPage = () => {
  showPreview.value = true
}

const savePage = () => {
  console.log('Сохранение страницы...')
  // TODO: Реализовать в следующей задаче
}

const saveBlocks = () => {
  console.log('Сохранение блоков...')
  // TODO: Реализовать в следующей задаче
}

const savePageAndBlocks = () => {
  console.log('Сохранение страницы и блоков...')
  // TODO: Реализовать в следующей задаче
}

const addBootstrap = () => {
  const bootstrapCss = `
/* Bootstrap CSS */
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
`

  if (!pageData.value.css.includes('bootstrap')) {
    pageData.value.css = bootstrapCss + '\n' + pageData.value.css

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Bootstrap CSS добавлен',
      life: 3000
    })
  } else {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Bootstrap уже добавлен',
      life: 3000
    })
  }
}

const addVueJs = () => {
  const vueJs = `
/* Vue.js */
// Vue.js 3 CDN
const { createApp } = Vue;
`

  if (!pageData.value.js.includes('Vue')) {
    pageData.value.js = vueJs + '\n' + pageData.value.js

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Vue.js добавлен',
      life: 3000
    })
  } else {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Vue.js уже добавлен',
      life: 3000
    })
  }
}

const getFullHtml = () => {
  const title = pageData.value.title || 'Страница'
  const css = pageData.value.css || ''
  const html = compiledHtml.value || ''
  const js = pageData.value.js || ''
  const scriptOpen = '<' + 'script>'
  const scriptClose = '</' + 'script>'

  return [
    '<!DOCTYPE html>',
    '<html>',
    '<head>',
    '  <meta charset="utf-8">',
    '  <meta name="viewport" content="width=device-width, initial-scale=1">',
    '  <title>' + title + '</title>',
    '  <style>' + css + '</style>',
    '</head>',
    '<body>',
    '  ' + html,
    '  ' + scriptOpen + js + scriptClose,
    '</body>',
    '</html>'
  ].join('\n')
}

// Функции подсветки синтаксиса
const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

// Watchers
watch(canvasBlocks, () => {
  compileHtml()
  saveToHistory()
}, { deep: true })

watch(pageData, () => {
  saveToHistory()
}, { deep: true })

// Lifecycle
onMounted(() => {
  loadBlockProtoData()
})
</script>

<style scoped>
.search-dropdown,
.filters-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-width: 200px;
}

.block-card {
  transition: all 0.2s ease;
}

.block-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.schema-block {
  transition: all 0.2s ease;
}

.schema-block:hover {
  transform: translateX(2px);
}

.toolbar-container {
  min-height: 40px;
}
</style>
