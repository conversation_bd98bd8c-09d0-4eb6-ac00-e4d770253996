// server/api/split-html-files-simple.js
import { defineEventHandler, readBody, createError } from 'h3';
import { load } from 'cheerio';
import { htmlToHandlebarsAndJson } from '../utils/htmlToTemplate-simple.js';

/**
 * Упрощенная версия обработчика для разделения HTML файлов
 * Фокусируется только на корректной обработке полей html и hbs
 * Сохраняет оригинальное форматирование атрибутов, особенно data-атрибутов с JSON
 */
export default defineEventHandler(async (event) => {
  try {
    const { html } = await readBody(event);
    
    if (!html || html.trim().length === 0) {
      return {
        success: false,
        error: 'Пустой HTML для обработки'
      };
    }
    
    // Разделяем HTML на блоки
    const blocks = splitHtmlIntoBlocks(html);
    
    if (blocks.length === 0) {
      return {
        success: false,
        error: 'Не найдено блоков для разделения'
      };
    }
    
    // Обрабатываем каждый блок
    const results = [];
    
    for (let i = 0; i < blocks.length; i++) {
      const block = blocks[i];
      
      // Пропускаем пустые или слишком маленькие блоки
      if (!block.trim() || block.length < 100) {
        continue;
      }
      
      // Используем упрощенную функцию для конвертации HTML в handlebars и JSON
      const result = htmlToHandlebarsAndJson(block);
      
      // Добавляем результат в список
      results.push({
        blockNumber: i + 1,
        html: result.html,
        hbs: result.hbsTemplate,
        json: result.jsonData,
        success: result.success
      });
    }
    
    return {
      success: true,
      blocksProcessed: results.length,
      results
    };
    
  } catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: error.message
    });
  }
});

/**
 * Функция для разделения HTML на блоки
 * @param {string} html - HTML-код для разделения
 * @returns {Array} Массив блоков HTML
 */
function splitHtmlIntoBlocks(html) {
  if (!html || html.length < 100) {
    return [];
  }
  
  // Используем cheerio для парсинга HTML
  const $ = load(html, {
    decodeEntities: false,  // Критически важно: не декодируем сущности, чтобы сохранить оригинальные кавычки в data-атрибутах с JSON
    xmlMode: false,
    _useHtmlParser2: true
  });
  
  // Важно: при использовании cheerio для парсинга HTML, мы должны быть осторожны с атрибутами,
  // содержащими JSON, так как cheerio может автоматически экранировать кавычки внутри атрибутов.
  // Функция decodeHtmlEntities в htmlToTemplate-simple.js обрабатывает это корректно.
  
  // Разделитель для блоков (только header, section и footer)
  const dividers = [
    'header',
    'section',
    'footer'
  ];
  
  // Собираем блоки
  const blocks = [];
  
  // Для каждого разделителя находим элементы и добавляем их как отдельные блоки
  dividers.forEach(selector => {
    try {
      const elements = $(selector);
      
      elements.each((i, element) => {
        // Проверяем, что элемент достаточно большой, чтобы быть блоком
        const html = $(element).html();
        if (html && html.length > 100) {
          // Добавляем весь элемент вместе с его обрамляющим тегом
          const elementHtml = $.html(element);
          blocks.push(elementHtml);
          
          // Удаляем элемент, чтобы не добавлять его дважды
          $(element).remove();
        }
      });
    } catch (e) {
      // Пропускаем ошибки при обработке селектора
    }
  });
  
  // Добавляем оставшуюся часть, если она достаточно большая
  const remainingBody = $('body').html();
  if (remainingBody && remainingBody.length > 100) {
    // Оборачиваем оставшуюся часть в div
    const wrapperElement = $('<div class="block-wrapper"></div>');
    wrapperElement.html(remainingBody);
    blocks.push($.html(wrapperElement));
  }
  
  // Если не нашли ни одного блока, возвращаем весь HTML как один блок
  if (blocks.length === 0 && html.length > 100) {
    blocks.push(html);
  }
  
  return blocks;
}
