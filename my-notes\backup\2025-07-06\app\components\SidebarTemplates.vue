<template>
  <aside class="sidebar left h-full flex flex-col">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-semibold">Шаблоны</h2>
      <Button 
        :label="viewMode === 'compact' ? '<>' : '><'" 
        class="p-button-text p-button-sm" 
        @click="toggleViewMode" 
      />
    </div>

    <div class="inline-flex gap-2 mb-2">
      <div class="flex-1">
        
        <InputText v-model="search" placeholder="Поиск..." class="w-full text-xs p-1" />
      </div>
      
      <MultiSelect 
        v-model="selectedTags" 
        :options="availableTags" 
        placeholder="Теги" 
        class="flex-1"
        display="chip"
      />
      
    </div>

    <div class="flex-1 overflow-hidden">
      <div class="flex-container">
        <div class="table-container">
          <DataTable 
            v-model:selection="selectedItem"
            :value="filteredTemplates" 
            :scrollable="true" 
            scroll-height="flex"
            class="text-xs"
            :rows="20"
            :paginator="true"
            :row-hover="true"
            striped-rows
            :sort-field="'art'"
            :sort-order="1"
            responsive-layout="scroll"
            @row-click="onRowClick"
          >
            <!-- Колонка для добавления на холст -->
            <Column style="width: 0.5rem">
              <template #body="{ data }">
                <Button
                  v-tooltip.top="'Добавить на холст'"
                  label="+"
                  class="p-button-text p-button-sm"
                  @click="addToCanvas(data)"
                />
              </template>
            </Column>
            
            <!-- Колонка для drag & drop -->
            <Column style="width: 0.5rem">
              <template #body="{ data }">
                <i
                  v-tooltip.top="'Перетащите на холст'" 
                  class="cursor-move"
                  draggable="true"
                  @dragstart="dragStart($event, data)"
                >
                  ⋮⋮
                </i>
              </template>
            </Column>
            
            <!-- Базовые колонки -->
            <Column field="art" header="Art" :sortable="true" />
            <Column field="title" header="Title" :sortable="true" />
            
            <!-- Дополнительные колонки для полного режима -->
            <template v-if="viewMode === 'full'">
              <Column field="description" header="Description" />
              <Column field="tags" header="Tags">
                <template #body="{ data }">
                  <div class="flex flex-wrap gap-1">
                    <Tag 
                      v-for="tag in data.tags" 
                      :key="tag" 
                      :value="tag"
                      class="text-xs"
                      @click="addTagToFilter(tag)"
                    />
                  </div>
                </template>
              </Column>
              <Column field="hbs" header="HBS">
                <template #body="{ data }">
                  <div class="flex items-center gap-2">
                    <div class="max-h-32 overflow-y-auto flex-1">
                      <pre class="text-xs whitespace-pre-wrap">{{ data.hbs }}</pre>
                    </div>
                    <Button 
                      v-tooltip.top="'Копировать в буфер'" 
                      label="📋" 
                      class="p-button-text p-button-sm"
                      @click="copyToClipboard(data.hbs)"
                    />
                  </div>
                </template>
              </Column>
              <Column field="wpage" header="Pages">
                <template #body="{ data }">
                  <div class="flex gap-1">
                    <Tag 
                      v-for="title in getRelatedTitles(data.wpage, 'wpage')" 
                      :key="title" 
                      :value="title"
                      severity="info"
                      class="text-xs"
                    />
                  </div>
                </template>
              </Column>
              <Column field="wblock" header="Blocks">
                <template #body="{ data }">
                  <div class="flex gap-1">
                    <Tag 
                      v-for="title in getRelatedTitles(data.wblock, 'wblock')" 
                      :key="title" 
                      :value="title"
                      severity="success"
                      class="text-xs"
                    />
                  </div>
                </template>
              </Column>
              <Column field="wjson" header="Data">
                <template #body="{ data }">
                  <div class="flex gap-1">
                    <Tag 
                      v-for="title in getRelatedTitles(data.wjson, 'wjson')" 
                      :key="title" 
                      :value="title"
                      severity="warning"
                      class="text-xs"
                    />
                  </div>
                </template>
              </Column>
            </template>

            <!-- Колонка с действиями -->
            <Column :exportable="false" style="width: 0.5rem">
              <template #body="{ data }">
                <div class="flex gap-1">
                  <Button 
                    label="✏️" 
                    class="p-button-text p-button-sm" 
                    @click="openEditSidebar(data)" 
                  />
                  <Button 
                    label="⿻" 
                    class="p-button-text p-button-sm" 
                    @click="duplicateItem(data)" 
                  />
                </div>
              </template>
            </Column>
          </DataTable>
        </div>

        <!-- Сайдбар для редактирования -->
        <div 
          v-if="editSidebarVisible" 
          class="edit-sidebar"
          :class="{ 'collapsed': editSidebarCollapsed }"
        >
          <div class="flex justify-between items-center p-0 ps-4 border-b">
            <h3 v-if="!editSidebarCollapsed" class="text-sm font-semibold">Редактирование</h3>
            <div class="flex gap-2">
              <Button 
                :label="editSidebarCollapsed ? '▶️' : '◀️'" 
                class="p-button-text" 
                @click="toggleEditSidebar" 
              />
              <Button label="❌" class="p-button-text" @click="closeEditSidebar" />
            </div>
          </div>

          <div v-if="!editSidebarCollapsed" class="flex-1 overflow-y-auto p-4">
            <div class="space-y-2">
              <div class="inline-flex">
              <div class="field w-28">                
                <InputText v-model="editingItem.art" placeholder="art" class="w-full text-xs" />
              </div>

              <div class="field w-100">
                
                <InputText v-model="editingItem.title" placeholder="title" class="w-full text-xs" />
              </div>
            </div>

              <div class="field">
                <label class="block mb-1 text-xs">Description</label>
                <InputText v-model="editingItem.description" class="w-full text-xs" />
              </div>

              <div class="field">
                <label class="block mb-1 text-xs">Tags</label>
                <InputChips v-model="editingItem.tags" class="w-full text-xs" />
              </div>

              <div class="field">
                <label class="block mb-1 text-xs">HBS Template</label>
                <div class="relative">
                  <Textarea 
                    v-model="editingItem.hbs" 
                    rows="10" 
                    class="w-full text-sm font-mono"
                    :auto-resize="true"
                  />
                  <Button 
                    v-tooltip.left="'Копировать в буфер'" 
                    label="⿻" 
                    class="p-button-text p-button-sm absolute top-0 right-0"
                    @click="copyToClipboard(editingItem.hbs)"
                  />
                </div>
              </div>

              <div class="field">
                <label class="block mb-1 text-sm">Связанные страницы</label>
                <MultiSelect
                  v-model="editingItem.wpage"
                  :options="relatedPages"
                  option-label="title"
                  option-value="id"
                  placeholder="Выберите страницы"
                  class="w-full text-sm"
                  :filter="true"
                  display="chip"
                />
              </div>

              <div class="field">
                <label class="block mb-1 text-sm">Связанные блоки</label>
                <MultiSelect
                  v-model="editingItem.wblock"
                  :options="relatedBlocks"
                  option-label="title"
                  option-value="id"
                  placeholder="Выберите блоки"
                  class="w-full text-sm"
                  :filter="true"
                  display="chip"
                />
              </div>

              <div class="field">
                <label class="block mb-1 text-sm">Связанные данные</label>
                <MultiSelect
                  v-model="editingItem.wjson"
                  :options="relatedData"
                  option-label="title"
                  option-value="id"
                  placeholder="Выберите данные"
                  class="w-full text-sm"
                  :filter="true"
                  display="chip"
                />
              </div>

              <div class="flex justify-end gap-2 mt-4">
                <Button label="Отмена" icon="❌" class="p-button-outlined" @click="closeEditSidebar" />
                <Button label="Сохранить" icon="✅" @click="saveItem" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useNuxtApp } from '#app';
import { useDirectusItems } from '#imports';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import MultiSelect from 'primevue/multiselect';
import Tag from 'primevue/tag';
import InputChips from 'primevue/inputchips';

const search = ref('');
const selectedTags = ref([]);
const availableTags = ref([]);
const viewMode = ref('compact');
const selectedItem = ref(null);
const editSidebarVisible = ref(false);
const editSidebarCollapsed = ref(false);
const editingItem = ref(null);

const { $fetchJson } = useNuxtApp();

// Состояния для связанных записей
const relatedPages = ref([]);
const relatedBlocks = ref([]);
const relatedData = ref([]);

// Загрузка данных с сервера
const { data: templates } = await useAsyncData('templates', () => $fetchJson('http://localhost:8055/items/whbs'));

// Получение заголовков связанных записей с кэшированием
const getRelatedTitles = (ids, collection) => {
  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return [];
  }
  
  // Преобразуем ids в массив идентификаторов
  const idArray = ids.map(item => typeof item === 'object' ? item.id : item);
  
  // Проверяем, что все id действительны
  const validIds = idArray.filter(id => id !== null && id !== undefined);
  
  if (validIds.length === 0) {
    return [];
  }
  
  // Возвращаем массив ID
  return validIds;
};

// Загрузка связанных данных
const loadRelatedTitles = async (ids, collection) => {
  if (!ids || ids.length === 0) return;
  
  try {
    const { getItems } = useDirectusItems();
    console.log(`Загрузка заголовков для ${collection}, ids:`, ids);
    
    // Делаем специальный запрос для loadRelatedTitles, без лимита
    const response = await getItems({
      collection,
      params: {
        filter: {
          id: { _in: ids }
        },
        fields: ['id', 'title'],
        limit: -1  // Без ограничений
      }
    });
    
    if (response?.data && response.data.length > 0) {
      console.log(`Получены данные для ${collection}:`, response.data);
      
      // Обновляем кэш
      response.data.forEach(item => {
        if (!relatedTitlesCache.value[collection]) {
          relatedTitlesCache.value[collection] = {};
        }
        if (item && item.id && item.title) {
          relatedTitlesCache.value[collection][item.id] = item.title;
          console.log(`Кэширован заголовок: ${collection} ID ${item.id} -> "${item.title}"`);
        }
      });
    } else {
      console.log(`Нет данных для ${collection} с ids:`, ids);
      
      // Если данные не получены, делаем запрос поштучно
      for (const id of ids) {
        try {
          const singleResponse = await getItems({
            collection,
            id,
            params: {
              fields: ['id', 'title']
            }
          });
          
          if (singleResponse && singleResponse.id && singleResponse.title) {
            if (!relatedTitlesCache.value[collection]) {
              relatedTitlesCache.value[collection] = {};
            }
            relatedTitlesCache.value[collection][singleResponse.id] = singleResponse.title;
            console.log(`Кэширован индивидуальный заголовок: ${collection} ID ${singleResponse.id} -> "${singleResponse.title}"`);
          }
        } catch (err) {
          console.warn(`Ошибка загрузки индивидуальной записи ${collection} ID ${id}:`, err);
        }
      }
    }
  } catch (error) {
    console.error(`Ошибка загрузки ${collection}:`, error);
  }
};

// Загрузка всех связанных данных при инициализации
const preloadAllRelatedTitles = async () => {
  try {
    const { getItems } = useDirectusItems();
    
    // Загружаем все записи из каждой коллекции
    const collections = ['wpage', 'wblock', 'wjson'];
    
    for (const collection of collections) {
      const response = await getItems({
        collection,
        params: {
          fields: ['id', 'title'],
          limit: 1000
        }
      });
      
      if (response?.data) {
        if (!relatedTitlesCache.value[collection]) {
          relatedTitlesCache.value[collection] = {};
        }
        
        response.data.forEach(item => {
          if (item && item.id && item.title) {
            relatedTitlesCache.value[collection][item.id] = item.title;
          }
        });
        
        console.log(`Загружено ${response.data.length} заголовков для ${collection}`);
      }
    }
  } catch (error) {
    console.error('Ошибка при предзагрузке связанных данных:', error);
  }
};

// Вызываем предзагрузку при монтировании компонента
onMounted(async () => {
  console.log('SidebarTemplates: onMounted начало');
  
  // Сначала загружаем все связанные заголовки
  await preloadAllRelatedTitles();
  
  if (templates.value?.data) {
    // Собираем теги
    const tags = new Set();
    templates.value.data.forEach(item => {
      if (item.tags) {
        item.tags.forEach(tag => tags.add(tag));
      }
    });
    availableTags.value = Array.from(tags);
    console.log('Собраны теги:', availableTags.value);
    
    // Загружаем связанные данные для формы редактирования
    await loadRelatedData();
    
    console.log('SidebarTemplates: onMounted завершено');
  }
});

// Фильтрация данных
const filteredTemplates = computed(() => {
  let filtered = templates.value?.data || [];

  // Фильтр по поиску
  if (search.value) {
    const searchLower = search.value.toLowerCase();
    filtered = filtered.filter(item => 
      item.title?.toLowerCase().includes(searchLower) ||
      item.art?.toLowerCase().includes(searchLower) ||
      item.description?.toLowerCase().includes(searchLower)
    );
  }

  // Фильтр по тегам
  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(item => 
      item.tags && selectedTags.value.every(tag => item.tags.includes(tag))
    );
  }

  return filtered;
});

// Методы
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'compact' ? 'full' : 'compact';
};

const addTagToFilter = (tag) => {
  if (!selectedTags.value.includes(tag)) {
    selectedTags.value = [...selectedTags.value, tag];
  }
};

const openEditSidebar = (item) => {
  const itemCopy = JSON.parse(JSON.stringify(item));
  
  // Преобразуем ID в массивы для MultiSelect
  ['wpage', 'wblock', 'wjson'].forEach(field => {
    if (itemCopy[field]) {
      if (Array.isArray(itemCopy[field])) {
        // Если это массив объектов, преобразуем в массив ID
        if (itemCopy[field].length > 0 && typeof itemCopy[field][0] === 'object') {
          itemCopy[field] = itemCopy[field].map(obj => obj.id);
        }
      } else if (typeof itemCopy[field] === 'object') {
        // Если это объект, преобразуем в массив ID
        itemCopy[field] = [itemCopy[field].id];
      } else if (typeof itemCopy[field] === 'number' || typeof itemCopy[field] === 'string') {
        // Если это просто ID, создаем массив с одним ID
        itemCopy[field] = [itemCopy[field]];
      } else {
        // Если это не массив и не объект, создаем пустой массив
        itemCopy[field] = [];
      }
    } else {
      itemCopy[field] = [];
    }
  });
  
  console.log('Открытие формы редактирования:', itemCopy);
  editingItem.value = itemCopy;
  editSidebarVisible.value = true;
  
  // Загружаем связанные данные
  loadRelatedData();
};

const toggleEditSidebar = () => {
  editSidebarCollapsed.value = !editSidebarCollapsed.value;
};

const closeEditSidebar = () => {
  editSidebarVisible.value = false;
  editingItem.value = null;
};

const saveItem = async () => {
  if (!editingItem.value) return;

  try {
    console.log('Сохранение записи:', editingItem.value);
    
    const saveData = { ...editingItem.value };
    
    // Форматируем поля отношений
    ['wpage', 'wblock', 'wjson'].forEach(field => {
      if (saveData[field] && Array.isArray(saveData[field])) {
        saveData[field] = saveData[field].map(id => ({ id: typeof id === 'object' ? id.id : id }));
      } else if (saveData[field] && typeof saveData[field] === 'object') {
        saveData[field] = [{ id: saveData[field].id }];
      } else if (!saveData[field]) {
        saveData[field] = [];
      }
    });

    // Добавляем заголовки для запроса
    const headers = {
      'Content-Type': 'application/json'
    };

    // Отправляем запрос на обновление
    const response = await fetch(`http://localhost:8055/items/whbs/${saveData.id}`, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(saveData)
    });

    if (!response.ok) {
      throw new Error(`Ошибка HTTP: ${response.status}`);
    }

    const updatedItem = await response.json();
    console.log('Запись успешно сохранена:', updatedItem);

    // Обновляем данные в таблице
    if (templates.value?.data) {
      const index = templates.value.data.findIndex(item => item.id === updatedItem.data.id);
      if (index !== -1) {
        templates.value.data[index] = updatedItem.data;
        console.log('Локальные данные обновлены');
      }
    }

    // Обновляем все данные
    await refreshData();
    
    closeEditSidebar();
  } catch (error) {
    console.error('Ошибка при сохранении:', error);
  }
};

const duplicateItem = async (item) => {
  try {
    console.log('Дублирование записи:', item);
    
    const newItem = { ...item };
    delete newItem.id;
    newItem.title = `${newItem.title} (копия)`;

    // Форматируем поля отношений
    ['wpage', 'wblock', 'wjson'].forEach(field => {
      if (newItem[field] && Array.isArray(newItem[field])) {
        newItem[field] = newItem[field].map(id => ({ id: typeof id === 'object' ? id.id : id }));
      } else if (newItem[field] && typeof newItem[field] === 'object') {
        newItem[field] = [{ id: newItem[field].id }];
      } else if (!newItem[field]) {
        newItem[field] = [];
      }
    });

    // Добавляем заголовки для запроса
    const headers = {
      'Content-Type': 'application/json'
    };

    // Отправляем запрос на создание
    const response = await fetch('http://localhost:8055/items/whbs', {
      method: 'POST',
      headers,
      body: JSON.stringify(newItem)
    });

    if (!response.ok) {
      throw new Error(`Ошибка HTTP: ${response.status}`);
    }

    const createdItem = await response.json();
    console.log('Запись успешно дублирована:', createdItem);

    // Добавляем новую запись в локальные данные
    if (templates.value?.data) {
      templates.value.data.push(createdItem.data);
      console.log('Локальные данные обновлены');
    }

    // Обновляем все данные
    await refreshData();
  } catch (error) {
    console.error('Ошибка при дублировании:', error);
  }
};

const onRowClick = (event) => {
  // Обработка клика по строке, если нужно
  console.log('Clicked row:', event.data);
};

// Drag & Drop
const dragStart = (event, template) => {
  event.dataTransfer.setData('whbs', JSON.stringify(template));
  // Добавляем визуальный эффект
  event.target.style.opacity = '0.5';
  event.target.addEventListener('dragend', () => {
    event.target.style.opacity = '1';
  }, { once: true });
};

// Копирование в буфер
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    // Можно добавить toast уведомление
  } catch (err) {
    console.error('Ошибка копирования:', err);
  }
};

// Кэш для связанных заголовков
const relatedTitlesCache = ref({
  wpage: {},
  wblock: {},
  wjson: {}
});

// Загрузка связанных данных
const loadRelatedData = async () => {
  try {
    console.log('Загрузка связанных данных для формы редактирования...');
    const { getItems } = useDirectusItems();
    
    // Загружаем страницы
    const pagesResponse = await getItems({
      collection: 'wpage',
      params: {
        fields: ['id', 'title'],
        limit: 1000,
        sort: ['title']
      }
    });

    if (pagesResponse?.data) {
      relatedPages.value = pagesResponse.data;
      // Обновляем кэш заголовков
      pagesResponse.data.forEach(page => {
        if (!relatedTitlesCache.value.wpage) {
          relatedTitlesCache.value.wpage = {};
        }
        relatedTitlesCache.value.wpage[page.id] = page.title;
      });
      console.log('Загружено страниц:', pagesResponse.data.length);
    }

    // Загружаем блоки
    const blocksResponse = await getItems({
      collection: 'wblock',
      params: {
        fields: ['id', 'title'],
        limit: 1000,
        sort: ['title']
      }
    });

    if (blocksResponse?.data) {
      relatedBlocks.value = blocksResponse.data;
      // Обновляем кэш заголовков
      blocksResponse.data.forEach(block => {
        if (!relatedTitlesCache.value.wblock) {
          relatedTitlesCache.value.wblock = {};
        }
        relatedTitlesCache.value.wblock[block.id] = block.title;
      });
      console.log('Загружено блоков:', blocksResponse.data.length);
    }

    // Загружаем JSON данные
    const jsonResponse = await getItems({
      collection: 'wjson',
      params: {
        fields: ['id', 'title'],
        limit: 1000,
        sort: ['title']
      }
    });

    if (jsonResponse?.data) {
      relatedData.value = jsonResponse.data;
      // Обновляем кэш заголовков
      jsonResponse.data.forEach(json => {
        if (!relatedTitlesCache.value.wjson) {
          relatedTitlesCache.value.wjson = {};
        }
        relatedTitlesCache.value.wjson[json.id] = json.title;
      });
      console.log('Загружено JSON записей:', jsonResponse.data.length);
    }

    console.log('Загрузка связанных данных завершена');
  } catch (error) {
    console.error('Ошибка загрузки связанных данных:', error);
  }
};

// Наблюдаем за состоянием сайдбара редактирования
watch(editSidebarVisible, (visible) => {
  // Отправляем событие для изменения размера canvas с указанием направления
  if (visible) {
    emit('update:canvasWidth', '40%', 'left');
  } else {
    emit('update:canvasWidth', '60%');
    // Обновляем данные после закрытия формы
    refreshData();
  }
});

// Функция обновления данных
const refreshData = async () => {
  try {
    console.log('Обновление данных в SidebarTemplates...');
    
    const response = await fetch('http://localhost:8055/items/whbs');
    if (!response.ok) {
      throw new Error(`Ошибка HTTP: ${response.status}`);
    }
    
    const data = await response.json();
    if (data?.data) {
      templates.value = data;
      console.log('Данные успешно обновлены в SidebarTemplates');
    }
  } catch (error) {
    console.error('Ошибка обновления данных:', error);
  }
};

// Добавление на холст
const addToCanvas = (template) => {
  emit('add-to-canvas', { type: 'whbs', data: template });
};

// Определяем emit для добавления на холст
const emit = defineEmits(['update:canvasWidth', 'add-to-canvas']);

// Изменяем стиль сайдбара редактирования
const editSidebarStyle = computed(() => ({
  position: 'fixed',
  top: 0,
  left: '50%',
  height: '100%',
  width: '50%',
  background: 'white',
  borderRight: '1px solid #e5e7eb',
  boxShadow: '2px 0 5px rgba(0, 0, 0, 0.1)',
  zIndex: 30,
  transition: 'all 0.3s ease',
  overflow: 'auto',
  boxSizing: 'border-box'
}));
</script>

<style scoped>
.sidebar {
  background: #f4f4f4;
  padding: 1rem;
  resize: horizontal;
  overflow: auto;
  min-width: 250px;
  max-width: 50vw;
  position: relative;
  z-index: 30;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex: 0 0 auto; /* Добавляем это свойство */
}

input {font-size: 13px;}
textarea {font-size: 11px; line-height: 1.2;}

/* Контейнер для таблицы и формы редактирования */
.flex-container {
  display: flex;
  width: 100%;
  height: 100%;
  flex: 1;
}

/* Стили для таблицы */
.table-container {
  width: 100%;
  overflow: auto;
  padding-right: 1rem;
  transition: width 0.3s ease;
}

/* При открытом сайдбаре редактирования */
.flex-container:has(.edit-sidebar) .table-container {
  width: 50%;
}

/* Стили для сайдбара редактирования */
.edit-sidebar {
  position: relative;
  width: 50%;
  height: 100%;
  background: white;
  border-left: 1px solid #e5e7eb;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  overflow: auto;
  box-sizing: border-box;
}

.edit-sidebar.collapsed {
  width: 40px;
}

/* Стили для MultiSelect */
:deep(.p-multiselect) {
  width: 100%;
}

:deep(.p-multiselect-label) {
  font-size: 13px;
}

:deep(.p-multiselect-item) {
  font-size: 13px;
  padding: 0.5rem;
}

/* Стили для DataTable */
:deep(.p-datatable-wrapper) {
  overflow-x: auto !important;
}

:deep(.p-datatable) {
  font-size: 13px;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  padding: 0.5rem;
}

.cursor-move {
  cursor: grab;
  font-size: 18px;
  color: #666;
  &:active {
    cursor: grabbing;
  }
  &:hover {
    color: #333;
  }
}
</style>
  
  