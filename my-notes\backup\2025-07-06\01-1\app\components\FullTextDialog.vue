<template>
  <Dialog 
    v-model:visible="dialogVisible" 
    :header="title" 
    :style="{ width: '80vw', maxWidth: '1200px' }"
    modal
  >
    <div class="text-content">
      <div v-if="text" class="mb-4">
        <h3 class="text-xl font-bold mb-2">Полный текст</h3>
        <div class="prose max-w-full" v-html="formattedText"></div>
      </div>
      
      <div v-if="items && items.length" class="nested-items mt-4">
        <h3 class="text-xl font-bold mb-2">Вложенные элементы</h3>
        <div 
          v-for="(item, index) in items" 
          :key="index" 
          class="nested-item border-b pb-3 mb-3"
        >
          <h4 class="text-lg font-semibold">{{ item.title || item.name }}</h4>
          <p v-if="item.text" class="mt-2">{{ item.text }}</p>
        </div>
      </div>
    </div>
    
    <template #footer>
      <Button 
        label="Закрыть" 
        icon="pi pi-times" 
        @click="closeDialog" 
        class="p-button-text" 
      />
      <Button 
        v-if="text" 
        label="Копировать" 
        icon="pi pi-copy" 
        @click="copyText" 
        class="p-button-secondary ml-2" 
      />
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Dialog from 'primevue/dialog'
import Button from 'primevue/button'
import type { WcontItem } from '~/types/wconts'

const props = defineProps<{
  modelValue: boolean
  title?: string
  text?: string
  items?: WcontItem[]
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formattedText = computed(() => {
  if (!props.text) return ''
  return props.text.replace(/\n/g, '<br>')
})

function closeDialog() {
  dialogVisible.value = false
}

function copyText() {
  if (props.text) {
    navigator.clipboard.writeText(props.text)
    alert('Текст скопирован в буфер обмена')
  }
}
</script>

<style scoped>
.prose {
  max-width: 100%;
  line-height: 1.6;
}
</style>
