<template>
  <div class="flex flex-col gap-4 p-1">
    <!-- Toolbar с полями ввода и кнопками -->
    <div class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <InputText
        v-model="formData.number"
        placeholder="Номер блока"
        class="w-28 text-xs"
        style="font-size: 11px"
      />
      <InputText
        v-model="formData.title"
        placeholder="Название блока"
        class="flex-1 text-xs"
        style="font-size: 12px"
      />
      <MultiSelect
        v-model="formData.block_type"
        :options="blockTypeOptions"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Выберите типы блока"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="formData.collection"
        :options="collectionOptions"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Выберите коллекции"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <Button
        v-tooltip.top="'Генерировать скриншоты'"
        icon="pi pi-camera"
        class="p-button-warning text-xs"
        :loading="generating"
        :disabled="!selectedBlocks.length"
        @click="generateScreenshots"
      />
      <Button
        v-tooltip.top="'Сохранить'"
        icon="pi pi-save"
        class="text-xs"
        :loading="saving"
        :disabled="!selectedBlocks.length"
        @click="saveSelectedBlocks"
      />
    </div>

    <!-- Группы JSON и HBS редакторов -->
    <div class="flex flex-col gap-2">
      <div
        v-for="group in dataGroups"
        :key="group.id"
        class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg"
      >
        <!-- Номер группы -->
        <div class="flex-shrink-0 w-8 h-8 bg-primary-500  rounded-full flex items-center justify-center text-sm font-medium">
          {{ group.id }}
        </div>

        <!-- JSON редактор -->
        <div class="w-[30%]">
          <ClientOnly>
            <PrismEditor
              v-model="group.jsonContent"
              class="w-full text-xs p-2 border rounded my-editor h-[180px] overflow-auto max-h-[180px]"
              :highlight="highlightJson"
              placeholder="Введите JSON данные"
              line-numbers
            />
          </ClientOnly>
        </div>

        <!-- HBS редактор -->
        <div class="flex-1">
          <ClientOnly>
            <PrismEditor
              v-model="group.hbsContent"
              class="w-full text-xs p-2 border rounded my-editor h-[180px] overflow-auto max-h-[180px]"
              :highlight="highlightHtml"
              placeholder="Введите HBS шаблон"
              line-numbers
            />
          </ClientOnly>
        </div>

        <!-- Дополнительные поля для групп 2+ -->
        <div v-if="group.id > 1" class="flex flex-col gap-2 w-48">
          <MultiSelect
            v-model="group.block_type"
            :options="blockTypeOptions"
            display="chip"
            class="text-xs"
            filter
            placeholder="Тип блока"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
          <MultiSelect
            v-model="group.collection"
            :options="collectionOptions"
            display="chip"
            class="text-xs"
            filter
            placeholder="Коллекция"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
          <!-- Кнопка добавления новой группы (только для последней группы) -->
          <Button
            v-if="group.id === dataGroups[dataGroups.length - 1].id"
            v-tooltip.top="'Добавить новую группу'"
            icon="pi pi-plus"
            class="p-button-outlined text-xs"
            @click="addNewGroup"
          />
        </div>

        <!-- Кнопка добавления новой группы для первой группы -->
        <div v-else class="flex-shrink-0">
          <Button
            v-if="group.id === dataGroups[dataGroups.length - 1].id"
            v-tooltip.top="'Добавить новую группу'"
            icon="pi pi-plus"
            class="p-button-outlined text-xs"
            @click="addNewGroup"
          />
        </div>
      </div>
    </div>

    <!-- Фильтры -->
    <div class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <MultiSelect
        v-model="filters.block_type"
        :options="filterOptions.block_type"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Тип блока"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.collection"
        :options="filterOptions.collection"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Коллекция"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.concept"
        :options="filterOptions.concept"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Концепт"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.elements"
        :options="filterOptions.elements"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Элементы"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.layout"
        :options="filterOptions.layout"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Макет"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.style"
        :options="filterOptions.style"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Стиль"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.features"
        :options="filterOptions.features"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Функции"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <Button
        v-tooltip.top="'Сбросить все фильтры'"
        icon="pi pi-times"
        class="p-button-outlined text-xs"
        @click="clearAllFilters"
      />
      <Button
        v-tooltip.top="'Показать только выбранные'"
        icon="pi pi-filter"
        class="p-button-outlined text-xs"
        :class="{ 'p-button-info': showOnlySelected }"
        @click="toggleShowOnlySelected"
      />
      <Button
        v-tooltip.top="'Снять все отметки'"
        icon="pi pi-check-square"
        class="p-button-outlined text-xs"
        @click="clearAllSelections"
      />
    </div>

    <!-- Блоки с пагинацией -->
    <div v-if="filteredBlocks.length > 0" class="flex flex-col gap-4">
      <!-- Информация о количестве блоков -->
      <div class="text-sm text-surface-600 dark:text-surface-400">
        Найдено блоков: {{ filteredBlocks.length }}
        <span v-if="selectedBlocks.length > 0">
          | Выбрано: {{ selectedBlocks.length }}
        </span>
      </div>

      <!-- Сетка блоков -->
      <div class="masonry-grid gap-4">
        <Card
          v-for="block in paginatedBlocks"
          :key="block.id"
          class="overflow-hidden cursor-pointer transition-all duration-50"
          :class="{
            'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 border-blue-500': isBlockSelected(block),
            'hover:shadow-lg': !isBlockSelected(block)
          }"
          :style="isBlockSelected(block) ? 'border: 2px solid #3b82f6; background-color: rgba(59, 130, 246, 0.1);' : ''"
          style="background-color: #f9f9f9;"
          @click="toggleSelection(block)"
        >
          <template #header>
            <div class="relative">
              <!-- Показываем временный скриншот если есть -->
              <Image
                v-if="tempScreenshots.has(block.id!)"
                :src="tempScreenshots.get(block.id!)"
                alt="Сгенерированный скриншот"
                class="w-full h-auto object-contain cursor-pointer"
                preview
                @click.stop
              />
              <!-- Показываем оригинальный эскиз если нет временного скриншота -->
              <Image
                v-else-if="block.sketch"
                :src="`http://localhost:8055/assets/${block.sketch}`"
                alt="Предпросмотр блока"
                class="w-full h-auto object-contain cursor-pointer"

                preview
                @click.stop
              />
              <!-- Показываем заглушку если нет изображений -->
              <div
                v-else
                class="w-full h-12 bg-surface-100 dark:bg-surface-800 flex items-center justify-center"
              >
                <i class="pi pi-image text-4xl text-surface-400"/>
              </div>
              <div class="absolute top-2 right-2">
                <Checkbox
                  :model-value="isBlockSelected(block)"
                  binary
                  class="bg-white rounded shadow"
                  @click.stop
                  @change="toggleSelection(block)"
                />
              </div>
            </div>
          </template>
          <template #content>
            <div class="px-2 py-1">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <span class="text-sm font-medium text-gray-500" style="font-size: 13px;">{{ block.number }}</span>
                  <span class="text-base" style="font-size: 16px;">{{ block.title }}</span>
                </div>
                <!-- Dropdown для выбора группы (показывается только если групп больше 1) -->
                <div v-if="dataGroups.length > 1" class="flex-shrink-0">
                  <Dropdown
                    :model-value="getSelectedGroupForBlock(block.id!)"
                    :options="groupOptions"
                    option-label="label"
                    option-value="value"
                    class="text-xs w-12"
                    :pt="{
                      root: { class: 'text-xs h-6' },
                      input: { class: 'text-xs p-1 h-6' },
                      trigger: { class: 'w-4' },
                      panel: { class: 'text-xs' },
                      item: { class: 'text-xs p-1' }
                    }"
                    @change="(event) => setSelectedGroupForBlock(block.id!, event.value)"
                    @click.stop
                  />
                </div>
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Пагинация -->
      <Paginator
        v-model:first="first"
        :rows="rowsPerPage"
        :total-records="filteredBlocks.length"
        :rows-per-page-options="[30, 60, 90]"
        template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
        @page="onPageChange"
      />
    </div>

    <!-- Сообщение если блоков нет -->
    <div v-else-if="!loading" class="text-center py-8 text-surface-500">
      <i class="pi pi-inbox text-4xl mb-4 block"/>
      <p>Блоки не найдены</p>
      <p class="text-sm">Попробуйте изменить фильтры</p>
    </div>

    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { PrismEditor } from 'vue-prism-editor'
import 'vue-prism-editor/dist/prismeditor.min.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import handlebars from 'handlebars/dist/handlebars.min.js'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'

// PrimeVue компоненты
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import MultiSelect from 'primevue/multiselect'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import Card from 'primevue/card'
import Checkbox from 'primevue/checkbox'
import Toast from 'primevue/toast'
import Image from 'primevue/image'
import Paginator from 'primevue/paginator'
import Dropdown from 'primevue/dropdown'

// Определение метаданных страницы
definePageMeta({
  title: 'Генератор блоков',
  description: 'Генерация и предпросмотр блоков с JSON/HBS',
  navOrder: 15,
  type: 'primary',
  icon: 'i-mdi-view-grid-plus',
})

// Интерфейсы
interface WBlock {
  id?: string
  number: string
  title: string
  block_type?: string[]
  collection?: string[]
  json?: string
  hbs?: string
  html?: string
  sketch?: string
  css?: string
  js?: string
  concept?: string[]
  elements?: string[]
  layout?: string[]
  style?: string[]
  features?: string[]
}

interface DataGroup {
  id: number
  jsonContent: string
  hbsContent: string
  block_type?: string[]
  collection?: string[]
}

interface GeneratedCard {
  id: string
  number: string
  title: string
  html: string
  hbs: string
  json: string
  screenshot?: string
  originalBlock: WBlock
}

// Composables
const { getItems, createItems, updateItem, deleteFile } = useDirectusItems()
const toast = useToast()

// Реактивные данные
const formData = ref({
  number: '',
  title: '',
  block_type: [] as string[],
  collection: [] as string[]
})

// Группы данных JSON/HBS
const dataGroups = ref<DataGroup[]>([
  { id: 1, jsonContent: '', hbsContent: '', block_type: [], collection: [] }
])

// Выбранная группа для каждого блока (по умолчанию группа 1)
const blockGroupSelection = ref<Map<string, number>>(new Map())

// Кэширование для оптимизации генерации скриншотов
const blockDataHashes = ref<Map<string, string>>(new Map()) // blockId -> hash данных
const tempScreenshots = ref<Map<string, string>>(new Map()) // blockId -> blob URL

const loading = ref(false)
const generating = ref(false)
const saving = ref(false)

// Данные для блоков
const allBlocks = ref<WBlock[]>([])
const selectedBlocks = ref<WBlock[]>([])
const first = ref(0)
const rowsPerPage = ref(30)

// ID временных файлов для последующего удаления
const tempFileIds = ref<string[]>([])

// Опции для мультиселектов (для сохранения)
const blockTypeOptions = ref<string[]>([])
const collectionOptions = ref<string[]>([])

// Фильтры
const filters = ref({
  block_type: [] as string[],
  collection: [] as string[],
  concept: [] as string[],
  elements: [] as string[],
  layout: [] as string[],
  style: [] as string[],
  features: [] as string[]
})

// Опции для фильтров
const filterOptions = ref({
  block_type: [] as string[],
  collection: [] as string[],
  concept: [] as string[],
  elements: [] as string[],
  layout: [] as string[],
  style: [] as string[],
  features: [] as string[]
})

// Дополнительные состояния
const showOnlySelected = ref(false)

// Вычисляемые свойства для групп
const groupOptions = computed(() => {
  return dataGroups.value.map(group => ({
    label: group.id.toString(),
    value: group.id
  }))
})

// Функции для работы с группами
const addNewGroup = () => {
  const newId = Math.max(...dataGroups.value.map(g => g.id)) + 1
  dataGroups.value.push({
    id: newId,
    jsonContent: '',
    hbsContent: '',
    block_type: [],
    collection: []
  })
  console.log(`Добавлена новая группа ${newId}`)
}

const getSelectedGroupForBlock = (blockId: string): number => {
  return blockGroupSelection.value.get(blockId) || 1
}

const setSelectedGroupForBlock = (blockId: string, groupId: number) => {
  blockGroupSelection.value.set(blockId, groupId)
  console.log(`Блок ${blockId} теперь использует группу ${groupId}`)
}

const getGroupData = (groupId: number): DataGroup | undefined => {
  return dataGroups.value.find(g => g.id === groupId)
}

// Функции для кэширования и оптимизации
const generateDataHash = (block: WBlock, groupId: number): string => {
  const group = getGroupData(groupId)
  if (!group) return ''

  // Создаем хэш на основе всех данных, влияющих на генерацию
  const dataToHash = {
    blockId: block.id,
    blockHbs: block.hbs,
    blockJson: block.json,
    blockCss: block.css,
    blockJs: block.js,
    groupId: groupId,
    groupJson: group.jsonContent,
    groupHbs: group.hbsContent
  }

  // Безопасный хэш с поддержкой Unicode
  const jsonString = JSON.stringify(dataToHash)
  // Используем простой хэш вместо btoa для поддержки Unicode
  let hash = 0
  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Конвертируем в 32-битное число
  }
  return hash.toString()
}

const isScreenshotActual = (blockId: string, currentHash: string): boolean => {
  const storedHash = blockDataHashes.value.get(blockId)
  const hasScreenshot = tempScreenshots.value.has(blockId)

  return storedHash === currentHash && hasScreenshot
}

const updateBlockHash = (blockId: string, hash: string) => {
  blockDataHashes.value.set(blockId, hash)
}

// Вычисляемые свойства
const filteredBlocks = computed(() => {
  let blocks = allBlocks.value

  // Применяем фильтры
  if (filters.value.block_type.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.block_type) return false
      return filters.value.block_type.some(selectedType =>
        block.block_type.includes(selectedType)
      )
    })
  }

  if (filters.value.collection.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.collection) return false
      return filters.value.collection.some(selectedCollection =>
        block.collection.includes(selectedCollection)
      )
    })
  }

  if (filters.value.concept.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.concept) return false
      return filters.value.concept.some(selectedConcept =>
        block.concept.includes(selectedConcept)
      )
    })
  }

  if (filters.value.elements.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.elements) return false
      return filters.value.elements.some(selectedElement =>
        block.elements.includes(selectedElement)
      )
    })
  }

  if (filters.value.layout.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.layout) return false
      return filters.value.layout.some(selectedLayout =>
        block.layout.includes(selectedLayout)
      )
    })
  }

  if (filters.value.style.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.style) return false
      return filters.value.style.some(selectedStyle =>
        block.style.includes(selectedStyle)
      )
    })
  }

  if (filters.value.features.length > 0) {
    blocks = blocks.filter(block => {
      if (!block.features) return false
      return filters.value.features.some(selectedFeature =>
        block.features.includes(selectedFeature)
      )
    })
  }

  // Фильтр "показать только выбранные"
  if (showOnlySelected.value) {
    blocks = blocks.filter(block => selectedBlocks.value.some(b => b.id === block.id))
  }

  // Сортируем по полю number по возрастанию
  return blocks.sort((a, b) => {
    const numA = a.number || ''
    const numB = b.number || ''
    return numA.localeCompare(numB, undefined, { numeric: true })
  })
})

// Пагинированные блоки
const paginatedBlocks = computed(() => {
  const start = first.value
  const end = start + rowsPerPage.value
  return filteredBlocks.value.slice(start, end)
})

// Функция проверки выбора блока
const isBlockSelected = (block: WBlock): boolean => {
  return selectedBlocks.value.some(b => b.id === block.id)
}

// Функции подсветки синтаксиса
const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

// Методы
const loadOptions = async () => {
  try {
    const blocks = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: ['block_type', 'collection', 'concept', 'elements', 'layout', 'style', 'features']
      }
    })

    // Собираем уникальные значения для опций сохранения
    const blockTypes = new Set<string>()
    const collections = new Set<string>()

    // Собираем уникальные значения для фильтров
    const filterBlockTypes = new Set<string>()
    const filterCollections = new Set<string>()
    const concepts = new Set<string>()
    const elements = new Set<string>()
    const layouts = new Set<string>()
    const styles = new Set<string>()
    const features = new Set<string>()

    blocks.forEach((block: any) => {
      if (block.block_type) {
        block.block_type.forEach((type: string) => {
          blockTypes.add(type)
          filterBlockTypes.add(type)
        })
      }
      if (block.collection) {
        block.collection.forEach((coll: string) => {
          collections.add(coll)
          filterCollections.add(coll)
        })
      }
      if (block.concept) {
        block.concept.forEach((concept: string) => concepts.add(concept))
      }
      if (block.elements) {
        block.elements.forEach((element: string) => elements.add(element))
      }
      if (block.layout) {
        block.layout.forEach((layout: string) => layouts.add(layout))
      }
      if (block.style) {
        block.style.forEach((style: string) => styles.add(style))
      }
      if (block.features) {
        block.features.forEach((feature: string) => features.add(feature))
      }
    })

    // Опции для сохранения
    blockTypeOptions.value = Array.from(blockTypes).sort()
    collectionOptions.value = Array.from(collections).sort()

    // Опции для фильтров
    filterOptions.value.block_type = Array.from(filterBlockTypes).sort()
    filterOptions.value.collection = Array.from(filterCollections).sort()
    filterOptions.value.concept = Array.from(concepts).sort()
    filterOptions.value.elements = Array.from(elements).sort()
    filterOptions.value.layout = Array.from(layouts).sort()
    filterOptions.value.style = Array.from(styles).sort()
    filterOptions.value.features = Array.from(features).sort()
  } catch (error) {
    console.error('Error loading options:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить опции',
      life: 3000
    })
  }
}

// Методы для работы с выбором блоков
const toggleSelection = (block: WBlock) => {
  const index = selectedBlocks.value.findIndex(b => b.id === block.id)
  if (index > -1) {
    selectedBlocks.value.splice(index, 1)
  } else {
    selectedBlocks.value.push(block)
  }
  console.log('Выбранные блоки:', selectedBlocks.value.length)
}

// Функции для управления фильтрами
const clearAllFilters = () => {
  filters.value.block_type = []
  filters.value.collection = []
  filters.value.concept = []
  filters.value.elements = []
  filters.value.layout = []
  filters.value.style = []
  filters.value.features = []
  console.log('Все фильтры сброшены')
}

const toggleShowOnlySelected = () => {
  showOnlySelected.value = !showOnlySelected.value
  console.log('Показать только выбранные:', showOnlySelected.value)
}

const clearAllSelections = () => {
  selectedBlocks.value = []
  console.log('Все выборы сняты')
}

const onPageChange = (event: any) => {
  first.value = event.first
  rowsPerPage.value = event.rows
  console.log('Переход на страницу:', event.page, 'first:', event.first, 'rows:', event.rows)
}

const loadAllBlocks = async () => {
  loading.value = true

  try {
    // Загружаем все блоки из базы
    const blocks = await getItems({
      collection: 'wblock_proto',
      params: {
        limit: -1,
        fields: ['*']
      }
    })

    allBlocks.value = blocks
    console.log(`Загружено ${blocks.length} блоков из базы`)

  } catch (error) {
    console.error('Error loading blocks:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить блоки',
      life: 3000
    })
  } finally {
    loading.value = false
  }
}

const generateScreenshots = async () => {
  if (selectedBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите блоки для генерации скриншотов',
      life: 3000
    })
    return
  }

  // Проверяем, что хотя бы в одной группе есть данные
  const hasAnyData = dataGroups.value.some(group => group.jsonContent || group.hbsContent)
  if (!hasAnyData) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните JSON или HBS поле хотя бы в одной группе',
      life: 3000
    })
    return
  }

  generating.value = true

  try {
    console.log(`Генерация скриншотов для ${selectedBlocks.value.length} выбранных блоков...`)

    let generatedCount = 0
    let skippedCount = 0

    for (const block of selectedBlocks.value) {
      try {
        // Получаем выбранную группу для этого блока
        const selectedGroupId = getSelectedGroupForBlock(block.id!)
        const selectedGroup = getGroupData(selectedGroupId)

        if (!selectedGroup) {
          console.warn(`Группа ${selectedGroupId} не найдена для блока ${block.number}`)
          continue
        }

        // Генерируем хэш текущих данных
        const currentHash = generateDataHash(block, selectedGroupId)

        // Проверяем актуальность существующего скриншота
        if (isScreenshotActual(block.id!, currentHash)) {
          console.log(`⏭️ Пропускаем блок ${block.number} - скриншот актуален`)
          skippedCount++
          continue
        }

        let html = ''

        // Функционал 1: JSON заполнен в группе - используем его с HBS из блока
        if (selectedGroup.jsonContent && block.hbs) {
          const template = handlebars.compile(block.hbs)
          const jsonData = JSON.parse(selectedGroup.jsonContent)
          html = template(jsonData)
        }
        // Функционал 2: HBS заполнен в группе - используем его с JSON из блока
        else if (selectedGroup.hbsContent && block.json) {
          const template = handlebars.compile(selectedGroup.hbsContent)
          const jsonData = typeof block.json === 'string'
            ? JSON.parse(block.json)
            : block.json
          html = template(jsonData)
        }

        if (html) {
          // Освобождаем старый blob URL если есть
          const oldUrl = tempScreenshots.value.get(block.id!)
          if (oldUrl && oldUrl.startsWith('blob:')) {
            URL.revokeObjectURL(oldUrl)
          }

          // Создаем новый временный скриншот
          const tempUrl = await generateTempScreenshot(block, html)
          if (tempUrl) {
            tempScreenshots.value.set(block.id!, tempUrl)
            updateBlockHash(block.id!, currentHash)
            generatedCount++
            console.log(`✅ Временный скриншот создан для блока ${block.number} с группой ${selectedGroupId}`)
          }
        }

      } catch (error) {
        console.error(`Ошибка генерации для блока ${block.number}:`, error)
      }
    }

    const totalBlocks = selectedBlocks.value.length
    let message = ''

    if (generatedCount > 0 && skippedCount > 0) {
      message = `Создано: ${generatedCount}, пропущено: ${skippedCount} (актуальные)`
    } else if (generatedCount > 0) {
      message = `Создано ${generatedCount} скриншотов`
    } else if (skippedCount > 0) {
      message = `Все ${skippedCount} скриншотов актуальны`
    } else {
      message = 'Нет данных для генерации'
    }

    toast.add({
      severity: 'success',
      summary: 'Генерация завершена',
      detail: message,
      life: 3000
    })

  } catch (error) {
    console.error('Error generating screenshots:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сгенерировать скриншоты',
      life: 3000
    })
  } finally {
    generating.value = false
  }
}

// Функция для создания временного скриншота (БЕЗ сохранения в Directus)
const generateTempScreenshot = async (block: WBlock, html: string): Promise<string | null> => {
  try {
    console.log(`Создание временного скриншота для ${block.number}...`)

    // Получаем CSS и JS из оригинального блока
    const css = block.css || ''
    const js = block.js || ''

    // Создаем полный HTML для скриншота с CSS/JS из блока
    const fullHtml = '<!DOCTYPE html>' +
      '<html lang="en">' +
      '<head>' +
      '<meta charset="UTF-8">' +
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
      '<title>' + (block.title || '') + '</title>' +

      css +
      '</head>' +
      '<body>' +
      html +
      js +
      '</body>' +
      '</html>'

    // Используем новый API для временных скриншотов (НЕ сохраняет в Directus)
    const response = await $fetch('/api/capture-html-screenshot-temp', {
      method: 'POST',
      body: {
        html: fullHtml,
        width: 1400,
        height: 800
      },
      responseType: 'blob'
    })

    // Создаем временный URL из blob
    const tempUrl = URL.createObjectURL(response as Blob)

    console.log(`Временный скриншот создан (БЕЗ сохранения в Directus)`)
    return tempUrl

  } catch (error) {
    console.error(`Ошибка создания временного скриншота для ${block.number}:`, error)
    return null
  }
}

// Функция для сохранения временного blob скриншота в Directus
const saveBlobToDirectus = async (blobUrl: string, filename: string): Promise<string | null> => {
  try {
    console.log(`💾 Сохранение временного скриншота в Directus: ${filename}...`)

    // Получаем blob из URL
    const response = await fetch(blobUrl)
    const blob = await response.blob()

    // Создаем FormData для загрузки в Directus
    const formData = new FormData()
    formData.append('title', filename)
    formData.append('file', blob, filename)

    // Загружаем в Directus
    const uploadResponse = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!uploadResponse.ok) {
      throw new Error(`Failed to upload to Directus: ${uploadResponse.statusText}`)
    }

    const data = await uploadResponse.json()
    console.log(`✅ Временный скриншот сохранен в Directus: ${data.data.id}`)

    return data.data.id

  } catch (error) {
    console.error(`❌ Ошибка сохранения временного скриншота в Directus:`, error)
    return null
  }
}

const generateScreenshotForCard = async (card: GeneratedCard) => {
  if (card.screenshot) {
    return // Скриншот уже создан
  }

  try {
    console.log(`Создание скриншота для ${card.number}...`)

    // Получаем CSS и JS из оригинального блока
    const originalBlock = card.originalBlock
    const css = originalBlock.css || ''
    const js = originalBlock.js || ''

    // Создаем полный HTML для скриншота с CSS/JS из блока
    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${card.title}</title>

  ${css}
</head>
<body>
  ${card.html}
  ${js}
</body>
</html>`

    const filename = `wblock_gen_${card.number}_${card.title.replace(/[^a-zA-Z0-9]/g, '_')}`

    const response = await $fetch('/api/capture-html-screenshot', {
      method: 'POST',
      body: {
        html: fullHtml,
        filename: filename,
        width: 1400,
        height: 800
      }
    })

    card.screenshot = response.fileId
    console.log(`Скриншот создан для ${card.number}: ${response.fileId}`)

  } catch (error) {
    console.error(`Ошибка создания скриншота для ${card.number}:`, error)
  }
}

const saveSelectedBlocks = async () => {
  if (selectedBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите блоки для сохранения',
      life: 3000
    })
    return
  }

  // Проверяем, что хотя бы в одной группе есть данные
  const hasAnyData = dataGroups.value.some(group => group.jsonContent || group.hbsContent)
  if (!hasAnyData) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните JSON или HBS поле хотя бы в одной группе',
      life: 3000
    })
    return
  }

  saving.value = true

  try {

    const blocksToSave = []
    const analysisData = []

    console.log(`Обработка ${selectedBlocks.value.length} выбранных блоков для сохранения...`)

    // Подготавливаем данные для сохранения
    for (let i = 0; i < selectedBlocks.value.length; i++) {
      const block = selectedBlocks.value[i]
      const counter = String(i + 1).padStart(2, '0')

      // Генерируем HTML для блока
      let html = ''
      let sourceData = ''
      let sourceTemplate = ''

      // Получаем выбранную группу для этого блока
      const selectedGroupId = getSelectedGroupForBlock(block.id!)
      const selectedGroup = getGroupData(selectedGroupId)

      if (!selectedGroup) {
        console.warn(`Группа ${selectedGroupId} не найдена для блока ${block.number}`)
        continue
      }

      // Функционал 1: JSON заполнен в группе - используем его с HBS из блока
      if (selectedGroup.jsonContent && block.hbs) {
        const template = handlebars.compile(block.hbs)
        const jsonData = JSON.parse(selectedGroup.jsonContent)
        html = template(jsonData)
        sourceData = selectedGroup.jsonContent
        sourceTemplate = block.hbs
      }
      // Функционал 2: HBS заполнен в группе - используем его с JSON из блока
      else if (selectedGroup.hbsContent && block.json) {
        const template = handlebars.compile(selectedGroup.hbsContent)
        const jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
        html = template(jsonData)
        sourceData = typeof block.json === 'string' ? block.json : JSON.stringify(block.json, null, 2)
        sourceTemplate = selectedGroup.hbsContent
      }

      // Используем уже созданный скриншот или создаем новый
      let screenshotId = null
      if (html) {
        // Генерируем хэш для проверки актуальности
        const currentHash = generateDataHash(block, selectedGroupId)

        // Проверяем, есть ли актуальный временный скриншот
        if (isScreenshotActual(block.id!, currentHash)) {
          // Если есть актуальный временный скриншот, сохраняем его в Directus
          try {
            const tempBlobUrl = tempScreenshots.value.get(block.id!)
            if (tempBlobUrl) {
              const filename = `wblock_gen_${block.number}_${block.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}.png`
              screenshotId = await saveBlobToDirectus(tempBlobUrl, filename)
              console.log(`✅ Временный скриншот сохранен в Directus для блока ${block.number}: ${screenshotId}`)
            } else {
              console.warn(`⚠️ Временный скриншот не найден для блока ${block.number}`)
            }
          } catch (error) {
            console.error(`Ошибка сохранения временного скриншота для блока ${block.number}:`, error)
          }
        } else {
          // Если нет временного скриншота, создаем новый
          console.log(`🔄 Создание нового скриншота для блока ${block.number} (нет временного)...`)
          try {
            const tempCard = {
              id: block.id,
              number: block.number,
              title: block.title,
              html,
              hbs: sourceTemplate,
              json: sourceData,
              originalBlock: block,
              screenshot: undefined
            }

            await generateScreenshotForCard(tempCard)
            screenshotId = tempCard.screenshot
            console.log(`✅ Новый скриншот создан для блока ${block.number}: ${screenshotId}`)
          } catch (error) {
            console.error(`Ошибка создания нового скриншота для блока ${block.number}:`, error)
          }
        }
      }

      // Определяем block_type и collection для этого блока
      const blockType = (selectedGroup.block_type && selectedGroup.block_type.length > 0)
        ? selectedGroup.block_type
        : formData.value.block_type

      const collection = (selectedGroup.collection && selectedGroup.collection.length > 0)
        ? selectedGroup.collection
        : formData.value.collection

      const blockData = {
        number: `${formData.value.number}-${counter}`,
        title: `${formData.value.title}-${counter}`,
        block_type: blockType,
        collection: collection,
        json: sourceData,
        hbs: sourceTemplate,
        html: html,
        css: block.css || '',
        js: block.js || '',
        sketch: screenshotId,
        status: 'draft'
      }

      blocksToSave.push(blockData)
      analysisData.push({
        html: html,
        tempIndex: i
      })
    }

    // Сохраняем блоки в базу
    const savedBlocks = await createItems({
      collection: 'wblock_proto',
      items: blocksToSave
    })

    console.log(`Сохранено ${savedBlocks.length} блоков`)

    // Анализируем HTML и обновляем поля
    if (analysisData.length > 0) {
      try {
        const analysisRecords = savedBlocks.map((block: any, index: number) => ({
          id: block.id,
          html: analysisData[index].html
        }))

        const { results } = await $fetch('/api/batch-analyze-html', {
          method: 'POST',
          body: { records: analysisRecords }
        })

        // Обновляем блоки с результатами анализа
        for (const result of results) {
          await updateItem({
            collection: 'wblock_proto',
            id: result.id,
            item: {
              layout: result.layout,
              elements: result.elements,
              graphics: result.graphics,
              composition: result.treeStructure
            }
          })
        }

        console.log('Анализ HTML завершен')
      } catch (analysisError) {
        console.error('Ошибка анализа HTML:', analysisError)
      }
    }

    // Очищаем временные blob URL из памяти
    tempScreenshots.value.forEach(url => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url)
      }
    })

    // Очищаем выбранные блоки и временные скриншоты
    selectedBlocks.value = []
    tempScreenshots.value.clear()
    tempFileIds.value = []

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранено ${savedBlocks.length} блоков`,
      life: 3000
    })

  } catch (error) {
    console.error('Error saving blocks:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блоки',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}

// Функция cleanupTempFiles больше не нужна - временные скриншоты не сохраняются в Directus

// Инициализация
onMounted(async () => {
  await loadOptions()
  await loadAllBlocks() // Загружаем блоки сразу при загрузке страницы
})
</script>

<style scoped>
.my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 10px;
    line-height: 1.4;
    padding: 2px;
  }

.my-editor .prism-editor__textarea:focus {
  outline: none;
}

.masonry-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1rem;
  grid-auto-rows: min-content;
  align-items: start;
}

@media (max-width: 1024px) {
  .masonry-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .masonry-grid {
    grid-template-columns: 1fr;
  }
}

/* Улучшенное выделение выбранных карточек */
.selected-card {
  border: 1px solid #68a0f8 !important;

  background-color: rgba(59, 131, 246, 0.24) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}
</style>
