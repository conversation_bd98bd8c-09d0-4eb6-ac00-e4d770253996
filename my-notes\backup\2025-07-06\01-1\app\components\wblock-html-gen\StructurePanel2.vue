<template>
    <div class="flex flex-col h-full bg-gray-100 p-4 overflow-auto">
      <h3 class="text-lg font-semibold mb-4">Структура элементов</h3>
      <div class="text-sm">
        <div class="flex flex-col gap-2">
          <div
            v-for="(node, index) in nodes"
            :key="node.id"
            class="flex items-center p-2 bg-white rounded shadow-sm hover:bg-gray-50 relative"
            :class="{ 'drag-over': dragOverNodeIndex === index }"
            :style="{ marginLeft: `${node.level * 20}px` }"
            draggable="true"
            @dragstart="onDragStart($event, index)"
            @dragend="onDragEnd"
            @dragover.prevent="onDragOver($event, index)"
            @drop="onDrop($event, index)"
          >
            <div
              v-if="dragOverNodeIndex === index"
              class="absolute inset-0 border-2 border-primary rounded pointer-events-none"
            />
            <span class="flex-1">{{ node.name }}</span>
            <div class="flex gap-1">
              <Button
                icon="pi pi-arrow-up"
                class="p-button-text p-button-sm"
                :disabled="index === 0"
                @click="moveUp(index)"
              />
              <Button
                icon="pi pi-arrow-down"
                class="p-button-text p-button-sm"
                :disabled="index === nodes.length - 1"
                @click="moveDown(index)"
              />
              <Button
                icon="pi pi-arrow-left"
                class="p-button-text p-button-sm"
                :disabled="node.level === 0"
                @click="moveLeft(index)"
              />
              <Button
                icon="pi pi-arrow-right"
                class="p-button-text p-button-sm"
                :disabled="index === 0 || !canMoveRight(index)"
                @click="moveRight(index)"
              />
              <Button
  icon="pi pi-trash"
  class="p-button-text p-button-sm p-button-danger"
  @click="removeElement(index)"
/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, watch } from 'vue';
  
  interface Element {
  id: string;
  name: string;
  type: string;
  icon: string;
  template: string;
  level: number;
  children?: Element[];
}

const props = defineProps<{
  elements: Element[]
}>();
  
  const emit = defineEmits(['update:elements']);
  
  const nodes = ref([]);
  const draggedNodeIndex = ref(null);
  const dragOverNodeIndex = ref(null);
  
  // 1. Инициализация и обновление данных
  watch(() => props.elements, (newElements) => {
    updateNodes(newElements);
  }, { immediate: true, deep: true });
  
  function updateNodes(elements) {
  const flattenElements = (items, level = 0) => {
    let result = [];
    items.forEach(item => {
      // Гарантируем, что у элемента есть ID
      const node = { 
        ...item, 
        id: item.id || generateId(),
        level 
      };
      result.push(node);
      if (item.children && item.children.length > 0) {
        result = result.concat(flattenElements(item.children, level + 1));
      }
    });
    return result;
  };
  nodes.value = flattenElements(elements);
}

  
  // Обновленные функции moveUp и moveDown
  function moveUp(index) {
  if (index <= 0) return

  const updatedNodes = [...nodes.value]
  const current = updatedNodes[index]
  
  // Находим границы группы (элемент + дети)
  const groupIndices = [index]
  for (let i = index + 1; i < updatedNodes.length; i++) {
    if (updatedNodes[i].level > current.level) {
      groupIndices.push(i)
    } else {
      break
    }
  }

  // Находим позицию для вставки
  let insertAt = index - 1
  while (insertAt >= 0 && updatedNodes[insertAt].level !== current.level) {
    insertAt--
  }

  if (insertAt < 0) return

  // Перемещаем группу
  const group = updatedNodes.splice(index, groupIndices.length)
  updatedNodes.splice(insertAt, 0, ...group)

  nodes.value = updatedNodes
  emitUpdate()
}

function emitUpdate() {
  // Сначала строим иерархию
  const hierarchicalElements = buildHierarchy(nodes.value);
  
  // Затем гарантируем правильные уровни вложенности
  const normalizedElements = normalizeLevels(hierarchicalElements);
  
  emit('update:elements', normalizedElements);
}

function normalizeLevels(elements: Element[], level = 0): Element[] {
  return elements.map(el => ({
    ...el,
    level,
    children: el.children ? normalizeLevels(el.children, level + 1) : []
  }));
}





function moveDown(index) {
  if (index >= nodes.value.length - 1) return;

  const updatedNodes = [...nodes.value];
  const current = updatedNodes[index];
  const currentLevel = current.level;

  // Находим всех детей текущего элемента
  const childrenIndices = [];
  for (let i = index + 1; i < updatedNodes.length; i++) {
    if (updatedNodes[i].level > currentLevel) {
      childrenIndices.push(i);
    } else {
      break;
    }
  }

  // Находим следующий элемент того же уровня и его детей
  const nextElementIndex = index + childrenIndices.length + 1;
  let nextElementChildrenEnd = nextElementIndex + 1;
  
  while (nextElementChildrenEnd < updatedNodes.length && 
         updatedNodes[nextElementChildrenEnd].level > updatedNodes[nextElementIndex].level) {
    nextElementChildrenEnd++;
  }

  // Если следующего элемента нет или он другого уровня - не перемещаем
  if (nextElementIndex >= updatedNodes.length || 
      updatedNodes[nextElementIndex].level !== currentLevel) {
    return;
  }

  // Перемещаем текущий элемент и его детей
  const movingNodes = [current, ...childrenIndices.map(i => updatedNodes[i])];
  updatedNodes.splice(index, 1 + childrenIndices.length);
  updatedNodes.splice(nextElementChildrenEnd - childrenIndices.length - 1, 0, ...movingNodes);

  nodes.value = updatedNodes;
  emitUpdate();
}

  
  // 3. Функции изменения вложенности
  function canMoveRight(index) {
    if (index === 0) return false;
    return nodes.value[index].level === nodes.value[index - 1].level;
  }
  
  function moveRight(index) {
    if (!canMoveRight(index)) return;
  
    const updatedNodes = [...nodes.value];
    const current = updatedNodes[index];
    
    current.level += 1;
    
    // Обновляем уровень всех дочерних элементов
    for (let i = index + 1; i < updatedNodes.length; i++) {
      if (updatedNodes[i].level > current.level - 1) {
        updatedNodes[i].level += 1;
      } else {
        break;
      }
    }
  
    nodes.value = updatedNodes;
    emitUpdate();
  }
  
  function moveLeft(index) {
    if (nodes.value[index].level === 0) return;
  
    const updatedNodes = [...nodes.value];
    const current = updatedNodes[index];
    
    current.level -= 1;
    
    // Обновляем уровень всех дочерних элементов
    for (let i = index + 1; i < updatedNodes.length; i++) {
      if (updatedNodes[i].level > current.level) {
        updatedNodes[i].level -= 1;
      } else {
        break;
      }
    }
  
    nodes.value = updatedNodes;
    emitUpdate();
  }
  
  // 4. Drag and Drop
  function onDragStart(event, index) {
    draggedNodeIndex.value = index;
    event.dataTransfer.effectAllowed = 'move';
  }
  
  function onDragOver(event, index) {
    event.preventDefault();
    if (draggedNodeIndex.value !== null && draggedNodeIndex.value !== index) {
      dragOverNodeIndex.value = index;
    }
  }
  
  function onDragEnd() {
    dragOverNodeIndex.value = null;
    draggedNodeIndex.value = null;
  }
  
  function onDrop(event, index) {
  event.preventDefault()
  if (draggedNodeIndex.value === null) return

  const updatedNodes = [...nodes.value]
  const draggedNode = updatedNodes[draggedNodeIndex.value]

  // Находим всех детей перемещаемого элемента
  const childrenIndices = []
  for (let i = draggedNodeIndex.value + 1; i < updatedNodes.length; i++) {
    if (updatedNodes[i].level > draggedNode.level) {
      childrenIndices.push(i)
    } else {
      break
    }
  }

  // Удаляем перемещаемую группу
  const movingGroup = updatedNodes.splice(
    draggedNodeIndex.value,
    1 + childrenIndices.length
  )

  // Вставляем на новую позицию
  let insertAt = index
  if (index > draggedNodeIndex.value) {
    insertAt = index - childrenIndices.length - 1
  }

  updatedNodes.splice(insertAt, 0, ...movingGroup)
  nodes.value = updatedNodes
  emitUpdate()
}

  
  // 5. Преобразование в иерархическую структуру
  function buildHierarchy(flatNodes) {
    const result = [];
    const stack = [];
    
    for (let i = 0; i < flatNodes.length; i++) {
      const node = flatNodes[i];
      const { level, ...nodeData } = node;
      const newNode = { ...nodeData, children: [] };
      
      // Находим родителя в стеке
      while (stack.length > 0 && stack[stack.length - 1].level >= level) {
        stack.pop();
      }
      
      if (stack.length > 0) {
        // Добавляем к последнему подходящему родителю
        stack[stack.length - 1].children.push(newNode);
      } else {
        // Добавляем как корневой элемент
        result.push(newNode);
      }
      
      stack.push({ ...newNode, level });
    }
    
    return result;
  }
  
  



function removeElement(index) {
  if (confirm('Удалить элемент и все вложенные элементы?')) {
    const updatedNodes = [...nodes.value];
    const current = updatedNodes[index];
    const currentLevel = current.level;

    // Находим всех детей элемента
    const childrenIndices = [];
    for (let i = index + 1; i < updatedNodes.length; i++) {
      if (updatedNodes[i].level > currentLevel) {
        childrenIndices.push(i);
      } else {
        break;
      }
    }

    // Удаляем элемент и всех его детей
    updatedNodes.splice(index, 1 + childrenIndices.length);
    nodes.value = updatedNodes;
    emitUpdate();
  }
}


  </script>
  

  
  
  <style scoped>
  .drag-over {
    background-color: var(--primary-50);
    transition: all 0.2s;
    position: relative;
    z-index: 1;
  }
  </style>
  
  
  