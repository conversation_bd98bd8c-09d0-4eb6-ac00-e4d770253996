<template>
  <div class="flex flex-col gap-4 p-1">
    <div class="flex h-[calc(100vh-1rem)]">
      <!-- Левый сайдбар с контейнером для редактирования -->
      <div class="flex" :style="{ width: leftSidebarWidth }">
        <ElementsPanel
ref="elementsPanelRef" class="w-full" @element-added="handleElementAdded"
          @open-edit-form="showEditElementForm" @edit-element="editElement" @update:canvas-width="updateCanvasWidth" />
      </div>

      <!-- Дополнительный левый сайдбар для редактирования элемента -->
      <div
v-if="isElementFormVisible"
        class="flex flex-col bg-surface-50 dark:bg-surface-800 border-l border-r border-surface-200 dark:border-surface-700 h-full"
        :style="{
          width: elementSidebarWidth,
          minWidth: '250px',
          maxWidth: '400px',
          position: 'relative',
          zIndex: 10
        }">
        <div
          class="flex justify-between items-center p-2 border-b border-surface-200 dark:border-surface-700 bg-white flex-shrink-0">
          <h3 class="text-sm font-semibold">
            {{ editingElement ? 'Редактировать' : 'Добавить' }}
          </h3>
          <Button icon="pi pi-times" text class="p-button-sm" @click="closeElementForm" />
        </div>
        <!-- Используем компонент ElementEditForm для редактирования элементов -->
        <div class="flex-1 overflow-hidden">
          <ElementEditForm
v-model="elementFormData" :available-types="availableElementTypes" @save="saveElement"
            @cancel="closeElementForm" />
        </div>
      </div>

      <!-- Область предпросмотра и кода -->
      <div class="flex flex-col p-1" :style="{ width: canvasWidth }">
        <!-- Область предпросмотра -->
        <div class="flex-grow p-0 bg-surface-0 dark:bg-surface-900">
          <Preview
:elements="previewElements" :custom-css="customCss" :custom-js="customJs"
            @update:elements="updatePreviewElements">
            <template #header>
              <div class="flex items-center gap-1 flex-grow text-xs">
                <!-- Переключатели режимов -->
                <Button
v-tooltip.bottom="'Режим блока'"
                  :class="['text-xs mr-1', workMode === 'block' ? 'p-button-outlined' : '']" icon="pi pi-th-large"
                  @click="workMode = 'block'" />
                <Button
v-tooltip.bottom="'Режим страницы'"
                  :class="['text-xs mr-2', workMode === 'page' ? 'p-button-outlined' : '']" icon="pi pi-file"
                  @click="workMode = 'page'" />

                <!-- Поля для режима блока -->
                <template v-if="workMode === 'block'">
                  <InputText
id="number" v-model="info.number" placeholder="Номер блока"
                    class="mr-2 w-28 text-xs [&>input]:text-xs" style="font-size: 11px" />
                  <InputText
id="title" v-model="info.title" required placeholder="Название блока"
                    class="mr-2 text-xs [&>input]:text-xs w-full" style="font-size: 12px" />
                  <MultiSelect
v-model="info.block_type" :options="blockTypeOptions" display="chip"
                    class="text-xs mr-2 max-w-sm" filter placeholder="Выберите типы блока" panel-class="text-xs" :pt="{
                      item: { class: 'text-xs' },
                      header: { class: 'text-xs' },
                    }" />
                  <Button
v-tooltip.bottom="'Анализировать'" class="text-xs" icon="pi pi-cog"
                    @click="analyzeHtmlContent" />
                  <Button
v-tooltip.bottom="'Создать скриншот'" class="text-xs" icon="pi pi-camera"
                    @click="generateScreenshot" />
                  <Button v-tooltip.bottom="'Сохранить'" class="text-xs" icon="pi pi-save" @click="saveBlock" />
                </template>

                <!-- Поля для режима страницы -->
                <template v-if="workMode === 'page'">
                  <InputText
v-model="pageInfo.number" placeholder="Номер страницы"
                    class="mr-2 w-28 text-xs [&>input]:text-xs" style="font-size: 11px" />
                  <InputText
v-model="pageInfo.title" placeholder="Название страницы"
                    class="mr-2 text-xs [&>input]:text-xs w-full" style="font-size: 12px" />
                  <MultiSelect
v-model="pageInfo.tags" :options="pageTagsOptions" display="chip"
                    class="text-xs mr-2 max-w-sm" filter placeholder="Теги" panel-class="text-xs" :pt="{
                      item: { class: 'text-xs' },
                      header: { class: 'text-xs' },
                    }" />
                  <MultiSelect
v-model="pageInfo.page_type" :options="pageTypeOptions" display="chip"
                    class="text-xs mr-2 max-w-sm" filter placeholder="Тип страницы" panel-class="text-xs" :pt="{
                      item: { class: 'text-xs' },
                      header: { class: 'text-xs' },
                    }" />
                  <Button v-tooltip.bottom="'Сохранить страницу'" class="text-xs" icon="pi pi-save" @click="savePage" />
                  <Button
v-tooltip.bottom="'Сохранить блоки'" class="text-xs" icon="pi pi-th-large"
                    @click="saveWblockItems" />
                  <Button
v-tooltip.bottom="'Сохранить страницу и блоки'" class="text-xs" icon="pi pi-sitemap"
                    @click="savePageAndBlocks" />
                </template>
                <!-- Undo/Redo buttons -->
                <!-- <div class="absolute top-2 left-2 z-50">
                  <Button v-tooltip.bottom="'Отменить (Ctrl+Z)'" :disabled="!canUndo" icon="pi pi-undo" text
                    class="mr-2" @click="undo" />
                  <Button v-tooltip.bottom="'Повторить (Ctrl+Y)'" :disabled="!canRedo" icon="pi pi-redo" text
                    @click="redo" />
                </div> -->
              </div>
            </template>
          </Preview>
        </div>

        <!-- Панель с кодом -->
        <div class="border rounded-lg bg-surface-0 dark:bg-surface-900">
          <TabView
class="p-0" :pt="{
            panelcontainer: { style: 'padding:0' },
          }">
            <TabPanel header="HTML" value="html">
              <PrismEditorWithCopy
v-model="htmlContent" editor-class="w-full text-xs p-2 border rounded my-editor"
                :highlight="highlightHtml" placeholder="Enter HTML content" field-name="HTML"
                @input="updatePreviewFromHtml">
                <template #additional-buttons>
                  <Button
v-tooltip.left="'Вставить <!-- BLOCK -->'" icon="pi pi-plus" size="small" text
                    @click="insertBlockComment" />
                </template>
              </PrismEditorWithCopy>
            </TabPanel>
            <TabPanel header="HBS/JSON" value="hbs-json">
              <div class="p-0 grid grid-cols-2 gap-4">
                <div class="flex flex-col h-full">

                  <PrismEditorWithCopy
v-model="info.hbs" editor-class="my-editor flex-grow" :highlight="highlightHtml"
                    placeholder="Handlebars template will be generated here" field-name="HBS" max-height="150px" />
                </div>
                <div class="flex flex-col h-full">

                  <PrismEditorWithCopy
v-model="info.json" editor-class="my-editor flex-grow" :highlight="highlightJson"
                    placeholder="JSON data will be generated here" field-name="JSON" max-height="150px" />
                </div>
              </div>
            </TabPanel>
            <TabPanel header="CSS/JS" value="css-js">
              <div class="p-0 grid grid-cols-2 gap-4">
                <div class="flex flex-col h-full">
                  <PrismEditorWithCopy
v-model="customCss" editor-class="my-editor flex-grow" :highlight="highlightCss"
                    placeholder="Enter custom CSS" field-name="CSS" max-height="150px" />
                  <div class="flex justify-end mt-1">
                    <Button class="text-xs ms-1" label="BS" @click="addBootstrap" />
                    <Button class="text-xs ms-1" label="VJ" @click="addVj" />
                  </div>
                </div>
                <div class="flex flex-col h-full">
                  <PrismEditorWithCopy
v-model="customJs" editor-class="my-editor flex-grow" :highlight="highlightJs"
                    placeholder="Enter custom JavaScript" field-name="JavaScript" max-height="150px" />
                </div>
              </div>
            </TabPanel>
            <TabPanel header="Info" value="info">
              <div class="grid grid-cols-3 gap-4 p-2 text-xs">
                <div class="col-span-1">
                  <div class="mb-2">
                    <MultiSelect
v-model="info.welem_proto" :options="welemOptions" option-label="label"
                      option-value="value" display="chip" class="w-full" placeholder="Выберите элементы"
                      panel-class="text-xs" :pt="{
                        item: { class: 'text-xs' },
                        header: { class: 'text-xs' },
                      }" />
                  </div>
                  <div class="mb-2">
                    <Textarea
v-model="info.description" placeholder="Описание"
                      class="w-full text-xs [&>textarea]:text-xs" style="font-size: 10px; padding: 2px" />
                  </div>
                  <div class="mb-2">
                    <Textarea
v-model="info.composition" placeholder="Композиция"
                      class="w-full text-xs [&>textarea]:text-xs" style="font-size: 10px; padding: 2px" />
                  </div>
                  <div class="mb-2">
                    <Textarea
v-model="info.notes" placeholder="Заметки" class="w-full text-xs [&>textarea]:text-xs"
                      style="font-size: 10px; padding: 2px" />
                  </div>
                </div>

                <div class="col-span-1">
                  <div class="mb-2">
                    <Dropdown
v-model="info.status" :options="statusOptions" option-label="label" option-value="value"
                      placeholder="Выберите статус" class="w-full" />
                  </div>
                  <div class="mb-2">
                    <MultiSelect
v-model="info.concept" :options="conceptOptions" display="chip" class="w-full"
                      placeholder="Выберите концепты" panel-class="text-xs" :pt="{
                        item: { class: 'text-xs' },
                        header: { class: 'text-xs' },
                      }" />
                  </div>
                  <div class="mb-2">
                    <MultiSelect
v-model="info.layout" :options="layoutOptions" display="chip" class="w-full"
                      placeholder="Выберите макеты" panel-class="text-xs" :pt="{
                        item: { class: 'text-xs' },
                        header: { class: 'text-xs' },
                      }" />
                  </div>
                  <div class="mb-2">
                    <MultiSelect
v-model="info.elements" :options="elementOptions" display="chip" class="w-full"
                      placeholder="Выберите типы элементов" panel-class="text-xs" :pt="{
                        item: { class: 'text-xs' },
                        header: { class: 'text-xs' },
                      }" />
                  </div>
                  <div class="mb-2">
                    <MultiSelect
v-model="info.features" :options="featuresOptions" display="chip" class="w-full"
                      placeholder="Выберите функции" panel-class="text-xs" :pt="{
                        item: { class: 'text-xs' },
                        header: { class: 'text-xs' },
                      }" />
                  </div>
                </div>

                <div class="col-span-1">
                  <div class="mb-2">
                    <MultiSelect
v-model="info.style" :options="styleOptions" display="chip" class="w-full"
                      placeholder="Выберите стили" panel-class="text-xs" :pt="{
                        item: { class: 'text-xs' },
                        header: { class: 'text-xs' },
                      }" />
                  </div>
                  <div class="mb-2">
                    <MultiSelect
v-model="info.graphics" :options="graphicsOptions" display="chip" class="w-full"
                      placeholder="Выберите графику" panel-class="text-xs" :pt="{
                        item: { class: 'text-xs' },
                        header: { class: 'text-xs' },
                      }" />
                  </div>
                  <div class="mb-2">
                    <MultiSelect
v-model="info.collection" :options="collectionOptions" display="chip" class="w-full"
                      placeholder="Выберите коллекции" panel-class="text-xs" :pt="{
                        item: { class: 'text-xs' },
                        header: { class: 'text-xs' },
                      }" />
                  </div>
                  <div class="mb-2">
                    <div class="flex gap-2">
                      <FileUpload
mode="basic" :auto="true" accept="image/*" :max-file-size="1000000"
                        choose-label="Эскиз" class="text-xs" @select="onSketchSelect" />


                    </div>
                    <Image
v-if="info.sketch" :src="`http://localhost:8055/assets/${info.sketch}`" alt="Эскиз"
                      width="200" preview />
                  </div>
                </div>
              </div>
            </TabPanel>
          </TabView>
        </div>
      </div>

      <!-- Дополнительный правый сайдбар для редактирования блока -->
      <div
v-if="isBlockFormVisible"
        class="flex flex-col bg-surface-50 dark:bg-surface-800 border-l border-r border-surface-200 dark:border-surface-700 h-full"
        :style="{
          width: blockSidebarWidth,
          minWidth: '250px',
          maxWidth: '400px',
          position: 'relative',
          zIndex: 10
        }">
        <div
          class="flex justify-between items-center p-2 border-b border-surface-200 dark:border-surface-700 bg-white flex-shrink-0">
          <h3 class="text-sm font-semibold">
            {{ selectedBlock ? 'Редактировать' : 'Добавить' }}
          </h3>
          <Button icon="pi pi-times" text class="p-button-sm" @click="closeBlockForm" />
        </div>
        <div class="flex-1 overflow-hidden">
          <BlockForm
v-if="selectedBlock" :block="selectedBlock" :is-edit="!!selectedBlock" @save="handleBlockFormSave"
            @cancel="closeBlockForm" />
          <BlockForm v-else @save="handleBlockFormSave" @cancel="closeBlockForm" />
        </div>
      </div>

      <!-- Правый сайдбар с контейнером для редактирования -->
      <div class="flex" :style="{ width: rightSidebarWidth }">
        <RightPanel
ref="rightPanelRef" class="w-full" :elements="previewElements" :html-content="htmlContent"
          @block-select="handleBlockSelect" @update:elements="updatePreviewElements" @update:css="updateCustomCss"
          @update:js="updateCustomJs" @add-block-to-canvas="addBlockToCanvas" @open-block-form="openBlockForm"
          @edit-block="editBlock" @delete-block="deleteBlock" />
      </div>
    </div>
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import Preview from '~/components/wblock-html-gen/Preview.vue'
import ElementsPanel from '~/components/wblock-html-gen/ElementsPanel.vue'
import ElementEditForm from '~/components/wblock-html-gen/ElementEditForm.vue'
import RightPanel from '~/components/wblock-html-gen/RightPanel.vue'
import BlockForm from '~/components/wblock-html-gen/BlockForm.vue'
import Button from 'primevue/button'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import InputText from 'primevue/inputtext'
import Dialog from 'primevue/dialog'
import 'vue-prism-editor/dist/prismeditor.min.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import Textarea from 'primevue/textarea'
import MultiSelect from 'primevue/multiselect'
import Chips from 'primevue/chips'
import Dropdown from 'primevue/dropdown'
import FileUpload from 'primevue/fileupload'
import Image from 'primevue/image'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'
import Toast from 'primevue/toast'

interface Element {
  id: string
  name: string
  type: string
  icon: string
  template: string
  level: number
  children?: Element[]
}

interface WBlock {
  id?: string
  number: string
  title: string
  status: string
  description?: string
  composition?: string
  concept?: string[]
  block_type: string[] // Важно: обязательное поле
  layout?: string[]
  style?: string[]
  elements?: string[]
  graphics?: string[]
  collection?: string[]
  sketch?: string
  html?: string
  css?: string
  js?: string
  hbs?: string
  json?: string
  notes?: string
  date_updated?: string
  welem_proto?: string[]
}

const toast = useToast()
const blockName = ref('')
const previewElements = ref<Element[]>([])
const customCss = ref('')
const customJs = ref('')
const rightPanelRef = ref(null) // Ссылка на компонент RightPanel для прямого доступа
const elementsPanelRef = ref(null) // Ссылка на компонент ElementsPanel для прямого доступа

// Функция для добавления Bootstrap CSS и JS
const addBootstrap = () => {
  customCss.value =
    '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">' +
    (customCss.value ? '\n' + customCss.value : '')
  customJs.value =
    '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js" integrity="sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq" crossorigin="anonymous"><\/script>' +
    (customJs.value ? '\n' + customJs.value : '')
  toast.add({
    severity: 'success',
    summary: 'Bootstrap добавлен',
    detail: 'CSS и JavaScript Bootstrap успешно добавлены',
    life: 3000,
  })
}

const addVj = () => {
  customCss.value =
    '<link href="https://fm-demo.ru/html/css/vvv-style.bundle.css" rel="stylesheet" media="screen">' +
    (customCss.value ? '\n' + customCss.value : '')
  toast.add({
    severity: 'success',
    summary: 'VVVjs добавлен',
    detail: 'VVVjs CSS Style bundle успешно добавлены',
    life: 3000,
  })
}

// Размеры панелей
const leftSidebarWidth = ref('17%')
const rightSidebarWidth = ref('17%')
const canvasWidth = ref('66%')
const elementSidebarWidth = ref('17%')
const blockSidebarWidth = ref('17%')

// Функция для обновления ширины панелей при открытии/закрытии дополнительных сайдбаров
const updatePanelWidths = () => {
  if (isElementFormVisible.value && isBlockFormVisible.value) {
    // Оба дополнительных сайдбара открыты
    leftSidebarWidth.value = '7%'
    rightSidebarWidth.value = '7%'
    elementSidebarWidth.value = '15%'
    blockSidebarWidth.value = '15%'
    canvasWidth.value = '56%'
  } else if (isElementFormVisible.value) {
    // Только левый дополнительный сайдбар открыт
    leftSidebarWidth.value = '7%'
    rightSidebarWidth.value = '17%'
    elementSidebarWidth.value = '15%'
    canvasWidth.value = '61%'
  } else if (isBlockFormVisible.value) {
    // Только правый дополнительный сайдбар открыт
    leftSidebarWidth.value = '17%'
    rightSidebarWidth.value = '7%'
    blockSidebarWidth.value = '15%'
    canvasWidth.value = '61%'
  } else {
    // Оба дополнительных сайдбара закрыты
    leftSidebarWidth.value = '17%'
    rightSidebarWidth.value = '17%'
    canvasWidth.value = '66%'
  }
}

// Функция для обновления ширины канваса при открытии/закрытии дополнительных сайдбаров
const updateCanvasWidth = (width, direction) => {
  if (direction === 'left') {
    // Левый сайдбар редактирования открыт
    leftSidebarWidth.value = '7%'
    elementSidebarWidth.value = '15%'
    canvasWidth.value = '61%'
    if (isBlockFormVisible.value) {
      rightSidebarWidth.value = '7%'
      blockSidebarWidth.value = '15%'
    } else {
      rightSidebarWidth.value = '17%'
    }
  } else if (direction === 'right') {
    // Правый сайдбар редактирования открыт
    rightSidebarWidth.value = '7%'
    blockSidebarWidth.value = '15%'
    canvasWidth.value = '61%'
    if (isElementFormVisible.value) {
      leftSidebarWidth.value = '7%'
      elementSidebarWidth.value = '15%'
    } else {
      leftSidebarWidth.value = '17%'
    }
  } else {
    // Возвращаем к исходным размерам
    updatePanelWidths()
  }
}

const info = ref({
  number: '',
  title: '',
  description: '',
  composition: '',
  concept: [],
  block_type: [],
  layout: [],
  style: [],
  elements: [],
  features: [],
  graphics: [],
  collection: [],
  welem_proto: [],
  sketch: '',
  status: 'idea',
  html: '',
  css: '',
  js: '',
  hbs: '',
  json: '',
  notes: '',
  date_updated: '',
})

// Опции для выпадающих списков
const statusOptions = [
  { label: 'Идея', value: 'idea' },
  { label: 'В разработке', value: 'in_progress' },
  { label: 'Готово', value: 'done' },
  { label: 'Архив', value: 'archived' },
]

const conceptOptions = ref<string[]>([])
const blockTypeOptions = ref<string[]>([])
const elementOptions = ref<string[]>([])
const featuresOptions = ref<string[]>([])
const layoutOptions = ref<string[]>([])
const styleOptions = ref<string[]>([])
const graphicsOptions = ref<string[]>([])
const collectionOptions = ref<string[]>([])
const welemOptions = ref<string[]>([])

// Состояние режима работы (блок/страница)
const workMode = ref('block') // 'block' или 'page'

// Поля для режима страницы
const pageInfo = ref({
  number: '',
  title: '',
  tags: [],
  page_type: [],
  description: ''
})

// Опции для страниц
const pageTagsOptions = ref<string[]>([])
const pageTypeOptions = ref<string[]>([])

// Загрузка опций из коллекции wblock_proto
const loadOptions = async () => {
  try {
    const { getItems } = useDirectusItems()
    const items = await getItems({
      collection: 'wblock_proto',
      params: { limit: -1 },
    })

    if (Array.isArray(items)) {
      const concepts = new Set()
      const blockTypes = new Set()
      const elements = new Set()
      const features = new Set()
      const layouts = new Set()
      const styles = new Set()
      const graphics = new Set()
      const collections = new Set()

      items.forEach((item) => {
        item.concept?.forEach((c) => concepts.add(c))
        item.block_type?.forEach((t) => blockTypes.add(t))
        item.elements?.forEach((e) => elements.add(e))
        item.features?.forEach((f) => features.add(f))
        item.layout?.forEach((l) => layouts.add(l))
        item.style?.forEach((s) => styles.add(s))
        item.graphics?.forEach((g) => graphics.add(g))
        item.collection?.forEach((c) => collections.add(c))
      })

      conceptOptions.value = Array.from(concepts)
      blockTypeOptions.value = Array.from(blockTypes)
      elementOptions.value = Array.from(elements)
      featuresOptions.value = Array.from(features)
      layoutOptions.value = Array.from(layouts)
      styleOptions.value = Array.from(styles)
      graphicsOptions.value = Array.from(graphics)
      collectionOptions.value = Array.from(collections)
    }

    await loadWelemOptions()
    await loadPageOptions()
  } catch (error) {
    console.error('Ошибка загрузки опций:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить справочники',
      life: 3000,
    })
  }
}

// Add this function to load element options directly from welem_proto collection
// Обновленная функция loadWelemOptions
const loadWelemOptions = async () => {
  try {
    const { getItems } = useDirectusItems()
    const elements = await getItems({
      collection: 'welem_proto',
      params: {
        limit: -1,
        fields: ['id', 'title', 'elem_type'],
      },
    })

    if (Array.isArray(elements)) {
      welemOptions.value = elements.map((elem) => ({
        value: elem.id,
        label: elem.title || elem.id,
        elem_type: elem.elem_type || [],
      }))
    }
  } catch (error) {
    console.error('Ошибка загрузки элементов:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить элементы для выбора',
      life: 3000,
    })
  }
}

// Загрузка опций для страниц
const loadPageOptions = async () => {
  try {
    const { getItems } = useDirectusItems()

    // Загружаем данные из коллекции wpage
    const wpageItems = await getItems({
      collection: 'wpage',
      params: {
        limit: -1,
        fields: ['wpage_type', 'tags']
      }
    })

    // Извлекаем уникальные типы страниц
    const allPageTypes = new Set()
    wpageItems?.forEach(item => {
      if (item.wpage_type && Array.isArray(item.wpage_type)) {
        item.wpage_type.forEach(type => allPageTypes.add(type))
      }
    })
    pageTypeOptions.value = Array.from(allPageTypes)

    // Извлекаем уникальные теги
    const allPageTags = new Set()
    wpageItems?.forEach(item => {
      if (item.tags && Array.isArray(item.tags)) {
        item.tags.forEach(tag => allPageTags.add(tag))
      }
    })
    pageTagsOptions.value = Array.from(allPageTags)

  } catch (error) {
    console.error('Ошибка загрузки опций страниц:', error)
  }
}

// Храним соответствие ID элементов и их данных
const elementMap = ref<Record<string, { value: string; label: string }>>({})

// Обработчик добавления элемента
const handleElementAdded = async ({ element, source }) => {
  try {
    // 1. Добавляем элемент в превью
    if (element?.template) {
      previewElements.value = [...previewElements.value, element]

      // Обрабатываем CSS и JS напрямую из элемента, добавленного через ElementsPanel
      if (element.css) {
        updateCustomCss(element.css)
      }

      if (element.js) {
        updateCustomJs(element.js)
      }
    }

    // 2. Обрабатываем добавление в MultiSelect welem_proto
    if (source?.id) {
      // Проверяем, есть ли элемент в загруженных опциях
      const existingOption = welemOptions.value.find(
        (opt) => opt.value === source.id,
      )

      if (existingOption) {
        // Если элемент уже есть в опциях, добавляем его значение
        if (!info.value.welem_proto?.includes(existingOption.value)) {
          info.value.welem_proto = [
            ...(info.value.welem_proto || []),
            existingOption.value,
          ]
        }

        // 3. Если элемент уже был в опциях, добавляем CSS/JS
        // Загружаем CSS и JS для существующего элемента
        const { getItems } = useDirectusItems()
        const elementData = await getItems({
          collection: 'welem_proto',
          params: {
            filter: { id: { _eq: source.id } },
            fields: ['css', 'js', 'title'],
          },
        })

        if (elementData?.length > 0) {
          const elementRecord = elementData[0]

          // Добавляем CSS из элемента в поле customCss
          if (elementRecord.css) {
            updateCustomCss(elementRecord.css)
          }

          // Добавляем JavaScript из элемента в поле customJs
          if (elementRecord.js) {
            updateCustomJs(elementRecord.js)
          }

          console.log(`✅ Добавлен элемент ${elementRecord.title} с HTML${elementRecord.css ? ', CSS' : ''}${elementRecord.js ? ', JS' : ''}`)
        }
      } else {
        // Если элемента нет в опциях, загружаем его данные с CSS и JS
        const { getItems } = useDirectusItems()
        const elementData = await getItems({
          collection: 'welem_proto',
          params: {
            filter: { id: { _eq: source.id } },
            fields: ['id', 'title', 'elem_type', 'css', 'js'],
          },
        })

        if (elementData?.length > 0) {
          const elementRecord = elementData[0]
          const newOption = {
            value: elementRecord.id,
            label: elementRecord.title || elementRecord.id,
          }

          // Добавляем в опции
          welemOptions.value = [...welemOptions.value, newOption]

          // Добавляем в выбранные
          info.value.welem_proto = [
            ...(info.value.welem_proto || []),
            newOption.value,
          ]

          // 4. Добавляем CSS из элемента в поле customCss
          if (elementRecord.css) {
            updateCustomCss(elementRecord.css)
          }

          // 5. Добавляем JavaScript из элемента в поле customJs
          if (elementRecord.js) {
            updateCustomJs(elementRecord.js)
          }

          console.log(`✅ Добавлен элемент ${elementRecord.title} с HTML${elementRecord.css ? ', CSS' : ''}${elementRecord.js ? ', JS' : ''}`)
        }
      }
    }

    // 6. Обновляем типы элементов в info.elements
    if (source?.elem_type) {
      const newTypes = new Set(info.value.elements || [])

      source.elem_type.forEach((type) => {
        if (elementOptions.value.includes(type)) {
          newTypes.add(type)
        }
      })

      info.value.elements = Array.from(newTypes)
    }
  } catch (error) {
    console.error('Error handling added element:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось обработать добавленный элемент',
      life: 3000,
    })
  }
}

const updateElementTypes = (element) => {
  const newTypes = new Set(info.value.elements || [])

  // Добавляем тип элемента, если он есть в options
  if (
    element.type &&
    element.type !== 'html' &&
    elementOptions.value.includes(element.type)
  ) {
    newTypes.add(element.type)
  }

  // Добавляем типы из elem_type
  if (element.elem_type?.length) {
    element.elem_type.forEach((type) => {
      if (elementOptions.value.includes(type)) {
        newTypes.add(type)
      }
    })
  }

  info.value.elements = Array.from(newTypes)
}

const updatePreviewElements = (elements: Element[]) => {
  previewElements.value = elements
}

const htmlContent = ref('')

const updateHtmlContent = () => {
  if (!previewElements.value) return ''

  htmlContent.value = previewElements.value
    .filter((el) => el?.template) // Фильтруем элементы с template
    .map((el) => el.template)
    .join('\n')
}

const updatePreviewFromHtml = () => {
  if (htmlContent.value.trim()) {
    previewElements.value = [
      {
        name: 'custom-html',
        type: 'html',
        icon: 'pi pi-code',
        template: htmlContent.value,
      },
    ]
  }
}

// Функция для обновления CSS из внешнего компонента
function updateCustomCss(newCss) {
  if (!newCss) return

  if (customCss.value && customCss.value.trim() !== '') {
    if (!customCss.value.includes(newCss)) {
      customCss.value = `${customCss.value}${newCss}`
      console.log('CSS обновлен:', customCss.value.length, 'символов')
      toast.add({
        severity: 'success',
        summary: 'CSS добавлен',
        detail: `Добавлено ${newCss.length} символов CSS`,
        life: 2000,
      })
    }
  } else {
    customCss.value = newCss
    console.log('CSS добавлен:', customCss.value.length, 'символов')
    toast.add({
      severity: 'success',
      summary: 'CSS добавлен',
      detail: `Добавлено ${newCss.length} символов CSS`,
      life: 2000,
    })
  }
}

// Функция для обновления JavaScript из внешнего компонента
function updateCustomJs(newJs) {
  if (!newJs) return

  if (customJs.value && customJs.value.trim() !== '') {
    if (!customJs.value.includes(newJs)) {
      customJs.value = `${customJs.value}${newJs}`
      console.log('JavaScript обновлен:', customJs.value.length, 'символов')
      toast.add({
        severity: 'success',
        summary: 'JavaScript добавлен',
        detail: `Добавлено ${newJs.length} символов JavaScript`,
        life: 2000,
      })
    }
  } else {
    customJs.value = newJs
    console.log('JavaScript добавлен:', customJs.value.length, 'символов')
    toast.add({
      severity: 'success',
      summary: 'JavaScript добавлен',
      detail: `Добавлено ${newJs.length} символов JavaScript`,
      life: 2000,
    })
  }
}

watch(previewElements, updateHtmlContent, { immediate: true })

// Состояние формы элемента
const isElementFormVisible = ref(false)
const showElementForm = ref(false)
const elementFormLeft = ref('-30rem')
const editingElement = ref(null)
const elementFormData = ref({
  number: '',
  title: '',
  description: '',
  elem_type: [],
  html: '',
})

// Состояние формы блока
const isBlockFormVisible = ref(false)
const blockFormRight = ref('-30rem')
const selectedBlock = ref(null)
const editingBlock = ref(null)
const blockFormData = ref({
  number: '',
  title: '',
  description: '',
  block_type: [],
  status: 'idea',
  concept: [],
  layout: [],
  elements: [],
  html: '',
})

// Обработчики событий блоков (BlockForm)
function showBlockForm() {
  selectedBlock.value = null
  isBlockFormVisible.value = true
  updateCanvasWidth(null, 'right')
}

function editBlock(block) {
  selectedBlock.value = { ...block }
  isBlockFormVisible.value = true
  updateCanvasWidth(null, 'right')
}

function closeBlockForm() {
  isBlockFormVisible.value = false
  selectedBlock.value = null
  updateCanvasWidth()
}

// Обработка сохранения блока
function handleBlockFormSave(savedBlock) {
  closeBlockForm()
  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Блок сохранён',
    life: 3000,
  })

  // Используем ref для прямого доступа к компоненту RightPanel для обновления списка
  // и вызываем его метод loadBlocks для обновления списка
  if (
    rightPanelRef.value &&
    typeof rightPanelRef.value.loadBlocks === 'function'
  ) {
    rightPanelRef.value.loadBlocks()
  }

  // Если блок был выбран для редактирования, обновляем его данные
  if (selectedBlock.value && savedBlock.id === selectedBlock.value.id) {
    selectedBlock.value = { ...savedBlock }
  }
}

// Добавим функцию для загрузки блоков
async function loadBlocks() {
  try {
    const { getItems } = useDirectusItems()
    blocks.value = await getItems({
      collection: 'wblock_proto',
      params: { sort: ['number'] },
    })
  } catch (error) {
    console.error('Ошибка загрузки блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить блоки',
      life: 3000,
    })
  }
}

// Доступные типы элементов для формы редактирования
const availableElementTypes = ref([])

// Обработчики событий элементов
function showEditElementForm(element, types) {
  console.log('showEditElementForm called with:', { element, types })
  editingElement.value = element
  elementFormData.value = element
    ? { ...element }
    : {
      number: '',
      title: '',
      description: '',
      elem_type: [],
      html: '',
    }
  // Сохраняем доступные типы элементов
  if (types && Array.isArray(types)) {
    availableElementTypes.value = types
  } else {
    // Если типы не переданы, используем значения по умолчанию
    availableElementTypes.value = ['Заголовок', 'Текст', 'Краткий текст']
  }
  console.log('Setting isElementFormVisible to true')
  isElementFormVisible.value = true
  updateCanvasWidth(null, 'left')
  console.log('Element form should be visible now:', isElementFormVisible.value)
}

function editElement(element) {
  showEditElementForm(element)
}

function closeElementForm() {
  isElementFormVisible.value = false
  editingElement.value = null
  updateCanvasWidth()
}

async function saveElement() {
  try {
    const { createItems, updateItem } = useDirectusItems()

    // Убедимся, что все поля правильно заполнены
    const itemToSave = {
      ...elementFormData.value,
      // Убедимся, что elem_type всегда массив
      elem_type: Array.isArray(elementFormData.value.elem_type)
        ? elementFormData.value.elem_type
        : [],
      // Убедимся, что html и description сохраняются
      html: elementFormData.value.html || '',
      description: elementFormData.value.description || '',
    }

    if (editingElement.value && editingElement.value.id) {
      // Обновление существующего элемента
      await updateItem({
        collection: 'welem_proto',
        id: editingElement.value.id,
        item: itemToSave,
      })
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Элемент обновлен',
        life: 3000,
      })
    } else {
      // Создание нового элемента
      await createItems({
        collection: 'welem_proto',
        items: [itemToSave], // Исправлено: items вместо item
      })
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Элемент создан',
        life: 3000,
      })
    }

    // Обновляем список элементов
    if (
      elementsPanelRef.value &&
      typeof elementsPanelRef.value.loadElements === 'function'
    ) {
      elementsPanelRef.value.loadElements()
    }

    closeElementForm()
  } catch (error) {
    console.error('Ошибка при сохранении элемента:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить элемент',
      life: 3000,
    })
  }
}

// Обработка загрузки эскиза
async function onSketchSelect(event) {
  const file = event.files[0]
  if (!file) return

  try {
    const formData = new FormData() // С большой буквы!
    formData.append('file', file)

    const response = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) throw new Error('Ошибка загрузки файла')

    const { data } = await response.json()
    info.value.sketch = data.id

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Эскиз загружен',
      life: 3000,
    })
  } catch (error) {
    console.error('Ошибка при загрузке эскиза:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить эскиз',
      life: 3000,
    })
  }
}

// Сохранение блока из редактора
const saveBlock = async () => {
  // Проверка обязательных полей
  if (
    !info.value.number ||
    !info.value.title ||
    !info.value.block_type?.length
  ) {
    toast.add({
      severity: 'warn',
      summary: 'Внимание',
      detail: 'Заполните номер, название и тип блока',
      life: 3000,
    })
    return
  }

  try {
    const { createItems } = useDirectusItems()

    // 1. Сначала создаем основной блок
    const blockData = {
      number: info.value.number,
      title: info.value.title,
      status: info.value.status || 'idea',
      block_type: info.value.block_type,
      description: info.value.description || undefined,
      composition: info.value.composition || undefined,
      concept: info.value.concept?.length ? info.value.concept : undefined,
      layout: info.value.layout?.length ? info.value.layout : undefined,
      style: info.value.style?.length ? info.value.style : undefined,
      elements: info.value.elements?.length ? info.value.elements : undefined,
      graphics: info.value.graphics?.length ? info.value.graphics : undefined,
      collection: info.value.collection?.length
        ? info.value.collection
        : undefined,
      sketch: info.value.sketch || undefined,
      html: htmlContent.value || undefined,
      css: customCss.value || undefined,
      js: customJs.value || undefined,
      hbs: info.value.hbs || undefined,
      json: info.value.json || undefined,
      notes: info.value.notes || undefined,
    }

    // Создаем блок в Directus
    const result = await createItems({
      collection: 'wblock_proto',
      items: [blockData],
    })

    // Получаем ID созданного блока
    const blockId = Array.isArray(result) ? result[0]?.id : result?.id
    if (!blockId) throw new Error('Не удалось получить ID созданного блока')

    // 2. Создаем связи M2M с элементами welem_proto
    if (info.value.welem_proto?.length) {
      // Преобразуем элементы связи в нужный формат
      const relations = info.value.welem_proto.map((welemId) => ({
        wblock_proto_id: blockId,
        welem_proto_id: typeof welemId === 'object' ? welemId.value : welemId,
      }))

      // Создаем связи в промежуточной таблице
      await createItems({
        collection: 'wblock_proto_welem_proto',
        items: relations,
      })
    }

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Блок "${info.value.title}" сохранён`,
      life: 3000,
    })

    // Обновляем список блоков в RightPanel
    if (rightPanelRef.value?.loadBlocks) {
      rightPanelRef.value.loadBlocks()
    }

    resetForm()
  } catch (error) {
    console.error('Ошибка сохранения:', error)

    let errorDetail = 'Не удалось сохранить блок'
    if (error.response?.data?.errors) {
      errorDetail +=
        ': ' + error.response.data.errors.map((e) => e.message).join(', ')
    }

    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: errorDetail,
      life: 5000,
    })
  }
}

// Сброс формы
function resetForm() {
  info.value = {
    number: '',
    title: '',
    description: '',
    composition: '',
    concept: [],
    block_type: [],
    layout: [],
    style: [],
    elements: [],
    features: [],
    graphics: [],
    collection: [],
    welem_proto: [],
    sketch: '',
    status: 'idea',
    html: '',
    css: '',
    js: '',
    hbs: '',
    json: '',
    notes: '',
    date_updated: '',
  }
  htmlContent.value = ''
  customCss.value = ''
  customJs.value = ''
  previewElements.value = []
}

function deleteBlock(block) {
  // Здесь будет логика удаления блока
}

function addBlockToCanvas(block) {
  previewElements.value = [
    ...previewElements.value,
    {
      name: block.title,
      type: block.block_type[0],
      icon: 'pi pi-code',
      template: block.html || '',
    },
  ]
}

// Добавляем недостающие функции
function handleBlockSelect(block) {
  selectedBlock.value = block
  console.log('Block selected:', block)
}

function openBlockForm(block = null) {
  if (block) {
    selectedBlock.value = { ...block }
  } else {
    selectedBlock.value = null
  }
  isBlockFormVisible.value = true
  updateCanvasWidth(null, 'right')
}

// Функция для подсветки выбранного элемента
function highlightElement(nodeId) {
  // Реализация подсветки элемента
  console.log('Highlight element:', nodeId)
}



// Инициализация при монтировании компонента
onMounted(async () => {
  await loadOptions()
  // Инициализируем карту элементов
  const { getItems } = useDirectusItems()
  const elements = await getItems({
    collection: 'welem_proto',
    params: {
      limit: -1,
      fields: ['id', 'title'],
    },
  })

  if (Array.isArray(elements)) {
    elements.forEach((el) => {
      elementMap.value[el.id] = {
        value: el.id,
        label: el.title || el.id,
      }
    })
    welemOptions.value = Object.values(elementMap.value)
  }

})

// Автоматическое обновление описания при изменении выбранного макета
watch(
  () => info.value.layout,
  (newLayout) => {
    if (newLayout && newLayout.length) {
      // Добавляем информацию о макете в описание, если её еще нет
      const layoutInfo = `Используемые макеты: ${newLayout.join(', ')}`
      if (!info.value.description.includes(layoutInfo)) {
        info.value.description = info.value.description
          ? `${info.value.description}\n${layoutInfo}`
          : layoutInfo
      }
    }
  },
  { deep: true },
)

// Модифицированная версия наблюдателя для previewElements
// Add this watch to track types from added elements
watch(
  previewElements,
  (newElements) => {
    // Extract types from elements
    // Extract types from elements
    const types = new Set()

    newElements.forEach((element) => {
      if (element.type && element.type !== 'html') {
        types.add(element.type)
      }
    })

    // Add these types to info.elements if they exist in elementOptions
    Array.from(types).forEach((type) => {
      if (
        elementOptions.value.includes(type) &&
        !info.value.elements.includes(type)
      ) {
        info.value.elements.push(type)
      }
    })
  },
  { deep: true },
)

const analyzeHtmlContent = async () => {
  try {
    // 1. Анализ HTML структуры
    const { layout, elements, graphics, features, treeStructure } = await $fetch(
      '/api/analyze-html',
      {
        method: 'POST',
        body: { html: htmlContent.value },
      },
    )

    info.value.layout = [...new Set([...info.value.layout, ...layout])]
    info.value.elements = [...new Set([...info.value.elements, ...elements])]
    info.value.graphics = [...new Set([...info.value.graphics, ...graphics])]
    info.value.features = [...new Set([...info.value.features, ...features])]
    // Записываем древовидное представление HTML-структуры в поле composition
    if (treeStructure) {
      info.value.composition = treeStructure
    }

    // 2. Генерация HBS и JSON шаблонов
    if (htmlContent.value.trim()) {
      const templateResult = await $fetch('/api/convert-html-template', {
        method: 'POST',
        body: {
          html: htmlContent.value,
          format: 'handlebars',
          blockName: info.value.title || '',
          blockNumber: info.value.number || ''
        }
      })

      if (templateResult.success) {
        info.value.hbs = templateResult.template
        info.value.json = JSON.stringify(templateResult.jsonData, null, 2)
      } else {
        console.warn('Ошибка генерации HBS/JSON:', templateResult.error)
      }
    }

    toast.add({
      severity: 'success',
      summary: 'Анализ завершён',
      detail: 'Поля обновлены, HBS и JSON сгенерированы',
      life: 2000,
    })
  } catch (error) {
    console.error('Ошибка анализа HTML:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось проанализировать HTML',
      life: 3000,
    })
  }
}

// Функция для создания скриншота
const generateScreenshot = async () => {
  if (!htmlContent.value.trim()) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Нет HTML контента для создания скриншота',
      life: 3000,
    })
    return
  }

  try {
    const blockTitle = info.value.title || 'Untitled Block'

    // Создаем полный HTML для скриншота
    const blockFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${blockTitle}</title>
  ${customCss.value || ''}
</head>
<body>
  ${htmlContent.value}
  ${customJs.value || ''}
</body>
</html>`

    // Создаем временный скриншот
    const response = await $fetch('/api/capture-html-screenshot-temp', {
      method: 'POST',
      body: {
        html: blockFullHtml,
        width: 1400,
        height: 800
      },
      responseType: 'blob'
    })

    // Создаем временный URL из blob
    const tempUrl = URL.createObjectURL(response as Blob)

    // Создаем файл для FileUpload
    const blob = response as Blob
    const file = new File([blob], `screenshot_${Date.now()}.png`, { type: 'image/png' })

    // Загружаем файл в Directus
    const formData = new FormData()
    formData.append('file', file)

    const uploadResponse = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!uploadResponse.ok) throw new Error('Ошибка загрузки файла')

    const { data } = await uploadResponse.json()
    info.value.sketch = data.id

    toast.add({
      severity: 'success',
      summary: 'Скриншот создан',
      detail: 'Скриншот успешно создан и загружен как эскиз',
      life: 3000,
    })
  } catch (error) {
    console.error('Ошибка создания скриншота:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось создать скриншот',
      life: 3000,
    })
  }
}

// Syntax highlighting functions
const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}



// Функция вставки комментария блока в HTML
const insertBlockComment = () => {
  const textarea = document.querySelector('.my-editor textarea') as HTMLTextAreaElement
  if (!textarea) return

  const cursorPosition = textarea.selectionStart
  const textBefore = htmlContent.value.substring(0, cursorPosition)
  const textAfter = htmlContent.value.substring(cursorPosition)

  // Вставляем комментарий и перенос строки
  const blockComment = '<!-- BLOCK -->\n'
  htmlContent.value = textBefore + blockComment + textAfter

  // Устанавливаем курсор после вставленного комментария
  nextTick(() => {
    textarea.focus()
    textarea.setSelectionRange(cursorPosition + blockComment.length, cursorPosition + blockComment.length)
  })

  toast.add({
    severity: 'success',
    summary: 'Комментарий вставлен',
    detail: 'Разделитель блока <!-- BLOCK --> добавлен',
    life: 2000,
  })
}

// Функции для режима страницы

// Функция сохранения страницы
const savePage = async () => {
  if (!pageInfo.value.title || !htmlContent.value) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните название страницы и HTML контент',
      life: 3000,
    })
    return
  }

  try {
    const { createItems } = useDirectusItems()

    // Создаем полный HTML для скриншота
    const pageTitle = pageInfo.value.title || 'Untitled Page'
    const pageFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${pageTitle}</title>
  ${customCss.value || ''}
</head>
<body>
  ${htmlContent.value}
  ${customJs.value || ''}
</body>
</html>`

    // Создаем скриншот страницы
    let screenshotId = null
    try {
      const screenshotResponse = await $fetch('/api/capture-html-screenshot-temp', {
        method: 'POST',
        body: {
          html: pageFullHtml,
          width: 1400,
          height: 800
        },
        responseType: 'blob'
      })

      // Загружаем скриншот в Directus
      const blob = screenshotResponse as Blob
      // Создаем имя файла по правилу: page + number + title
      const sanitizedPageTitle = pageTitle.replace(/[^a-zA-Z0-9\-_]/g, '_')
      const pageNumber = pageInfo.value.number || 'page'
      const file = new File([blob], `page_${pageNumber}_${sanitizedPageTitle}.png`, { type: 'image/png' })

      const formData = new FormData()
      formData.append('file', file)

      const uploadResponse = await fetch('http://localhost:8055/files', {
        method: 'POST',
        body: formData,
      })

      if (uploadResponse.ok) {
        const { data } = await uploadResponse.json()
        screenshotId = data.id
      }
    } catch (screenshotError) {
      console.warn('Ошибка создания скриншота:', screenshotError)
    }

    // Сохраняем страницу
    const pageData = {
      number: pageInfo.value.number || '',
      title: pageInfo.value.title,
      html: htmlContent.value,
      css: customCss.value || '',
      js: customJs.value || '',
      wpage_type: pageInfo.value.page_type,
      tags: pageInfo.value.tags,
      sketch: screenshotId
    }

    await createItems({
      collection: 'wpage',
      items: [pageData]
    })

    toast.add({
      severity: 'success',
      summary: 'Страница сохранена',
      detail: `Страница "${pageInfo.value.title}" (${pageInfo.value.number || 'без номера'}) успешно сохранена${screenshotId ? ' со скриншотом' : ''}`,
      life: 4000,
    })

  } catch (error) {
    console.error('Ошибка сохранения страницы:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить страницу',
      life: 3000,
    })
  }
}

// Функция сохранения блоков из HTML
const saveWblockItems = async () => {
  if (!htmlContent.value || !pageInfo.value.title) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните HTML контент и название страницы',
      life: 3000,
    })
    return
  }

  try {
    const { createItems } = useDirectusItems()

    // Разделяем HTML на блоки по комментариям <!-- BLOCK -->
    const blockDelimiter = '<!-- BLOCK -->'
    const htmlParts = htmlContent.value.split(blockDelimiter).filter(part => part.trim())

    if (htmlParts.length === 0) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'HTML не содержит разделителей блоков <!-- BLOCK -->',
        life: 3000,
      })
      return
    }

    const blocksToSave = []
    const screenshotIds = []

    // Обрабатываем каждый блок
    for (let i = 0; i < htmlParts.length; i++) {
      const blockHtml = htmlParts[i].trim()
      if (!blockHtml) continue

      // Генерируем номер и название блока
      const blockNumber = `${pageInfo.value.number || 'page'}-${String(i + 1).padStart(2, '0')}`
      const blockTitle = `${pageInfo.value.title} - ${String(i + 1).padStart(2, '0')}`

      // Анализируем HTML блока
      let analysisResult = {
        layout: [],
        elements: [],
        graphics: [],
        features: [],
        composition: '',
        treeStructure: ''
      }

      try {
        const analysisResponse = await $fetch('/api/analyze-html', {
          method: 'POST',
          body: { html: blockHtml }
        })
        if (analysisResponse) {
          analysisResult = analysisResponse
        }
      } catch (analysisError) {
        console.warn('Ошибка анализа HTML блока:', analysisError)
      }

      // Генерируем HBS и JSON для блока
      let hbsTemplate = ''
      let jsonData = ''

      try {
        const templateResponse = await $fetch('/api/convert-html-template', {
          method: 'POST',
          body: {
            html: blockHtml,
            format: 'handlebars',
            blockName: blockTitle,
            blockNumber: blockNumber
          }
        })
        if (templateResponse && templateResponse.success) {
          hbsTemplate = templateResponse.template || ''
          jsonData = JSON.stringify(templateResponse.jsonData || {}, null, 2)
        }
      } catch (templateError) {
        console.warn('Ошибка генерации шаблона для блока:', templateError)
      }

      // Создаем скриншот блока
      let screenshotId = null
      try {
        const blockFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${blockTitle}</title>
  ${customCss.value || ''}
</head>
<body>
  ${blockHtml}
  ${customJs.value || ''}
</body>
</html>`

        const screenshotResponse = await $fetch('/api/capture-html-screenshot-temp', {
          method: 'POST',
          body: {
            html: blockFullHtml,
            width: 1400,
            height: 800
          },
          responseType: 'blob'
        })

        // Загружаем скриншот в Directus
        const blob = screenshotResponse as Blob
        // Создаем имя файла по правилу: block + number + title
        const sanitizedTitle = blockTitle.replace(/[^a-zA-Z0-9\-_]/g, '_')
        const file = new File([blob], `block_${blockNumber}_${sanitizedTitle}.png`, { type: 'image/png' })

        const formData = new FormData()
        formData.append('file', file)

        const uploadResponse = await fetch('http://localhost:8055/files', {
          method: 'POST',
          body: formData,
        })

        if (uploadResponse.ok) {
          const { data } = await uploadResponse.json()
          screenshotId = data.id
        }
      } catch (screenshotError) {
        console.warn('Ошибка создания скриншота блока:', screenshotError)
      }

      // Подготавливаем данные блока
      const blockData = {
        number: blockNumber,
        title: blockTitle,
        html: blockHtml,
        css: customCss.value || '',
        js: customJs.value || '',
        hbs: hbsTemplate,
        json: jsonData,
        layout: analysisResult.layout,
        elements: analysisResult.elements,
        graphics: analysisResult.graphics,
        features: analysisResult.features,
        composition: analysisResult.treeStructure || analysisResult.composition || '',
        collection: pageInfo.value.tags || [],
        block_type: ['Контент'],
        status: 'idea',
        sketch: screenshotId
      }

      blocksToSave.push(blockData)
    }

    // Сохраняем все блоки
    if (blocksToSave.length > 0) {
      await createItems({
        collection: 'wblock_proto',
        items: blocksToSave
      })

      toast.add({
        severity: 'success',
        summary: 'Блоки сохранены',
        detail: `Успешно сохранено ${blocksToSave.length} блоков из страницы "${pageInfo.value.title}" с анализом HTML, HBS/JSON шаблонами и скриншотами`,
        life: 4000,
      })
    }

  } catch (error) {
    console.error('Ошибка сохранения блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блоки',
      life: 3000,
    })
  }
}

// Функция сохранения страницы и блоков с созданием связей
const savePageAndBlocks = async () => {
  if (!htmlContent.value || !pageInfo.value.title) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните HTML контент и название страницы',
      life: 3000,
    })
    return
  }

  try {
    const { createItems } = useDirectusItems()

    // 1. Сначала сохраняем страницу
    const pageTitle = pageInfo.value.title || 'Untitled Page'
    const pageFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${pageTitle}</title>
  ${customCss.value || ''}
</head>
<body>
  ${htmlContent.value}
  ${customJs.value || ''}
</body>
</html>`

    // Создаем скриншот страницы
    let pageScreenshotId = null
    try {
      const screenshotResponse = await $fetch('/api/capture-html-screenshot-temp', {
        method: 'POST',
        body: {
          html: pageFullHtml,
          width: 1400,
          height: 800
        },
        responseType: 'blob'
      })

      const blob = screenshotResponse as Blob
      // Создаем имя файла по правилу: page + number + title
      const sanitizedPageTitle = pageTitle.replace(/[^a-zA-Z0-9\-_]/g, '_')
      const pageNumber = pageInfo.value.number || 'page'
      const file = new File([blob], `page_${pageNumber}_${sanitizedPageTitle}.png`, { type: 'image/png' })

      const formData = new FormData()
      formData.append('file', file)

      const uploadResponse = await fetch('http://localhost:8055/files', {
        method: 'POST',
        body: formData,
      })

      if (uploadResponse.ok) {
        const { data } = await uploadResponse.json()
        pageScreenshotId = data.id
      }
    } catch (screenshotError) {
      console.warn('Ошибка создания скриншота страницы:', screenshotError)
    }

    // Сохраняем страницу
    const pageData = {
      number: pageInfo.value.number || '',
      title: pageInfo.value.title,
      html: htmlContent.value,
      css: customCss.value || '',
      js: customJs.value || '',
      wpage_type: pageInfo.value.page_type,
      tags: pageInfo.value.tags,
      sketch: pageScreenshotId
    }

    const savedPage = await createItems({
      collection: 'wpage',
      items: [pageData]
    })

    const pageId = Array.isArray(savedPage) ? savedPage[0].id : savedPage.id

    // 2. Теперь сохраняем блоки
    const blockDelimiter = '<!-- BLOCK -->'
    const htmlParts = htmlContent.value.split(blockDelimiter).filter(part => part.trim())

    if (htmlParts.length === 0) {
      toast.add({
        severity: 'warn',
        summary: 'Предупреждение',
        detail: 'HTML не содержит разделителей блоков <!-- BLOCK -->',
        life: 3000,
      })
      return
    }

    const blocksToSave = []
    const savedBlockIds = []

    // Обрабатываем каждый блок
    for (let i = 0; i < htmlParts.length; i++) {
      const blockHtml = htmlParts[i].trim()
      if (!blockHtml) continue

      const blockNumber = `${pageInfo.value.number || 'page'}-${String(i + 1).padStart(2, '0')}`
      const blockTitle = `${pageInfo.value.title} - ${String(i + 1).padStart(2, '0')}`

      // Анализируем HTML блока
      let analysisResult = {
        layout: [],
        elements: [],
        graphics: [],
        features: [],
        composition: '',
        treeStructure: ''
      }

      try {
        const analysisResponse = await $fetch('/api/analyze-html', {
          method: 'POST',
          body: { html: blockHtml }
        })
        if (analysisResponse) {
          analysisResult = analysisResponse
        }
      } catch (analysisError) {
        console.warn('Ошибка анализа HTML блока:', analysisError)
      }

      // Генерируем HBS и JSON для блока
      let hbsTemplate = ''
      let jsonData = ''

      try {
        const templateResponse = await $fetch('/api/convert-html-template', {
          method: 'POST',
          body: {
            html: blockHtml,
            format: 'handlebars',
            blockName: blockTitle,
            blockNumber: blockNumber
          }
        })
        if (templateResponse && templateResponse.success) {
          hbsTemplate = templateResponse.template || ''
          jsonData = JSON.stringify(templateResponse.jsonData || {}, null, 2)
        }
      } catch (templateError) {
        console.warn('Ошибка генерации шаблона для блока:', templateError)
      }

      // Создаем скриншот блока
      let screenshotId = null
      try {
        const blockFullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${blockTitle}</title>
  ${customCss.value || ''}
</head>
<body>
  ${blockHtml}
  ${customJs.value || ''}
</body>
</html>`

        const screenshotResponse = await $fetch('/api/capture-html-screenshot-temp', {
          method: 'POST',
          body: {
            html: blockFullHtml,
            width: 1400,
            height: 800
          },
          responseType: 'blob'
        })

        const blob = screenshotResponse as Blob
        // Создаем имя файла по правилу: block + number + title
        const sanitizedTitle = blockTitle.replace(/[^a-zA-Z0-9\-_]/g, '_')
        const file = new File([blob], `block_${blockNumber}_${sanitizedTitle}.png`, { type: 'image/png' })

        const formData = new FormData()
        formData.append('file', file)

        const uploadResponse = await fetch('http://localhost:8055/files', {
          method: 'POST',
          body: formData,
        })

        if (uploadResponse.ok) {
          const { data } = await uploadResponse.json()
          screenshotId = data.id
        }
      } catch (screenshotError) {
        console.warn('Ошибка создания скриншота блока:', screenshotError)
      }

      const blockData = {
        number: blockNumber,
        title: blockTitle,
        html: blockHtml,
        css: customCss.value || '',
        js: customJs.value || '',
        hbs: hbsTemplate,
        json: jsonData,
        layout: analysisResult.layout,
        elements: analysisResult.elements,
        graphics: analysisResult.graphics,
        features: analysisResult.features,
        composition: analysisResult.treeStructure || analysisResult.composition || '',
        collection: pageInfo.value.tags || [],
        block_type: ['Контент'],
        status: 'idea',
        sketch: screenshotId
      }

      blocksToSave.push(blockData)
    }

    // Сохраняем все блоки
    if (blocksToSave.length > 0) {
      const savedBlocks = await createItems({
        collection: 'wblock_proto',
        items: blocksToSave
      })

      // Получаем ID сохраненных блоков
      const blockIds = Array.isArray(savedBlocks)
        ? savedBlocks.map(block => block.id)
        : [savedBlocks.id]

      // 3. Создаем связи между страницей и блоками
      const relations = blockIds.map(blockId => ({
        wpage_id: pageId,
        wblock_proto_id: blockId
      }))

      await createItems({
        collection: 'wpage_wblock_proto',
        items: relations
      })

      toast.add({
        severity: 'success',
        summary: 'Страница и блоки сохранены',
        detail: `Успешно сохранена страница "${pageInfo.value.title}" и ${blocksToSave.length} блоков с полным анализом, шаблонами, скриншотами и связями между ними`,
        life: 5000,
      })
    }

  } catch (error) {
    console.error('Ошибка сохранения страницы и блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить страницу и блоки',
      life: 3000,
    })
  }
}
</script>

<style scoped>
.my-editor {
  /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
  background: #f3f3f3;
  color: #666;

  /* you must provide font-family font-size line-height. Example: */
  font-family:
    Fira code,
    Fira Mono,
    Consolas,
    Menlo,
    Courier,
    monospace;
  font-size: 10px;
  line-height: 1.4;
  padding: 2px;
}

/* optional class for removing the outline */
.prism-editor__textarea:focus {
  outline: none;
}

/* Стили для адаптивной высоты */
.h-\[calc\(100vh-6rem\)\] {
  height: calc(100vh - 6rem);
  max-height: calc(100vh - 6rem);
  min-height: calc(100vh - 6rem);
}

/* Стили для сайдбаров */
.flex>div {
  transition: width 0.3s ease;
  /* Убираем возможность resize */
  resize: none !important;
}

/* Предотвращаем проблемы с overflow в toolbar */
.structure-toolbar {
  overflow: visible !important;
  position: relative;
}

/* Убираем все resize handles */
* {
  resize: none !important;
}

/* Исправляем позиционирование форм */
.flex.h-\[calc\(100vh-1rem\)\] {
  position: relative;
  overflow: hidden;
}

/* Обеспечиваем корректное отображение боковых форм */
.flex.h-\[calc\(100vh-1rem\)\]>div {
  flex-shrink: 0;
  position: relative;
}

/* Предотвращаем горизонтальную прокрутку */
.flex.h-\[calc\(100vh-1rem\)\] {
  overflow-x: hidden;
}
</style>
