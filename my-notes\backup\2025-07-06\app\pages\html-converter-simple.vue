<template>
  <div class="html-converter-page p-4">
    <div class="mb-4">
      <h4 class="text-3xl font-bold">Упрощенный конвертер HTML в шаблоны</h4>
      <p class="text-lg mt-2">Версия с сохранением оригинального форматирования атрибутов data-*</p>
    </div>
    
    <Divider />
    
    <HtmlTemplateConverterSimple />
    
    <Divider class="mt-8" />
    
    <div class="mt-4">
      <h2 class="text-xl font-semibold mb-2">Особенности упрощенной версии</h2>
      <ul class="list-disc pl-6 space-y-2">
        <li>Сохраняет оригинальное форматирование атрибутов data-* с JSON</li>
        <li>Не модифицирует кавычки и форматирование в атрибутах</li>
        <li>Корректно обрабатывает одинарные и двойные кавычки</li>
        <li>Фокусируется только на полях html и hbs</li>
      </ul>
      
      <h2 class="text-xl font-semibold mt-6 mb-2">Как использовать</h2>
      <ol class="list-decimal pl-6 space-y-2">
        <li>Вставьте HTML код в левое поле ввода</li>
        <li>Нажмите кнопку "Конвертировать"</li>
        <li>Получите оригинальный HTML и шаблон Handlebars на вкладках справа</li>
        <li>Проверьте, что атрибуты data-* сохранились в исходном виде</li>
      </ol>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import Divider from 'primevue/divider';
import HtmlTemplateConverterSimple from '../components/HtmlTemplateConverterSimple.vue';
import { useToast } from 'primevue/usetoast';

const toast = useToast();

onMounted(() => {
  toast.add({
    severity: 'info',
    summary: 'Упрощенный конвертер готов',
    detail: 'Эта версия сохраняет оригинальное форматирование атрибутов data-*',
    life: 5000
  });
});
</script>

<style scoped>
.html-converter-page {
  max-width: 1200px;
  margin: 0 auto;
}
</style>