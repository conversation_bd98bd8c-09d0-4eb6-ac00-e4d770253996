<template>
  <div class="flex flex-col" @dragover.prevent @drop.prevent="onDrop">
    <!-- <PERSON><PERSON> and Export Button -->
    <div class="">
      <div class="flex items-center gap-4 pb-1">
        <slot name="header" />
        <div class="flex items-center gap-1">
          <Button
            v-for="device in devices"
            :key="device.name"
            :icon="device.icon"
            :class="{
              'bg-primary-100 dark:bg-primary-900':
                activeDevice === device.name,
            }"
            text
            rounded
            size="small"
            @click="activeDevice = device.name"
          />
        </div>

        <Button
          icon="pi pi-eye"
          text
          size="small"
          class="ml-2"
          @click="openPreview"
        />
        <Button
          icon="pi pi-download"
          text
          size="small"
          class="ml-0 mr-2"
          @click="exportHtml"
        />
      </div>

      <!-- Full-height Preview Area with Scroll -->
      <div class="flex-1 overflow-auto p-0">
        <div
          :class="{
            'mx-auto max-w-[375px] border rounded-lg':
              activeDevice === 'mobile',
            'mx-auto max-w-[768px] border rounded-lg':
              activeDevice === 'tablet',
            'mx-auto max-w-[1440px] border rounded-lg':
              activeDevice === 'desktop',
          }"
        >
          <div
            v-if="elements.length === 0"
            class="min-h-[600px] flex items-center justify-center text-gray-500"
          >
            <span>Preview will be shown here</span>
          </div>

          <div v-else>
            <iframe
              ref="previewFrame"
              class="preview-render w-full border-0 rounded-lg"
              style="min-height: 600px"
            />
            <div v-show="false" ref="tempContainer">
              <div v-for="(element, index) in elements" :key="index">
                <div v-if="element.type === 'html'" v-html="element.template" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onUnmounted } from 'vue'

  interface Element {
    name: string
    type: string
    icon: string
    template: string
  }

  interface Props {
    elements: Element[]
    customJs?: string
    customCss?: string
  }

  const props = defineProps<Props>()
  const emit = defineEmits(['update:elements'])

  const elements = ref<Element[]>(props.elements)

  watch(
    elements,
    () => {
      emit('update:elements', elements.value)
    },
    { deep: true },
  )

  const devices = [
    { name: 'desktop', icon: 'pi pi-desktop' },
    { name: 'tablet', icon: 'pi pi-tablet' },
    { name: 'mobile', icon: 'pi pi-mobile' },
  ]

  const activeDevice = ref('desktop')

  const generateHtml = () => {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generated HTML</title>
  
    ${props.customCss || ''}
  
</head>
<body>
  
    ${props.elements.map((element) => element.template).join('\n    ')}
  
  
      ${props.customJs ? props.customJs.replace(/`/g, '\\`').replace(/\$/g, '\\$') : ''}
    
</body>
</html>
`
  }

  const openPreview = () => {
    const html = generateHtml()
    const blob = new Blob([html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    window.open(url, '_blank')
    // We don't revoke the URL immediately as the new window needs to access it
    // The browser will automatically clean it up when the window is closed
  }

  const exportHtml = () => {
    const html = generateHtml()
    const blob = new Blob([html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'generated.html'
    a.click()
    URL.revokeObjectURL(url)
  }

  const onDrop = (event: DragEvent) => {
    const elementData = event.dataTransfer?.getData('element')
    if (elementData) {
      const element = JSON.parse(elementData)
      elements.value.push(element)
      emit('update:elements', elements.value)
    }
  }

  const cssContainer = ref<HTMLElement | null>(null)
  const jsContainer = ref<HTMLElement | null>(null)
  const injectedElements = ref<(HTMLStyleElement | HTMLScriptElement)[]>([])

  const previewFrame = ref<HTMLIFrameElement | null>(null)
  const tempContainer = ref<HTMLElement | null>(null)

  const observer = ref<MutationObserver | null>(null)

  const updateDynamicContent = async () => {
    try {
      if (!previewFrame.value) return

      const iframeDoc = previewFrame.value.contentDocument
      if (!iframeDoc) return

      // Отключаем предыдущий observer
      if (observer.value) {
        observer.value.disconnect()
      }

      // Создаем чистый документ с CSS в head и JS в body
      iframeDoc.open()
      iframeDoc.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          ${props.customCss ? `${props.customCss}` : ''}
        </head>
        <body>
          ${tempContainer.value?.innerHTML || ''}
          ${props.customJs ? `${props.customJs}` : ''}
        </body>
      </html>
    `)
      iframeDoc.close()

      // Настраиваем observer для отслеживания изменений в tempContainer
      observer.value = new MutationObserver(() => {
        if (tempContainer.value && iframeDoc) {
          iframeDoc.body.innerHTML = `
          ${tempContainer.value.innerHTML || ''}
          ${props.customJs ? `${props.customJs}` : ''}
        `
        }
      })

      if (tempContainer.value) {
        observer.value.observe(tempContainer.value, {
          childList: true,
          subtree: true,
          characterData: true,
        })
      }
    } catch (error) {
      console.error('Error updating dynamic content:', error)
    }
  }

  watch(
    () => props.elements,
    async (newElements) => {
      elements.value = newElements
      await updateDynamicContent()
    },
    { deep: true },
  )

  watch(
    () => props.customCss,
    async () => {
      await updateDynamicContent()
    },
    { immediate: true },
  )

  watch(
    () => props.customJs,
    async () => {
      await updateDynamicContent()
    },
    { immediate: true },
  )
  onMounted(async () => {
    await updateDynamicContent()
  })

  onUnmounted(() => {
    // Clean up all injected elements when component is unmounted
    injectedElements.value.forEach((el) => el.remove())
    injectedElements.value = []
  })
</script>

<style>
  .my-editor {
    background: #2d2d2d;
    color: #ccc;
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 12px;
    line-height: 1.5;
    padding: 12px;
    border-radius: 4px;
    /* margin-top: 20px; */
  }

  .preview-render {
    min-height: 50px;
  }

  /* Стили для подсветки синтаксиса Prism */
  .prism-editor__textarea:focus {
    outline: none;
  }

  .prism-editor__line-numbers {
    background: #2d2d2d !important;
    border-right: 1px solid #3c3c3c;
  }

  .token.comment,
  .token.prolog,
  .token.doctype,
  .token.cdata {
    color: #6a9955;
  }

  .token.punctuation {
    color: #d4d4d4;
  }

  .token.property,
  .token.tag,
  .token.boolean,
  .token.number,
  .token.constant,
  .token.symbol,
  .token.deleted {
    color: #b5cea8;
  }

  .token.selector,
  .token.attr-name,
  .token.string,
  .token.char,
  .token.builtin,
  .token.inserted {
    color: #ce9178;
  }

  .token.operator,
  .token.entity,
  .token.url,
  .language-css .token.string,
  .style .token.string {
    color: #d4d4d4;
  }

  .token.atrule,
  .token.attr-value,
  .token.keyword {
    color: #569cd6;
  }

  .token.function,
  .token.class-name {
    color: #dcdcaa;
  }

  .token.regex,
  .token.important,
  .token.variable {
    color: #d16969;
  }
</style>
