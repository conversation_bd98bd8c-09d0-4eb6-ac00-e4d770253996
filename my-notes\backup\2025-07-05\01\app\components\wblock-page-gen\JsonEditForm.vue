<template>
  <div class="flex flex-col h-full">
    <div class="flex-1 overflow-auto p-4 space-y-4">
      <div class="field">
        <label for="art" class="block text-sm font-medium mb-1">Артикул</label>
        <InputText
          id="art"
          v-model="formData.art"
          class="w-full"
          placeholder="Введите артикул"
        />
      </div>

      <div class="field">
        <label for="title" class="block text-sm font-medium mb-1">Название</label>
        <InputText
          id="title"
          v-model="formData.title"
          class="w-full"
          placeholder="Введите название"
        />
      </div>

      <div class="field">
        <label for="description" class="block text-sm font-medium mb-1">Описание</label>
        <Textarea
          id="description"
          v-model="formData.description"
          class="w-full"
          rows="3"
          placeholder="Введите описание"
        />
      </div>

      <div class="field">
        <label for="tags" class="block text-sm font-medium mb-1">Теги</label>
        <MultiSelect
          id="tags"
          v-model="formData.tags"
          :options="tagOptions"
          placeholder="Выберите теги"
          class="w-full"
          display="chip"
          filter
          :show-toggle-all="false"
        />
      </div>

      <div class="field">
        <label class="block text-sm font-medium mb-2">JSON данные</label>
        <PrismEditorWithCopy
          v-model="formData.json"
          :highlight="highlightJson"
          line-numbers
          class="my-editor"
          style="height: 300px; font-family: 'Fira code', 'Fira Mono', monospace; font-size: 12px;"
        />
      </div>
    </div>

    <div class="flex justify-end gap-2 p-4 border-t border-surface-200 dark:border-surface-700">
      <Button
        label="Отмена"
        text
        class="p-button-sm"
        @click="$emit('cancel')"
      />
      <Button
        label="Сохранить"
        class="p-button-sm"
        @click="handleSave"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import MultiSelect from 'primevue/multiselect'
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'
import 'vue-prism-editor/dist/prismeditor.min.css'
import { highlight, languages } from 'prismjs/components/prism-core'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'

interface JsonData {
  id?: string
  art?: string
  title?: string
  description?: string
  tags?: string[]
  json?: string
}

interface Props {
  jsonData?: JsonData
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  jsonData: () => ({}),
  isEdit: false
})

const emit = defineEmits(['save', 'cancel'])

const formData = ref<JsonData>({
  art: '',
  title: '',
  description: '',
  tags: [],
  json: ''
})

const tagOptions = ref([
  'header',
  'hero',
  'features',
  'about',
  'services',
  'portfolio',
  'testimonials',
  'contact',
  'footer',
  'navigation',
  'sidebar',
  'content',
  'gallery',
  'pricing',
  'team',
  'blog',
  'news',
  'faq',
  'cta',
  'stats',
  'landing',
  'ecommerce',
  'corporate',
  'personal',
  'creative',
  'business',
  'technology',
  'education',
  'healthcare',
  'restaurant',
  'real-estate',
  'travel',
  'fitness',
  'fashion',
  'music',
  'photography',
  'nonprofit'
])

// Syntax highlighting function
const highlightJson = (code: string) => {
  return highlight(code, languages.json, 'json')
}

const handleSave = () => {
  // Validate JSON before saving
  if (formData.value.json) {
    try {
      JSON.parse(formData.value.json)
    } catch (error) {
      // You might want to show an error message here
      console.error('Invalid JSON:', error)
      return
    }
  }
  
  emit('save', { ...formData.value })
}

// Watch for prop changes
watch(() => props.jsonData, (newJsonData) => {
  if (newJsonData && Object.keys(newJsonData).length > 0) {
    formData.value = { ...newJsonData }
    
    // Format JSON if it's a string
    if (formData.value.json && typeof formData.value.json === 'string') {
      try {
        const parsed = JSON.parse(formData.value.json)
        formData.value.json = JSON.stringify(parsed, null, 2)
      } catch (error) {
        // Keep original if parsing fails
      }
    }
  }
}, { immediate: true, deep: true })

onMounted(() => {
  if (props.jsonData && Object.keys(props.jsonData).length > 0) {
    formData.value = { ...props.jsonData }
    
    // Format JSON if it's a string
    if (formData.value.json && typeof formData.value.json === 'string') {
      try {
        const parsed = JSON.parse(formData.value.json)
        formData.value.json = JSON.stringify(parsed, null, 2)
      } catch (error) {
        // Keep original if parsing fails
      }
    }
  } else {
    // Set default JSON structure
    formData.value.json = JSON.stringify({
      "title": "Пример заголовка",
      "description": "Пример описания",
      "items": []
    }, null, 2)
  }
})
</script>

<style scoped>
.my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 10px;
    line-height: 1.4;
    padding: 2px;
  }

.my-editor .prism-editor__textarea:focus {
  outline: none;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: var(--text-color);
}
</style>
