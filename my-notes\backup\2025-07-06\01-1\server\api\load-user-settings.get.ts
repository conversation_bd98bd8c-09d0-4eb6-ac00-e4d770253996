import { promises as fs } from 'fs'
import { join } from 'path'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { type } = query

    if (!type) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Отсутствует параметр type'
      })
    }

    // Определяем директорию для загрузки
    const typeDir = join(process.cwd(), 'storage', 'user-settings', type as string)
    
    try {
      await fs.access(typeDir)
    } catch {
      return {
        success: false,
        message: 'Директория не найдена',
        data: null
      }
    }

    // Ищем последний файл в директории
    const files = await fs.readdir(typeDir)
    const jsonFiles = files.filter(f => f.endsWith('.json')).sort().reverse()

    if (jsonFiles.length === 0) {
      return {
        success: false,
        message: 'Файлы настроек не найдены',
        data: null
      }
    }

    // Загружаем последний файл
    const latestFile = jsonFiles[0]
    const filePath = join(typeDir, latestFile)
    const fileContent = await fs.readFile(filePath, 'utf8')
    const data = JSON.parse(fileContent)

    console.log(`✅ Настройки загружены: ${filePath}`)

    return {
      success: true,
      message: 'Настройки успешно загружены',
      filename: latestFile,
      path: filePath,
      type,
      data
    }

  } catch (error: any) {
    console.error('❌ Ошибка загрузки настроек:', error)
    
    return {
      success: false,
      message: `Ошибка загрузки настроек: ${error.message}`,
      data: null
    }
  }
})
