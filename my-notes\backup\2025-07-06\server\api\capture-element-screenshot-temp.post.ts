import puppeteer from 'puppeteer'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { html, elementSelector, width = 1400, height = 800 } = body

    if (!html) {
      throw createError({
        statusCode: 400,
        statusMessage: 'HTML content is required'
      })
    }

    console.log('🔄 Создание временного скриншота элемента с селектором:', elementSelector)

    // Запускаем браузер
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    })

    const page = await browser.newPage()

    // Устанавливаем размер viewport
    await page.setViewport({
      width: width,
      height: height,
      deviceScaleFactor: 1
    })

    console.log(`📐 Viewport установлен: ${width}x${height}px`)

    // Загружаем HTML контент
    await page.setContent(html, {
      waitUntil: ['networkidle0', 'domcontentloaded'],
      timeout: 30000
    })

    console.log('📄 HTML контент загружен')

    // Ждем дополнительное время для загрузки всех ресурсов
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Прокручиваем страницу для активации анимаций (EXTREME delays)
    console.log('🔄 Интеллектуальная прокрутка для активации анимаций...')

    // Прокрутка вниз с задержкой
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight)
    })
    await new Promise(resolve => setTimeout(resolve, 5000)) // EXTREME delay для анимаций

    // Прокрутка обратно вверх
    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })
    await new Promise(resolve => setTimeout(resolve, 5000)) // EXTREME delay для анимаций

    // Дополнительное время для стабилизации
    await new Promise(resolve => setTimeout(resolve, 8000)) // EXTREME base delay

    let screenshotBuffer

    if (elementSelector) {
      try {
        // Пытаемся найти элемент по селектору
        const element = await page.$(elementSelector)

        if (element) {
          console.log(`🎯 Элемент найден по селектору: ${elementSelector}`)

          // Получаем размеры элемента
          const boundingBox = await element.boundingBox()

          if (boundingBox && boundingBox.width > 0 && boundingBox.height > 0) {
            console.log(`📏 Размеры элемента: ${boundingBox.width}x${boundingBox.height}px`)

            // Делаем скриншот элемента
            screenshotBuffer = await element.screenshot({
              type: 'png',
              omitBackground: false
            })

            console.log('📸 Скриншот элемента создан')
          } else {
            console.warn('⚠️ Элемент имеет нулевые размеры, используем fallback')
            throw new Error('Element has zero dimensions')
          }
        } else {
          console.warn(`⚠️ Элемент не найден по селектору: ${elementSelector}`)
          throw new Error('Element not found')
        }
      } catch (elementError) {
        console.warn('⚠️ Ошибка захвата элемента, используем fallback к body:', elementError.message)

        // Fallback: захватываем body элемент
        const bodyElement = await page.$('body')
        if (bodyElement) {
          const boundingBox = await bodyElement.boundingBox()
          if (boundingBox && boundingBox.width > 0 && boundingBox.height > 0) {
            console.log(`📏 Fallback: размеры body: ${boundingBox.width}x${boundingBox.height}px`)
            screenshotBuffer = await bodyElement.screenshot({
              type: 'png',
              omitBackground: false
            })
            console.log('📸 Fallback скриншот body создан')
          } else {
            // Последний fallback: полная страница
            console.log('📸 Последний fallback: полная страница')
            screenshotBuffer = await page.screenshot({
              type: 'png',
              fullPage: true,
              omitBackground: false
            })
          }
        }
      }
    } else {
      // Если селектор не указан, захватываем body
      const bodyElement = await page.$('body')
      if (bodyElement) {
        const boundingBox = await bodyElement.boundingBox()
        console.log(`📏 Размеры body: ${boundingBox?.width}x${boundingBox?.height}px`)

        screenshotBuffer = await bodyElement.screenshot({
          type: 'png',
          omitBackground: false
        })
        console.log('📸 Скриншот body создан')
      }
    }

    // Финальная задержка
    await new Promise(resolve => setTimeout(resolve, 8000)) // EXTREME final delay

    console.log('📸 Временный скриншот элемента создан')

    // Закрываем браузер
    await browser.close()
    console.log('🔒 Браузер закрыт')

    // Возвращаем скриншот как blob
    setHeader(event, 'Content-Type', 'image/png')
    setHeader(event, 'Content-Length', screenshotBuffer.length.toString())

    return screenshotBuffer

  } catch (error) {
    console.error('❌ Ошибка создания временного скриншота элемента:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to capture element screenshot'
    })
  }
})
