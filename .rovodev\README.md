# Документация проекта для Rovo Dev

Добро пожаловать в документацию проекта! Эта папка содержит подробный анализ архитектуры и руководства для эффективной работы с системой управления контентом и генерации веб-компонентов.

## Содержание документации

### 📋 [Анализ проекта](./project-analysis.md)
Комплексный обзор проекта, включающий:
- Общую архитектуру системы
- Технологический стек
- Структуру данных и коллекций
- Описание всех ключевых страниц и их функциональности
- Архитектурные решения и паттерны
- Рекомендации для разработки

### 🏗️ [Архитектура компонентов](./component-architecture.md)
Детальное описание структуры компонентов:
- Иерархия компонентов проекта
- Паттерны проектирования компонентов
- Специализированные компоненты для разных задач
- Композиция и переиспользование кода
- Интеграционные паттерны

### 🔧 [API и серверная архитектура](./api-and-server-architecture.md)
Полное руководство по серверной части:
- Структура серверных API endpoints
- Ключевые утилиты для обработки HTML
- Паттерны серверной разработки
- Интеграция с Puppeteer для скриншотов
- Оптимизация производительности

### 📖 [Руководство по разработке](./development-guidelines.md)
Стандарты и лучшие практики:
- Общие принципы разработки
- Структура Vue компонентов
- Работа с данными и API
- Стилизация и UI компоненты
- Обработка ошибок и отладка
- Тестирование и производительность

### 🔄 [Общие паттерны и решения](./common-patterns-and-solutions.md)
Готовые решения для типичных задач:
- Массовые операции (Bulk Operations)
- Фильтрация и поиск
- Генерация скриншотов
- Динамические панели
- Режимы работы компонентов
- Drag & Drop интерфейсы
- Предпросмотр с viewport переключением

## Быстрый старт

### Для понимания проекта
1. Начните с [анализа проекта](./project-analysis.md) для общего понимания
2. Изучите [архитектуру компонентов](./component-architecture.md) для понимания структуры
3. Ознакомьтесь с [руководством по разработке](./development-guidelines.md) для стандартов

### Для разработки новых функций
1. Изучите [общие паттерны](./common-patterns-and-solutions.md) для поиска готовых решений
2. Следуйте [руководству по разработке](./development-guidelines.md) для соблюдения стандартов
3. При работе с API обращайтесь к [серверной архитектуре](./api-and-server-architecture.md)

### Для отладки и оптимизации
1. Используйте паттерны из [руководства по разработке](./development-guidelines.md)
2. Изучите серверные утилиты в [API архитектуре](./api-and-server-architecture.md)
3. Применяйте готовые решения из [общих паттернов](./common-patterns-and-solutions.md)

## Ключевые особенности проекта

### 🎯 Основные возможности
- **Управление контентом**: Работа с файлами, страницами, блоками и элементами
- **Генерация скриншотов**: Автоматическое создание превью для контента
- **HTML анализ**: Интеллектуальный анализ и разбор HTML структуры
- **Конвертация шаблонов**: Преобразование HTML в Handlebars + JSON
- **Массовые операции**: Эффективная работа с множественными элементами

### 🛠️ Технологии
- **Frontend**: Nuxt 3, Vue 3, TypeScript, PrimeVue, TailwindCSS
- **Backend**: Nuxt Server API (Nitro), Puppeteer, Cheerio
- **CMS**: Directus (headless CMS)
- **Инструменты**: ESLint, Prettier, Handlebars

### 📱 Типы интерфейсов
- **Табличные интерфейсы**: Для управления данными с фильтрацией
- **Генераторы**: Визуальные конструкторы для создания контента
- **Многорежимные компоненты**: С переключением между различными режимами работы
- **Drag & Drop**: Интерактивные интерфейсы для комбинирования элементов

## Архитектурные принципы

### 🔄 Переиспользование
- Универсальные компоненты для типичных задач
- Общие паттерны для массовых операций
- Централизованные утилиты для обработки данных

### 📊 Производительность
- Виртуализация для больших списков
- Batch обработка для серверных операций
- Debounced поиск и фильтрация
- Ленивая загрузка компонентов

### 🔒 Надежность
- Централизованная обработка ошибок
- Валидация данных на клиенте и сервере
- Graceful degradation для API вызовов
- Автоматическое управление ресурсами

## Рекомендации по использованию

### При добавлении новых страниц
1. Используйте существующие паттерны из [архитектуры компонентов](./component-architecture.md)
2. Интегрируйтесь с BulkFormContainer для массовых операций
3. Следуйте стандартам фильтрации и поиска

### При создании API endpoints
1. Следуйте паттернам из [серверной архитектуры](./api-and-server-architecture.md)
2. Используйте существующие утилиты для HTML обработки
3. Реализуйте proper error handling

### При работе с UI
1. Используйте установленные размеры и стили PrimeVue
2. Следуйте паттернам TailwindCSS из проекта
3. Обеспечивайте responsive design

## Поддержка и развитие

Эта документация должна обновляться при:
- Добавлении новых архитектурных паттернов
- Создании новых типов компонентов
- Изменении API структуры
- Внедрении новых технологий

Для вопросов и предложений по улучшению документации обращайтесь к команде разработки.

---

*Документация создана для эффективной работы с Rovo Dev AI Assistant*