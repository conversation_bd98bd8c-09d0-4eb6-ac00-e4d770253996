<script setup lang="ts">
    import WblockList from '~/components/WblockList.vue'; 
	definePageMeta({
		// layout: 'default',
		// name: 'wblocks',
		// alias: 'wblocks',
		title: 'Wblocks',
		description: 'Wblocks',
		// hidden: true,
		navOrder: '2',
		type: 'primary',
		icon: 'i-mdi-home',
		// ogImage: 'images/ogImage.png', // url or local images inside public folder, for eg, ~/public/images/ogImage.png
	})
</script> 
<template> 
    <!-- <div> 
        <div style="min-height: 100px; display: flex; align-items: center; justify-content: center; font-weight: bold">               -->
            <WblockList/> 
        <!-- </div>         
    </div>      -->
</template> 
<style scoped></style>
