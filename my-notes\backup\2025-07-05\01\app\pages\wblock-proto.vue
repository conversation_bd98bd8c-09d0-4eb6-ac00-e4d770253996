<template>
  <div class="flex h-screen">
    <!-- Основной контент -->
    <div class="flex-1 overflow-auto p-4">
      <div class="flex justify-between mb-4">
        <h1 class="text-2xl font-semibold">Прототипы Блоков</h1>
        
        <div class="flex gap-2">
          <Button
            label="Создать"
            icon="pi pi-plus"
            class="p-button-sm"
            @click="openCreateDialog"
          />
          <Button
            icon="pi pi-refresh"
            class="p-button-sm p-button-text"
            title="Обновить"
            @click="loadData"
          />
        </div>
      </div>

      <DataTable
        v-model:sort-field="sortField"
        v-model:sort-order="sortOrder"
        :value="filteredItems"
        :loading="loading"
        :paginator="true"
        :rows="10"
        :rows-per-page-options="[5,10,20]"
        sort-mode="single"
        data-key="id"
        striped-rows
        removable-sort
        class="p-datatable-sm"
      >
        <Column field="title" header="Название" sortable>
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <span>{{ data.title }}</span>
              <Button
                icon="pi pi-copy"
                class="p-button-text p-button-sm"
                title="Копировать"
                @click="copyToClipboard(data.title)"
              />
            </div>
          </template>
        </Column>

        <Column field="type" header="Тип" sortable>
          <template #body="{ data }">
            <Tag :value="data.type" :severity="getTypeSeverity(data.type)" />
          </template>
        </Column>

        <Column field="description" header="Описание">
          <template #body="{ data }">
            <div v-if="data.description" class="flex items-center gap-2">
              <span>{{ truncateText(data.description) }}</span>
              <Button
                icon="pi pi-copy"
                class="p-button-text p-button-sm"
                title="Копировать"
                @click="copyToClipboard(data.description)"
              />
            </div>
            <span v-else>-</span>
          </template>
        </Column>

        <Column header="Действия" style="width: 120px">
          <template #body="{ data }">
            <div class="flex gap-1">
              <Button
                icon="pi pi-pencil"
                class="p-button-text p-button-sm"
                title="Редактировать"
                @click="editItem(data)"
              />
              <Button
                icon="pi pi-trash"
                class="p-button-text p-button-sm p-button-danger"
                title="Удалить"
                @click="confirmDelete(data)"
              />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>

    <!-- Диалог редактирования -->
    <Dialog
      v-model:visible="showDialog"
      :header="editingItem?.id ? 'Редактирование' : 'Создание'"
      :style="{ width: '600px' }"
      :modal="true"
    >
      <div class="p-fluid grid">
        <div class="field col-12">
          <label for="title">Название*</label>
          <InputText id="title" v-model="editingItem.title" required />
        </div>
        
        <div class="field col-6">
          <label for="type">Тип*</label>
          <Dropdown   
            v-model="editingItem.type"
            :options="['header', 'content', 'footer', 'custom']"
            placeholder="Выберите тип"
            required
          />
        </div>

        <div class="field col-12">
          <label for="description">Описание</label>
          <Textarea id="description" v-model="editingItem.description" rows="3" />
        </div>
      </div>

      <template #footer>
        <Button
          label="Отмена"
          icon="pi pi-times"
          class="p-button-text"
          @click="closeDialog"
        />
        <Button
          label="Сохранить"
          icon="pi pi-check"
          class="p-button-success"
          :disabled="!isFormValid"
          @click="saveItem"
        />
      </template>
    </Dialog>

    <!-- Диалог подтверждения удаления -->
    <ConfirmDialog />

    <!-- Уведомления -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useDirectusItems } from '#imports'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'

interface Prototype {
  id?: string
  title: string
  type: string
  description?: string
}

interface DirectusResult<T = any> {
  data: T
}

const { 
  getItems, 
  createItems, 
  updateItem, 
  deleteItems 
} = useDirectusItems()
const confirm = useConfirm()
const toast = useToast()

// Состояние
const items = ref<Prototype[]>([])
const loading = ref(false)
const showDialog = ref(false)
const editingItem = ref<Prototype>({
  title: '',
  type: '',
  description: ''
})
const sortField = ref('title')
const sortOrder = ref(1) // 1 - asc, -1 - desc

// Загрузка данных
const loadData = async () => {
  loading.value = true
  try {
    const response = await getItems({
      collection: 'wblock_proto',
      params: { sort: [sortField.value] }
    })
    items.value = Array.isArray(response) ? response : []
  } catch (error) {
    showError('Ошибка загрузки данных')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// Инициализация
onMounted(loadData)

// Компьютеды
const filteredItems = computed(() => [...items.value])
const isFormValid = computed(() => editingItem.value.title && editingItem.value.type)

const getTypeSeverity = (type: string) => {
  switch(type) {
    case 'header': return 'primary'
    case 'content': return 'success'
    case 'footer': return 'warning'
    default: return 'info'
  }
}

const truncateText = (text?: string) => {
  if (!text) return ''
  return text.length > 100 ? text.substring(0, 100) + '...' : text
}

// Методы
const openCreateDialog = () => {
  editingItem.value = { title: '', type: '', description: '' }
  showDialog.value = true
}

const editItem = (item: Prototype) => {
  editingItem.value = { ...item }
  showDialog.value = true
}

const saveItem = async () => {
  if (!isFormValid.value) return

  try {
    loading.value = true

    if (editingItem.value.id) {
      const updated = await updateItem({
        collection: 'wblock_proto',
        id: editingItem.value.id,
        item: editingItem.value
      })
      const index = items.value.findIndex(i => i.id === updated.id)
      if (index !== -1) items.value[index] = updated
      showSuccess('Прототип обновлен')
    } else {
      const created = await createItems({
        collection: 'wblock_proto',
        items: [editingItem.value]
      })
      if (created?.[0]) items.value.push(created[0])
      showSuccess('Прототип создан')
    }
    
    closeDialog()
  } catch (error) {
    showError('Ошибка сохранения')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const confirmDelete = (item: Prototype) => {
  confirm.require({
    message: 'Удалить этот прототип?',
    header: 'Подтверждение',
    icon: 'pi pi-exclamation-triangle',
    accept: () => deleteItem(item.id as string)
  })
}

const deleteItem = async (id: string) => {
  try {
    loading.value = true
    await deleteItems({ collection: 'wblock_proto', items: [id] })
    items.value = items.value.filter(i => i.id !== id)
    showSuccess('Прототип удален')
  } catch (error) {
    showError('Ошибка удаления')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const closeDialog = () => {
  showDialog.value = false
}

const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text)
    .then(() => showSuccess('Скопировано'))
    .catch(err => {
      console.error(err)
      showError('Ошибка копирования')
    })
}

const showSuccess = (message: string) => {
  toast.add({
    severity: 'success',
    summary: 'Успех',
    detail: message,
    life: 3000
  })
}

const showError = (message: string) => {
  toast.add({
    severity: 'error',
    summary: 'Ошибка',
    detail: message,
    life: 3000
  })
}
</script>

<style scoped>
.table-container {
  height: calc(100vh - 120px);
  padding: 1rem;
}

:deep(.p-datatable) {
  font-size: 14px;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  padding: 0.5rem;
}
</style>
