<template>
  <div>
    <DataTable
      :value="sortedBlocks"
      class="p-datatable-sm text-xs border-2"
      empty-message="Нет связанных блоков."
      sort-field="number"
      :sort-order="1"
    >
      <Column field="number" header="№" sortable style="font-size: 9px; padding: 3px; width: 50px" />
      <Column field="sketch" header="" style="width: 50px; padding: 2px; width: 100px">
        <template #body="{ data: blockData }">
          <Image
            v-if="blockData.sketch"
            :src="`http://localhost:8055/assets/${blockData.sketch}`"
            alt="Sketch"
            width="85"
            class="my"
            preview
          />
        </template>
      </Column>
      <Column field="title" header="Название" sortable style="padding: 1px; width: 120px" />

      <Column field="description" header="Описание" style="padding: 1px; font-size: 9px; width: 150px">
        <template #body="{ data }">
          <div class="composition-cell">{{ data.description }}</div>
        </template>
      </Column>

      <Column field="composition" header="Композиция" style="padding: 1px; font-size: 9px; width: 350px">
        <template #body="{ data }">
          <div class="flex items-center gap-2">
            <span class="composition-cell">{{ data.composition }}</span>
            <div class="flex-col items-center gap-2">
              <Button
                label="⿻ HTML"
                class="p-button-text p-button-sm"
                style="width: 70px; font-size: 10px; padding: 1px"
                @click="copyToClipboard(data.html, 'HTML')"
              />
              <Button
                label="⿻ HBS"
                class="p-button-text p-button-sm"
                style="width: 70px; font-size: 10px; padding: 1px"
                @click="copyToClipboard(data.hbs, 'HBS')"
              />
              <Button
                label="⿻ JSON"
                class="p-button-text p-button-sm"
                style="width: 70px; font-size: 10px; padding: 1px"
                @click="copyToClipboard(data.json, 'JSON')"
              />
            </div>
          </div>
        </template>
      </Column>

      <Column field="status" header="Статус" style="padding: 1px; width: 70px">
        <template #body="{ data }">
          <Tag
            :value="data.status"
            :severity="getStatusSeverity(data.status)"
            style="padding: 0 3px; font-size: 9px"
          />
        </template>
      </Column>

      <Column field="block_type" header="Тип блока" style="padding: 1px; width: 250px">
        <template #body="{ data }">
          <div class="flex flex-wrap gap-1">
            <Tag
              v-for="type in data.block_type"
              :key="type"
              :value="type"
              style="padding: 0 3px; font-size: 9px"
            />
          </div>
        </template>
      </Column>

      <Column header="Действия" :exportable="false" style="width: 180px">
        <template #body="{ data }">
          <div class="flex gap-1">
            <Button
              icon="pi pi-pencil"
              class="p-button-text p-button-sm"
              @click="$emit('edit', data)"
            />
            <Button
              icon="pi pi-copy"
              class="p-button-text p-button-sm"
              @click="$emit('duplicate', data)"
            />
            <Button
              icon="pi pi-trash"
              class="p-button-text p-button-sm p-button-danger"
              @click="$emit('delete', data)"
            />
          </div>
        </template>
      </Column>
    </DataTable>
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Image from 'primevue/image';
import Tag from 'primevue/tag';
import Toast from 'primevue/toast';
import { useToast } from 'primevue/usetoast';

interface WBlockProto {
  id?: string;
  number: string;
  title: string;
  description?: string;
  composition?: string;
  html?: string;
  hbs?: string;
  json?: string;
  status?: string;
  block_type?: string[];
  sketch?: string;
}

const props = defineProps<{
  blocks: WBlockProto[];
}>();

const emit = defineEmits<{
  (e: 'edit' | 'duplicate' | 'delete', item: WBlockProto): void;
}>();

const toast = useToast();

const sortedBlocks = computed(() => {
  if (!props.blocks) return [];
  // The default sort is handled by DataTable's sortField and sortOrder props
  return props.blocks;
});

const getStatusSeverity = (status: string): string => {
  const severityMap: Record<string, string> = {
    idea: 'info',
    in_progress: 'warning',
    done: 'success',
    archived: 'danger',
  };
  return severityMap[status] || 'info';
};

const copyToClipboard = (text: string | undefined, fieldName: string) => {
  if (!text) {
    toast.add({
      severity: 'warn',
      summary: 'Нечего копировать',
      detail: `Поле ${fieldName} пусто.`,
      life: 3000,
    });
    return;
  }
  navigator.clipboard.writeText(text);
  toast.add({
    severity: 'info',
    summary: 'Скопировано',
    detail: `${fieldName} скопирован в буфер обмена`,
    life: 3000,
  });
};
</script>

<style scoped>
.composition-cell {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 80px;
}


  
  .my2 img  {
      object-fit: contain !important;
      
      
    }

</style>