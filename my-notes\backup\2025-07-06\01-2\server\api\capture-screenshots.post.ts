import { defineEventHandler, readBody, createError } from 'h3'
import puppeteer from 'puppeteer'

interface ScreenshotRequest {
  url: string
}

interface Screenshot {
  fileId: string
  filename: string
}

export default defineEventHandler(async (event) => {
  const { url } = await readBody<ScreenshotRequest>(event)

  if (!url) {
    throw createError({
      statusCode: 400,
      message: 'URL is required',
    })
  }

  // Запускаем браузер
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  })

  try {
    const page = await browser.newPage()

    // Устанавливаем размер окна браузера (увеличиваем ширину до 1400px)
    await page.setViewport({ width: 1400, height: 800 })

    // Переходим на указанный URL с максимальным ожиданием
    await page.goto(url, { waitUntil: 'networkidle0', timeout: 60000 })

    // Оптимизированная базовая задержка для полной загрузки контента
    console.log('⏱️ Базовая задержка 3000ms для полной загрузки контента...')
    await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))

    // Ждем загрузки всех изображений с увеличенным timeout
    console.log('🖼️ Ожидание загрузки всех изображений с timeout 10000ms...')
    await page.evaluate(() => {
      return Promise.all(
        Array.from(document.images)
          .filter(img => !img.complete)
          .map(img => new Promise(resolve => {
            img.onload = img.onerror = resolve
            // Увеличенный timeout для изображений
            setTimeout(resolve, 10000)
          }))
      )
    })

    // УМНАЯ прокрутка для активации scroll-анимаций (имитация ручного процесса)
    console.log('📜 УМНАЯ прокрутка для активации scroll-анимаций...')
    await page.evaluate(() => {
      return new Promise((resolve) => {
        // Имитируем ручной процесс: медленная прокрутка вниз, затем быстро вверх
        const scrollToBottom = () => {
          // Медленная прокрутка вниз по частям для активации всех scroll-триггеров
          let currentScroll = 0
          const maxScroll = document.body.scrollHeight
          const scrollStep = Math.max(200, maxScroll / 10) // Прокручиваем по частям

          const scrollInterval = setInterval(() => {
            currentScroll += scrollStep
            window.scrollTo(0, Math.min(currentScroll, maxScroll))

            if (currentScroll >= maxScroll) {
              clearInterval(scrollInterval)
              // Пауза внизу для активации анимаций
              setTimeout(scrollToTop, 2000)
            }
          }, 300) // Медленная прокрутка для активации всех триггеров
        }

        // Быстрая прокрутка вверх
        const scrollToTop = () => {
          window.scrollTo(0, 0)
          setTimeout(resolve, 1500) // Пауза вверху для стабилизации
        }

        // Начинаем процесс
        setTimeout(scrollToBottom, 1000)
      })
    })

    // Скрываем элемент div.demo-button-wrapper
    await page.evaluate(() => {
      const demoButtons = document.querySelectorAll('div.demo-button-wrapper')
      demoButtons.forEach(button => {
        if (button instanceof HTMLElement) {
          button.style.display = 'none'
        }
      })
    })

    // Оптимизированная финальная стабилизация после всех операций
    console.log('⏱️ Финальная стабилизация 2000ms...')
    await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 2000)))

    // ДИАГНОСТИКА: Проверяем состояние страницы перед созданием скриншотов
    console.log('🔍 ДИАГНОСТИКА: Анализ состояния страницы...')
    const pageAnalysis = await page.evaluate(() => {
      const analysis = {
        totalImages: document.images.length,
        loadedImages: Array.from(document.images).filter(img => img.complete).length,
        failedImages: Array.from(document.images).filter(img => !img.complete).length,
        totalElements: document.querySelectorAll('*').length,
        visibleElements: Array.from(document.querySelectorAll('*')).filter(el => {
          const style = window.getComputedStyle(el)
          return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0'
        }).length,
        documentReadyState: document.readyState,
        bodyHeight: document.body.scrollHeight,
        viewportHeight: window.innerHeight,
        currentScrollPosition: window.pageYOffset
      }

      // Проверяем конкретные библиотеки
      analysis.libraries = {
        jquery: !!window.jQuery,
        aos: !!window.AOS,
        gsap: !!window.gsap,
        bootstrap: !!window.bootstrap || !!window.Bootstrap
      }

      return analysis
    })

    console.log('📊 ДИАГНОСТИКА РЕЗУЛЬТАТЫ:')
    console.log(`   📷 Изображения: ${pageAnalysis.loadedImages}/${pageAnalysis.totalImages} загружено (${pageAnalysis.failedImages} не загружено)`)
    console.log(`   👁️ Элементы: ${pageAnalysis.visibleElements}/${pageAnalysis.totalElements} видимо (${Math.round(pageAnalysis.visibleElements / pageAnalysis.totalElements * 100)}%)`)
    console.log(`   📄 Состояние документа: ${pageAnalysis.documentReadyState}`)
    console.log(`   📏 Высота: ${pageAnalysis.bodyHeight}px, Viewport: ${pageAnalysis.viewportHeight}px, Scroll: ${pageAnalysis.currentScrollPosition}px`)
    console.log(`   📚 Библиотеки:`, pageAnalysis.libraries)

    // Находим все секции, header и footer на странице
    const elements = await page.$$('section, header, footer')

    if (elements.length === 0) {
      throw createError({
        statusCode: 404,
        message: 'No sections, headers or footers found on the page',
      })
    }

    // Массив для хранения информации о созданных скриншотах
    const screenshots: Screenshot[] = []

    // Делаем скриншот каждого элемента
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i]

      // Получаем тип элемента (section, header или footer)
      const elementType = await page.evaluate(el => el.tagName.toLowerCase(), element)

      // Получаем размеры и позицию элемента
      const boundingBox = await element.boundingBox()

      // Проверяем, что элемент имеет ненулевые размеры
      if (!boundingBox || boundingBox.width <= 0 || boundingBox.height <= 0) {
        console.log(`Пропускаем элемент ${elementType} #${i + 1} с нулевыми размерами`)
        continue
      }

      try {
        // КЛЮЧЕВОЕ РЕШЕНИЕ: Прокручиваем К ЭТОМУ КОНКРЕТНОМУ ЭЛЕМЕНТУ
        console.log(`📜 Прокрутка к элементу ${elementType} #${i + 1} для активации его анимаций...`)
        await page.evaluate((el) => {
          // Прокручиваем так, чтобы элемент был в центре экрана
          el.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center'
          })
        }, element)

        // Ждем завершения прокрутки и активации анимаций ЭТОГО элемента
        console.log(`⏱️ Ожидание активации анимаций элемента ${elementType} #${i + 1} (3000ms)...`)
        await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 3000)))

        // Дополнительная проверка: ждем анимации конкретно в этом элементе
        console.log(`🎭 Проверка анимаций в элементе ${elementType} #${i + 1}...`)
        await page.evaluate((el) => {
          return new Promise((resolve) => {
            // Проверяем анимации в элементе
            const animatedElements = Array.from(el.querySelectorAll('*')).filter(child => {
              const style = window.getComputedStyle(child)
              return style.animationDuration !== '0s' || style.transitionDuration !== '0s'
            })

            if (animatedElements.length === 0) {
              // Нет анимаций - можем продолжать
              setTimeout(resolve, 500)
            } else {
              // Есть анимации - ждем их завершения
              const maxDuration = Math.max(...animatedElements.map(child => {
                const style = window.getComputedStyle(child)
                const animDuration = parseFloat(style.animationDuration) * 1000
                const transDuration = parseFloat(style.transitionDuration) * 1000
                return Math.max(animDuration, transDuration)
              }))

              const waitTime = Math.min(maxDuration + 1000, 5000) // Максимум 5 секунд
              setTimeout(resolve, waitTime)
            }
          })
        }, element)

        // Финальная стабилизация для этого элемента
        console.log(`⏱️ Финальная стабилизация элемента ${elementType} #${i + 1} (1000ms)...`)
        await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 1000)))

        // Делаем скриншот элемента
        console.log(`📸 Создание скриншота элемента ${elementType} #${i + 1}...`)
        const screenshot = await element.screenshot({
          type: 'jpeg',
          quality: 90,
          omitBackground: false,
        })

        // Генерируем имя файла на основе URL и порядкового номера элемента
        const urlObj = new URL(url)
        let hostname = urlObj.hostname.replace(/\./g, '_')
        let pathname = urlObj.pathname.replace(/\//g, '_')

        // Удаляем лишние части из названия файла
        if (hostname.startsWith('craftohtml_themezaa_com_')) {
          hostname = hostname.replace('craftohtml_themezaa_com_', '')
        }

        if (pathname.endsWith('_html')) {
          pathname = pathname.replace('_html', '')
        }

        // Форматируем номер с ведущим нулем
        const formattedNumber = String(i + 1).padStart(2, '0')

        // Создаем имя файла с учетом типа элемента
        const filename = `${hostname}${pathname}_${elementType}_${formattedNumber}.jpg`

        // Создаем файл в Directus через fetch API
        const formData = new FormData()

        // Устанавливаем title, соответствующий имени файла
        formData.append('title', filename)

        // Создаем Blob из буфера
        const blob = new Blob([screenshot], { type: 'image/jpeg' })
        formData.append('file', blob, filename)

        const response = await fetch('http://localhost:8055/files', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          throw new Error(`Failed to upload screenshot to Directus: ${response.statusText}`)
        }

        const data = await response.json()

        // Добавляем информацию о скриншоте в массив
        screenshots.push({
          fileId: data.data.id,
          filename: filename,
        })
      } catch (screenshotError) {
        console.error(`Ошибка при создании скриншота элемента ${elementType} #${i + 1}:`, screenshotError)
        // Продолжаем с следующим элементом
      }
    }

    if (screenshots.length === 0) {
      throw createError({
        statusCode: 404,
        message: 'Не удалось создать ни одного скриншота',
      })
    }

    return { screenshots }
  } catch (error) {
    console.error('Error capturing screenshots:', error)
    throw createError({
      statusCode: 500,
      message: error.message || 'Failed to capture screenshots',
    })
  } finally {
    // Закрываем браузер
    await browser.close()
  }
})