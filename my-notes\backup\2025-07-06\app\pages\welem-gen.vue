<template>
  <div class="flex flex-col gap-4 p-1">
    <!-- Too<PERSON>bar с полями ввода и кнопками -->
    <div class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <InputText
        v-model="formData.number"
        placeholder="Номер элемента"
        class="w-28 text-xs"
        style="font-size: 11px"
      />
      <InputText
        v-model="formData.title"
        placeholder="Название элемента"
        class="flex-1 text-xs"
        style="font-size: 12px"
      />
      <MultiSelect
        v-model="formData.collection"
        :options="collectionOptions"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Выберите коллекции"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <Button
        v-tooltip.top="'Генерировать скриншоты'"
        icon="pi pi-camera"
        class="p-button-warning text-xs"
        :loading="generating"
        :disabled="!selectedElements.length"
        @click="generateScreenshots"
      />
      <Button
        v-tooltip.top="'Сохранить'"
        icon="pi pi-save"
        class="text-xs"
        :loading="saving"
        :disabled="!selectedElements.length"
        @click="saveSelectedElements"
      />
    </div>

    <!-- Группы JSON и HBS редакторов с Bootstrap wrapper -->
    <div class="flex flex-col gap-2">
      <div
        v-for="group in dataGroups"
        :key="group.id"
        class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg"
      >
        <!-- Номер группы -->
        <div class="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-sm font-medium">
          {{ group.id }}
        </div>

        <!-- JSON редактор -->
        <div class="w-[30%]">
          <ClientOnly>
            <PrismEditor
              v-model="group.jsonContent"
              class="w-full text-xs p-2 border rounded my-editor h-[180px] overflow-auto max-h-[180px]"
              :highlight="highlightJson"
              placeholder="Введите JSON данные"
              line-numbers
            />
          </ClientOnly>
        </div>

        <!-- HBS редактор -->
        <div class="flex-1">
          <ClientOnly>
            <PrismEditor
              v-model="group.hbsContent"
              class="w-full text-xs p-2 border rounded my-editor h-[180px] overflow-auto max-h-[180px]"
              :highlight="highlightHtml"
              placeholder="Введите HBS шаблон"
              line-numbers
            />
          </ClientOnly>
        </div>

        <!-- Bootstrap Wrapper селекторы для всех групп -->
        <div class="flex flex-col gap-2 w-48">
          <Dropdown
            v-model="group.wrapperRowType"
            :options="rowTypeOptions"
            option-label="label"
            option-value="value"
            class="text-xs"
            placeholder="Тип контейнера"
            :pt="{
              root: { class: 'text-xs' },
              input: { class: 'text-xs p-1' },
              panel: { class: 'text-xs' },
              item: { class: 'text-xs p-1' }
            }"
          />
          <Dropdown
            v-model="group.wrapperColWidth"
            :options="colWidthOptions"
            option-label="label"
            option-value="value"
            class="text-xs"
            placeholder="Ширина колонки"
            :pt="{
              root: { class: 'text-xs' },
              input: { class: 'text-xs p-1' },
              panel: { class: 'text-xs' },
              item: { class: 'text-xs p-1' }
            }"
          />
          <MultiSelect
            v-if="group.id > 1"
            v-model="group.collection"
            :options="collectionOptions"
            display="chip"
            class="text-xs"
            filter
            placeholder="Коллекция"
            panel-class="text-xs"
            :pt="{
              item: { class: 'text-xs' },
              header: { class: 'text-xs' },
            }"
          />
          <!-- Кнопка добавления новой группы (только для последней группы) -->
          <Button
            v-if="group.id === dataGroups[dataGroups.length - 1].id"
            v-tooltip.top="'Добавить новую группу'"
            icon="pi pi-plus"
            class="p-button-outlined text-xs"
            @click="addNewGroup"
          />
        </div>
      </div>
    </div>

    <!-- Фильтры -->
    <div class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border rounded-lg">
      <MultiSelect
        v-model="filters.elem_type"
        :options="filterOptions.elem_type"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Тип элемента"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <MultiSelect
        v-model="filters.collection"
        :options="filterOptions.collection"
        display="chip"
        class="text-xs max-w-sm"
        filter
        placeholder="Коллекция"
        panel-class="text-xs"
        :pt="{
          item: { class: 'text-xs' },
          header: { class: 'text-xs' },
        }"
      />
      <Button
        v-tooltip.top="'Сбросить все фильтры'"
        icon="pi pi-times"
        class="p-button-outlined text-xs"
        @click="clearAllFilters"
      />
      <Button
        v-tooltip.top="'Показать только выбранные'"
        icon="pi pi-filter"
        class="p-button-outlined text-xs"
        :class="{ 'p-button-info': showOnlySelected }"
        @click="toggleShowOnlySelected"
      />
      <Button
        v-tooltip.top="'Снять все отметки'"
        icon="pi pi-check-square"
        class="p-button-outlined text-xs"
        @click="clearAllSelections"
      />
    </div>

    <!-- Элементы с пагинацией -->
    <div v-if="filteredElements.length > 0" class="flex flex-col gap-4">
      <!-- Информация о количестве элементов -->
      <div class="text-sm text-surface-600 dark:text-surface-400">
        Найдено элементов: {{ filteredElements.length }}
        <span v-if="selectedElements.length > 0">
          | Выбрано: {{ selectedElements.length }}
        </span>
      </div>

      <!-- Сетка элементов -->
      <div class="masonry-grid gap-4">
        <Card
          v-for="element in paginatedElements"
          :key="element.id"
          class="overflow-hidden cursor-pointer transition-all duration-50"
          :class="{
            'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20 border-blue-500': isElementSelected(element),
            'hover:shadow-lg': !isElementSelected(element)
          }"
          :style="isElementSelected(element) ? 'border: 2px solid #3b82f6; background-color: rgba(59, 130, 246, 0.1);' : ''"
          style="background-color: #f9f9f9;"
          @click="toggleSelection(element)"
        >
          <template #header>
            <div class="relative">
              <!-- Показываем временный скриншот если есть -->
              <Image
                v-if="tempScreenshots.has(element.id!)"
                :src="tempScreenshots.get(element.id!)"
                alt="Сгенерированный скриншот"
                class="w-full h-auto object-contain cursor-pointer"
                preview
                @click.stop
              />
              <!-- Показываем оригинальный эскиз если нет временного скриншота -->
              <Image
                v-else-if="element.sketch"
                :src="`http://localhost:8055/assets/${element.sketch}`"
                alt="Предпросмотр элемента"
                class="w-full h-auto object-contain cursor-pointer"
                preview
                @click.stop
              />
              <!-- Показываем заглушку если нет изображений -->
              <div
                v-else
                class="w-full h-12 bg-surface-100 dark:bg-surface-800 flex items-center justify-center"
              >
                <i class="pi pi-image text-4xl text-surface-400"/>
              </div>
              <div class="absolute top-2 right-2">
                <Checkbox
                  :model-value="isElementSelected(element)"
                  binary
                  class="bg-white rounded shadow"
                  @click.stop
                  @change="toggleSelection(element)"
                />
              </div>
            </div>
          </template>
          <template #content>
            <div class="px-2 py-1">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <span class="text-sm font-medium text-gray-500" style="font-size: 13px;">{{ element.number }}</span>
                  <span class="text-base" style="font-size: 16px;">{{ element.title }}</span>
                </div>
                <!-- Dropdown для выбора группы (показывается только если групп больше 1) -->
                <div v-if="dataGroups.length > 1" class="flex-shrink-0">
                  <Dropdown
                    :model-value="getSelectedGroupForElement(element.id!)"
                    :options="groupOptions"
                    option-label="label"
                    option-value="value"
                    class="text-xs w-12"
                    :pt="{
                      root: { class: 'text-xs h-6' },
                      input: { class: 'text-xs p-1 h-6' },
                      trigger: { class: 'w-4' },
                      panel: { class: 'text-xs' },
                      item: { class: 'text-xs p-1' }
                    }"
                    @change="(event) => setSelectedGroupForElement(element.id!, event.value)"
                    @click.stop
                  />
                </div>
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Пагинация -->
      <Paginator
        v-model:first="first"
        :rows="rowsPerPage"
        :total-records="filteredElements.length"
        :rows-per-page-options="[60, 90, 120]"
        template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
        @page="onPageChange"
      />
    </div>

    <!-- Сообщение если элементов нет -->
    <div v-else-if="!loading" class="text-center py-8 text-surface-500">
      <i class="pi pi-inbox text-4xl mb-4 block"/>
      <p>Элементы не найдены</p>
      <p class="text-sm">Попробуйте изменить фильтры</p>
    </div>

    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { PrismEditor } from 'vue-prism-editor'
import 'vue-prism-editor/dist/prismeditor.min.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import handlebars from 'handlebars/dist/handlebars.min.js'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'

// PrimeVue компоненты
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import MultiSelect from 'primevue/multiselect'
import Card from 'primevue/card'
import Checkbox from 'primevue/checkbox'
import Toast from 'primevue/toast'
import Image from 'primevue/image'
import Paginator from 'primevue/paginator'
import Dropdown from 'primevue/dropdown'

// Определение метаданных страницы
definePageMeta({
  title: 'Генератор элементов',
  description: 'Генерация и предпросмотр элементов с JSON/HBS',
  navOrder: 16,
  type: 'primary',
  icon: 'i-mdi-view-module',
})

// Интерфейсы
interface WElem {
  id?: string
  number: string
  title: string
  description?: string
  elem_type?: string[]
  collection?: string[]
  html?: string
  css?: string
  js?: string
  hbs?: string
  json?: string
  composition?: string
  sketch?: string
  wblock_proto?: string[]
}

interface DataGroup {
  id: number
  jsonContent: string
  hbsContent: string
  wrapperRowType?: string
  wrapperColWidth?: string
  collection?: string[]
}

interface GeneratedElement {
  id: string
  number: string
  title: string
  html: string
  hbs: string
  json: string
  screenshot?: string
  originalElement: WElem
}

// Composables
const { getItems, createItems, updateItem } = useDirectusItems()
const toast = useToast()

// Bootstrap wrapper опции
const rowTypeOptions = ref([
  { label: 'div.row', value: 'row' },
  { label: 'div.row-fluid', value: 'row-fluid' }
])

const colWidthOptions = ref([
  { label: '.col-12', value: 'col-12' },
  { label: '.col-6', value: 'col-6' },
  { label: '.col-4', value: 'col-4' },
  { label: '.col-8', value: 'col-8' },
  { label: '.col-3', value: 'col-3' },
  { label: '.col-9', value: 'col-9' },
  { label: '.col-2', value: 'col-2' },
  { label: '.col-10', value: 'col-10' },
  { label: '.col-1', value: 'col-1' },
  { label: '.col-5', value: 'col-5' },
  { label: '.col-7', value: 'col-7' },
  { label: '.col-11', value: 'col-11' },
  { label: '.col-1-5', value: 'col-1-5' },
  { label: '.col-2-5', value: 'col-2-5' },
  { label: '.col-3-5', value: 'col-3-5' },
  { label: '.col-4-5', value: 'col-4-5' }
])

// Реактивные данные
const formData = ref({
  number: '',
  title: '',
  collection: [] as string[]
})

// Группы данных JSON/HBS с Bootstrap wrapper
const dataGroups = ref<DataGroup[]>([
  {
    id: 1,
    jsonContent: '',
    hbsContent: '',
    wrapperRowType: 'row',
    wrapperColWidth: 'col-12',
    collection: []
  }
])

// Выбранная группа для каждого элемента (по умолчанию группа 1)
const elementGroupSelection = ref<Map<string, number>>(new Map())

// Кэширование для оптимизации генерации скриншотов
const elementDataHashes = ref<Map<string, string>>(new Map()) // elementId -> hash данных
const tempScreenshots = ref<Map<string, string>>(new Map()) // elementId -> blob URL

const loading = ref(false)
const generating = ref(false)
const saving = ref(false)

// Данные для элементов
const allElements = ref<WElem[]>([])
const selectedElements = ref<WElem[]>([])
const first = ref(0)
const rowsPerPage = ref(30)

// Опции для мультиселектов (для сохранения)
const collectionOptions = ref<string[]>([])

// Фильтры
const filters = ref({
  elem_type: [] as string[],
  collection: [] as string[]
})

// Опции для фильтров
const filterOptions = ref({
  elem_type: [] as string[],
  collection: [] as string[]
})

// Дополнительные состояния
const showOnlySelected = ref(false)

// Вычисляемые свойства для групп
const groupOptions = computed(() => {
  return dataGroups.value.map(group => ({
    label: group.id.toString(),
    value: group.id
  }))
})

// Функции для работы с группами
const addNewGroup = () => {
  const newId = Math.max(...dataGroups.value.map(g => g.id)) + 1
  dataGroups.value.push({
    id: newId,
    jsonContent: '',
    hbsContent: '',
    wrapperRowType: 'row',
    wrapperColWidth: 'col-12',
    collection: []
  })
  console.log(`Добавлена новая группа ${newId}`)
}

const getSelectedGroupForElement = (elementId: string): number => {
  return elementGroupSelection.value.get(elementId) || 1
}

const setSelectedGroupForElement = (elementId: string, groupId: number) => {
  elementGroupSelection.value.set(elementId, groupId)
  console.log(`Элемент ${elementId} теперь использует группу ${groupId}`)
}

const getGroupData = (groupId: number): DataGroup | undefined => {
  return dataGroups.value.find(g => g.id === groupId)
}

// Функции для кэширования и оптимизации
const generateDataHash = (element: WElem, groupId: number): string => {
  const group = getGroupData(groupId)
  if (!group) return ''

  // Создаем хэш на основе всех данных, влияющих на генерацию
  const dataToHash = {
    elementId: element.id,
    elementHbs: element.hbs,
    elementJson: element.json,
    elementCss: element.css,
    elementJs: element.js,
    groupId: groupId,
    groupJson: group.jsonContent,
    groupHbs: group.hbsContent,
    wrapperRowType: group.wrapperRowType,
    wrapperColWidth: group.wrapperColWidth
  }

  // Безопасный хэш с поддержкой Unicode
  const jsonString = JSON.stringify(dataToHash)
  let hash = 0
  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Конвертируем в 32-битное число
  }
  return hash.toString()
}

const isScreenshotActual = (elementId: string, currentHash: string): boolean => {
  const storedHash = elementDataHashes.value.get(elementId)
  const hasScreenshot = tempScreenshots.value.has(elementId)

  return storedHash === currentHash && hasScreenshot
}

const updateElementHash = (elementId: string, hash: string) => {
  elementDataHashes.value.set(elementId, hash)
}

// Вычисляемые свойства
const filteredElements = computed(() => {
  let elements = allElements.value

  // Применяем фильтры
  if (filters.value.elem_type.length > 0) {
    elements = elements.filter(element => {
      if (!element.elem_type) return false
      return filters.value.elem_type.some(selectedType =>
        element.elem_type.includes(selectedType)
      )
    })
  }

  if (filters.value.collection.length > 0) {
    elements = elements.filter(element => {
      if (!element.collection) return false
      return filters.value.collection.some(selectedCollection =>
        element.collection.includes(selectedCollection)
      )
    })
  }

  // Фильтр "показать только выбранные"
  if (showOnlySelected.value) {
    elements = elements.filter(element => selectedElements.value.some(e => e.id === element.id))
  }

  // Сортируем по полю number по возрастанию
  return elements.sort((a, b) => {
    const numA = a.number || ''
    const numB = b.number || ''
    return numA.localeCompare(numB, undefined, { numeric: true })
  })
})

// Пагинированные элементы
const paginatedElements = computed(() => {
  const start = first.value
  const end = start + rowsPerPage.value
  return filteredElements.value.slice(start, end)
})

// Функция проверки выбора элемента
const isElementSelected = (element: WElem): boolean => {
  return selectedElements.value.some(e => e.id === element.id)
}

// Функции подсветки синтаксиса
const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

// Методы
const loadOptions = async () => {
  try {
    const elements = await getItems({
      collection: 'welem_proto',
      params: {
        limit: -1,
        fields: ['elem_type', 'collection']
      }
    })

    // Собираем уникальные значения для опций сохранения
    const collections = new Set<string>()

    // Собираем уникальные значения для фильтров
    const filterCollections = new Set<string>()
    const elemTypes = new Set<string>()

    elements.forEach((element: any) => {
      if (element.elem_type) {
        element.elem_type.forEach((type: string) => {
          elemTypes.add(type)
        })
      }
      if (element.collection) {
        element.collection.forEach((coll: string) => {
          collections.add(coll)
          filterCollections.add(coll)
        })
      }
    })

    // Опции для сохранения
    collectionOptions.value = Array.from(collections).sort()

    // Опции для фильтров
    filterOptions.value.elem_type = Array.from(elemTypes).sort()
    filterOptions.value.collection = Array.from(filterCollections).sort()
  } catch (error) {
    console.error('Error loading options:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить опции',
      life: 3000
    })
  }
}

// Методы для работы с выбором элементов
const toggleSelection = (element: WElem) => {
  const index = selectedElements.value.findIndex(e => e.id === element.id)
  if (index > -1) {
    selectedElements.value.splice(index, 1)
  } else {
    selectedElements.value.push(element)
  }
  console.log('Выбранные элементы:', selectedElements.value.length)
}

// Функции для управления фильтрами
const clearAllFilters = () => {
  filters.value.elem_type = []
  filters.value.collection = []
  console.log('Все фильтры сброшены')
}

const toggleShowOnlySelected = () => {
  showOnlySelected.value = !showOnlySelected.value
  console.log('Показать только выбранные:', showOnlySelected.value)
}

const clearAllSelections = () => {
  selectedElements.value = []
  console.log('Все выборы сняты')
}

const onPageChange = (event: any) => {
  first.value = event.first
  rowsPerPage.value = event.rows
  console.log('Переход на страницу:', event.page, 'first:', event.first, 'rows:', event.rows)
}

const loadAllElements = async () => {
  loading.value = true

  try {
    // Загружаем все элементы из базы
    const elements = await getItems({
      collection: 'welem_proto',
      params: {
        limit: -1,
        fields: ['*']
      }
    })

    allElements.value = elements
    console.log(`Загружено ${elements.length} элементов из базы`)

  } catch (error) {
    console.error('Error loading elements:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить элементы',
      life: 3000
    })
  } finally {
    loading.value = false
  }
}

const generateScreenshots = async () => {
  if (selectedElements.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите элементы для генерации скриншотов',
      life: 3000
    })
    return
  }

  // Проверяем, что хотя бы в одной группе есть данные
  const hasAnyData = dataGroups.value.some(group => group.jsonContent || group.hbsContent)
  if (!hasAnyData) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните JSON или HBS поле хотя бы в одной группе',
      life: 3000
    })
    return
  }

  generating.value = true

  try {
    console.log(`Генерация скриншотов для ${selectedElements.value.length} выбранных элементов...`)

    let generatedCount = 0
    let skippedCount = 0

    for (const element of selectedElements.value) {
      try {
        // Получаем выбранную группу для этого элемента
        const selectedGroupId = getSelectedGroupForElement(element.id!)
        const selectedGroup = getGroupData(selectedGroupId)

        if (!selectedGroup) {
          console.warn(`Группа ${selectedGroupId} не найдена для элемента ${element.number}`)
          continue
        }

        // Генерируем хэш текущих данных
        const currentHash = generateDataHash(element, selectedGroupId)

        // Проверяем актуальность существующего скриншота
        if (isScreenshotActual(element.id!, currentHash)) {
          console.log(`⏭️ Пропускаем элемент ${element.number} - скриншот актуален`)
          skippedCount++
          continue
        }

        let html = ''

        // Функционал 1: JSON заполнен в группе - используем его с HBS из элемента
        if (selectedGroup.jsonContent && element.hbs) {
          const template = handlebars.compile(element.hbs)
          const jsonData = JSON.parse(selectedGroup.jsonContent)
          html = template(jsonData)
        }
        // Функционал 2: HBS заполнен в группе - используем его с JSON из элемента
        else if (selectedGroup.hbsContent && element.json) {
          const template = handlebars.compile(selectedGroup.hbsContent)
          const jsonData = typeof element.json === 'string'
            ? JSON.parse(element.json)
            : element.json
          html = template(jsonData)
        }

        if (html) {
          // Освобождаем старый blob URL если есть
          const oldUrl = tempScreenshots.value.get(element.id!)
          if (oldUrl && oldUrl.startsWith('blob:')) {
            URL.revokeObjectURL(oldUrl)
          }

          // Создаем новый временный скриншот с Bootstrap wrapper
          const tempUrl = await generateTempScreenshotWithWrapper(element, html, selectedGroup)
          if (tempUrl) {
            tempScreenshots.value.set(element.id!, tempUrl)
            updateElementHash(element.id!, currentHash)
            generatedCount++
            console.log(`✅ Временный скриншот создан для элемента ${element.number} с группой ${selectedGroupId}`)
          }
        }

      } catch (error) {
        console.error(`Ошибка генерации для элемента ${element.number}:`, error)
      }
    }

    const totalElements = selectedElements.value.length
    let message = ''

    if (generatedCount > 0 && skippedCount > 0) {
      message = `Создано: ${generatedCount}, пропущено: ${skippedCount} (актуальные)`
    } else if (generatedCount > 0) {
      message = `Создано ${generatedCount} скриншотов`
    } else if (skippedCount > 0) {
      message = `Все ${skippedCount} скриншотов актуальны`
    } else {
      message = 'Нет данных для генерации'
    }

    toast.add({
      severity: 'success',
      summary: 'Генерация завершена',
      detail: message,
      life: 3000
    })

  } catch (error) {
    console.error('Error generating screenshots:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сгенерировать скриншоты',
      life: 3000
    })
  } finally {
    generating.value = false
  }
}

// Функция для создания временного скриншота с Bootstrap wrapper (БЕЗ сохранения в Directus)
const generateTempScreenshotWithWrapper = async (element: WElem, html: string, group: DataGroup): Promise<string | null> => {
  try {
    console.log(`Создание временного скриншота для ${element.number} с wrapper...`)

    // Получаем CSS и JS из оригинального элемента
    const css = element.css || ''
    const js = element.js || ''

    // Создаем Bootstrap wrapper
    const rowClass = group.wrapperRowType || 'row'
    const colClass = group.wrapperColWidth || 'col-12'

    // Добавляем Bootstrap CSS если его нет в элементе
    let bootstrapCss = ''
    if (!css.includes('bootstrap')) {
      bootstrapCss = `
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
          .container-fluid { padding: 20px; }
          .${rowClass} { margin: 0; }
          .${colClass} { padding: 15px; }
        </style>
      `
    }

    // Оборачиваем элемент в Bootstrap структуру
    const wrappedHtml = `
      <div class="container-fluid">
        <div class="${rowClass}">
          <div class="${colClass}" id="target-element">
            ${html}
          </div>
        </div>
      </div>
    `

    // Создаем полный HTML для скриншота с CSS/JS из элемента
    const fullHtml = '<!DOCTYPE html>' +
      '<html lang="en">' +
      '<head>' +
      '<meta charset="UTF-8">' +
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
      '<title>' + (element.title || '') + '</title>' +
      bootstrapCss +
      css +
      '</head>' +
      '<body>' +
      wrappedHtml +
      js +
      '</body>' +
      '</html>'

    // Используем API для временных скриншотов с селектором элемента
    const response = await $fetch('/api/capture-element-screenshot-temp', {
      method: 'POST',
      body: {
        html: fullHtml,
        elementSelector: '#target-element', // Захватываем только колонку с элементом
        width: 1400,
        height: 800
      },
      responseType: 'blob'
    })

    // Создаем временный URL из blob
    const tempUrl = URL.createObjectURL(response as Blob)

    console.log(`Временный скриншот с wrapper создан (БЕЗ сохранения в Directus)`)
    return tempUrl

  } catch (error) {
    console.error(`Ошибка создания временного скриншота для ${element.number}:`, error)

    // Fallback: пробуем без селектора элемента
    try {
      console.log(`Fallback: создание скриншота без селектора для ${element.number}`)

      const css = element.css || ''
      const js = element.js || ''

      const fullHtml = '<!DOCTYPE html>' +
        '<html lang="en">' +
        '<head>' +
        '<meta charset="UTF-8">' +
        '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
        '<title>' + (element.title || '') + '</title>' +
        css +
        '</head>' +
        '<body>' +
        html +
        js +
        '</body>' +
        '</html>'

      const response = await $fetch('/api/capture-html-screenshot-temp', {
        method: 'POST',
        body: {
          html: fullHtml,
          width: 1400,
          height: 800
        },
        responseType: 'blob'
      })

      const tempUrl = URL.createObjectURL(response as Blob)
      console.log(`Fallback скриншот создан`)
      return tempUrl

    } catch (fallbackError) {
      console.error(`Fallback также не удался для ${element.number}:`, fallbackError)
      return null
    }
  }
}

// Функция для сохранения временного blob скриншота в Directus
const saveBlobToDirectus = async (blobUrl: string, filename: string): Promise<string | null> => {
  try {
    console.log(`💾 Сохранение временного скриншота в Directus: ${filename}...`)

    // Получаем blob из URL
    const response = await fetch(blobUrl)
    const blob = await response.blob()

    // Создаем FormData для загрузки в Directus
    const formData = new FormData()
    formData.append('title', filename)
    formData.append('file', blob, filename)

    // Загружаем в Directus
    const uploadResponse = await fetch('http://localhost:8055/files', {
      method: 'POST',
      body: formData,
    })

    if (!uploadResponse.ok) {
      throw new Error(`Failed to upload to Directus: ${uploadResponse.statusText}`)
    }

    const data = await uploadResponse.json()
    console.log(`✅ Временный скриншот сохранен в Directus: ${data.data.id}`)

    return data.data.id

  } catch (error) {
    console.error(`❌ Ошибка сохранения временного скриншота в Directus:`, error)
    return null
  }
}

const saveSelectedElements = async () => {
  if (selectedElements.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите элементы для сохранения',
      life: 3000
    })
    return
  }

  // Проверяем, что хотя бы в одной группе есть данные
  const hasAnyData = dataGroups.value.some(group => group.jsonContent || group.hbsContent)
  if (!hasAnyData) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните JSON или HBS поле хотя бы в одной группе',
      life: 3000
    })
    return
  }

  saving.value = true

  try {
    const elementsToSave = []
    const analysisData = []

    console.log(`Обработка ${selectedElements.value.length} выбранных элементов для сохранения...`)

    // Подготавливаем данные для сохранения
    for (let i = 0; i < selectedElements.value.length; i++) {
      const element = selectedElements.value[i]
      const counter = String(i + 1).padStart(2, '0')

      // Генерируем HTML для элемента
      let html = ''
      let sourceData = ''
      let sourceTemplate = ''

      // Получаем выбранную группу для этого элемента
      const selectedGroupId = getSelectedGroupForElement(element.id!)
      const selectedGroup = getGroupData(selectedGroupId)

      if (!selectedGroup) {
        console.warn(`Группа ${selectedGroupId} не найдена для элемента ${element.number}`)
        continue
      }

      // Функционал 1: JSON заполнен в группе - используем его с HBS из элемента
      if (selectedGroup.jsonContent && element.hbs) {
        const template = handlebars.compile(element.hbs)
        const jsonData = JSON.parse(selectedGroup.jsonContent)
        html = template(jsonData)
        sourceData = selectedGroup.jsonContent
        sourceTemplate = element.hbs
      }
      // Функционал 2: HBS заполнен в группе - используем его с JSON из элемента
      else if (selectedGroup.hbsContent && element.json) {
        const template = handlebars.compile(selectedGroup.hbsContent)
        const jsonData = typeof element.json === 'string' ? JSON.parse(element.json) : element.json
        html = template(jsonData)
        sourceData = typeof element.json === 'string' ? element.json : JSON.stringify(element.json, null, 2)
        sourceTemplate = selectedGroup.hbsContent
      }

      // Используем уже созданный скриншот или создаем новый
      let screenshotId = null
      if (html) {
        // Генерируем хэш для проверки актуальности
        const currentHash = generateDataHash(element, selectedGroupId)

        // Проверяем, есть ли актуальный временный скриншот
        if (isScreenshotActual(element.id!, currentHash)) {
          // Если есть актуальный временный скриншот, сохраняем его в Directus
          try {
            const tempBlobUrl = tempScreenshots.value.get(element.id!)
            if (tempBlobUrl) {
              const filename = `welem_gen_${element.number}_${element.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}.png`
              screenshotId = await saveBlobToDirectus(tempBlobUrl, filename)
              console.log(`✅ Временный скриншот сохранен в Directus для элемента ${element.number}: ${screenshotId}`)
            } else {
              console.warn(`⚠️ Временный скриншот не найден для элемента ${element.number}`)
            }
          } catch (error) {
            console.error(`Ошибка сохранения временного скриншота для элемента ${element.number}:`, error)
          }
        }
      }

      // Определяем collection для этого элемента
      const collection = (selectedGroup.collection && selectedGroup.collection.length > 0)
        ? selectedGroup.collection
        : formData.value.collection

      const elementData = {
        number: `${formData.value.number}-${counter}`,
        title: `${formData.value.title}-${counter}`,
        collection: collection,
        json: sourceData,
        hbs: sourceTemplate,
        html: html,
        css: element.css || '',
        js: element.js || '',
        sketch: screenshotId,
        status: 'draft'
      }

      elementsToSave.push(elementData)
      analysisData.push({
        html: html,
        tempIndex: i
      })
    }

    // Сохраняем элементы в базу
    const savedElements = await createItems({
      collection: 'welem_proto',
      items: elementsToSave
    })

    console.log(`Сохранено ${savedElements.length} элементов`)

    // Анализируем HTML и обновляем поля
    if (analysisData.length > 0) {
      try {
        const analysisRecords = savedElements.map((element: any, index: number) => ({
          id: element.id,
          html: analysisData[index].html
        }))

        const { results } = await $fetch('/api/batch-analyze-html-element', {
          method: 'POST',
          body: { records: analysisRecords }
        })

        // Обновляем элементы с результатами анализа
        for (const result of results) {
          await updateItem({
            collection: 'welem_proto',
            id: result.id,
            item: {
              elem_type: result.elementTypes,
              composition: result.treeStructure
            }
          })
        }

        console.log('Анализ HTML завершен')
      } catch (analysisError) {
        console.error('Ошибка анализа HTML:', analysisError)
      }
    }

    // Очищаем временные blob URL из памяти
    tempScreenshots.value.forEach(url => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url)
      }
    })

    // Очищаем выбранные элементы и временные скриншоты
    selectedElements.value = []
    tempScreenshots.value.clear()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранено ${savedElements.length} элементов`,
      life: 3000
    })

  } catch (error) {
    console.error('Error saving elements:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить элементы',
      life: 3000
    })
  } finally {
    saving.value = false
  }
}

// Инициализация
onMounted(async () => {
  await loadOptions()
  await loadAllElements() // Загружаем элементы сразу при загрузке страницы
})
</script>

<style scoped>
.my-editor {
    /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
    background: #f3f3f3;
    color: #666;

    /* you must provide font-family font-size line-height. Example: */
    font-family:
      Fira code,
      Fira Mono,
      Consolas,
      Menlo,
      Courier,
      monospace;
    font-size: 10px;
    line-height: 1.4;
    padding: 2px;
  }

.my-editor .prism-editor__textarea:focus {
  outline: none;
}

.masonry-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 1rem;
  grid-auto-rows: min-content;
  align-items: start;
}

@media (max-width: 1024px) {
  .masonry-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .masonry-grid {
    grid-template-columns: 1fr;
  }
}

/* Улучшенное выделение выбранных карточек */
.selected-card {
  border: 1px solid #68a0f8 !important;
  background-color: rgba(59, 131, 246, 0.24) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}
</style>
