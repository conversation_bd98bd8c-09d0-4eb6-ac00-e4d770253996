import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { writeFile, unlink } from 'fs/promises'
import { resolve } from 'path'
import { fetch } from 'undici'

const BASE_URL = 'http://localhost:3010'

const TEST_FILE_NAME = 'test-note.md'
const TEST_CONTENT = '# Test Note\nThis is a test note content.'
const NOTES_DIR = resolve(process.cwd(), 'my-notes')
const TEST_FILE_PATH = resolve(NOTES_DIR, TEST_FILE_NAME)

describe('Notes API', () => {
  // Создаем тестовый файл перед тестами
  beforeAll(async () => {
    await writeFile(TEST_FILE_PATH, TEST_CONTENT, 'utf-8')
  })

  // Удаляем тестовый файл после тестов
  afterAll(async () => {
    try {
      await unlink(TEST_FILE_PATH)
    } catch (error) {
      console.error('Error cleaning up test file:', error)
    }
  })

  it('should return list of markdown files', async () => {
    const response = await fetch(`${BASE_URL}/api/notes`)
    const data = await response.json()
    expect(Array.isArray(data)).toBe(true)
    const testFile = data.find(file => file.name === TEST_FILE_NAME)
    expect(testFile).toBeDefined()
    expect(testFile.path).toBe(TEST_FILE_NAME)
  })

  it('should return file content', async () => {
    const response = await fetch(`${BASE_URL}/api/notes/${TEST_FILE_NAME}`)
    const data = await response.json()
    expect(data).toBeDefined()
    expect(data.content).toBe(TEST_CONTENT)
  })

  it('should update file content', async () => {
    const newContent = '# Updated Test Note\nThis content has been updated.'
    await fetch(`${BASE_URL}/api/notes/${TEST_FILE_NAME}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ content: newContent })
    })

    const response = await fetch(`${BASE_URL}/api/notes/${TEST_FILE_NAME}`)
    const data = await response.json()
    expect(data.content).toBe(newContent)
  })
})