<template>
  <div class="flex flex-col gap-4 p-1">
    <div class="flex h-[calc(100vh-1rem)]">
      <!-- Левый сайдбар -->
      <div class="flex" :style="{ width: leftSidebarWidth }">
        <div class="flex flex-col w-full bg-surface-50 dark:bg-surface-800 border-r border-surface-200 dark:border-surface-700">
          
          <!-- Область добавления (70% высоты) -->
          <div class="flex flex-col" style="height: 70%;">
            <!-- Toolbar для блоков -->
            <div class="structure-toolbar toolbar-container flex items-center p-1 border-b border-surface-200 dark:border-surface-700 bg-white flex-none">
              <div class="flex items-center gap-1">
                <!-- Кнопка обновления -->
                <Button
                  v-tooltip.top="'Обновить данные'"
                  icon="pi pi-refresh"
                  class="p-button-sm p-button-text"
                  @click="loadBlockProtoData"
                />

                <!-- Поиск -->
                <div class="relative">
                  <Button
                    icon="pi pi-search"
                    class="p-button-sm p-button-text"
                    @click="toggleBlockSearch"
                  />
                  <div v-if="showBlockSearch" class="search-dropdown">
                    <InputText
                      v-model="blockProtoSearch"
                      placeholder="Поиск блоков..."
                      class="w-full text-xs"
                      style="font-size: 12px"
                    />
                  </div>
                </div>

                <!-- Фильтр по типам блоков -->
                <div class="relative">
                  <Button
                    icon="pi pi-filter"
                    class="p-button-sm p-button-text"
                    @click="toggleBlockFilters"
                  />
                  <div v-if="showBlockFilters" class="filters-dropdown">
                    <MultiSelect
                      v-model="selectedBlockTypes"
                      :options="blockTypeOptions"
                      filter
                      placeholder="Типы блоков"
                      class="w-48 text-xs"
                      display="chip"
                      panel-class="text-xs"
                    />
                  </div>
                </div>

                <!-- Фильтр по коллекциям -->
                <div class="relative">
                  <Button
                    icon="pi pi-folder"
                    class="p-button-sm p-button-text"
                    @click="toggleCollectionFilters"
                  />
                  <div v-if="showCollectionFilters" class="filters-dropdown">
                    <MultiSelect
                      v-model="selectedCollections"
                      :options="collectionOptions"
                      filter
                      placeholder="Коллекции"
                      class="w-48 text-xs"
                      display="chip"
                      panel-class="text-xs"
                    />
                  </div>
                </div>

                <!-- Добавить выбранные на холст -->
                <Button
                  v-tooltip.top="'Добавить выбранные на холст'"
                  icon="pi pi-plus"
                  class="p-button-sm p-button-text"
                  :disabled="selectedBlocks.length === 0"
                  @click="addSelectedBlocksToCanvas"
                />
              </div>
            </div>

            <!-- Плитка блоков (3 колонки) с собственной прокруткой -->
            <div class="flex-1 overflow-auto p-1">
              <div class="grid grid-cols-3 gap-1">
                <div
                  v-for="block in filteredBlocks"
                  :key="block.id"
                  class="block-card relative border rounded p-1 cursor-pointer hover:bg-surface-100 dark:hover:bg-surface-700 group"
                  :class="{ 'bg-blue-50 border-blue-300': selectedBlocks.includes(block.id) }"
                >
                  <!-- Превью изображения -->
                  <div class="w-full h-12 mb-1 bg-surface-200 rounded flex items-center justify-center relative overflow-hidden">
                    <Image
                      v-if="block.sketch"
                      :src="'http://localhost:8055/assets/' + block.sketch"
                      :alt="block.title"
                      class="w-full h-full object-cover rounded cursor-pointer"
                      preview
                    />
                    <i v-else class="pi pi-image text-surface-400 text-xs"/>

                    <!-- Чекбокс поверх картинки -->
                    <Checkbox
                      v-model="selectedBlocks"
                      :value="block.id"
                      class="absolute top-0.5 left-0.5 scale-75"
                    />

                    <!-- Кнопки действий при наведении -->
                    <div class="absolute top-0.5 right-0.5 opacity-0 group-hover:opacity-100 transition-opacity flex gap-0.5">
                      <Button
                        icon="pi pi-plus"
                        class="p-button-sm p-button-text p-button-rounded"
                        style="width: 16px; height: 16px; font-size: 8px;"
                        @click="addSingleBlockToCanvas(block)"
                      />
                      <Button
                        icon="pi pi-ellipsis-h"
                        class="p-button-sm p-button-text p-button-rounded"
                        style="width: 16px; height: 16px; font-size: 8px;"
                        @click="showBlockActions(block, $event)"
                      />
                    </div>
                  </div>

                  <!-- Номер и название -->
                  <div class="text-xs text-surface-600" style="font-size: 7px; line-height: 1;">{{ block.number }}</div>
                  <div class="text-xs font-medium truncate" style="font-size: 8px; line-height: 1.1;">{{ block.title }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Область схемы (30% высоты) -->
          <div class="flex flex-col border-t border-surface-200 dark:border-surface-700" style="height: 30%;">
            <div class="p-1 bg-surface-100 dark:bg-surface-900 text-xs font-medium border-b flex-none">
              Схема страницы
            </div>
            <div class="flex-1 overflow-auto p-1">
              <div
                v-for="(block, index) in canvasBlocks"
                :key="block.tempId"
                class="schema-block relative p-1 mb-1 bg-white dark:bg-surface-800 border rounded cursor-pointer hover:bg-surface-50 group"
                :class="{ 'bg-blue-50 border-blue-300': selectedBlock?.tempId === block.tempId }"
                draggable="true"
                @click="selectSchemaBlock(block)"
                @dragstart="onDragStart(index, $event)"
                @dragover.prevent
                @drop="onDrop(index, $event)"
              >
                <div class="flex items-center">
                  <i class="pi pi-bars text-surface-400 mr-1" style="font-size: 8px;"/>
                  <span class="text-xs truncate flex-1" style="font-size: 9px;">{{ block.title }}</span>
                </div>

                <!-- Компактные кнопки управления при наведении -->
                <div class="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity bg-white dark:bg-surface-800 rounded-bl border-l border-b flex">
                  <Button
                    v-if="index > 0"
                    icon="pi pi-arrow-up"
                    class="p-button-sm p-button-text"
                    style="width: 16px; height: 16px; font-size: 8px;"
                    @click.stop="moveBlockUp(index)"
                  />
                  <Button
                    v-if="index < canvasBlocks.length - 1"
                    icon="pi pi-arrow-down"
                    class="p-button-sm p-button-text"
                    style="width: 16px; height: 16px; font-size: 8px;"
                    @click.stop="moveBlockDown(index)"
                  />
                  <Button
                    icon="pi pi-copy"
                    class="p-button-sm p-button-text"
                    style="width: 16px; height: 16px; font-size: 8px;"
                    @click.stop="duplicateBlock(index)"
                  />
                  <Button
                    icon="pi pi-pencil"
                    class="p-button-sm p-button-text"
                    style="width: 16px; height: 16px; font-size: 8px;"
                    @click.stop="editSchemaBlock(block)"
                  />
                  <Button
                    icon="pi pi-trash"
                    class="p-button-sm p-button-text p-button-danger"
                    style="width: 16px; height: 16px; font-size: 8px;"
                    @click.stop="removeBlock(index)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Центральная часть -->
      <div class="flex flex-col" :style="{ width: centerWidth }">
        <!-- Верхняя строка управления -->
        <div class="flex items-center gap-2 p-2 bg-surface-0 dark:bg-surface-900 border-b border-surface-200 dark:border-surface-700">
          <!-- Undo/Redo -->
          <div class="flex gap-1">
            <Button
              v-tooltip.top="'Отменить'"
              icon="pi pi-undo"
              class="p-button-sm p-button-text"
              :disabled="!canUndo"
              @click="undo"
            />
            <Button
              v-tooltip.top="'Повторить'"
              icon="pi pi-redo"
              class="p-button-sm p-button-text"
              :disabled="!canRedo"
              @click="redo"
            />
          </div>

          <!-- Поля страницы -->
          <InputText
            v-model="pageData.number"
            placeholder="Номер"
            class="w-20 text-xs"
            style="font-size: 11px"
          />
          <InputText
            v-model="pageData.title"
            placeholder="Название страницы"
            class="flex-1 text-xs"
            style="font-size: 12px"
          />
          <MultiSelect
            v-model="pageData.tags"
            :options="pageTagsOptions"
            display="chip"
            class="text-xs max-w-sm"
            filter
            placeholder="Теги"
            panel-class="text-xs"
          />
          <MultiSelect
            v-model="pageData.wpage_type"
            :options="pageTypeOptions"
            display="chip"
            class="text-xs max-w-sm"
            filter
            placeholder="Тип страницы"
            panel-class="text-xs"
          />

          <!-- Viewport переключатели -->
          <div class="flex gap-1">
            <Button
              v-for="device in devices"
              :key="device.value"
              :icon="device.icon"
              :class="[
                'p-button-sm',
                activeDevice === device.value ? 'p-button-info' : 'p-button-outlined'
              ]"
              @click="setActiveDevice(device.value)"
            />
          </div>

          <!-- Действия -->
          <div class="flex gap-1">
            <Button
              v-tooltip.top="'Скачать'"
              icon="pi pi-download"
              class="p-button-sm p-button-text"
              @click="downloadPage"
            />
            <Button
              v-tooltip.top="'Просмотр'"
              icon="pi pi-eye"
              class="p-button-sm p-button-text"
              @click="previewPage"
            />
            <Button
              v-tooltip.top="'Сохранить страницу'"
              icon="pi pi-save"
              class="p-button-sm p-button-success"
              @click="savePage"
            />
            <Button
              v-tooltip.top="'Сохранить блоки'"
              icon="pi pi-save"
              class="p-button-sm p-button-info"
              @click="saveBlocks"
            />
            <Button
              v-tooltip.top="'Сохранить страницу и блоки'"
              icon="pi pi-save"
              class="p-button-sm p-button-warning"
              @click="savePageAndBlocks"
            />
          </div>
        </div>

        <!-- Холст (iframe) -->
        <div class="flex-1 flex justify-center p-2 bg-surface-100 dark:bg-surface-900">
          <div
            :class="{
              'mx-auto max-w-[375px] border rounded-lg': activeDevice === 'mobile',
              'mx-auto max-w-[768px] border rounded-lg': activeDevice === 'tablet',
              'mx-auto max-w-[1280px] border rounded-lg': activeDevice === 'desktop',
            }"
            style="width: 100%; height: 100%;"
          >
            <iframe
              v-if="compiledHtml"
              :srcdoc="getFullHtml()"
              class="w-full h-full border-0 rounded-lg"
            />
            <div v-else class="flex items-center justify-center h-full text-surface-500">
              <div class="text-center">
                <i class="pi pi-file-o text-4xl mb-2"/>
                <p>Добавьте блоки для создания страницы</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Нижние табы -->
        <div class="flex-none h-64 border-t border-surface-200 dark:border-surface-700">
          <TabView v-model:active-index="activeTabIndex">
            <TabPanel header="HTML">
              <PrismEditorWithCopy
                v-model="compiledHtml"
                :highlight="highlightHtml"
                placeholder="HTML код страницы"
                field-name="HTML"
                class="text-xs"
                style="height: 200px;"
              />
            </TabPanel>
            <TabPanel header="CSS + JS">
              <div class="flex gap-2 h-full">
                <div class="flex-1">
                  <div class="text-xs mb-1 flex items-center gap-2">
                    CSS
                    <Button
                      label="BS"
                      class="p-button-sm p-button-outlined text-xs"
                      @click="addBootstrap"
                    />
                  </div>
                  <PrismEditorWithCopy
                    v-model="pageData.css"
                    :highlight="highlightCss"
                    placeholder="CSS код"
                    field-name="CSS"
                    class="text-xs"
                    style="height: 160px;"
                  />
                </div>
                <div class="flex-1">
                  <div class="text-xs mb-1 flex items-center gap-2">
                    JavaScript
                    <Button
                      label="VJ"
                      class="p-button-sm p-button-outlined text-xs"
                      @click="addVueJs"
                    />
                  </div>
                  <PrismEditorWithCopy
                    v-model="pageData.js"
                    :highlight="highlightJs"
                    placeholder="JavaScript код"
                    field-name="JavaScript"
                    class="text-xs"
                    style="height: 160px;"
                  />
                </div>
              </div>
            </TabPanel>
          </TabView>
        </div>
      </div>

      <!-- Правый сайдбар -->
      <div class="flex" :style="{ width: rightSidebarWidth }">
        <div class="flex flex-col w-full bg-surface-50 dark:bg-surface-800 border-l border-surface-200 dark:border-surface-700">
          <div class="p-2 bg-surface-100 dark:bg-surface-900 text-xs font-medium border-b">
            {{ sidebarMode === 'edit-canvas-block' ? 'Редактирование блока' : 'Редактирование прототипа' }}
          </div>
          <div class="flex-1 overflow-auto p-2">
            <!-- Режим редактирования добавленного блока -->
            <div v-if="sidebarMode === 'edit-canvas-block' && selectedBlock">
              <!-- Базовая информация -->
              <div class="space-y-2 mb-3">
                <div class="field">
                  <Textarea
                    v-model="selectedBlock.description"
                    rows="2"
                    class="w-full text-xs"
                    placeholder="Описание"
                    style="font-size: 10px"
                  />
                </div>

                <div class="field">
                  <MultiSelect
                    v-model="selectedBlock.block_type"
                    :options="blockTypeOptions"
                    placeholder="Типы блока"
                    display="chip"
                    class="text-xs w-full"
                    panel-class="text-xs"
                    style="font-size: 11px"
                  />
                </div>

                <div class="field">
                  <Textarea
                    v-model="selectedBlock.notes"
                    rows="2"
                    class="w-full text-xs"
                    placeholder="Заметки"
                    style="font-size: 10px"
                  />
                </div>
              </div>

              <!-- Табы для редактирования -->
              <TabView v-model:active-index="rightSidebarTabIndex">
                <TabPanel header="Редактор">
                  <!-- Интеллектуальный редактор JSON (аналогично WBlockProtoSidebarV2) -->
                  <div class="json-editor-container">
                    <!-- TODO: Добавить интеллектуальный редактор -->
                    <div class="text-xs text-surface-500 p-2">
                      Интеллектуальный редактор JSON будет добавлен
                    </div>
                  </div>
                </TabPanel>

                <TabPanel header="JSON">
                  <PrismEditorWithCopy
                    v-model="selectedBlock.json"
                    :highlight="highlightJson"
                    placeholder="JSON данные блока"
                    field-name="JSON"
                    class="text-xs"
                    style="height: 300px;"
                  />
                </TabPanel>

                <TabPanel header="HBS">
                  <PrismEditorWithCopy
                    v-model="selectedBlock.hbs"
                    :highlight="highlightHtml"
                    placeholder="HBS шаблон блока"
                    field-name="HBS"
                    class="text-xs"
                    style="height: 300px;"
                  />
                </TabPanel>

                <TabPanel header="Картинки">
                  <!-- Интеллектуальный редактор картинок (аналогично WBlockProtoSidebarV2) -->
                  <div class="images-editor-container">
                    <!-- TODO: Добавить интеллектуальный редактор картинок -->
                    <div class="text-xs text-surface-500 p-2">
                      Интеллектуальный редактор картинок будет добавлен
                    </div>
                  </div>
                </TabPanel>
              </TabView>

              <!-- Кнопки действий -->
              <div class="flex gap-2 pt-3">
                <Button
                  label="Применить"
                  icon="pi pi-check"
                  class="p-button-sm p-button-success flex-1"
                  @click="applyBlockChanges"
                />
                <Button
                  label="Сброс"
                  icon="pi pi-refresh"
                  class="p-button-sm p-button-outlined flex-1"
                  @click="resetBlockChanges"
                />
              </div>
            </div>

            <!-- Заглушка когда блок не выбран -->
            <div v-else-if="sidebarMode === 'edit-canvas-block'" class="text-center text-surface-500 mt-8">
              <i class="pi pi-info-circle text-2xl mb-2"/>
              <p class="text-xs">Выберите блок из схемы для редактирования</p>
            </div>

            <!-- Режим редактирования блока из базы -->
            <div v-else-if="sidebarMode === 'edit-proto-block'" class="text-center text-surface-500 mt-8">
              <i class="pi pi-pencil text-2xl mb-2"/>
              <p class="text-xs">Редактирование блока из базы</p>
              <p class="text-xs text-surface-400 mt-2">Используйте боковую панель</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Диалоги и всплывающие окна -->
    <Toast />
    <Dialog v-model:visible="showPreview" modal header="Предпросмотр страницы" :style="{ width: '90vw', height: '90vh' }">
      <iframe :srcdoc="getFullHtml()" class="w-full h-full border-0" />
    </Dialog>

    <!-- Контекстное меню для блоков -->
    <ContextMenu ref="blockContextMenu" :model="blockContextMenuItems" />

    <!-- Диалог редактирования блока из базы -->
    <!-- TODO: Добавить WBlockProtoSidebarV2 когда будет готов -->
    <Dialog
      v-model:visible="showProtoBlockSidebar"
      modal
      header="Редактирование блока из базы"
      :style="{ width: '50vw' }"
    >
      <div v-if="editingProtoBlock" class="p-4">
        <p>Редактирование блока: {{ editingProtoBlock.title }}</p>
        <div class="flex gap-2 mt-4">
          <Button label="Сохранить" @click="handleProtoBlockSave(editingProtoBlock)" />
          <Button label="Отмена" class="p-button-secondary" @click="closeProtoBlockSidebar" />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import MultiSelect from 'primevue/multiselect'
import Tag from 'primevue/tag'
import Image from 'primevue/image'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import Dialog from 'primevue/dialog'
import Toast from 'primevue/toast'
import ContextMenu from 'primevue/contextmenu'
import 'vue-prism-editor/dist/prismeditor.min.css'
import Prism from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/themes/prism-tomorrow.css'
import { useDirectusItems } from '#imports'
import { useToast } from 'primevue/usetoast'
import handlebars from 'handlebars/dist/handlebars.min.js'


// Определяем название страницы
definePageMeta({
  title: 'Page Builder 2.0'
})

// Composables
const toast = useToast()

// Panel width management - согласно ТЗ
const leftSidebarWidth = ref('calc((100vw - 1280px) / 2)')
const rightSidebarWidth = ref('calc((100vw - 1280px) / 2)')
const centerWidth = ref('1280px')

// Состояние поиска и фильтров
const showBlockSearch = ref(false)
const showBlockFilters = ref(false)
const showCollectionFilters = ref(false)
const blockProtoSearch = ref('')
const selectedBlockTypes = ref([])
const selectedCollections = ref([])
const blockTypeOptions = ref([])
const collectionOptions = ref([])

// Drag & Drop
const draggedIndex = ref(-1)

// Данные блоков
const blockProtoData = ref([])
const selectedBlocks = ref([])
const canvasBlocks = ref([])

// Данные страницы
const pageData = ref({
  number: '',
  title: '',
  tags: [],
  wpage_type: [],
  css: '',
  js: ''
})

// Опции для селектов
const pageTagsOptions = ref([])
const pageTypeOptions = ref([])

// Состояние устройств
const activeDevice = ref('desktop')
const devices = [
  { value: 'mobile', icon: 'pi pi-mobile' },
  { value: 'tablet', icon: 'pi pi-tablet' },
  { value: 'desktop', icon: 'pi pi-desktop' }
]

// Состояние табов
const activeTabIndex = ref(0)
const rightSidebarTabIndex = ref(0)

// HTML компиляция
const compiledHtml = ref('')

// История для undo/redo
const history = ref([])
const historyIndex = ref(-1)
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

// Правый сайдбар
const sidebarMode = ref('edit-canvas-block') // 'edit-canvas-block' | 'edit-proto-block'
const selectedBlock = ref(null)

// Диалоги
const showPreview = ref(false)
const showProtoBlockSidebar = ref(false)
const editingProtoBlock = ref(null)

// Контекстное меню
const blockContextMenu = ref(null)
const blockContextMenuItems = ref([
  {
    label: 'Добавить на холст',
    icon: 'pi pi-plus',
    command: () => addSingleBlockToCanvas(selectedContextBlock.value)
  },
  {
    label: 'Редактировать',
    icon: 'pi pi-pencil',
    command: () => editProtoBlock(selectedContextBlock.value)
  },
  {
    label: 'Дублировать',
    icon: 'pi pi-copy',
    command: () => duplicateProtoBlock(selectedContextBlock.value)
  },
  {
    separator: true
  },
  {
    label: 'Удалить',
    icon: 'pi pi-trash',
    command: () => deleteProtoBlock(selectedContextBlock.value)
  }
])
const selectedContextBlock = ref(null)

// Computed
const filteredBlocks = computed(() => {
  let filtered = blockProtoData.value

  // Поиск
  if (blockProtoSearch.value) {
    const search = blockProtoSearch.value.toLowerCase()
    filtered = filtered.filter(block =>
      block.title?.toLowerCase().includes(search) ||
      block.number?.toLowerCase().includes(search)
    )
  }

  // Фильтр по типам
  if (selectedBlockTypes.value.length > 0) {
    filtered = filtered.filter(block =>
      block.block_type?.some(type => selectedBlockTypes.value.includes(type))
    )
  }

  // Фильтр по коллекциям
  if (selectedCollections.value.length > 0) {
    filtered = filtered.filter(block =>
      block.collection?.some(collection => selectedCollections.value.includes(collection))
    )
  }

  // Сортировка по number
  return filtered.sort((a, b) => {
    const numA = a.number || ''
    const numB = b.number || ''
    return numA.localeCompare(numB, undefined, { numeric: true })
  })
})

// Methods для работы с данными
const loadBlockProtoData = async () => {
  try {
    console.log('Загрузка данных блоков...')
    const { getItems } = useDirectusItems()

    const blocks = await getItems({
      collection: 'wblock_proto',
      params: {
        fields: ['*'],
        sort: ['number', 'title'],
        limit: -1
      }
    })

    blockProtoData.value = blocks || []
    console.log('Загружено блоков:', blockProtoData.value.length)

    // Загружаем опции для фильтров
    await loadFilterOptions()

  } catch (error) {
    console.error('Ошибка загрузки блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось загрузить блоки',
      life: 3000
    })
  }
}

const loadFilterOptions = async () => {
  try {
    // Загружаем уникальные типы блоков и коллекции
    const uniqueBlockTypes = new Set()
    const uniqueCollections = new Set()

    blockProtoData.value.forEach(block => {
      if (block.block_type && Array.isArray(block.block_type)) {
        block.block_type.forEach(type => uniqueBlockTypes.add(type))
      }
      if (block.collection && Array.isArray(block.collection)) {
        block.collection.forEach(collection => uniqueCollections.add(collection))
      }
    })

    blockTypeOptions.value = Array.from(uniqueBlockTypes).sort()
    collectionOptions.value = Array.from(uniqueCollections).sort()

    // Загружаем опции для страниц
    const { getItems } = useDirectusItems()
    const pages = await getItems({
      collection: 'wpage',
      params: {
        fields: ['tags', 'wpage_type'],
        limit: -1
      }
    })

    const uniqueTags = new Set()
    const uniquePageTypes = new Set()

    pages?.forEach(page => {
      if (page.tags && Array.isArray(page.tags)) {
        page.tags.forEach(tag => uniqueTags.add(tag))
      }
      if (page.wpage_type && Array.isArray(page.wpage_type)) {
        page.wpage_type.forEach(type => uniquePageTypes.add(type))
      }
    })

    pageTagsOptions.value = Array.from(uniqueTags).sort()
    pageTypeOptions.value = Array.from(uniquePageTypes).sort()

  } catch (error) {
    console.error('Ошибка загрузки опций:', error)
  }
}

const toggleBlockSearch = () => {
  showBlockSearch.value = !showBlockSearch.value
  if (!showBlockSearch.value) {
    blockProtoSearch.value = ''
  }
}

const toggleBlockFilters = () => {
  showBlockFilters.value = !showBlockFilters.value
}

const toggleCollectionFilters = () => {
  showCollectionFilters.value = !showCollectionFilters.value
}

const addSelectedBlocksToCanvas = () => {
  if (selectedBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Выберите блоки для добавления',
      life: 3000
    })
    return
  }

  const blocksToAdd = blockProtoData.value.filter(block =>
    selectedBlocks.value.includes(block.id)
  )

  blocksToAdd.forEach((block, index) => {
    // Автоматическое именование на основе данных страницы
    const blockIndex = canvasBlocks.value.length + index + 1
    const blockNumber = `${pageData.value.number || 'page'}-${String(blockIndex).padStart(2, '0')}`
    const blockTitle = `${pageData.value.title || 'Page'} - Block ${String(blockIndex).padStart(2, '0')}`

    const canvasBlock = {
      tempId: Date.now() + Math.random() + index, // Уникальный временный ID
      id: block.id,
      number: blockNumber,
      title: blockTitle,
      description: block.description,
      hbs: block.hbs,
      json: block.json,
      css: block.css,
      js: block.js,
      block_type: block.block_type,
      collection: block.collection,
      sketch: block.sketch
    }

    canvasBlocks.value.push(canvasBlock)
  })

  // Очищаем выбор
  selectedBlocks.value = []

  // Компилируем HTML
  compileHtml()

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: `Добавлено блоков: ${blocksToAdd.length}`,
    life: 3000
  })
}

const addSingleBlockToCanvas = (block) => {
  // Автоматическое именование на основе данных страницы
  const blockIndex = canvasBlocks.value.length + 1
  const blockNumber = `${pageData.value.number || 'page'}-${String(blockIndex).padStart(2, '0')}`
  const blockTitle = `${pageData.value.title || 'Page'} - Block ${String(blockIndex).padStart(2, '0')}`

  const canvasBlock = {
    tempId: Date.now() + Math.random(),
    id: block.id,
    number: blockNumber,
    title: blockTitle,
    description: block.description,
    hbs: block.hbs,
    json: block.json,
    css: block.css,
    js: block.js,
    block_type: block.block_type,
    collection: block.collection,
    sketch: block.sketch
  }

  canvasBlocks.value.push(canvasBlock)
  compileHtml()

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: `Блок "${blockTitle}" добавлен`,
    life: 3000
  })
}

const showBlockActions = (block, event) => {
  selectedContextBlock.value = block
  blockContextMenu.value.show(event)
}

const editProtoBlock = (block) => {
  // Открываем WBlockProtoSidebarV2 аналогично другим страницам
  editingProtoBlock.value = { ...block }
  sidebarMode.value = 'edit-proto-block'

  // TODO: Здесь должен открываться настоящий WBlockProtoSidebarV2
  // Пока используем временное решение
  showProtoBlockSidebar.value = true
}

const duplicateProtoBlock = async (block) => {
  try {
    const { createItems } = useDirectusItems()

    const duplicatedBlock = {
      ...block,
      id: undefined,
      number: block.number + '_copy',
      title: block.title + ' (копия)',
      date_created: undefined,
      date_updated: undefined
    }

    await createItems({
      collection: 'wblock_proto',
      items: [duplicatedBlock]
    })

    await loadBlockProtoData()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Блок продублирован',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка дублирования блока:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось продублировать блок',
      life: 3000
    })
  }
}

const deleteProtoBlock = async (block) => {
  if (!confirm(`Удалить блок "${block.title}"?`)) {
    return
  }

  try {
    const { deleteItems } = useDirectusItems()

    await deleteItems({
      collection: 'wblock_proto',
      ids: [block.id]
    })

    await loadBlockProtoData()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Блок удален',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка удаления блока:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось удалить блок',
      life: 3000
    })
  }
}

const handleProtoBlockSave = async (updatedBlock) => {
  try {
    const { updateItem } = useDirectusItems()

    await updateItem({
      collection: 'wblock_proto',
      id: updatedBlock.id,
      item: updatedBlock
    })

    await loadBlockProtoData()
    closeProtoBlockSidebar()

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Блок сохранен',
      life: 3000
    })
  } catch (error) {
    console.error('Ошибка сохранения блока:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блок',
      life: 3000
    })
  }
}

const closeProtoBlockSidebar = () => {
  showProtoBlockSidebar.value = false
  editingProtoBlock.value = null
  sidebarMode.value = 'edit-canvas-block'
}

// Функции для правого сайдбара
const applyBlockChanges = () => {
  if (!selectedBlock.value) return

  // Валидируем JSON
  if (selectedBlock.value.json) {
    const jsonValidation = validateJson(selectedBlock.value.json)
    if (!jsonValidation.valid) {
      toast.add({
        severity: 'error',
        summary: 'Ошибка JSON',
        detail: jsonValidation.error,
        life: 5000
      })
      return
    }
  }

  // Валидируем HBS с JSON данными
  if (selectedBlock.value.hbs) {
    let jsonData = {}
    if (selectedBlock.value.json) {
      try {
        jsonData = JSON.parse(selectedBlock.value.json)
      } catch (e) {
        // JSON уже проверен выше
      }
    }

    const hbsValidation = validateHbs(selectedBlock.value.hbs, jsonData)
    if (!hbsValidation.valid) {
      toast.add({
        severity: 'error',
        summary: 'Ошибка HBS',
        detail: hbsValidation.error,
        life: 5000
      })
      return
    }
  }

  // Применяем изменения и перекомпилируем
  compileHtml()

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Изменения применены',
    life: 3000
  })
}

const resetBlockChanges = () => {
  if (!selectedBlock.value) return

  // Находим оригинальный блок и восстанавливаем его данные
  const originalBlock = blockProtoData.value.find(block => block.id === selectedBlock.value.id)
  if (originalBlock) {
    Object.assign(selectedBlock.value, {
      number: originalBlock.number,
      title: originalBlock.title,
      description: originalBlock.description,
      hbs: originalBlock.hbs,
      json: originalBlock.json,
      block_type: originalBlock.block_type,
      notes: originalBlock.notes
    })

    compileHtml()

    toast.add({
      severity: 'info',
      summary: 'Сброшено',
      detail: 'Изменения отменены',
      life: 3000
    })
  }
}

// Функция компиляции HTML из блоков
const compileHtml = () => {
  let html = ''
  const usedCss = new Set()
  const usedJs = new Set()

  canvasBlocks.value.forEach((block, index) => {
    if (block.hbs) {
      try {
        let blockHtml = block.hbs

        // Если есть JSON данные, компилируем через handlebars
        if (block.json) {
          let jsonData
          try {
            jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
          } catch (jsonError) {
            console.error('Ошибка парсинга JSON для блока:', block.title, jsonError)
            html += `<div style="color:red; padding:10px; border:1px solid red;">Ошибка JSON в блоке "${block.title}": ${jsonError.message}</div>\n\n`
            return
          }

          const template = handlebars.compile(block.hbs)
          blockHtml = template(jsonData)
        }

        html += `<!-- Block ${index + 1}: ${block.title} (ID: ${block.tempId}) -->\n`
        html += blockHtml
        html += '\n\n'

        // Собираем уникальные CSS и JS
        if (block.css && block.css.trim()) {
          usedCss.add(block.css.trim())
        }

        if (block.js && block.js.trim()) {
          usedJs.add(block.js.trim())
        }

      } catch (error) {
        console.error('Ошибка компиляции блока:', block.title, error)
        html += `<div style="color:red; padding:10px; border:1px solid red;">Ошибка компиляции блока "${block.title}": ${error.message}</div>\n\n`
      }
    }
  })

  compiledHtml.value = html

  // Интеллектуально обновляем CSS и JS страницы
  updatePageStyles(Array.from(usedCss))
  updatePageScripts(Array.from(usedJs))
}

// Функция обновления CSS страницы
const updatePageStyles = (blockCssArray) => {
  const currentCss = pageData.value.css || ''
  const cssLines = currentCss.split('\n').filter(line => line.trim())

  // Удаляем старые CSS блоков
  const filteredCss = cssLines.filter(line =>
    !blockCssArray.some(blockCss => currentCss.includes(blockCss))
  )

  // Добавляем новые CSS блоков
  blockCssArray.forEach(css => {
    if (css && !filteredCss.some(line => line.includes(css))) {
      filteredCss.push(css)
    }
  })

  pageData.value.css = filteredCss.join('\n')
}

// Функция обновления JS страницы
const updatePageScripts = (blockJsArray) => {
  const currentJs = pageData.value.js || ''
  const jsLines = currentJs.split('\n').filter(line => line.trim())

  // Удаляем старые JS блоков
  const filteredJs = jsLines.filter(line =>
    !blockJsArray.some(blockJs => currentJs.includes(blockJs))
  )

  // Добавляем новые JS блоков
  blockJsArray.forEach(js => {
    if (js && !filteredJs.some(line => line.includes(js))) {
      filteredJs.push(js)
    }
  })

  pageData.value.js = filteredJs.join('\n')
}

// Функция валидации JSON
const validateJson = (jsonString) => {
  try {
    JSON.parse(jsonString)
    return { valid: true, error: null }
  } catch (error) {
    return { valid: false, error: error.message }
  }
}

// Функция валидации HBS
const validateHbs = (hbsString, jsonData = {}) => {
  try {
    const template = handlebars.compile(hbsString)
    template(jsonData)
    return { valid: true, error: null }
  } catch (error) {
    return { valid: false, error: error.message }
  }
}

const selectSchemaBlock = (block) => {
  selectedBlock.value = block
  console.log('Выбран блок схемы:', block)
}

const moveBlockUp = (index) => {
  if (index > 0) {
    [canvasBlocks.value[index - 1], canvasBlocks.value[index]] = 
    [canvasBlocks.value[index], canvasBlocks.value[index - 1]]
  }
}

const moveBlockDown = (index) => {
  if (index < canvasBlocks.value.length - 1) {
    [canvasBlocks.value[index], canvasBlocks.value[index + 1]] = 
    [canvasBlocks.value[index + 1], canvasBlocks.value[index]]
  }
}

const duplicateBlock = (index) => {
  const block = { ...canvasBlocks.value[index], tempId: Date.now() }
  canvasBlocks.value.splice(index + 1, 0, block)
}

const editSchemaBlock = (block) => {
  selectedBlock.value = block
  sidebarMode.value = 'edit-canvas-block'
}

const removeBlock = (index) => {
  canvasBlocks.value.splice(index, 1)
}

// Drag & Drop функции
const onDragStart = (index, event) => {
  draggedIndex.value = index
  event.dataTransfer.effectAllowed = 'move'
}

const onDrop = (targetIndex, event) => {
  event.preventDefault()
  const sourceIndex = draggedIndex.value

  if (sourceIndex !== -1 && sourceIndex !== targetIndex) {
    const draggedBlock = canvasBlocks.value[sourceIndex]
    canvasBlocks.value.splice(sourceIndex, 1)
    canvasBlocks.value.splice(targetIndex, 0, draggedBlock)
  }

  draggedIndex.value = -1
}

const setActiveDevice = (device) => {
  activeDevice.value = device
}

const undo = () => {
  if (canUndo.value && historyIndex.value > 0) {
    historyIndex.value--
    const state = history.value[historyIndex.value]
    canvasBlocks.value = JSON.parse(JSON.stringify(state.canvasBlocks))
    pageData.value = JSON.parse(JSON.stringify(state.pageData))
    compileHtml()
  }
}

const redo = () => {
  if (canRedo.value && historyIndex.value < history.value.length - 1) {
    historyIndex.value++
    const state = history.value[historyIndex.value]
    canvasBlocks.value = JSON.parse(JSON.stringify(state.canvasBlocks))
    pageData.value = JSON.parse(JSON.stringify(state.pageData))
    compileHtml()
  }
}

const saveToHistory = () => {
  const state = {
    canvasBlocks: JSON.parse(JSON.stringify(canvasBlocks.value)),
    pageData: JSON.parse(JSON.stringify(pageData.value))
  }

  // Удаляем все состояния после текущего индекса
  history.value = history.value.slice(0, historyIndex.value + 1)

  // Добавляем новое состояние
  history.value.push(state)

  // Ограничиваем историю 30 шагами
  if (history.value.length > 30) {
    history.value.shift()
  } else {
    historyIndex.value++
  }
}

const downloadPage = () => {
  const html = getFullHtml()
  const blob = new Blob([html], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${pageData.value.title || 'page'}.html`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Страница скачана',
    life: 3000
  })
}

const previewPage = () => {
  const html = getFullHtml()
  const blob = new Blob([html], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  window.open(url, '_blank')

  // Освобождаем URL через некоторое время
  setTimeout(() => {
    URL.revokeObjectURL(url)
  }, 1000)
}

const savePage = async () => {
  if (!pageData.value.title || !compiledHtml.value) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните название страницы и добавьте блоки',
      life: 3000
    })
    return
  }

  try {
    console.log('📄 Сохранение страницы...')
    const { createItems } = useDirectusItems()

    // Создаем скриншот страницы
    let sketchFileId = null
    try {
      const fullHtml = getFullHtml()
      const filename = `page_${pageData.value.number || 'untitled'}_${pageData.value.title.replace(/[^a-zA-Z0-9]/g, '_')}`
      sketchFileId = await generateScreenshot(fullHtml, filename)
    } catch (screenshotError) {
      console.warn('⚠️ Ошибка создания скриншота страницы:', screenshotError)
    }

    // Анализируем HTML
    let analysisResult = {
      layout: [],
      elements: [],
      graphics: [],
      features: [],
      composition: ''
    }

    try {
      const analysisResponse = await $fetch('/api/analyze-html', {
        method: 'POST',
        body: { html: compiledHtml.value }
      })
      if (analysisResponse) {
        analysisResult = analysisResponse
      }
    } catch (analysisError) {
      console.warn('⚠️ Ошибка анализа HTML:', analysisError)
    }

    // Сохраняем страницу
    const pageDataToSave = {
      number: pageData.value.number || '',
      title: pageData.value.title,
      html: compiledHtml.value,
      css: pageData.value.css || '',
      js: pageData.value.js || '',
      tags: pageData.value.tags || [],
      wpage_type: pageData.value.wpage_type || [],
      sketch: sketchFileId,
      composition: analysisResult.composition,
      layout: analysisResult.layout,
      elements: analysisResult.elements,
      graphics: analysisResult.graphics,
      features: analysisResult.features
    }

    const savedPage = await createItems({
      collection: 'wpage',
      items: [pageDataToSave]
    })

    const pageId = Array.isArray(savedPage) ? savedPage[0].id : savedPage.id
    console.log('✅ Страница сохранена с ID:', pageId)

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Страница "${pageData.value.title}" сохранена`,
      life: 3000
    })

  } catch (error) {
    console.error('❌ Ошибка сохранения страницы:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить страницу',
      life: 3000
    })
  }
}

const saveBlocks = async () => {
  if (canvasBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Нет блоков для сохранения',
      life: 3000
    })
    return
  }

  try {
    console.log('📦 Сохранение блоков...')
    const { createItems } = useDirectusItems()

    const blocksToSave = []

    for (let i = 0; i < canvasBlocks.value.length; i++) {
      const block = canvasBlocks.value[i]

      // Генерируем номер и название блока
      const blockNumber = `${pageData.value.number || 'page'}-${String(i + 1).padStart(2, '0')}`
      const blockTitle = `${pageData.value.title || 'Page'} - Block ${String(i + 1).padStart(2, '0')}`

      // Компилируем HTML блока
      let blockHtml = block.hbs || ''
      if (block.json && block.hbs) {
        try {
          const template = handlebars.compile(block.hbs)
          const jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
          blockHtml = template(jsonData)
        } catch (error) {
          console.warn(`⚠️ Ошибка компиляции блока ${i + 1}:`, error)
        }
      }

      // Создаем скриншот блока
      let blockSketchFileId = null
      try {
        const blockFullHtml = [
          '<!DOCTYPE html>',
          '<html>',
          '<head>',
          '  <meta charset="utf-8">',
          '  <meta name="viewport" content="width=device-width, initial-scale=1">',
          '  <title>' + blockTitle + '</title>' + (pageData.value.css || '') + 
          '</head>',
          '<body>',
          '  ' + blockHtml + (pageData.value.js || '') + 
          '</body>',
          '</html>'
        ].join('\n')

        const blockFilename = `block_${blockNumber}_${block.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`
        blockSketchFileId = await generateScreenshot(blockFullHtml, blockFilename)
      } catch (screenshotError) {
        console.warn(`⚠️ Ошибка создания скриншота блока ${i + 1}:`, screenshotError)
      }

      // Анализируем HTML блока
      let analysisResult = {
        layout: [],
        elements: [],
        graphics: [],
        features: [],
        composition: ''
      }

      try {
        const analysisResponse = await $fetch('/api/analyze-html', {
          method: 'POST',
          body: { html: blockHtml }
        })
        if (analysisResponse) {
          analysisResult = analysisResponse
        }
      } catch (analysisError) {
        console.warn(`⚠️ Ошибка анализа HTML блока ${i + 1}:`, analysisError)
      }

      const blockData = {
        number: blockNumber,
        title: blockTitle,
        description: block.description || '',
        hbs: block.hbs || '',
        json: typeof block.json === 'string' ? block.json : JSON.stringify(block.json || {}),
        html: blockHtml,
        css: block.css || '',
        js: block.js || '',
        block_type: block.block_type || [],
        collection: pageData.value.tags || [],
        composition: analysisResult.composition,
        layout: analysisResult.layout,
        elements: analysisResult.elements,
        graphics: analysisResult.graphics,
        features: analysisResult.features,
        sketch: blockSketchFileId
      }

      blocksToSave.push(blockData)
    }

    const savedBlocks = await createItems({
      collection: 'wblock_proto',
      items: blocksToSave
    })

    console.log('✅ Блоки сохранены:', savedBlocks)

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранено блоков: ${blocksToSave.length}`,
      life: 3000
    })

  } catch (error) {
    console.error('❌ Ошибка сохранения блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить блоки',
      life: 3000
    })
  }
}

const savePageAndBlocks = async () => {
  if (!pageData.value.title || !compiledHtml.value || canvasBlocks.value.length === 0) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Заполните название страницы и добавьте блоки',
      life: 3000
    })
    return
  }

  try {
    console.log('📄📦 Сохранение страницы и блоков...')
    const { createItems } = useDirectusItems()

    // 1. Создаем скриншот страницы
    let pageSketchFileId = null
    try {
      const fullHtml = getFullHtml()
      const filename = `page_${pageData.value.number || 'untitled'}_${pageData.value.title.replace(/[^a-zA-Z0-9]/g, '_')}`
      pageSketchFileId = await generateScreenshot(fullHtml, filename)
    } catch (screenshotError) {
      console.warn('⚠️ Ошибка создания скриншота страницы:', screenshotError)
    }

    // 2. Анализируем HTML страницы
    let pageAnalysisResult = {
      layout: [],
      elements: [],
      graphics: [],
      features: [],
      composition: ''
    }

    try {
      const analysisResponse = await $fetch('/api/analyze-html', {
        method: 'POST',
        body: { html: compiledHtml.value }
      })
      if (analysisResponse) {
        pageAnalysisResult = analysisResponse
      }
    } catch (analysisError) {
      console.warn('⚠️ Ошибка анализа HTML страницы:', analysisError)
    }

    // 3. Сохраняем страницу
    const pageDataToSave = {
      number: pageData.value.number || '',
      title: pageData.value.title,
      html: compiledHtml.value,
      css: pageData.value.css || '',
      js: pageData.value.js || '',
      tags: pageData.value.tags || [],
      wpage_type: pageData.value.wpage_type || [],
      sketch: pageSketchFileId,
      composition: pageAnalysisResult.composition,
      layout: pageAnalysisResult.layout,
      elements: pageAnalysisResult.elements,
      graphics: pageAnalysisResult.graphics,
      features: pageAnalysisResult.features
    }

    const savedPage = await createItems({
      collection: 'wpage',
      items: [pageDataToSave]
    })

    const pageId = Array.isArray(savedPage) ? savedPage[0].id : savedPage.id
    console.log('✅ Страница сохранена с ID:', pageId)

    // 4. Сохраняем блоки (аналогично функции saveBlocks)
    const blocksToSave = []

    for (let i = 0; i < canvasBlocks.value.length; i++) {
      const block = canvasBlocks.value[i]

      const blockNumber = `${pageData.value.number || 'page'}-${String(i + 1).padStart(2, '0')}`
      const blockTitle = `${pageData.value.title || 'Page'} - Block ${String(i + 1).padStart(2, '0')}`

      let blockHtml = block.hbs || ''
      if (block.json && block.hbs) {
        try {
          const template = handlebars.compile(block.hbs)
          const jsonData = typeof block.json === 'string' ? JSON.parse(block.json) : block.json
          blockHtml = template(jsonData)
        } catch (error) {
          console.warn(`⚠️ Ошибка компиляции блока ${i + 1}:`, error)
        }
      }

      let blockSketchFileId = null
      try {
        const blockFullHtml = [
          '<!DOCTYPE html>',
          '<html>',
          '<head>',
          '  <meta charset="utf-8">',
          '  <meta name="viewport" content="width=device-width, initial-scale=1">',
          '  <title>' + blockTitle + '</title>' + (pageData.value.css || '') + 
          '</head>',
          '<body>',
          '  ' + blockHtml + (pageData.value.js || '') + 
          '</body>',
          '</html>'
        ].join('\n')

        const blockFilename = `block_${blockNumber}_${block.title?.replace(/[^a-zA-Z0-9]/g, '_') || 'untitled'}`
        blockSketchFileId = await generateScreenshot(blockFullHtml, blockFilename)
      } catch (screenshotError) {
        console.warn(`⚠️ Ошибка создания скриншота блока ${i + 1}:`, screenshotError)
      }

      let analysisResult = {
        layout: [],
        elements: [],
        graphics: [],
        features: [],
        composition: ''
      }

      try {
        const analysisResponse = await $fetch('/api/analyze-html', {
          method: 'POST',
          body: { html: blockHtml }
        })
        if (analysisResponse) {
          analysisResult = analysisResponse
        }
      } catch (analysisError) {
        console.warn(`⚠️ Ошибка анализа HTML блока ${i + 1}:`, analysisError)
      }

      const blockData = {
        number: blockNumber,
        title: blockTitle,
        description: block.description || '',
        hbs: block.hbs || '',
        json: typeof block.json === 'string' ? block.json : JSON.stringify(block.json || {}),
        html: blockHtml,
        css: block.css || '',
        js: block.js || '',
        block_type: block.block_type || [],
        collection: pageData.value.tags || [],
        composition: analysisResult.composition,
        layout: analysisResult.layout,
        elements: analysisResult.elements,
        graphics: analysisResult.graphics,
        features: analysisResult.features,
        sketch: blockSketchFileId
      }

      blocksToSave.push(blockData)
    }

    const savedBlocks = await createItems({
      collection: 'wblock_proto',
      items: blocksToSave
    })

    const blockIds = Array.isArray(savedBlocks)
      ? savedBlocks.map(block => block.id)
      : [savedBlocks.id]

    // 5. Создаем связи между страницей и блоками
    const relations = blockIds.map(blockId => ({
      wpage_id: pageId,
      wblock_proto_id: blockId
    }))

    await createItems({
      collection: 'wpage_wblock_proto',
      items: relations
    })

    console.log('✅ Страница, блоки и связи сохранены')

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: `Сохранена страница "${pageData.value.title}" и ${blocksToSave.length} блоков со связями`,
      life: 5000
    })

  } catch (error) {
    console.error('❌ Ошибка сохранения страницы и блоков:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить страницу и блоки',
      life: 3000
    })
  }
}

const addBootstrap = () => {
  const bootstrapCss = `
/* Bootstrap CSS */
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
`

  if (!pageData.value.css.includes('bootstrap')) {
    pageData.value.css = bootstrapCss + '\n' + pageData.value.css

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Bootstrap CSS добавлен',
      life: 3000
    })
  } else {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Bootstrap уже добавлен',
      life: 3000
    })
  }
}

const addVueJs = () => {
  const vueJs = `
/* Vue.js */
// Vue.js 3 CDN
const { createApp } = Vue;
`

  if (!pageData.value.js.includes('Vue')) {
    pageData.value.js = vueJs + '\n' + pageData.value.js

    toast.add({
      severity: 'success',
      summary: 'Успешно',
      detail: 'Vue.js добавлен',
      life: 3000
    })
  } else {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Vue.js уже добавлен',
      life: 3000
    })
  }
}

const getFullHtml = () => {
  const title = pageData.value.title || 'Страница'
  const css = pageData.value.css || ''
  const html = compiledHtml.value || ''
  const js = pageData.value.js || ''
  

  return [
    '<!DOCTYPE html>',
    '<html>',
    '<head>',
    '  <meta charset="utf-8">',
    '  <meta name="viewport" content="width=device-width, initial-scale=1">',
    '  <title>' + title + '</title>' + css + 
    '</head>',
    '<body>',
    '  ' + html + js + 
    '</body>',
    '</html>'
  ].join('\n')
}

// Функции подсветки синтаксиса
const highlightHtml = (code: string) => {
  return Prism.highlight(code, Prism.languages.markup, 'html')
}

const highlightCss = (code: string) => {
  return Prism.highlight(code, Prism.languages.css, 'css')
}

const highlightJs = (code: string) => {
  return Prism.highlight(code, Prism.languages.javascript, 'javascript')
}

const highlightJson = (code: string) => {
  return Prism.highlight(code, Prism.languages.json, 'json')
}

// Функция создания скриншота HTML контента
const generateScreenshot = async (htmlContent: string, filename: string = 'screenshot') => {
  try {
    console.log(`📸 Создание скриншота: ${filename}...`)

    const response = await $fetch('/api/capture-html-screenshot', {
      method: 'POST',
      body: {
        html: htmlContent,
        filename: filename,
        width: 1400,
        height: 800
      }
    })

    console.log(`✅ Скриншот создан: ${response.filename}, ID: ${response.fileId}`)
    return response.fileId

  } catch (error) {
    console.error(`❌ Ошибка создания скриншота ${filename}:`, error)
    throw error
  }
}

// Watchers
watch(canvasBlocks, () => {
  compileHtml()
  saveToHistory()
}, { deep: true })

watch(pageData, () => {
  saveToHistory()
}, { deep: true })

// Интеллектуальное обновление iframe при изменении выбранного блока
watch(selectedBlock, (newBlock, oldBlock) => {
  if (newBlock && oldBlock && newBlock.tempId !== oldBlock.tempId) {
    // Переключение между блоками - не перезагружаем iframe
    return
  }

  if (newBlock && oldBlock && newBlock.tempId === oldBlock.tempId) {
    // Изменение текущего блока - обновляем только при применении изменений
    return
  }
}, { deep: true })

// Lifecycle
onMounted(() => {
  loadBlockProtoData()
})
</script>

<style scoped>
.search-dropdown,
.filters-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-width: 200px;
}

.block-card {
  transition: all 0.2s ease;
}

.block-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.schema-block {
  transition: all 0.2s ease;
}

.schema-block:hover {
  transform: translateX(2px);
}

.toolbar-container {
  min-height: 40px;
}
</style>
