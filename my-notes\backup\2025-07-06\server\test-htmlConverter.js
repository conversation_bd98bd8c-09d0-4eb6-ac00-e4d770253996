import { htmlToHandlebarsAndJson } from './utils/htmlToTemplate.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Получаем путь к текущему файлу и директории для ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Тестируем оба примера
const testFiles = ['nab1', 'nab2'];

for (const file of testFiles) {
  console.log(`\n\n=========== Тестирование примера ${file} ===========\n`);
  
  // Загружаем тестовый HTML
  const htmlPath = path.join(__dirname, '..', 'my-notes', `htmlToTemplate-${file}.html`);
  const html = fs.readFileSync(htmlPath, 'utf8');
  
  // Конвертируем HTML в шаблон и JSON
  const result = htmlToHandlebarsAndJson(html);
  
  // Выводим результаты
  console.log('JSON Data:');
  console.log(JSON.stringify(result.jsonData, null, 2));
  
  console.log('\nHandlebars Template (первые 500 символов):');
  console.log(result.hbsTemplate.substring(0, 500) + '...');
  
  // Создаем директорию для результатов, если нет
  const outputDir = path.join(__dirname, '..', 'my-notes');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Записываем результаты в файлы для анализа
  fs.writeFileSync(
    path.join(outputDir, `test-result-${file}.json`), 
    JSON.stringify(result.jsonData, null, 2), 
    'utf8'
  );
  
  fs.writeFileSync(
    path.join(outputDir, `test-result-${file}.hbs`), 
    result.hbsTemplate, 
    'utf8'
  );
} 