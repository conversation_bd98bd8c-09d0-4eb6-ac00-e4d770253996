export default defineNuxtPlugin((nuxtApp) => {
    const fetchJson = async (endpoint) => {
      try {
        console.log('Fetching data from:', endpoint);
        const response = await $fetch(`${endpoint}`);
        console.log('Response:', response);
        return response;
      } catch (error) {
        console.error(`Ошибка при загрузке ${endpoint}:`, error);
        return { data: [] }; // Возвращаем пустой массив, чтобы избежать ошибок в UI
      }
    };
  
    return {
      provide: {
        fetchJson, // Доступно как useNuxtApp().$fetchJson
      },
    };
  });
  
  