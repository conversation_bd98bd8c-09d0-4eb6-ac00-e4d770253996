import { writeFile, readFile } from 'fs/promises'
import { resolve } from 'path'

const NOTES_DIR = resolve(process.cwd(), 'my-notes')

export default defineEventHandler(async (event) => {
  const path = event.context.params?.path
  if (!path) {
    throw createError({
      statusCode: 400,
      message: 'Path parameter is required'
    })
  }

  const method = event.method
  const filePath = resolve(NOTES_DIR, path)

  if (!filePath.startsWith(NOTES_DIR)) {
    throw createError({
      statusCode: 403,
      message: 'Access denied'
    })
  }

  if (method === 'PUT') {
    const body = await readBody(event)
    if (!body?.content) {
      throw createError({
        statusCode: 400,
        message: 'Content is required'
      })
    }

    try {
      await writeFile(filePath, body.content, 'utf-8')
      return { success: true }
    } catch (error) {
      throw createError({
        statusCode: 500,
        message: 'Failed to save file'
      })
    }
  }

  if (method === 'GET') {
    try {
      const content = await readFile(filePath, 'utf-8')
      return { content }
    } catch (error) {
      throw createError({
        statusCode: 500,
        message: 'Failed to read file'
      })
    }
  }

  throw createError({
    statusCode: 405,
    message: 'Method not allowed'
  })
})