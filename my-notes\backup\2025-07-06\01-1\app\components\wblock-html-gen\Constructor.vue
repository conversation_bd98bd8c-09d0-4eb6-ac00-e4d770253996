<template>
  <div class="flex flex-col gap-4">
    <div
      class="min-h-[400px] border-2 border-dashed rounded-lg p-4 transition-colors"
      :class="{
        'border-primary-500 bg-primary-50 dark:bg-primary-900/20': isDragging,
        'border-gray-300 dark:border-gray-600': !isDragging
      }"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <div v-if="elements.length === 0" class="h-full flex items-center justify-center">
        <div class="text-center">
          <i class="pi pi-download text-4xl mb-2 text-gray-400"/>
          <div class="text-gray-500">Перетащите элементы сюда для создания страницы</div>
        </div>
      </div>
      
      <div v-else class="space-y-4">
        <div
          v-for="(element, index) in elements"
          :key="index"
          class="relative group bg-white dark:bg-surface-700 rounded-lg shadow-sm hover:shadow transition-all"
        >
          <!-- Element Preview -->
          <div
            class="p-3 group-hover:bg-primary-50 dark:group-hover:bg-primary-900/20 transition-colors rounded-lg"
            v-html="element.template"
          />
          
          <!-- Element Actions -->
          <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
            <Button
              icon="pi pi-arrow-up"
              text
              rounded
              size="small"
              :disabled="index === 0"
              @click="moveElement(index, 'up')"
            />
            <Button
              icon="pi pi-arrow-down"
              text
              rounded
              size="small"
              :disabled="index === elements.length - 1"
              @click="moveElement(index, 'down')"
            />
            <Button
              icon="pi pi-trash"
              severity="danger"
              text
              rounded
              size="small"
              @click="removeElement(index)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Element {
  name: string
  type: string
  icon: string
  template: string
}

const elements = ref<Element[]>([])
const isDragging = ref(false)

const handleDragOver = () => {
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

const handleDrop = (event: DragEvent) => {
  isDragging.value = false
  const data = event.dataTransfer?.getData('text/plain')
  if (data) {
    try {
      const element = JSON.parse(data) as Element
      elements.value.push(element)
    } catch (error) {
      console.error('Failed to parse dropped element:', error)
    }
  }
}

const removeElement = (index: number) => {
  elements.value.splice(index, 1)
}

const moveElement = (index: number, direction: 'up' | 'down') => {
  if (direction === 'up' && index > 0) {
    const temp = elements.value[index]
    elements.value[index] = elements.value[index - 1]
    elements.value[index - 1] = temp
  } else if (direction === 'down' && index < elements.value.length - 1) {
    const temp = elements.value[index]
    elements.value[index] = elements.value[index + 1]
    elements.value[index + 1] = temp
  }
}
</script>