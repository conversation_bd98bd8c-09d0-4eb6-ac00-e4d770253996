import { promises as fs } from 'fs'
import { join } from 'path'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { type, filename } = query

    if (!type) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Требуется параметр type'
      })
    }

    const userDataDir = join(process.cwd(), 'user-data')
    
    // Если указан конкретный файл
    if (filename) {
      const filePath = join(userDataDir, filename as string)
      try {
        const fileContent = await fs.readFile(filePath, 'utf-8')
        return {
          success: true,
          data: JSON.parse(fileContent),
          filename: filename
        }
      } catch (error) {
        throw createError({
          statusCode: 404,
          statusMessage: 'Файл не найден'
        })
      }
    }

    // Ищем все файлы данного типа
    try {
      const files = await fs.readdir(userDataDir)
      const typeFiles = files.filter(file => file.startsWith(type as string) && file.endsWith('.json'))
      
      if (typeFiles.length === 0) {
        return {
          success: true,
          data: null,
          message: 'Файлы настроек не найдены'
        }
      }

      // Загружаем последний файл (по дате в имени)
      const latestFile = typeFiles.sort().pop()
      const filePath = join(userDataDir, latestFile!)
      const fileContent = await fs.readFile(filePath, 'utf-8')

      return {
        success: true,
        data: JSON.parse(fileContent),
        filename: latestFile,
        availableFiles: typeFiles
      }

    } catch (error) {
      return {
        success: true,
        data: null,
        message: 'Директория настроек не найдена'
      }
    }

  } catch (error) {
    console.error('Ошибка загрузки настроек:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Ошибка загрузки настроек из файла'
    })
  }
})
