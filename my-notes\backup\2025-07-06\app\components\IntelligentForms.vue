<template>
  <div class="intelligent-forms px-1 pb-1">
    <div class="flex items-center justify-between mb-2">
      <h4 class="text-sm font-semibold">🧠 Интеллектуальные формы</h4>
      <div class="flex gap-1">
        <Button
          label="Добавить"
          icon="pi pi-plus"
          class="text-xs p-button-success"
          style="font-size: 10px; padding: 2px 6px"
          @click="addNewForm"
        />
        <Button
          label="Сохранить"
          icon="pi pi-save"
          class="text-xs p-button-primary"
          :disabled="forms.length === 0"
          style="font-size: 10px; padding: 2px 6px"
          @click="saveAllForms"
        />
      </div>
    </div>

    <!-- Формы в горизонтальной прокрутке -->
    <div class="forms-container overflow-x-auto" style="height: 100%;">
      <div class="flex gap-2" style="min-width: fit-content; height: 100%;">
        <div
          v-for="(form, index) in forms"
          :key="form.id"
          class="form-card flex-shrink-0 border rounded p-2 bg-surface-0 dark:bg-surface-900 overflow-y-auto"
          style="width: 350px; height: 100%;"
        >
          <!-- Заголовок формы -->
          <div class="flex items-center justify-between mb-2">
            <span class="text-xs text-gray-500 font-semibold">#{{ index + 1 }}</span>
            <div class="flex gap-1">
              <Button
                icon="pi pi-save"
                class="p-button-text p-button-sm p-button-success"
                style="width: 16px; height: 16px; padding: 0; font-size: 8px"
                @click="saveForm(form, index)"
                v-tooltip="'Сохранить'"
              />
              <Button
                icon="pi pi-copy"
                class="p-button-text p-button-sm"
                style="width: 16px; height: 16px; padding: 0; font-size: 8px"
                @click="duplicateForm(index)"
                v-tooltip="'Дублировать'"
              />
              <Button
                icon="pi pi-trash"
                class="p-button-text p-button-sm p-button-danger"
                style="width: 16px; height: 16px; padding: 0; font-size: 8px"
                @click="removeForm(index)"
                v-tooltip="'Удалить'"
              />
            </div>
          </div>

          <!-- Базовые поля в компактном виде -->
          <div class="space-y-1 mb-2">
            <!-- Первая строка: Артикул (20%) + Название (80%) -->
            <div class="flex gap-1">
              <InputText
                v-model="form.art"
                placeholder="Артикул"
                class="text-xs"
                style="font-size: 9px; padding: 2px; width: 20%"
              />
              <InputText
                v-model="form.title"
                placeholder="Название*"
                class="text-xs flex-1"
                style="font-size: 9px; padding: 2px"
              />
            </div>

            <!-- Вторая строка: Теги (50%) + Описание (50%) -->
            <div class="flex gap-1">
              <MultiSelect
                v-model="form.tags"
                :options="tagOptions"
                placeholder="Теги"
                class="text-xs"
                
                display="chip"
                :maxSelectedLabels="1"
                style="height:30px;font-size: 11px;width: 50%"
              :pt="{
                item: { class: 'text-xs', style: 'margin-top:-8px' },
                header: { class: 'text-xs', style: 'margin-top:-8px' },
                placeholder: { class: 'text-xs', style: 'margin-top:-8px' }
              }"
              />
              <InputText
                v-model="form.description"
                placeholder="Описание"
                class="text-xs flex-1"
                style="font-size: 9px; height:30px; padding: 2px"
              />
            </div>
          </div>

          <!-- Минимальные табы -->
          <TabView
            class="intelligent-form-tabs"
            :pt="{
              nav: { class: 'p-0 mb-1' },
              navContainer: { class: 'p-0' },
              panelcontainer: { style: 'padding:0' },
            }"
          >
            <TabPanel header="JSON" value="json"
            :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
              <div class="p-1">
                <PrismEditorWithCopy
                  v-model="form.json"
                  editor-class="compact-editor"
                  :highlight="highlightJson"
                  placeholder='{"key": "value"}'
                  field-name="JSON"
                  max-height="220px"
                  style="font-size: 8px"
                  @update:model-value="updateFormFields(form)"
                />
              </div>
            </TabPanel>

            <TabPanel header="Редактор" value="editor"
            :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
              <div class="p-1" style="line-height: 1.1;">

                <!-- Быстрое добавление переменных как в сайдбаре -->
                <div class="quick-variables flex flex-wrap gap-1 mb-1" style="margin-bottom: 4px;">
                  <div
                    v-for="varType in quickVariableTypes"
                    :key="varType"
                    class="relative"
                  >
                    <Button
                      :label="varType"
                      class="p-button-sm p-button-outlined"
                      style="font-size: 9px; padding: 1px 2px; height: 16px"
                      @click="addQuickVariable(form, varType)"
                    />
                    <!-- Счетчик использованных переменных -->
                    <span
                      v-if="getVariableCount(form, varType) > 0"
                      class="absolute -top-1 -right-1 bg-blue-500 text-white rounded-full text-xs w-3 h-3 flex items-center justify-center"
                      style="font-size: 6px; min-width: 12px; min-height: 12px"
                    >
                      {{ getVariableCount(form, varType) }}
                    </span>
                  </div>
                </div>

                <!-- Поля переменных -->
                <div v-for="(field, fieldIndex) in form.fields" :key="fieldIndex" class="flex gap-1 items-start mb-1">
                  <div class="w-1/5">
                    <InputText
                      v-model="field.key"
                      class="w-full text-xs"
                      style="font-size: 8px; padding: 1px 2px; height: 18px"
                      placeholder="Переменная"
                      @input="updateFormJson(form)"
                    />
                  </div>
                  <div class="w-3/5 flex gap-1">
                    <div class="flex-1 relative">
                      <Textarea
                        v-if="isTextVariable(field.key)"
                        v-model="field.value"
                        class="w-full text-xs"
                        style="font-size: 8px; padding: 1px 2px; min-height: 18px; line-height: 1.2"
                        placeholder="Значение"
                        rows="1"
                        autoResize
                        @input="updateFormJson(form)"
                      />
                      <InputText
                        v-else
                        v-model="field.value"
                        class="w-full text-xs"
                        style="font-size: 8px; padding: 1px 2px; height: 18px"
                        placeholder="Значение"
                        @input="updateFormJson(form)"
                      />
                    </div>
                    <!-- Кнопка копирования справа от поля -->
                    <Button
                      icon="pi pi-copy"
                      class="p-button-text p-button-sm"
                      style="width: 16px; height: 18px; padding: 0; font-size: 6px; min-width: 16px"
                      @click="copyToClipboard(field.value)"
                      v-tooltip="'Копировать'"
                    />
                  </div>
                  <div class="w-1/12 text-center">
                    <span class="text-xs text-gray-500" style="font-size: 7px">{{ field.value ? field.value.length : 0 }}</span>
                  </div>
                  <div class="w-1/12 flex gap-1">
                    <Button
                      icon="pi pi-clone"
                      class="p-button-text p-button-sm"
                      style="width: 16px; height: 16px; padding: 0; font-size: 6px"
                      @click="duplicateField(form, fieldIndex)"
                      v-tooltip="'Дублировать'"
                    />
                    <Button
                      icon="pi pi-trash"
                      class="p-button-text p-button-sm p-button-danger"
                      style="width: 16px; height: 16px; padding: 0; font-size: 6px"
                      @click="removeField(form, fieldIndex)"
                      v-tooltip="'Удалить'"
                    />
                  </div>
                </div>

                <!-- Добавить поле -->
                <div class="flex gap-2">
                <Button
                  label="+ Поле"
                  class="text-xs p-button-text w-2/3"
                  style="font-size: 9px; padding: 2px; height: 18px; margin-top: 2px"
                  @click="addField(form)"
                />

                <div class="text-xs text-gray-500 text-center" style="font-size: 9px; margin-top: 2px">
                  Переменных: {{ form.fields.length }}
                </div>
              </div>
              </div>
            </TabPanel>

            <TabPanel header="Картинки" value="images"
            :pt="{
                  header: { class: 'p-0' },
                  headerAction: { class: 'text-xs p-0' },
                  content: { class: 'p-0' }
                }">
              <div class="p-2 space-y-2">
                <div v-for="(imageData, imgIndex) in getFormImages(form)" :key="imgIndex" class="border rounded p-1">
                  <Image
                    :src="imageData.url"
                    alt="Preview"
                    width="100"
                    height="60"
                    preview
                    class="w-full mb-1"
                    style="max-height: 60px; object-fit: contain;"
                  />
                  <div class="text-xs font-semibold text-gray-700 mb-1">
                    {{ imageData.variable }}
                  </div>
                  <InputText
                    v-model="imageData.url"
                    class="w-full text-xs"
                    style="font-size: 9px; padding: 2px"
                    placeholder="URL изображения"
                    @input="updateImageInForm(form, imageData.variable, imageData.url)"
                  />
                </div>
                <p v-if="getFormImages(form).length === 0" class="text-xs text-gray-500 text-center py-4">
                  Изображения не найдены
                </p>
              </div>
            </TabPanel>
          </TabView>

          <!-- Статус сохранения -->
          <div class="mt-2 text-center">
            <div v-if="form.saving" class="text-xs text-blue-500">
              <i class="pi pi-spin pi-spinner mr-1"></i>
              Сохранение...
            </div>
            <div v-else-if="form.saved" class="text-xs text-green-500">
              <i class="pi pi-check mr-1"></i>
              Сохранено
            </div>
            <div v-else class="text-xs text-gray-500">
              <i class="pi pi-pencil mr-1"></i>
              Не сохранено
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Пустое состояние -->
    <div v-if="forms.length === 0" class="text-center py-8">
      <i class="pi pi-file-plus text-4xl text-gray-400 mb-4 block"></i>
      <div class="text-gray-500 mb-4">Нет форм для редактирования</div>
      <Button
        label="Создать первую форму"
        icon="pi pi-plus"
        class="p-button-outlined"
        @click="addNewForm"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import TabView from 'primevue/tabview'
import TabPanel from 'primevue/tabpanel'
import Image from 'primevue/image'
import MultiSelect from 'primevue/multiselect'
import PrismEditorWithCopy from '~/components/global/PrismEditorWithCopy.vue'
import { useDirectusItems } from '#imports'

// Props
interface Props {
  constructorData: any[]
  selectedItems: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'forms-updated': [data: any]
  'forms-count-changed': [count: number]
}>()

// Composables
const toast = useToast()
const { getItems, createItems } = useDirectusItems()

// Reactive data
const forms = ref<any[]>([])
const tagOptions = ref<string[]>([])

// Быстрые переменные как в сайдбаре
const quickVariableTypes = ['title', 'image', 'imageBackground', 'url', 'linkText', 'icon', 'text', 'excerpt']

// Функция подсветки JSON
const highlightJson = (code: string) => {
  // Простая подсветка без Prism
  return code
}

// Methods
const addNewForm = () => {
  const newForm = {
    id: Date.now(),
    title: `Форма ${forms.value.length + 1}`,
    art: '',
    description: '',
    tags: [],
    json: '{}',
    fields: [],
    saving: false,
    saved: false
  }
  forms.value.push(newForm)
  emit('forms-count-changed', forms.value.length)
}

const removeForm = (index: number) => {
  forms.value.splice(index, 1)
  emit('forms-count-changed', forms.value.length)
}

const duplicateForm = (index: number) => {
  const original = forms.value[index]
  const duplicate = {
    ...original,
    id: Date.now(),
    title: `${original.title} (копия)`,
    saved: false
  }
  forms.value.splice(index + 1, 0, duplicate)
}

const addField = (form: any) => {
  form.fields.push({ key: '', value: '' })
  updateFormJson(form)
}

const removeField = (form: any, fieldIndex: number) => {
  form.fields.splice(fieldIndex, 1)
  updateFormJson(form)
}

const duplicateField = (form: any, fieldIndex: number) => {
  const originalField = form.fields[fieldIndex]
  const duplicatedField = {
    key: originalField.key + '2',
    value: originalField.value
  }
  form.fields.splice(fieldIndex + 1, 0, duplicatedField)
  updateFormJson(form)
}

const addQuickVariable = (form: any, varType: string) => {
  const count = getVariableCount(form, varType)
  const suffix = count > 0 ? (count + 1).toString() : ''
  const variableName = varType + suffix

  form.fields.push({
    key: variableName,
    value: ''
  })
  updateFormJson(form)
}

const getVariableCount = (form: any, varType: string) => {
  return form.fields.filter((field: any) =>
    field.key.startsWith(varType) &&
    (field.key === varType || /\d+$/.test(field.key.substring(varType.length)))
  ).length
}

// Метод для применения данных из конструктора к выбранным формам
const applyConstructorDataToForms = (constructorData: any[], formIndexes: number[]) => {
  formIndexes.forEach(formIndex => {
    if (formIndex < forms.value.length) {
      const form = forms.value[formIndex]

      constructorData.forEach(item => {
        // Проверяем, есть ли уже такая переменная
        const existingField = form.fields.find((field: any) => field.key === item.variable)

        if (existingField) {
          // Обновляем существующее поле
          existingField.value = item.value
        } else {
          // Добавляем новое поле
          form.fields.push({
            key: item.variable,
            value: item.value
          })
        }
      })

      // Обновляем JSON после добавления полей
      updateFormJson(form)
    }
  })
}

// Экспортируем метод для использования через ref
defineExpose({
  applyConstructorDataToForms
})

const updateFormJson = (form: any) => {
  try {
    const jsonObj: Record<string, any> = {}
    form.fields.forEach((field: any) => {
      if (field.key && field.value) {
        jsonObj[field.key] = field.value
      }
    })
    form.json = JSON.stringify(jsonObj, null, 2)
    form.saved = false
  } catch (error) {
    console.error('Error updating JSON:', error)
  }
}

const updateFormFields = (form: any) => {
  try {
    const jsonObj = JSON.parse(form.json || '{}')

    // Очищаем существующие поля
    form.fields = []

    // Добавляем поля из JSON
    Object.entries(jsonObj).forEach(([key, value]) => {
      form.fields.push({
        key,
        value: String(value)
      })
    })

    form.saved = false
  } catch (error) {
    console.error('Error parsing JSON:', error)
  }
}



const isTextVariable = (key: string) => {
  return key.toLowerCase().includes('text') || 
         key.toLowerCase().includes('excerpt') || 
         key.toLowerCase().includes('description')
}

const getFormImages = (form: any) => {
  const images: Array<{url: string, variable: string}> = []
  form.fields.forEach((field: any) => {
    if ((field.key.toLowerCase().includes('image') || field.key.toLowerCase().includes('img')) && 
        typeof field.value === 'string' && 
        (field.value.startsWith('http') || field.value.startsWith('/'))) {
      images.push({ url: field.value, variable: field.key })
    }
  })
  return images
}

const updateImageInForm = (form: any, variableName: string, newUrl: string) => {
  const field = form.fields.find((f: any) => f.key === variableName)
  if (field) {
    field.value = newUrl
    updateFormJson(form)
  }
}

const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text || '')
  toast.add({
    severity: 'info',
    summary: 'Скопировано',
    detail: 'Значение скопировано в буфер обмена',
    life: 2000
  })
}

const saveForm = async (form: any, index: number) => {
  form.saving = true
  try {
    // Подготавливаем данные для сохранения
    const saveData = {
      art: form.art,
      title: form.title,
      description: form.description,
      tags: form.tags,
      json: form.json
    }

    // Сохраняем в Directus
    await createItems({
      collection: 'wjson',
      items: [saveData]
    })

    form.saved = true
    form.saving = false

    toast.add({
      severity: 'success',
      summary: 'Сохранено',
      detail: `Форма ${index + 1} сохранена в wjson`,
      life: 3000
    })

    emit('forms-updated', forms.value)
  } catch (error) {
    form.saving = false
    console.error('Error saving form:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось сохранить форму',
      life: 3000
    })
  }
}

const saveAllForms = async () => {
  for (let i = 0; i < forms.value.length; i++) {
    await saveForm(forms.value[i], i)
  }
}

const loadTagOptions = async () => {
  try {
    // Загружаем уникальные теги из коллекции wjson
    const items = await getItems({
      collection: 'wjson'
    })

    const allTags = new Set<string>()
    items.forEach((item: any) => {
      if (item.tags && Array.isArray(item.tags)) {
        item.tags.forEach((tag: string) => allTags.add(tag))
      }
    })

    tagOptions.value = Array.from(allTags).sort()
  } catch (error) {
    console.error('Error loading tag options:', error)
    // Fallback теги если не удалось загрузить
    tagOptions.value = ['основное', 'важное', 'дополнительно', 'тест']
  }
}

// Watchers
watch(() => props.selectedItems, (newItems) => {
  // Создаем формы для выбранных элементов
  if (newItems.length > 0 && forms.value.length === 0) {
    newItems.forEach((item, index) => {
      const form = {
        id: item.id || Date.now() + index,
        title: item.title || `Форма ${index + 1}`,
        art: item.art || '',
        description: item.description || '',
        json: item.json || '{}',
        fields: [],
        saving: false,
        saved: false
      }
      
      // Парсим существующий JSON в поля
      try {
        const jsonObj = JSON.parse(item.json || '{}')
        Object.entries(jsonObj).forEach(([key, value]) => {
          form.fields.push({ key, value: String(value) })
        })
      } catch (error) {
        console.error('Error parsing JSON:', error)
      }
      
      forms.value.push(form)
    })
  }
}, { immediate: true })

onMounted(() => {
  loadTagOptions()
})
</script>

<style scoped>
.intelligent-forms {
  height: 100%;
}

.forms-container {
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
}

.form-card {
  height: 100%;
  overflow-y: auto;
}

:deep(.intelligent-form-tabs .p-tabview-nav) {
  padding: 0;
  margin-bottom: 2px;
}

:deep(.intelligent-form-tabs .p-tabview-nav li .p-tabview-nav-link) {
  padding: 2px 6px;
  font-size: 8px;
  height: 20px;
}

:deep(.intelligent-form-tabs .p-tabview-panels) {
  padding: 0;
}

:deep(.intelligent-form-tabs .p-tabview-panel) {
  padding: 0;
}

/* Компактные поля */
:deep(.p-inputtext) {
  line-height: 1.1;
}

:deep(.p-inputtextarea) {
  line-height: 1.2;
}

/* Уменьшаем отступы между элементами */
.space-y-1 > * + * {
  margin-top: 2px !important;
}
</style>
